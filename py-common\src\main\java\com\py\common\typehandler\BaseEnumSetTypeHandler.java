package com.py.common.typehandler;

import com.py.common.enums.IBitEnum;
import com.py.common.utils.EnumUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.EnumSet;


/**
 * 枚举集合类型处理器基类
 * @param <TEnum> 枚举类型, 如: IpServiceType
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@MappedJdbcTypes(JdbcType.BIT)
public abstract class BaseEnumSetTypeHandler<TEnum extends Enum<TEnum> & IBitEnum>
        extends BaseTypeHandler<EnumSet<TEnum>> {

    /**
     * 获取枚举类型
     * @return 枚举类型
     */
    protected abstract Class<TEnum> getEnumClass();

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, EnumSet<TEnum> enumSet, JdbcType jdbcType) throws SQLException {
        preparedStatement.setObject(i, EnumUtils.serializeEnumSet(enumSet));
    }

    @Override
    public EnumSet<TEnum> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object value = rs.getObject(columnName);
        if(value == null && rs.wasNull()) {
            return null;
        }
        return this.deserialize(value);
    }

    @Override
    public EnumSet<TEnum> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object value = rs.getObject(columnIndex);
        if(value == null && rs.wasNull()) {
            return null;
        }
        return this.deserialize(value);
    }

    @Override
    public EnumSet<TEnum> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object value = cs.getObject(columnIndex);
        if(value == null && cs.wasNull()) {
            return null;
        }
        return this.deserialize(value);
    }

    private EnumSet<TEnum> deserialize(Object value) {
        byte[] enumSetValue = (byte[]) value;
        return EnumUtils.deserializeEnumSet(enumSetValue, this.getEnumClass());
    }
}
