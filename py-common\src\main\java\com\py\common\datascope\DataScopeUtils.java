package com.py.common.datascope;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.py.common.constant.Constants;
import com.py.common.core.domain.model.LoginUser;
import com.py.common.core.domain.model.UserDataPermission;
import com.py.common.core.domain.vo.user.AuthUser;
import com.py.common.core.domain.vo.user.AuthUserPostVO;
import com.py.common.exception.ServiceException;
import com.py.common.utils.DateUtils;
import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.Assert;
import rx.functions.Action1;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 数据权限共通
 * <AUTHOR>
 */
@Log4j2
public class DataScopeUtils {

    /** 通用部门关联SQL */
    private static final String COMMON_DEPT_RELATED_SQL = "Select user_id From sys_user_dept " +
            "Inner Join sys_dept on sys_user_dept.dept_id = sys_dept.dept_id ";

    /**
     * 使用默认权限上下文设置数据权限过滤
     * @param wrapper 查询包装器
     * @param pageType 页面类型
     */
    public static void setDateScope(LambdaQueryWrapper<?> wrapper, DataScopePageType pageType) {
        setDateScope(wrapper, pageType, null, null);
    }

    /**
     * 使用默认权限上下文设置数据权限过滤
     * @param wrapper 查询包装器
     * @param pageType 页面类型
     * @param loginUser 登录用户
     */
    public static void setDateScope(LambdaQueryWrapper<?> wrapper, DataScopePageType pageType, LoginUser loginUser) {
        setDateScope(wrapper, pageType, loginUser, null);
    }

    /**
     * 设置数据权限过滤
     * @param wrapper 查询包装器
     * @param pageType 页面类型
     * @param contextModifier 上下文修改器, 用于设置额外的上下文信息. 仅在初始时会调用一次, 不能实现动态变化
     */
    public static void setDateScope(LambdaQueryWrapper<?> wrapper, DataScopePageType pageType, Action1<DataScopeContext> contextModifier) {
        setDateScope(wrapper, pageType, null, contextModifier);
    }

    /**
     * 设置数据权限过滤
     * @param wrapper 查询包装器
     * @param pageType 页面类型
     * @param loginUser 设置数据权限的用户
     * @param contextModifier 上下文修改器, 用于设置额外的上下文信息. 仅在初始时会调用一次, 不能实现动态变化
     */
    public static void setDateScope(LambdaQueryWrapper<?> wrapper, DataScopePageType pageType, LoginUser loginUser, Action1<DataScopeContext> contextModifier) {
        loginUser = NullMergeUtils.nullMerge(loginUser, SecurityUtils.getLoginUser());
        if(loginUser == null || loginUser.isAdmin()) {
            return;
        }

        // 从缓存中获取数据权限上下文, 如果不存在则初始化
        DataScopeContext context = new DataScopeContext();
        context.setPageType(pageType);
        if(contextModifier != null){
            contextModifier.call(context);
        }

        String dataScopeSql = generateDataScopeSql(loginUser, context);
        if(StringUtils.isNotEmpty(dataScopeSql)) {
            wrapper.apply(dataScopeSql);
        }
    }

    /**
     * 获取数据权限过滤
     * @param pageType 页面类型
     */
    public static String getDateScope(DataScopePageType pageType) {
        return getDateScope(pageType, null, null);
    }

    /**
     * 获取数据权限过滤
     * @param pageType 页面类型
     */
    public static String getDateScope(DataScopePageType pageType, Action1<DataScopeContext> contextModifier) {
        return getDateScope(pageType, null, contextModifier);
    }

    /**
     * 获取数据权限过滤
     * @param pageType 页面类型
     */
    public static String getDateScope(DataScopePageType pageType, LoginUser loginUser, Action1<DataScopeContext> contextModifier) {
        loginUser = SecurityUtils.getLoginUser();
        if(loginUser == null || loginUser.isAdmin()) {
            return StringUtils.EMPTY;
        }
        // 从缓存中获取数据权限上下文, 如果不存在则初始化
        DataScopeContext context = new DataScopeContext();
        context.setPageType(pageType);
        if(contextModifier != null){
            contextModifier.call(context);
        }
        return generateDataScopeSql(loginUser, context);
    }

    /**
     * 获取项目数据权限过滤SQL
     * @return 项目数据权限过滤SQL
     */
    public static String getSedProjectCommitDataScopeSql(){
        String projectDataScope = DataScopeUtils.getDateScope(DataScopePageType.SED_ProjectManage,
                context -> {
                    context.setUserIdAlias("user_id");
                    context.setUserTableAlias("py_project_user");
                    context.setDateAlias("project_approval_time");
                    context.setDateTableAlias("py_project");
                });
        // 项目数据权限根据项目用户表关联
        if(StringUtils.isNotBlank(projectDataScope)) {
            projectDataScope = "project_id in (\n" +
                    "Select Distinct project_id\n" +
                    "FROM py_project_user\n" +
                    "Inner join py_project using(project_id)\n" +
                    "Where " + projectDataScope + "\n" +
                    "And py_project_user.del_flag = 0 And py_project.del_flag = 0\n" +
                    ")";
        }
        return projectDataScope;
    }

    /**
     * 生成数据权限SQL
     * @param user 设置数据权限的用户
     * @param dataScopeContext 数据权限上下文
     * @return 数据权限SQL
     */
    public static String generateDataScopeSql(LoginUser user, DataScopeContext dataScopeContext) {
        UserDataPermission dataPermission = user.getDataPermission();
        Assert.notNull(dataPermission, "用户数据权限不能为空");

        String dataScopeSql = getDataScopeTypeSqlList(user, dataScopeContext);
        String mainstayScopeSql = getMainstayScopeSql(dataPermission, dataScopeContext);
        String dateTimeScopeSql = getDateTimeScopeSql(dataPermission, dataScopeContext);
        String splicedSql = dataScopeContext.getSplicedSql();
        return Stream.of(dataScopeSql, mainstayScopeSql, dateTimeScopeSql, splicedSql)
                .filter(StringUtils::isNotBlank)
                .map(sql -> String.format("(%s)", sql))
                .collect(Collectors.joining(" And "));
    }

    /**
     * 获取数据权限SQL列表
     * @param user 用户信息
     * @param context 数据权限上下文
     * @return 数据权限SQL
     */
    private static String getDataScopeTypeSqlList(LoginUser user, DataScopeContext context) {
        DataScopePageType dataScopePageType = context.getPageType();
        Set<DataScopeType> pageDataScopeSet = user.getDataPermission().getPageDataScope(dataScopePageType);

        if(ListUtil.isEmpty(pageDataScopeSet)){
            throw new ServiceException("您的账户未设置页面 %s 的数据权限, 请联系管理员处理", dataScopePageType.getLabel());
        }
        if(pageDataScopeSet.contains(DataScopeType.ALL) || context.isIgnoreUserPermission() == true){
            return StringUtils.EMPTY;
        }

        List<String> dataScopeSqlList = new ArrayList<>(pageDataScopeSet.size());
        for (DataScopeType dataScopeType : pageDataScopeSet) {
            switch(dataScopeType) {
                case SELF:
                    dataScopeSqlList.add(getSelfDataScopeSql(user, context));
                    break;
                case SELF_AND_SUBSIDIARY:
                    dataScopeSqlList.add(getSelfDataScopeSql(user, context));
                    dataScopeSqlList.add(String.format("%s in (Select user_id From sys_user Where Find_In_Set( %d, direct_leader_list)) ",
                            context.getUserAlias(), user.getUserId()));
                    break;
                case DEPT:
                    List<String> deptSqlList = getDeptDataScopeSql(user, context);
                    dataScopeSqlList.addAll(deptSqlList);
                    break;
                case DEPT_AND_CHILD:
                    List<String> deptAndChildSqlList = getDeptAndChildDataScopeSql(user, context);
                    dataScopeSqlList.addAll(deptAndChildSqlList);
                    break;
                default:
                    throw new UnsupportedOperationException(String.format("枚举: %s对应操作未实现", dataScopeType));
            }
        }
        //如果是人脉查询，则需要加流转人记录查询
        if(StringUtils.startsWith(context.getUserTableAlias(), Constants.CRM_CONNECTION)) {
            dataScopeSqlList.add(String.format("FIND_IN_SET(%d, transferor_id_list)", user.getUserId()));
        }

        // 去重并拼接不同权限的SQL
        String userSql = dataScopeSqlList.stream()
                .distinct()
                .filter(StringUtils::isNotBlank)
                .map(sql -> String.format("(%s)", sql))
                .collect(Collectors.joining("Or"));
        // 不是审批查询权限过滤时, 直接返回用户数据权限过滤条件
        if (context.isIgnoreApprovalPermission() == true){
            return userSql;
        }else {
            // 为审批查询权限过滤时, 拼接审批用户表
            Assert.hasText(context.getApprovalAlias(), "");
            return String.format("%s in (select Distinct biz_id from py_flow_data_user where del_flag = 0 and biz_type = #{query.bizType} AND %s)",
                    context.getApprovalAlias(), userSql);
        }
    }

    /**
     * 获取本部门数据权限SQL
     * @param user 用户信息
     * @param context 数据权限上下文
     * @return 本部门数据权限SQL
     */
    private static List<String> getDeptDataScopeSql(LoginUser user, DataScopeContext context) {
        List<String> result = new ArrayList<>(2);
        result.add(getSelfDataScopeSql(user, context));

        String deptSql;
        if(ListUtil.isSingleton(user.getDeptIdList())){
            deptSql = String.format("= %s", ListUtil.first(user.getDeptIdList()));
        }else {
            deptSql = String.format("in (%s)", StringUtils.convertLongList(user.getDeptIdList()));
        }

        result.add(String.format("%s in (" + COMMON_DEPT_RELATED_SQL + "Where sys_dept.dept_id %s And sys_dept.del_flag = 0 )", context.getUserAlias(), deptSql));
        return result;
    }

    /**
     * 获取本部门及下级部门数据权限SQL
     * @param user 用户信息
     * @param context 数据权限上下文
     * @return 本部门及下级部门数据权限SQL
     */
    private static List<String> getDeptAndChildDataScopeSql(LoginUser user, DataScopeContext context) {
        List<String> result = new ArrayList<>(2);
        result.add(getSelfDataScopeSql(user, context));

        String deptSql;
        // 用户单部门的情况
        if(ListUtil.isSingleton(user.getDeptIdList())){
            Long deptId = ListUtil.first(user.getDeptIdList());
            deptSql = String.format("= %s Or Find_In_Set(%d, sys_dept.ancestors)", deptId, deptId);
        }else {
        // 用户多部门的情况
            String mathInSetSql = user.getDeptIdList().stream()
                    .map(Objects::toString)
                    .collect(Collectors.joining("|"));
            deptSql = String.format("in (%s) Or ancestors Regexp '%s'", StringUtils.convertLongList(user.getDeptIdList()), mathInSetSql);
        }

        result.add(String.format("%s in (" + COMMON_DEPT_RELATED_SQL + "Where sys_dept.dept_id %s And sys_dept.del_flag = 0)", context.getUserAlias(), deptSql));

        return result;
    }

    /**
     * 获取本人数据权限SQL
     * @param user 用户信息
     * @param context 数据权限上下文
     * @return 本人数据权限SQL
     */
    private static String getSelfDataScopeSql(LoginUser user, DataScopeContext context) {
        return String.format("%s = %d", context.getUserAlias(), user.getUserId());
    }

    /**
     * 生成数据权限SQL
     * @param user 设置数据权限的用户
     * @param userAlias 用户表别名
     * @param deptAlias 部门表别名
     * @return 数据权限SQL
     */
    public static StringBuilder generateDataScopeSql(AuthUser user, String userAlias, String deptAlias) {
        deptAlias = NullMergeUtils.blankMerge(deptAlias, alias -> alias + ".", StringUtils.EMPTY);
        userAlias = NullMergeUtils.blankMerge(userAlias, alias -> alias + ".", StringUtils.EMPTY);

        Assert.notEmpty(user.getPostList(), "用户角色不能为空");
        //noinspection OptionalGetWithoutIsPresent
        AuthUserPostVO dataScopePost = user.getPostList().stream()
                .map(post -> Pair.of(post.getDataScope(), post))
                .min(java.util.Map.Entry.comparingByKey())
                .get().getRight();

        StringBuilder sqlString = new StringBuilder();
        switch(dataScopePost.getDataScope()) {
            case ALL:
                sqlString = new StringBuilder();
                break;
            case SELF:
                sqlString.append(StringUtils.format(" OR {}user_id = {} ", userAlias, user.getUserId()));
                break;
            case SELF_AND_SUBSIDIARY:
                sqlString.append(StringUtils.format(" OR ( {}user_id = {} Or user_id in (Select user_id From sys_user Where Find_In_Set({}, direct_leader_list)))",
                        userAlias, user.getUserId(), user.getUserId()));
                break;
            case DEPT:
                sqlString.append(StringUtils.format(" OR {}dept_id in {} ", deptAlias, user.getDeptIdList()));
                break;
            case DEPT_AND_CHILD:
                String mathInSetSql = user.getDeptList().stream()
                        .map(Objects::toString)
                        .collect(Collectors.joining("|"));
                sqlString.append(StringUtils.format(
                        " OR {}dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id in {} or ancestors Regexp {} })",
                        deptAlias, user.getDeptIdList(), mathInSetSql));
                break;
            default:
                throw new UnsupportedOperationException(String.format("枚举: %s对应操作未实现", dataScopePost.getDataScope()));
        }
        return sqlString;
    }

    /**
     * 获取主体权限SQL
     * @param dataPermission 用户数据权限
     * @param dataScopeContext 数据权限上下文
     * @return 主体权限SQL
     */
    private static String getMainstayScopeSql(UserDataPermission dataPermission, DataScopeContext dataScopeContext) {
        if(dataScopeContext.getPageType().getHasMainstay() == false || dataScopeContext.isIgnoreMainstayPermission() == true) {
            return StringUtils.EMPTY;
        }

        List<Long> mainstayScope = dataPermission.getPageMainstayScope(dataScopeContext.getPageType());
        if(ListUtil.isEmpty(mainstayScope)) {
            throw new ServiceException("您的账户未设置页面 %s 的主体权限, 请联系管理员处理", dataScopeContext.getPageType().getLabel());
        }

        return String.format("%s in (%s)", dataScopeContext.getMainstayAlias(), StringUtils.convertLongList(mainstayScope));
    }

    /**
     * 获取日期数据权限SQL
     * @param dataPermission 用户数据权限
     * @param dataScopeContext 数据权限上下文
     * @return 日期数据权限SQL
     */
    private static String getDateTimeScopeSql(UserDataPermission dataPermission, DataScopeContext dataScopeContext) {
        LocalDate timeScope = dataPermission.getPageDateTimeScope(dataScopeContext.getPageType());
        if(timeScope == null || timeScope.equals(LocalDate.MIN) || dataScopeContext.isIgnoreDataPermission() == true){
            return StringUtils.EMPTY;
        }

        String dateStr = DateUtils.format(DateUtils.getDayFirstTime(timeScope), DateUtils.YYYY_MM_DD_HH_MM_SS);

        if(dataScopeContext.getCustomDatePermissionSqlGenerator() != null){
            return dataScopeContext.getCustomDatePermissionSqlGenerator()
                    .generate(dateStr, dataScopeContext);
        }else {
            return String.format("%s >= '%s'", dataScopeContext.getDateAlias(), dateStr);
        }
    }
}
