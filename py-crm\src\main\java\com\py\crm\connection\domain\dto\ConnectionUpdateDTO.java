package com.py.crm.connection.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.py.common.tools.modifycomparator.annotation.CompareField;
import com.py.common.typehandler.impl.LongSetTypeHandler;
import com.py.crm.connectionemployment.domain.dto.ConnectionEmploymentDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version ConnectionUpdateDTO 2023/8/11 10:26
 */
@Data
public class ConnectionUpdateDTO {
    private static final long serialVersionUID=1L;


    /** 人脉Id */
    @TableId
    @ApiModelProperty("人脉Id")
    private Long connectionId;

    /** 客户名称 */
    @ApiModelProperty("客户名称")
    @CompareField(value = "客户名称")
    private String connectionName;

    /** 电话 */
    @ApiModelProperty("电话")
    private String phone;

    /** 微信 */
    @ApiModelProperty("微信")
    @CompareField(value = "微信")
    private String wechatNumber;

    /** 钉钉/飞书/其它 */
    @ApiModelProperty("钉钉/飞书/其它")
    @CompareField(value = "钉钉/飞书/其它")
    private String otherNumber;

    /** 现任职企业id(客户id) */
    @ApiModelProperty("现任职企业id(客户id)")
    private Long customerId;

    /** 现任职企业（手动输入） */
    @ApiModelProperty("现任职企业（手动输入）")
    @CompareField(value = "现任职企业")
    private String currentEmployer;

    /** 负责品牌/业务线 */
    @ApiModelProperty("负责品牌/业务线")
    @CompareField(value = "负责品牌/业务线")
    private List<String> responsibleBrand;

    /** 行业类目id*/
    @ApiModelProperty("行业类目id")
    @CompareField(value = "行业类目")
    @TableField(typeHandler = LongSetTypeHandler.class)
    private List<Long> industryCategory;

    /** 所在部门 */
    @ApiModelProperty("所在部门")
    @CompareField(value = "所在部门")
    private String departmentName;

    /** 岗位名称 */
    @ApiModelProperty("岗位名称")
    @CompareField(value = "岗位")
    private String postName;
}
