package com.py.web.controller.system.hp;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.utils.SecurityUtils;
import com.py.system.configuration.MessageEventHandler;
import com.py.system.message.domain.dto.HpMessageDTO;
import com.py.system.message.service.IHpMessageService;
import com.py.system.messageread.domain.dto.HpMessageDeleteDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 首页-消息中心Controller
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Api(tags = "首页-消息中心")
@RestController
@RequestMapping("/message")
public class HpMessageController extends BaseController {

    /** 首页-消息中心服务 */
    @Resource
    private IHpMessageService hpMessageService;



    /**
     * 新增首页-消息中心
     *
     * @param dto 首页-消息中心修改参数
     * @return 是否成功
     */
    @ApiOperation("新增首页-消息中心")
    @PreAuthorize("@ss.hasPermi('hp:message:add')")
    @Log(title = "首页-消息中心", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Boolean> add(@RequestBody HpMessageDTO dto) {
        return R.success(hpMessageService.insertHpMessage(dto));
    }

    /**
     * 删除消息及是否已读记录
     *
     * @param dto 首页-消息参数
     * @return 是否成功
     */
    @ApiOperation("删除消息及是否已读记录")
    @PreAuthorize("@ss.hasPermi('hp:message:delete')")
    @Log(title = "删除消息及是否已读记录", businessType = BusinessType.UPDATE)
    @PostMapping("/delete")
    public R<Boolean> delete(@Validated @RequestBody HpMessageDeleteDTO dto) {
        return R.success(hpMessageService.deleteMessage(dto));
    }

    @Resource
    private MessageEventHandler messageEventHandler;
    /**
     * 广播
     *
     */
    @ApiOperation("测试")
    @GetMapping("/pushToAll")
    public void subscribe() {

        messageEventHandler.sendMessageToPeople("messageevent", SecurityUtils.getUserId());
    }
}
