package com.py.common.tools.reusableasynctask;

import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTask;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTaskArgs;

/**
 * 可视化异步任务服务
 * <AUTHOR>
 */
public interface IReusableAsyncTaskService {

    /**
     * 新增异步任务
     * @param taskName 任务名
     * @param taskType 任务类型
     * @param args 任务参数
     * @param taskClass 任务实现类型
     * @param <TaskArgs> 任务参数类型
     * @param <Task> 任务实现类型
     * @return 任务是否新增成功
     */
    <TaskArgs extends ReusableAsyncTaskArgs, Task extends ReusableAsyncTask<TaskArgs>>
    boolean addTask(String taskName, TaskType taskType, TaskArgs args, Class<Task> taskClass);

}
