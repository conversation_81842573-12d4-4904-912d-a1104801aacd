package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 合同类型 0框架合同，1项目合同
 * <AUTHOR>
 * @version ContractType 2023/8/4 15:52
 */
@AllArgsConstructor
public enum ContractType implements IDict<Integer>{

    /** 0框架合同 */
    FRAMEWORK_CONTRACT(0,"框架合同"),

    /** 1项目合同 */
    PROJECT_CONTRACT(1,"项目合同")
    ;


    private final Integer value;
    private final String label;

    @Override
    public String getLabel() {
        return this.label;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
