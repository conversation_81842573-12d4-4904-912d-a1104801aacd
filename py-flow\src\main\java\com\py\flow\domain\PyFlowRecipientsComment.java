package com.py.flow.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.py.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Description 流程抄送人评论
 * @Date 2023/7/27 13:56
 */
@TableName(value = "py_flow_recipients_comment" , autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class PyFlowRecipientsComment extends BaseEntity {

    /**
     * 流程实例抄送人评论表业务主键id
     */
    @TableId
    private Long flowRecipientsCommentId;

    /**
     * 流程实例状态业务主键id
     */
    private Long flowInstanceStatusId;

    /**
     * 业务类型（0：非业务；1：业务）
     */
    private Integer businessType;

    /**
     * 业务子类型
     *
     * ADD_PROJECT("add_project","新增项目"),
     * UPDATE_STATUS("update_status","修改状态"),
     * UPDATE_APPLY("update_apply","修改申请"),
     * CONFIRM_INCOME("confirm_income","确认收入"),
     * RESOURCE_SINGLE_PAYMENT_PROJECT("resource_single_payment_project","项目内资源单个付款"),
     * RESOURCE_BATCH_PAYMENT_PROJECT("resource_batch_payment_project","项目内资源批量付款"),
     * resource_batch_payment_cost("resource_batch_payment_cost","成本管理资源批量付款"),
     * RESOURCE_REFUND("resource_refund","资源退款"),
     * PROJECT_CLOSURE("project_closure","项目结案"),
     * BATCH_CONFIRM_INCOME("batch_confirm_income","批量确认收入"),
     * BATCH_CLOSURE("batch_closure","批量结案"),
     * PROJECT_COLLECTION_INVOICE("project_collection_invoice","项目收款开票"),
     * RESOURCE_REBATE_INVOICE("resource_rebate_invoice","资源返点开票"),
     * NEW_CUSTOMER_CONTRACT("new_customer_contract","新增客户合同"),
     * NEW_SUPPLIER_CONTRACT("new_supplier_contract","新增供应商合同"),
     * NEW_BAD_DEBT_REVENUE("new_bad_debt_revenue","新增收入坏账"),
     * NEW_RESOURCES_BAD_DEBT_REBATE("new_resources_bad_debt_rebate","新增资源返点坏账"),
     * NEW_CUSTOMER("new_customer","新增客户"),
     * NEW_CONTACTS("new_contacts","新增人脉"),
     * EXPENSE_APPROVAL("expense_approval","费用审批"),
     * OTHER_APPROVAL("other_approval","其它审批"),
     * ROUTINE_CONTRACT_APPROVAL("routine_contract_approval","日常合同审批"),
     * HANDOVER_APPLICATION("handover_application","交接申请");
     * */
    private String businessSubType;

    /**
     * 业务key
     */
    private String businessKey;


    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 抄送人id
     */
    private Long recipientsId;


    /**
     * 评论意见
     */
    private String comment;

    /**
     * 附件，存放oss key 和  文件名称，json数组方式组装存放
     */
    private String attachments;
}
