package com.py.common.enums.flow;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;

import java.util.Arrays;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;


/**
 * 审批流审批状态枚举
 * <AUTHOR>
 * @date 2023/7/27 9:54
 */
@AllArgsConstructor
public enum FlowApprovalStatus implements IDict<Integer> {

    /** 待审批 */
    WAIT_APPROVAL(-1,"待审批"),

    /** 审批中 */
    IN_EXAMINATION_AND_APPROVAL(0,"审批中"),

    /** 审批通过 */
    APPROVE(1,"审批通过"),

    /** 审批撤销 */
    APPROVAL_REVOCATION(2,"审批撤销"),

    /** 审批拒绝 */
    APPROVAL_REJECTION(3,"审批拒绝"),

    /** 审批作废 */
    APPROVAL_CANCELLATION(4,"审批作废"),

    /** 审批退回 */
    APPROVAL_RETURN(5,"审批退回");

    private final Integer value;

    private final String label;

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    /**
     * 当前状态是否为需要缓存快照的状态
     * @return true:是需要缓存快照的状态
     */
    public boolean needSnapshotStatus(){
        return this == APPROVE
            || this == APPROVAL_REVOCATION
            || this == APPROVAL_REJECTION
            || this == APPROVAL_CANCELLATION
            || this == APPROVAL_RETURN;
    }

    /**
     * 当前状态是否在审判中
     * @return true: 在审批中, false: 不在审批中
     */
    public boolean inApproval(){
        return this == WAIT_APPROVAL
            || this == IN_EXAMINATION_AND_APPROVAL;
    }

    /**
     * 审批中状态
     * @return
     */
    public static List<Integer> inApprovalStatus(){
        return Arrays.asList(
                WAIT_APPROVAL.value,
                IN_EXAMINATION_AND_APPROVAL.value
        );
    }


    /**
     * 获取需要重复验证的审批状态列表
     * @return 需要重复验证的审批状态列表
     */
    public static List<Integer> getNeedSameVerifyStatus(){
        return Arrays.asList(WAIT_APPROVAL.value, IN_EXAMINATION_AND_APPROVAL.value, APPROVE.value);
    }
}
