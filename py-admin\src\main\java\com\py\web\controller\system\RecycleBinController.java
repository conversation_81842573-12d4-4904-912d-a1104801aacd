package com.py.web.controller.system;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.utils.StringUtils;
import com.py.system.recyclebin.domain.dto.RecycleBinDeleteDTO;
import com.py.system.recyclebin.domain.query.BatchRestoreQuery;
import com.py.system.recyclebin.domain.query.RecycleBinListQuery;
import com.py.system.recyclebin.domain.query.RestoreQuery;
import com.py.system.recyclebin.domain.vo.RecycleBinListVO;
import com.py.system.recyclebin.numcount.domain.vo.RecycleBinCountVO;
import com.py.system.recyclebin.service.IRecycleBinService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统设置-回收站Controller
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@Api(tags = "系统设置-回收站")
@RestController
@RequestMapping("/system/recycle-bin/")
public class RecycleBinController extends BaseController {

    /** 系统设置-删除记录服务 */
    @Resource
    private IRecycleBinService recycleBinService;

    /**
     * 查询回收站记录
     * @param query 回收站记录列表查询参数
     * @return 回收站记录
     */
    @ApiOperation("查询回收站记录")
    @PreAuthorize("@ss.hasPermi('deletedrecord:deletedRecord:list')")
    @GetMapping("/list")
    public R<PageInfo<RecycleBinListVO>> pageRecycleBinRecode(RecycleBinListQuery query) {
        PageInfo<RecycleBinListVO> voList = this.recycleBinService.pageRecycleBinRecode(query);
        return R.success(voList);
    }

    /**
     * 统计回收站记录条数
     * @return 回收站记录条数
     */
    @ApiOperation("统计回收站记录条数")
    @PreAuthorize("@ss.hasPermi('deletedrecord:deletedRecord:list')")
    @GetMapping("/count")
    public R<RecycleBinCountVO> countRecycleBinRecodeNum() {
        RecycleBinCountVO vo = this.recycleBinService.countRecycleBinRecodeNum();
        return R.success(vo);
    }

    /**
     * 恢复回收站记录
     * @param query 恢复请求
     * @return 是否成功
     */
    @PreAuthorize("@ss.hasPermi('deletedrecord:deletedRecord:restore')")
    @ApiOperation("恢复回收站记录" )
    @PostMapping("/restore" )
    @Log(title = "回收站", businessType = BusinessType.UPDATE)
    public R<Boolean> restore(@Validated @RequestBody RestoreQuery query) {
        this.recycleBinService.restoreRecycleBinRecode(query.getRecordId());
        return R.success();
    }

    /**
     * 批量恢复回收站记录
     * @param query 批量恢复请求
     * @return 恢复中的错误信息
     */
    @ApiOperation("批量恢复回收站记录" )
    @PreAuthorize("@ss.hasPermi('deletedrecord:deletedRecord:restore')")
    @PostMapping("/batchRestore" )
    @Log(title = "回收站", businessType = BusinessType.UPDATE)
    public R<String> batchRestore(@Validated @RequestBody BatchRestoreQuery query) {
        String errorMsg = this.recycleBinService.batchRestoreRecycleBinRecode(query);
        if(StringUtils.isNotBlank(errorMsg)){
            return R.success(StringUtils.EMPTY,errorMsg);
        }
        return R.success();
    }

    /**
     * 刷新记录统计条数
     * @return 刷新结果
     */
    @ApiOperation("刷新记录统计条数")
    @PutMapping("/refreshRecodeNum")
    public R<Boolean> refreshRecodeNum(){
        this.recycleBinService.refreshRecodeNum();
        return R.success();
    }

    /**
     * 批量彻底删除回收站记录
     * @param dto 回收站删除DTO
     * @return 是否成功
     */
    @ApiOperation("批量彻底删除回收站记录" )
    @PreAuthorize("@ss.hasPermi('deletedrecord:deletedRecord:remove')")
    @Log(title = "回收站", businessType = BusinessType.DELETE)
    @DeleteMapping
    public R<Boolean> remove(@RequestBody RecycleBinDeleteDTO dto) {
        this.recycleBinService.deleteRecycleBinRecode(dto);
        return R.success();
    }


    /**
     * 查询列表上的更新部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询列表上的更新部门下拉" )
    @GetMapping("/listReclaimDept" )
    public R<List<String>> listReclaimDept(RecycleBinListQuery query){
        return R.success(recycleBinService.listReclaimDept(query));
    }

}
