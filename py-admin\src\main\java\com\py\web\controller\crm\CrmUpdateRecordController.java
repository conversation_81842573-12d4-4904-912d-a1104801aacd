package com.py.web.controller.crm;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.crm.crmupdaterecord.domain.dto.CrmUpdateRecordDTO;
import com.py.crm.crmupdaterecord.domain.dto.CrmUpdateRecordExportModel;
import com.py.crm.crmupdaterecord.domain.query.CrmUpdateRecordQuery;
import com.py.crm.crmupdaterecord.domain.vo.CrmUpdateRecordListVO;
import com.py.crm.crmupdaterecord.domain.vo.CrmUpdateRecordVO;
import com.py.crm.crmupdaterecord.service.ICrmUpdateRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户/人脉(状态)更新记录表Controller
 *系统设置-回收站
 * <AUTHOR>
 * @date 2023-07-20
 */
@Api (tags = "客户/人脉(状态)更新记录")
@RestController
@RequestMapping("/crmupdaterecord")
public class CrmUpdateRecordController extends BaseController {

    /** 客户/人脉(状态)更新记录表服务 */
    @Resource
    private ICrmUpdateRecordService crmUpdateRecordService;

    /**
     * 查询客户/人脉(状态)更新记录表列表
     *
     * @param query 客户/人脉(状态)更新记录表查询参数
     * @return 客户/人脉(状态)更新记录表列表
     */
    @ApiOperation("查询客户/人脉(状态)更新记录表列表")
    @PreAuthorize("@ss.hasPermi('crmupdaterecord:crmupdaterecord:list')")
    @GetMapping("/listCrmUpdateRecord")
    public R<List<CrmUpdateRecordListVO>> listCrmUpdateRecord(CrmUpdateRecordQuery query) {
        List<CrmUpdateRecordListVO> voList = this.crmUpdateRecordService.listCrmUpdateRecord(query);
        return R.success(voList);
    }

    /**
     * 分页查询客户/人脉(状态)更新记录表列表
     *
     * @param query 客户/人脉(状态)更新记录表查询参数
     * @return 客户/人脉(状态)更新记录表分页
     */
    @ApiOperation("分页查询询客户/人脉(状态)更新记录表列表")
    @PreAuthorize("@ss.hasPermi('crmupdaterecord:crmupdaterecord:list')")
    @GetMapping("/pageCrmUpdateRecord")
    public R<PageInfo<CrmUpdateRecordListVO>> pageCrmUpdateRecord( CrmUpdateRecordQuery query) {
        PageInfo<CrmUpdateRecordListVO> voList = this.crmUpdateRecordService.pageCrmUpdateRecordList(query);
        return R.success(voList);
    }

    /**
     * 获取客户/人脉(状态)更新记录表详细信息
     * @param id 客户/人脉(状态)更新记录表主键
     * @return 客户/人脉(状态)更新记录表视图模型
     */
    @ApiOperation("获取客户/人脉(状态)更新记录表详细信息")
    @PreAuthorize("@ss.hasPermi('crmupdaterecord:crmupdaterecord:query')")
    @GetMapping(value = "/{id}")
    public R<CrmUpdateRecordVO> getInfo(@PathVariable("id") Long id) {
        return R.success(crmUpdateRecordService.selectCrmUpdateRecordById(id));
    }

    /**
     * 获取客户管理-客户-更新记录详细信息
     * @param id 客户管理-客户-主键
     * @return 客户管理-客户-更新记录视图模型
     */
    @ApiOperation("获取客户管理-客户-更新记录详细信息")
    @GetMapping(value = "/getUpdateRecordInfo/{id}")
    public R<PageInfo<CrmUpdateRecordVO>> getUpdateRecordInfo(@PathVariable("id") Long id) {
        return R.success(crmUpdateRecordService.selectUpdateRecordDetailById(id));
    }
    /**
     * 获取客户管理-客户-流转记录详细信息
     * @param id 客户管理-客户-主键
     * @return 客户管理-客户-流转记录视图模型
     */
    @ApiOperation("获取客户管理-客户-流转记录详细信息")
    @GetMapping(value = "/getCirculationRecordInfo/{id}")
    public R<PageInfo<CrmUpdateRecordVO>> getCirculationRecordInfo(@PathVariable("id") Long id) {
        return R.success(crmUpdateRecordService.selectCirculationRecordDetailById(id));
    }
    /**
     * 获取客户管理-客户-状态变更记录详细信息
     * @param id 客户管理-客户-主键
     * @return 客户管理-客户-状态变更记录视图模型
     */
    @ApiOperation("获取客户管理-客户-状态变更记录详细信息")
    @GetMapping(value = "/getStateUpdateRecordInfo/{id}")
    public R<PageInfo<CrmUpdateRecordVO>> getStateUpdateRecordInfo(@PathVariable("id") Long id) {
        return R.success(crmUpdateRecordService.selectStateUpdateRecordDetailById(id));
    }

    /**
     * 新增客户/人脉(状态)更新记录表
     *
     * @param dto 客户/人脉(状态)更新记录表修改参数
     * @return 是否成功
     */
    @ApiOperation("新增客户/人脉(状态)更新记录表")
    @PreAuthorize("@ss.hasPermi('crmupdaterecord:crmupdaterecord:add')")
    @Log(title = "客户/人脉(状态)更新记录表", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody CrmUpdateRecordDTO dto) {
        return R.success(crmUpdateRecordService.insertCrmUpdateRecord(dto));
    }

    /**
     * 修改客户/人脉(状态)更新记录表
     *
     * @param dto 客户/人脉(状态)更新记录表修改参数
     * @return 是否成功
     */
    @ApiOperation("修改客户/人脉(状态)更新记录表")
    @PreAuthorize("@ss.hasPermi('crmupdaterecord:crmupdaterecord:edit')")
    @Log(title = "客户/人脉(状态)更新记录表", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody CrmUpdateRecordDTO dto) {
        return R.success(crmUpdateRecordService.updateCrmUpdateRecord(dto));
    }

    /**
     * 删除客户/人脉(状态)更新记录表
     * @param ids 需要删除的客户/人脉(状态)更新记录表主键集合
     * @return 是否成功
     */
    @ApiOperation("删除客户/人脉(状态)更新记录表" )
    @PreAuthorize("@ss.hasPermi('crmupdaterecord:crmupdaterecord:remove')")
    @Log(title = "客户/人脉(状态)更新记录表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(crmUpdateRecordService.deleteCrmUpdateRecordByIds(ids));
    }

    /**
     * 导出客户/人脉(状态)更新记录表
     * @param response 请求响应
     * @param query 导出查询参数
     */
    @ApiOperation("导出客户/人脉(状态)更新记录表")
    @PreAuthorize("@ss.hasPermi('crmupdaterecord:crmupdaterecord:export')")
    @Log(title = "客户/人脉(状态)更新记录表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CrmUpdateRecordQuery query) {
        List<CrmUpdateRecordExportModel> exportList = this.crmUpdateRecordService.exportCrmUpdateRecord(query);

        ExcelUtil<CrmUpdateRecordExportModel> util = new ExcelUtil<>(CrmUpdateRecordExportModel. class);
        util.exportExcel(response, exportList, "客户/人脉(状态)更新记录表数据" );
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('crmupdaterecord:crmupdaterecord:import')" )
    @Log(title = "客户/人脉(状态)更新记录表" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<CrmUpdateRecordExportModel> util = new ExcelUtil<>(CrmUpdateRecordExportModel.class);
        List<CrmUpdateRecordExportModel> crmUpdateRecordList = util.importExcel(file.getInputStream());
        String message = this.crmUpdateRecordService.importCrmUpdateRecord(crmUpdateRecordList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('crmupdaterecord:crmupdaterecord:import')" )
    @Log(title = "客户/人脉(状态)更新记录表" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<CrmUpdateRecordExportModel> util = new ExcelUtil<>(CrmUpdateRecordExportModel.class);
        util.importTemplateExcel(response, "客户/人脉(状态)更新记录表数据" );
    }

}
