package com.py.common.tools.dictconverter.model;

import com.py.common.tools.poiexcel.annotation.Excel;
import lombok.Data;

import java.lang.reflect.Field;

/**
 * 导入字典映射参数
 * <AUTHOR>
 */
@Data
public class ImportDictMapArgs {

    /** 字典名称 */
    private final String dictName;
    /** 字段名称 */
    private final String fieldName;
    /** 使用的字段 */
    private final Field field;
    /** 是否为字典列表 */
    private final boolean isDictList;

    public ImportDictMapArgs(Excel excel, Field field) {
        this.dictName = excel.dictType();
        this.fieldName = excel.name();
        this.field = field;
        this.isDictList = excel.isDictList();
    }

}
