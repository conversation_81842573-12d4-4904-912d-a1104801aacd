package com.py.common.tools.poiexcel.impl;

import com.py.common.tools.poiexcel.ExcelHandlerAdapter;
import com.py.common.utils.StringUtils;

import java.math.BigDecimal;

/**
 * 金额转换器
 * <AUTHOR>
 */
public class MoneyExcelHandler implements ExcelHandlerAdapter {

    @Override
    public Object format(Object value, String[] args) {

        if (value == null){
            return null;
        }
        if (value instanceof BigDecimal){
            return StringUtils.formatBigDecimal((BigDecimal) value);
        }

        return null;
    }
}
