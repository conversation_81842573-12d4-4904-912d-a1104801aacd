package com.py.common.constant;

/**
 * 字典常量
 * <AUTHOR>
 */
public class DictConstant {

    /** 数据权限页面类型 */
    public static final String DATA_SCOPE_PAGE_TYPE = "data_scope_page_type";
    /** 客户合作状态  */
    public static final  String  COOPERATION_STATUS="cooperation_status";
    /** 合作平台 */
    public static final String SENSATION_PLATFROM="sensation_platfrom";
    /** 字典：方案类型 */
    public static final String PLAN_TYPE = "plan_type";

    /** 字典：派芽业务类型 */
    public static final String PY_TYPE = "py_type";

    /** 字典：行业类目 */
    public static final String INDUSTRY_CATEGORY = "industry_category";

    /** 字典：蒲公英等级 */
    public static final String TARAXACUM_GRADE = "management_redskin_book_grade";

    /** 字典：创作者层级 */
    public static final String CREATOR_LEVEL = "management_redskin_taobao_creatorLevel";

    /** 字典：合作方式 */
    public static final String COOPERATION_METHOD = "cooperation_methods";

    /** 字典：资源类型 */
    public static final String RESOURCE_TYPE = "resource_type";
    /** 字典：项目资源合作状态 */
    public static final String PROJECT_COOPERATION_STATUS = "project_cooperation_status";

    /** 字典：成本项目资源合作状态 */
    public static final String COST_COOPERATIVE_STATUS = "cost_cooperative_status";
    /** 字典：付款状态 */
    public static final String PAYMENT_TYPE = "payment_type";

    /** 行业类目字段名 */
    public final static String INDUSTRY_CATEGORY_FIELD = "行业类目";
    /** 字典：收款状态 */
    public final static String PROCEEDS_TYPE = "proceeds_type";

    /** 字典：项目状态状态 */
    public final static String INCOME_PROJECT_STATUS = "income_project_status";

    /** 对派芽信任度 */
    public final static String PY_TRUST = "py_trust";

    /** 人脉更新记录使用-对派芽信任度 */
    public final static String PY_TRUST_LEVEL = "对派芽信任度";

    /** 字典：品牌阶段 */
    public final static String BRAND_STAGE = "brand_stage";

    /** 字典：小红书内容形式 */
    public final static String LITTLE_CONTENT_FORM = "management_redskin_book_contentform";

    /** 字典：小红书内容偏好 */
    public final static String CHOOSE_REDSKIN_BOOK_CONTENTPREFERENCE = "choose_redskin_book_contentpreference";
    /** 字典：合作方式 */
    public final static String COOPERATION_METHODS = "cooperation_methods";
    /** 字典：资源类型 */
    public final static String PROJECT_RESOURCE_TYPE = "project_resource_type";

    /** 字典：服务方向 */
    public final static String SERVE_SERVICE_DIRECTION = "management_serve_service_direction";

    /** 字典：合作平台 */
    public final static String COOPERATION_PLATFORM = "cooperation_platform";

    /** 字典：报价策略 */
    public final static String BIDDING_STRATEGY = "bidding_strategy";

    /** 字典：品牌核心生意来源 */
    public final static String BUSINESS_SOURCE = "business_source";

    /** 字典：派芽服务产品 */
    public final static String SERVICE_PRODUCT = "service_product";

    /** 字典：客户来源 */
    public final static String CUSTOMER_SOURCE = "customer_source";

    /** 字典：在职状态 */
    public final static String EMPLOYMENT_STATUS = "employment_status";

    /** 字典：客户类型 */
    public final static String CUSTOMER_TYPE = "customer_type";

    /** 字典：决策权限 */
    public final static String DECISION_AUTHORITY = "decision_authority";
    /** 字典：商务标比稿结果 */
    public final static String BUSINESS_DRAFT_RESULT = "business_draft_result";
    /** 技术标比稿结果 */
    public final static String TECHNICAL_DRAFT_RESULT = "technical_draft_result";
    /** 最终比稿结果 */
    public final static String FINAL_DRAFT_RESULT = "final_draft_result";

    /** 字典：日常合同 - 合同类型 */
    public final static String OA_DAILY_CONTRACT_TYPE = "oa_daily_contract_type";
    /** 字典：其它合同 - 合同类型 */
    public final static String OA_OTHER_MATTER_TYPE = "oa_other_matter_type";

    /** 内容形式 */
    public static class ContentForm{

        /** 小红书内容形式 */
        public final static String LITTLE_RED_BOOK = "management_redskin_book_contentpreference";

        /** 抖音内容形式 */
        public final static String TIKTOK = "management_redskin_tiktok_contentform";

        /** 淘宝内容形式 */
        public final static String TAO_BAO = "management_redskin_taobao_contentform";

        /** 微博内容形式 */
        public final static String MICRO_BLOG = "management_redskin_weibo_contentform";

    }

    /**字典：费用审批事项*/
    public final static String COST_APPROVAL_MATTER_TYPE = "cost_approval_matter_type";

    /**字典：费用事项*/
    public final static String OA_COST_MATTER_TYPE = "oa_cost_matter_type";

    /**字典：*/
    public final static String DAILY_CONTRACT_TYPE = "daily_contract_type";

    /** 字典：资源-服务商：评价等级 */
    public final static String ASSESS_GRADE_SERVICE = "resource_manage_evaluate_grade";

    /** 字典：资源-红人：评价等级 */
    public final static String ASSESS_GRADE_SENSATION = "resource_manage_evaluate_grade";

    /** 字典:数字是否*/
    public final static String CHOOSE_REDSKIN_HAS_LIVE_PERSON_EXIT = "choose_redskin_has_live_person_exit";

    /** 字典:服务商资源类型 */
    public final static String MANAGEMENT_SERVE_RESOURCE_TYPE = "management_serve_resource_type";

    /** 字典:服务商服务方向 */
    public final static String MANAGEMENT_SERVE_RESOURCE_DIRECTION = "management_serve_service_direction";
}
