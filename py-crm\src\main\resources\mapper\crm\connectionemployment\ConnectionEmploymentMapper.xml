<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.py.crm.connectionemployment.mapper.ConnectionEmploymentMapper">

    <resultMap type="com.py.crm.connectionemployment.domain.ConnectionEmployment" id="ConnectionEmploymentResult">
        <result property="id" column="id" />
        <result property="employmentId" column="employment_id" />
        <result property="connectionId" column="connection_id" />
        <result property="customerId" column="customer_id" />
        <result property="currentEmployer" column="current_employer" />
        <result property="responsibleBrand" column="responsible_brand"
                typeHandler="com.py.common.typehandler.impl.StringSetTypeHandler"/>
        <result property="industryCategory" column="industry_category"
                typeHandler="com.py.common.typehandler.impl.LongSetTypeHandler"/>
        <result property="departmentName" column="department_name" />
        <result property="postName" column="post_name" />
        <result property="officeTime" column="office_time" />
        <result property="sort" column="sort" />
        <result property="createId" column="create_id" />
        <result property="createBy" column="create_by" />
        <result property="createDept" column="create_dept" />
        <result property="createTime" column="create_time" />
        <result property="updateBy" column="update_by" />
        <result property="updateDept" column="update_dept" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
        <result property="updateId" column="update_id" />
    </resultMap>

    <!--查询最新的从业经历sort-->
    <select id="getLastOne" resultType="com.py.crm.connectionemployment.domain.ConnectionEmployment">
        select *
        from py_crm_connection_employment
        where connection_id = #{connectionId}
        and del_flag='0'
        order by sort desc limit 1
    </select>

</mapper>
