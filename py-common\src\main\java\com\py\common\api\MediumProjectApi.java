package com.py.common.api;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 提供项目查询api
 * <AUTHOR>
 */
public interface MediumProjectApi {

    /**
     * 查询已存在的项目id
     * @param ids 项目ids
     * @return 已存在的项目id列表
     */
    List<Long> selectProjectByIdList(Collection<Long> ids);

    /**
     * 检测项目是否可交接
     * @param ids 项目id列表
     * @return 错误信息
     */
    Set<String> checkProjectCanHandover(Collection<Long> ids);

}
