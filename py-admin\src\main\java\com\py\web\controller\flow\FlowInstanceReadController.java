package com.py.web.controller.flow;

import com.py.common.core.domain.R;
import com.py.flow.flowinstanceread.domain.query.FlowInstanceQuery;
import com.py.flow.flowinstanceread.service.IFlowInstanceReadService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户已读消息
 * <AUTHOR>
 */
@RestController
@RequestMapping("/flow")
public class FlowInstanceReadController {

    /**
     * 用户已读消息service
     */
    @Resource
    private IFlowInstanceReadService flowInstanceReadService;


    /**
     * 修改用户已读消息状态
     * @param query 流程bizId,消息id
     * @return 操作结果
     */
    @ApiOperation(value = "修改用户已读消息")
    @PostMapping("/user/read")
    public R<Void> editUserFlowRead(@RequestBody FlowInstanceQuery query){
        flowInstanceReadService.editUserReadMessage(query);
        return R.success();
    }
}
