package com.py.flow.flowdatauser.domain.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审批状态
 * <AUTHOR>
 * @version FlowDataUserAuditStatus 2023/9/19 10:40
 */
@Getter
@AllArgsConstructor
public enum FlowDataUserAuditStatus implements IDict<Integer> {

    /** 审批中 */
    Approval(1,"审批中"),
    /** 审批通过 */
    ApprovedPass(2,"审批通过"),
    ;

    private final Integer value;
    private final String label;
}
