package com.py.flow.flowinstance.approvalsnapshot.service;

import com.py.flow.flowinstance.domain.FlowInstance;
import com.py.flow.flowinstance.domain.vo.ApprovalFlowChartVO;

/**
 * 审批快照服务接口
 * <AUTHOR>
 */
public interface IApprovalSnapshotService {

    /**
     * 获取审批业务信息
     * @param flowInstance 需要详情的审批流程实例
     * @return 审批业务的详情VO
     */
    Object getApprovalBizInfo(FlowInstance flowInstance);

    /**
     * 创建审批快照
     * @param flowInstance 需要创建快照的审批流程实例
     */
    void createBizSnapshot(FlowInstance flowInstance);


    /**
     * 创建审批快照流程图)
     * @param approvalFlowChartVO 需要创建快照的审批流程实例
     */
    Boolean createChartSnapshot(ApprovalFlowChartVO approvalFlowChartVO);

    /**
     * 获取审批业务信息
     * @param flowInstanceId 需要详情的审批流程实例id
     * @return 审批业务的详情VO
     */
    String getSnapshot(Long flowInstanceId);
}
