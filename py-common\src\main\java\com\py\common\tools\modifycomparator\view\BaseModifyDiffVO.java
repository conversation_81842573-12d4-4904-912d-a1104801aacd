package com.py.common.tools.modifycomparator.view;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.py.common.oss.model.OssObjectInfo;
import com.py.common.tools.modifycomparator.enums.ModifyContextType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 修改差异视图模型
 * <AUTHOR>
 */
@Data
public abstract class BaseModifyDiffVO<T> {

    /** 字段名称 */
    @ApiModelProperty("字段名称")
    private String fieldName;

    /** 字段路径 */
    @ApiModelProperty("字段路径")
    private List<String> fieldPath;
    /** 修改前 */
    @ApiModelProperty("修改前")
    private T before;
    /** 修改后 */
    @ApiModelProperty("修改后")
    private T after;

    /**
     * 获取修改内容类型
     * @return 修改内容类型
     */
    @ApiModelProperty("修改内容类型")
    public abstract ModifyContextType getContextType();

    /** 纯文本修改差异 */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class StringModifyDiff extends BaseModifyDiffVO<String> {

        /** 是否为字典 */
        @JsonIgnore
        private boolean asDict;

        /** 是否为列表字典 */
        @JsonIgnore
        private boolean asListDict;

        /** 字典名称 */
        @JsonIgnore
        private String dictName;

        /** 修改内容类型 */
        @Override
        public ModifyContextType getContextType() {
            return ModifyContextType.STRING;
        }
    }

    /** Oss文件 */
    @NoArgsConstructor
    public static class OssFileModifyDiff extends BaseModifyDiffVO<OssObjectInfo> {
        /** 修改内容类型 */
        @Override
        public ModifyContextType getContextType() {
            return ModifyContextType.OSS_File;
        }
    }

    /** 列表OSS文件 */
    @NoArgsConstructor
    public static class ListOssFileModifyDiff extends BaseModifyDiffVO<List<OssObjectInfo>> {
        /** 修改内容类型 */
        @Override
        public ModifyContextType getContextType() {
            return ModifyContextType.LIST_OSS_FILE;
        }
    }
}
