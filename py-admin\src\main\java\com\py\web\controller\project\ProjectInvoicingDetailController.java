package com.py.web.controller.project;


import com.github.pagehelper.PageInfo;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.project.projectinvoicingdetail.domain.query.ProjectInvoicingDetailPageQuery;
import com.py.project.projectinvoicingdetail.domain.vo.ProjectInvoicingDetailListVO;
import com.py.project.projectinvoicingdetail.service.IProjectInvoicingDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 项目收款开票详情表Controller
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Api(tags = "项目收款开票详情表")
@RestController
@RequestMapping("/invoicingdetail")
public class ProjectInvoicingDetailController extends BaseController {

    /** 项目收款开票详情表服务 */
    @Resource
    private IProjectInvoicingDetailService projectInvoicingDetailService;

    /**
     * 分页查询项目收款开票详情表列表
     *
     * @param query 项目收款开票详情表查询参数
     * @return 项目收款开票详情表分页
     */
    @ApiOperation("分页查询询项目收款开票详情表列表")
    @PreAuthorize("@ss.hasPermi('projectinvoicing:projectinvoicing:query')")
    @GetMapping("/pageProjectInvoicingDetail")
    public R<PageInfo<ProjectInvoicingDetailListVO>> pageProjectInvoicingDetail(ProjectInvoicingDetailPageQuery query) {
        return R.success(this.projectInvoicingDetailService.pageProjectInvoicingDetailList(query));
    }
}
