package com.py.common.enums.flow;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;

/**
 * 工作流 Activity_80000至89999
 * <AUTHOR>
 * @date 2023/7/14 14:51
 */
@AllArgsConstructor
public enum FlowResultCode implements IDict<Integer> {

    /**
     * 流程已经存在
     */
    ACTIVITY_PROCESS_EXISTENCE(80000, "流程已经存在"),

    /**
     * 流程启动失败
     */
    ACTIVITY_PROCESS_EXISTENCE_START_FAILURE(80001, "流程启动失败"),

    /**
     * 审批任务不存在
     */
    ACTIVITY_VETTING_TASK_NOT_EXIST(80002, "审批任务不存在"),

    /**
     * 审批任务冻结
     */
    ACTIVITY_VETTING_TASK_FREEZE(80003, "审批任务冻结"),

    /**
     * 未查到流程信息
     */
    ACTIVITY_NO_PROCESS_CHECKED(80004, "未查到流程信息"),

    /**
     * 工作流异常
     */
    ACTIVITY_ERROR(80005, "工作流异常"),

    /**
     * 未找到结束节点
     */
    ACTIVITY_NO_END_NODE(80006, "未找到结束节点"),

    /**
     * 未找到对应类型的已激活的流程模板
     */
    ACTIVITI_NO_ACTIVATED_DEPLOYMENT(80007, "未找到对应类型的已激活的流程模板"),

    /**
     * 没有历史任务 or 现在是发起人节点，不能执行一键退回到发起人节点的操作
     */
    PROCCESS_INSTANCE_NO_HISTORY_TASK_OR_NOW_INITIATOR_NODE(80008, "没有历史任务 or 现在是发起人节点，不能执行一键退回到发起人节点的操作"),

    /**
     * 发起流程时，指定节点的节点ID或者审批人ID为空
     */
    PROCCESS_NODE_ID_NULL_OR_ASSIGNEE_USER_ID_NULL(82009, "发起流程时，指定节点的节点ID或者审批人ID为空");

    private final Integer value;

    private final String label;

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
