package com.py.common.approve;

import com.py.common.enums.flow.FlowApprovalStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 通用的审批回调接收模型
 * <AUTHOR>
 * @Description 通用的审批回调接收模型
 * @Date 2023/8/8 16:58
 */
@Data
@ApiModel("通用的审批回调接收模型" )
public class ApproveDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("业务的主键")
    private Long bizId;

    @ApiModelProperty("审批状态")
    private FlowApprovalStatus approvalStatus;
}
