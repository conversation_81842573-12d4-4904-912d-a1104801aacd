package com.py.crm.customer.domain.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 人脉客户
 * <AUTHOR>
 * @version CustomerConnectionVO 2023/8/14 9:40
 */
@Data
public class CustomerConnectionVO {

    /** 客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 行业类目*/
    @ApiModelProperty("行业类目")
    private List<CategoryCustomer> industryCategoryList;

    /** 品牌/业务线下拉选择 */
    @ApiModelProperty("品牌/业务线下拉选择")
    private List<String> lineBusinessList;

    /** 品牌/业务线*/
    @ApiModelProperty("品牌/业务线")
    private String lineBusiness;

    /** 客户名称*/
    @ApiModelProperty("客户名称")
    private String name;

    /** 合作部门*/
    @ApiModelProperty("合作部门")
    private String cooperativeSector;

}
