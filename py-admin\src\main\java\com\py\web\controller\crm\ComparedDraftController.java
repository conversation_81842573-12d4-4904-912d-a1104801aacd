package com.py.web.controller.crm;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.crm.compareddraft.domain.dto.ComparedDraftDTO;
import com.py.crm.compareddraft.domain.query.ComparedDraftQuery;
import com.py.crm.compareddraft.domain.query.CustomerComparedDraftExportQuery;
import com.py.crm.compareddraft.domain.vo.ComparedDraftAmountVO;
import com.py.crm.compareddraft.domain.vo.ComparedDraftListVO;
import com.py.crm.compareddraft.domain.vo.ComparedDraftVO;
import com.py.crm.compareddraft.service.IComparedDraftService;
import com.py.crm.compareddraft.service.impl.ComparedDraftServiceImpl;
import com.py.crm.customer.domain.vo.CustomerVO;
import com.py.crm.customer.service.ICustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 客户管理-比稿管理控制器
 *
 * <AUTHOR>
 * @date 2023/07/17 09:36
 */
@Slf4j
@Api(tags = "客户管理-比稿管理")
@RequestMapping("/comparedDraft")
@RestController
public class ComparedDraftController {

    /** 客户管理-比稿管理服务 */
    @Resource
    private IComparedDraftService comparedDraftService;

    /** 客户管理-客户服务 */
    @Resource
    private ICustomerService customerService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 分页查询客户管理-比稿管理
     *
     * @param query 查询条件
     * @return 客户管理-比稿管理查询内容
     */
    @ApiOperation("客户管理-比稿管理分页查询")
    @PreAuthorize("@ss.hasPermi('crm:compareddraft:page')")
    @GetMapping(value = "/page")
    public R<PageInfo<ComparedDraftListVO>> listComparedDraft(ComparedDraftQuery query) {
        return R.success(comparedDraftService.listComparedDraft(query));
    }

    /**
     * 查询客户管理-比稿管理
     *
     * @param id 主键id
     * @return 客户管理-比稿管理查询内容
     */
    @ApiOperation("客户管理-比稿管理查询")
    @GetMapping(value = "/query/{id}")
    public R<ComparedDraftVO> queryComparedDraft (@PathVariable("id") Long id) {
        return R.success(comparedDraftService.queryComparedDraft(id));
    }
    /**
     * 查询客户管理-客户-比稿记录详细信息
     *
     * @param id 主键id
     * @return 客户管理-客户-比稿记录详细信息
     */
    @ApiOperation("获取客户管理-客户-比稿记录详细信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:compareddraft:query')")
    @GetMapping(value = "/{id}")
    public R<PageInfo<ComparedDraftVO>> getInfo(@PathVariable("id") Long id) {
        return R.success(comparedDraftService.queryComparedDraftDetailById(id,null,null));
    }

    /**
     * 查询客户管理-客户-比稿记录详细信息
     *
     * @param query 查询条件
     * @return 客户管理-客户-比稿记录详细信息
     */
    @ApiOperation("获取客户管理-客户-比稿记录详细信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:compareddraft:customerComparedDraft')")
    @GetMapping(value = "/customerComparedDraft")
    public R<PageInfo<ComparedDraftVO>> getCustomerComparedDraft(ComparedDraftQuery query) {
        return R.success(comparedDraftService.queryComparedDraftDetailById(query.getCustomerId(),query.getCustomerCooperateId(),query.getProjectId()));
    }
    /**
     * 添加客户管理-比稿管理
     *
     * @param draftDTO 客户管理-比稿管理Req
     * @return 是否成功
     */
    @ApiOperation("客户管理-比稿管理添加")
    @PreAuthorize("@ss.hasPermi('crm:compareddraft:add')")
    @Log(title = "比稿管理",businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public R<Boolean> addComparedDraft (@Validated @RequestBody ComparedDraftDTO draftDTO) {
        Boolean isSuccess = comparedDraftService.addComparedDraft(draftDTO);
        return R.success(isSuccess);
    }

    /**
     * 修改客户管理-比稿管理
     *
     * @param draftDTO 客户管理-比稿管理Req
     * @return 是否成功
     */
    @ApiOperation("客户管理-比稿管理修改")
    @PreAuthorize("@ss.hasPermi('crm:compareddraft:edit')")
    @Log(title = "比稿管理",businessType = BusinessType.UPDATE)
    @PutMapping(value = "/edit")
    public R<Boolean> updateComparedDraft (@Validated @RequestBody ComparedDraftDTO draftDTO) {
        Boolean isSuccess = comparedDraftService.updateComparedDraft(draftDTO);
        return R.success(isSuccess);
    }

    /**
     * 删除客户管理-比稿管理
     *
     * @param draftDTO 要删除的客户管理-比稿管理Id
     * @return 是否成功
     */
    @ApiOperation("客户管理-比稿管理删除")
    @PreAuthorize("@ss.hasPermi('crm:compareddraft:remove')")
    @Log(title = "比稿管理",businessType = BusinessType.DELETE)
    @DeleteMapping(value ="/remove")
    public R<Boolean> deleteComparedDraft (@RequestBody ComparedDraftDTO draftDTO){
        Boolean isSuccess = comparedDraftService.deleteComparedDraft(draftDTO);
        return R.success(isSuccess);
    }


    /**
     * 客户管理-下载比稿
     *
     * @param query 客户管理-比稿管理查询内容
     */
    @ApiOperation("客户管理-下载比稿")
    @PreAuthorize("@ss.hasPermi('crm:compareddraft:downloadAsync')")
    @Log(title = "比稿管理",businessType = BusinessType.EXPORT)
    @PostMapping(value ="/download")
    public R<String> downloadAsync (@RequestBody ComparedDraftQuery query){
        query.setLoginUser(SecurityUtils.getLoginUser());
        String zipName = "比稿管理数据" + "-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(zipName);
        reusableAsyncTaskService.addTask("比稿管理", TaskType.Export,query, ComparedDraftServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 客户管理-比稿记录比稿记录下载
     * @param query 客户管理-比稿管理查询内容
     */
    @ApiOperation("客户管理-比稿记录下载")
    @PreAuthorize("@ss.hasPermi('crm:compareddraft:downloadAsync')")
    @Log(title = "客户管理-比稿记录下载", businessType = BusinessType.EXPORT)
    @PostMapping(value ="/exportCustomerComparedDraftInfo")
    public R<String> exportCustomerComparedDraftInfo (@Validated @RequestBody CustomerComparedDraftExportQuery query){
        ComparedDraftQuery comparedDraftQuery = new ComparedDraftQuery();
        comparedDraftQuery.setLoginUser(SecurityUtils.getLoginUser());
        comparedDraftQuery.setComparedDraftIdList(query.getComparedDraftIdList());

        CustomerVO customerVO = this.customerService.selectCustomerById(query.getCustomerId());
        String zipName = String.format("比稿明细-%s-%s.xlsx",customerVO.getName(), DateUtils.getTimeCn());
        comparedDraftQuery.setFileName(zipName);
        this.reusableAsyncTaskService.addTask(zipName, TaskType.Export,comparedDraftQuery, ComparedDraftServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

     /**
      * 客户管理-比稿管理合计
      *
      * @param query 要删除的客户管理-比稿管理查询内容
      * @return 是否成功
      */
     @ApiOperation("客户管理-比稿管理合计")
     @PreAuthorize("@ss.hasPermi('crm:compareddraft:amount')")
     @PostMapping(value ="/amount")
     public R<ComparedDraftAmountVO> amount(@RequestBody ComparedDraftQuery query){
         return R.success(comparedDraftService.amount(query));
     }


}
