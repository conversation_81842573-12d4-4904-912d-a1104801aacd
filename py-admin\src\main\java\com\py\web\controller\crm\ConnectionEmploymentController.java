package com.py.web.controller.crm;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.crm.connectionemployment.domain.dto.ConnectionEmploymentDTO;
import com.py.crm.connectionemployment.domain.query.ConnectionEmploymentQuery;
import com.py.crm.connectionemployment.domain.vo.ConnectionEmploymentListVO;
import com.py.crm.connectionemployment.domain.vo.ConnectionEmploymentVO;
import com.py.crm.connectionemployment.service.IConnectionEmploymentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 从业经历表Controller
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Api(tags = "从业经历表")
@RestController
@RequestMapping("/employment")
public class ConnectionEmploymentController extends BaseController {

    /** 从业经历表服务 */
    @Resource
    private IConnectionEmploymentService connectionEmploymentService;

    /**
     * 查询从业经历表列表
     *
     * @param query 从业经历表查询参数
     * @return 从业经历表列表
     */
    @ApiOperation("查询从业经历表列表")
    @PreAuthorize("@ss.hasPermi('employment:employment:list')")
    @GetMapping("/listConnectionEmployment")
    public R<List<ConnectionEmploymentListVO>> listConnectionEmployment(ConnectionEmploymentQuery query) {
        List<ConnectionEmploymentListVO> voList = this.connectionEmploymentService.listConnectionEmployment(query.getConnectionId());
        return R.success(voList);
    }

    /**
     * 分页查询从业经历表列表
     *
     * @param query 从业经历表查询参数
     * @return 从业经历表分页
     */
    @ApiOperation("分页查询询从业经历表列表")
    @PreAuthorize("@ss.hasPermi('employment:employment:list')")
    @GetMapping("/pageConnectionEmployment")
    public R<PageInfo<ConnectionEmploymentListVO>> pageConnectionEmployment(ConnectionEmploymentQuery query) {
        PageInfo<ConnectionEmploymentListVO> voList = this.connectionEmploymentService.pageConnectionEmploymentList(query);
        return R.success(voList);
    }

    /**
     * 获取从业经历表详细信息
     * @param id 从业经历表主键
     * @return 从业经历表视图模型
     */
    @ApiOperation("获取从业经历表详细信息")
    @PreAuthorize("@ss.hasPermi('employment:employment:query')")
    @GetMapping(value = "/{id}")
    public R<ConnectionEmploymentVO> getInfo(@PathVariable("id") Long id) {
        return R.success(connectionEmploymentService.selectConnectionEmploymentById(id));
    }

    /**
     * 新增从业经历表
     *
     * @param dto 从业经历表修改参数
     * @return 是否成功
     */
    @ApiOperation("新增从业经历表")
    @PreAuthorize("@ss.hasPermi('employment:employment:add')")
    @Log(title = "从业经历表", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody ConnectionEmploymentDTO dto) {
        return R.success(connectionEmploymentService.insertConnectionEmployment(dto));
    }

    /**
     * 修改从业经历表
     *
     * @param dto 从业经历表修改参数
     * @return 是否成功
     */
    @ApiOperation("修改从业经历表")
    @PreAuthorize("@ss.hasPermi('employment:employment:edit')")
    @Log(title = "从业经历表", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody ConnectionEmploymentDTO dto) {
        return R.success(connectionEmploymentService.updateConnectionEmployment(dto));
    }

    /**
     * 删除从业经历表
     * @param ids 需要删除的从业经历表主键集合
     * @return 是否成功
     */
    @ApiOperation("删除从业经历表" )
    @PreAuthorize("@ss.hasPermi('employment:employment:remove')")
    @Log(title = "从业经历表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(connectionEmploymentService.deleteConnectionEmploymentByIds(ids));
    }
}
