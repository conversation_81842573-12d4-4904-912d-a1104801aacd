package com.py.flow.domain.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;

/**
 * 流程版本状态：0已下线；1草稿；2已上线
 * <AUTHOR>
 * @date 2023/7/20 14:08
 */
@AllArgsConstructor
public enum FlowVersionStatus implements IDict<Integer> {

    /** 已下线 */
    OFFLINE(0,"已下线"),

    /** 草稿 */
    DRAFT(1,"草稿"),

    /** 已上线 */
    ONLINE(2,"已上线");

    private final Integer value;

    private final String label;

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public Integer getValue() {
        return value;
    }

}
