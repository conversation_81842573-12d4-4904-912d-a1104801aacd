package com.py.common.mybatisplus;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;


/**
 * Mysql自定义方法
 * <AUTHOR>
 */
public class RestoreDeleteData extends AbstractMethod {

    public static final String METHOD_NAME = "restoreDeleteData";

    public RestoreDeleteData() {
        super(METHOD_NAME);
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        final String sqlResult = String.format("<script>Update %s Set del_flag = 0 Where %s = #{%s}</script>",
                tableInfo.getTableName(), tableInfo.getKeyColumn(), ENTITY_DOT + tableInfo.getKeyProperty());
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, sqlResult, modelClass);
        return this.addUpdateMappedStatement(mapperClass, modelClass, sqlSource);
    }
}
