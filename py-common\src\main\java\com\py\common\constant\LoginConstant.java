package com.py.common.constant;

/**
 * 类描述：登陆相关的常量类
 * <AUTHOR> pyr
 * @date : 2022-03-16 17:21
 **/
public class LoginConstant {

    /**
     * web单点登录key
     */
    public static final String WEB_SINGLE_SIGN_ON = "WEB_SINGLE_SIGN_ON_";

    /**
     * 会话时间由1小时改成8小时
     */
    public static final Integer JWT_TOKEN_TIME = 3600 * 8;

    public static final Long JWT_TOKEN_TIME_LONG = 3600L * 1000;

    /**
     * jwt——token key获取面向的用户
     */
    public static final String JWT_SUB = "sub";
    /**
     * jwt——token 获取key
     */
    public static final String JWT_AUTHORIZATION = "authorization";
}
