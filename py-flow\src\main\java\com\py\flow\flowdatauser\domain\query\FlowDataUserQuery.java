package com.py.flow.flowdatauser.domain.query;

import com.py.common.datascope.DataScopePageType;
import com.py.flow.domain.enums.ApprovalBizType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import java.time.LocalDate;

/**
 * 审批数据与人员关联查询对象
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@Data
@ApiModel("审批数据与人员关联查询对象" )
public class FlowDataUserQuery {
    private static final long serialVersionUID = 1L;

    /** 自增主键*/
    @ApiModelProperty("自增主键" )
    private Long id;

    /** 业务id*/
    @ApiModelProperty("业务id" )
    private Long bizId;

    /** 业务类型*/
    @ApiModelProperty("业务类型" )
    private ApprovalBizType bizType;

    /** 用户id*/
    @ApiModelProperty("用户id" )
    private Long userId;

    /** 创建者Id*/
    @ApiModelProperty("创建者Id" )
    private Long createId;

    /** 创建者*/
    @ApiModelProperty("创建者" )
    private String createBy;

    /** 创建人部门*/
    @ApiModelProperty("创建人部门" )
    private String createDept;

    /** 创建时间*/
    @ApiModelProperty("创建时间" )
    private LocalDateTime createTime;

    /** 更新者Id*/
    @ApiModelProperty("更新者Id" )
    private Long updateId;

    /** 更新者*/
    @ApiModelProperty("更新者" )
    private String updateBy;

    /** 更新人部门*/
    @ApiModelProperty("更新人部门" )
    private String updateDept;

    /** 更新时间*/
    @ApiModelProperty("更新时间" )
    private LocalDateTime updateTime;

    /** 删除标志*/
    @ApiModelProperty("删除标志" )
    private String delFlag;


}
