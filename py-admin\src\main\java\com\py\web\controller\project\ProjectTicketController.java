package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.exception.ServiceException;
import com.py.common.oss.IOssService;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.project.projectticket.domain.dto.ProjectTicketImportModel;
import com.py.project.projectticket.domain.query.ProjectTicketCountQuery;
import com.py.project.projectticket.domain.query.ProjectTicketQuery;
import com.py.project.projectticket.domain.vo.*;
import com.py.project.projectticket.service.IProjectTicketService;
import com.py.project.projectticket.service.impl.ProjectTicketServiceImpl;
import com.py.resources.mediumresourceservice.domain.query.ImportType;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 项目-收票管理表Controller
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
@Api(tags = "项目-收票管理表")
@RestController
@RequestMapping("/projectTicket")
public class ProjectTicketController extends BaseController {

    /** 项目-收票管理表服务 */
    @Resource
    private IProjectTicketService projectTicketService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** OSS服务 */
    @Resource
    private IOssService ossService;

    /** 主体参数服务 */
    @Resource
    private ISystemMainstayParamService mainstayParamService;

    /**
     * 查询各项目主体下数量
     *
     * @return 项目-收票管理表列表
     */
    @ApiOperation("查询各项目主体下数量")
    @PreAuthorize("@ss.hasPermi('projectticket:projectticket:list')")
    @GetMapping("/selectProjectItemCount")
    public R<List<ProjectTicketMainstayCountVO>> selectProjectItemCount() {
        return R.success(this.projectTicketService.selectProjectItemCount());
    }

    /**
     * 获取项目主体各个状态收票数量
     * @return 项目主体下收票数量
     */
    @ApiOperation("获取项目主体各个状态收票数量")
    @PreAuthorize("@ss.hasPermi('projectrebate::query')")
    @GetMapping(value = "/selectStatusTotalCount")
    public R<MainstayTicketStatusCountVO> selectStatusTotalCount(ProjectTicketCountQuery query) {
        return R.success(projectTicketService.selectProjectItemCountByStatus(query));
    }

    /**
     * 分页查询项目-收票管理表列表
     *
     * @param query 项目-收票管理表查询参数
     * @return 项目-收票管理表分页
     */
    @ApiOperation("分页查询询项目-收票管理表列表")
    @PreAuthorize("@ss.hasPermi('projectticket:projectticket:list')")
    @GetMapping("/pageProjectTicket")
    public R<PageInfo<ProjectTicketListVO>> pageProjectTicket(@Validated ProjectTicketQuery query) {
        return R.success(this.projectTicketService.pageProjectTicket(query, true));
    }

    /**
     * 获取所有的付款渠道-收票管理表列表
     *
     * @param query 项目-收票管理表查询参数
     * @return 付款渠道列表
     */
    @ApiOperation("获取所有的付款渠道")
    @GetMapping("/listChannelsName")
    public R<List<String>> listChannelsName(@Validated ProjectTicketQuery query) {
        return R.success(this.projectTicketService.listChannelsName(query));
    }

    /**
     * 收票管理合计统计
     * @param query
     * @return
     */
    @ApiOperation("项目-收票管理合计统计-勾选数据计算合计")
    @PreAuthorize("@ss.hasPermi('projectrebate::add')")
    @Log(title = "项目-收票管理合计统计", businessType = BusinessType.INSERT)
    @PostMapping("/selectTotalStatistics")
    public R<TicketAmountVO> selectTotalStatistics(@Validated @RequestBody ProjectTicketQuery query) {
        return R.success(projectTicketService.selectTotalStatistics(query));
    }

    /**
     * 分页查询项目-收票管理表(收票登记选中的)列表
     *
     * @param query 项目-收票管理表查询参数
     * @return 项目-收票管理表分页
     */
    @ApiOperation("分页查询询项目-收票管理表(收票登记选中的)列表")
    @PreAuthorize("@ss.hasPermi('projectticket:projectticket:list')")
    @GetMapping("/pageProjectTicketSelect")
    public R<PageInfo<ProjectTicketListVO>> pageProjectTicketSelect(@Validated ProjectTicketQuery query) {
        PageInfo<ProjectTicketListVO> voList = this.projectTicketService.pageProjectTicketSelect(query);
        return R.success(voList);
    }

    /**
     * 获取项目-收票管理表详细信息
     * @param id 项目-收票管理表主键
     * @return 项目-收票管理表视图模型
     */
    @ApiOperation("获取项目-收票管理表详细信息")
    @PreAuthorize("@ss.hasPermi('projectticket:projectticket:query')")
    @GetMapping(value = "/{id}")
    public R<ProjectTicketVO> getInfo(@PathVariable("id") Long id) {
        return R.success(projectTicketService.selectProjectTicketById(id));
    }

    /**
     * 下载目-收票管理表
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目-收票管理表")
    @PreAuthorize("@ss.hasPermi('projectticket:projectticket:export')")
    @Log(title = "项目-收票管理表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export( @Validated @RequestBody ProjectTicketQuery query) throws IOException {
        query.setLoginUser(SecurityUtils.getLoginUser());
        String dateTimeStr = DateUtils.parseDate(DateUtils.YYYY_MM_DD_COLON_HH_MM_SS, LocalDateTime.now());
        if(query.getPyMainstayId() != null){
            SystemMainstayParamVO mainstayVo = this.mainstayParamService.selectSystemMainstayParamById(query.getPyMainstayId());
            query.setFileName(String.format("收票管理列表数据-%s-%s.xlsx", mainstayVo.getMainstayName(), dateTimeStr));
        } else {
            query.setFileName(String.format("收票管理列表数据-%s.xlsx", dateTimeStr));
        }
        reusableAsyncTaskService.addTask("收票管理", TaskType.Export, query, ProjectTicketServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('projectticket:projectticket:import')" )
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_ticket.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("收票管理批量登记模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('projectticket:projectticket:import')" )
    @Log(title = "收票管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        ExcelUtil<ProjectTicketImportModel> util = new ExcelUtil<>(ProjectTicketImportModel.class);
        List<ProjectTicketImportModel> ticketList = util.importExcel(file.getInputStream(),0);
        if(ListUtil.isEmpty(ticketList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        ProjectTicketQuery query = new ProjectTicketQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setImportType(ImportType.INSERT);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("收票管理", TaskType.Import, query, ProjectTicketServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }
}
