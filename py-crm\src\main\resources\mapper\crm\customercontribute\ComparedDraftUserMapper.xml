<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.py.crm.customer.customercontribute.mapper.ComparedDraftUserMapper">

<!--  根据策划人id查询比稿项目id  -->
    <select id="listComparedDraftIdByUserIds" resultType="java.lang.Long">
        SELECT
        compared_draft_id
        FROM
        `py_crm_compared_draft_user`
        WHERE
        del_flag = 0
        AND user_id IN
        <foreach collection="userIdList" item="userId" separator="," open="(" close=")" >
            #{userId}
        </foreach>
        ORDER BY
        compared_draft_time DESC
        <if test="pageNum != null and pageSize != null">
            limit
            #{pageNum},#{pageSize}
        </if>
    </select>
</mapper>
