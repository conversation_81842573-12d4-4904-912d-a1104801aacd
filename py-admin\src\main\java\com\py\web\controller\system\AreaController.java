package com.py.web.controller.system;

import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.system.area.domain.query.AreaQuery;
import com.py.system.area.domain.vo.AreaVO;
import com.py.system.area.service.IAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 地区Controller
 *
 * <AUTHOR>
 * @date 2022-06-27
 */
@Api(tags = "地区信息")
@RestController("/area")
public class AreaController extends BaseController {

    /** 公司信息服务 */
    @Resource
    private IAreaService areaService;

    /**
     * 查询城区信息列表
     * @param query 城区信息查询参数
     * @return 城区信息列表
     */
    @ApiOperation("查询城市信息列表")
    @GetMapping("/listArea")
    public R<List<AreaVO>> listArea(@Valid AreaQuery query) {
        return R.success(this.areaService.listArea(query));
    }

}
