package com.py.common.oss.typehandler;

import com.py.common.oss.model.OssUploadResult;
import com.py.common.typehandler.BaseListTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

/**
 * Oss上传结果列表类型处理器
 * <AUTHOR>
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
public class OssUploadResultListTypeHandler extends BaseListTypeHandler<OssUploadResult> {
    /**
     * 元素类型
     * @return 元素类型
     */
    @Override
    protected Class<OssUploadResult> getItemClass() {
        return OssUploadResult.class;
    }
}
