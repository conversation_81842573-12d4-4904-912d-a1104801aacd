<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.py.crm.crmdownload.mapper.CrmDownloadMapper">

    <resultMap type="com.py.crm.crmdownload.domain.CrmDownload" id="CrmDownloadResult">
        <result property="id" column="id" />
        <result property="downloadId" column="download_id" />
        <result property="bizType" column="biz_type" />
        <result property="fileName" column="file_name" />
        <result property="token" column="token" />
        <result property="crmNum" column="crm_num" />
        <result property="fileSize" column="file_size" />
        <result property="createId" column="create_id" />
        <result property="createBy" column="create_by" />
        <result property="createDept" column="create_dept" />
        <result property="createTime" column="create_time" />
        <result property="updateId" column="update_id" />
        <result property="updateBy" column="update_by" />
        <result property="updateDept" column="update_dept" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
    </resultMap>

</mapper>
