package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 付款状态
 * <AUTHOR>
 * @version PaymentType 2023/8/4 15:52
 */
@AllArgsConstructor
public enum PaymentType implements IDict<Integer>{

    /** 0未付款 */
    NON_PAYMENT(0,"未付款"),

    /** 1部分付款 */
    PARTIAL_PAYMENT(1,"部分付款"),

    /** 2已付款 */
    PAID(2,"已付款")
    ;


    private final Integer value;
    private final String label;

    @Override
    public String getLabel() {
        return this.label;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
