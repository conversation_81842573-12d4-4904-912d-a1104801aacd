package com.py.flow.domain.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Set;

/**
 * 审批操作
 * <AUTHOR>
 * @date 2023/7/27 9:54
 */
@Getter
@AllArgsConstructor
public enum FlowOperate implements IDict<Integer> {

    /** 发起 */
    START(-1,"发起"),

    /** 同意 */
    AGREE(0,"同意"),

    /** 拒绝 */
    REJECT(1,"拒绝"),

    /** 撤回 */
    CANCEL(2,"撤回"),

    /** 评论 */
    COMMENT(3,"评论"),

    /** 加签 */
    COUNTERSIGN(4,"加签"),

    /** 退回 */
    RETURN(5,"退回"),

    /** 转交 */
    FORWARD(6,"转交"),

    /** 抄送 */
    CC(7,"抄送"),

    /** 作废 */
    INVALIDATED(8,"作废");

    private final Integer value;
    private final String label;


    /** 审批操作 */
    public static Set<FlowOperate> ApprovalOperateSet = EnumSet.of(AGREE, REJECT);
}
