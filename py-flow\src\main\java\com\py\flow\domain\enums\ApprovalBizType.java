package com.py.flow.domain.enums;

import com.py.common.enums.IDict;
import com.py.common.utils.StringUtils;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 审批业务类型
 * <AUTHOR>
 */
@Getter
public enum ApprovalBizType implements IDict<String> {

    // ----------  测 试  ----------
    Test("Test", "系统调试", ApprovalKind.BusinessForms),

    // ---------- 项目管理 ----------
    /** 新增项目 */
    AddProject("AddProject", "新增项目", ApprovalKind.BusinessForms),
    /** 项目资源状态变更 */
    ProjectResourcesStatusChange("ProjectResourcesStatusChange", "修改状态", ApprovalKind.BusinessForms),

    /** 批量修改状态 */
    ProjectResourcesStatusChangeBatch("ProjectResourcesStatusChangeBatch", "批量修改状态", ApprovalKind.BusinessForms),

    /** 项目内批量修改状态 */
    ProjectResourcesStatusChangeBatchInner("ProjectResourcesStatusChangeBatchInner", "项目内批量修改状态", ApprovalKind.BusinessForms),
    /** 批量结案 */
    ProjectCloseCaseBatch("ProjectCloseCaseBatch", "批量结案", ApprovalKind.BusinessForms,false,true,true),

    /** 单个结案 */
    ProjectCloseCase("ProjectCloseCase", "单个结案", ApprovalKind.BusinessForms,false,true,true),

    /** 项目资源付款 */
    ProjectResourcesPayment("ProjectResourcesPayment", "项目内资源付款", ApprovalKind.BusinessForms, true,true,true),

    /** 项目资源批量付款 */
    ProjectResourcesPaymentBatch("ProjectResourcesPaymentBatch", "项目资源批量付款", ApprovalKind.BusinessForms, true, true,true),

    /** 成本内资源批量付款 */
    ProjectIncomeResourcesPaymentBatch("ProjectIncomeResourcesPaymentBatch", "成本资源批量付款", ApprovalKind.BusinessForms, true,true,true),

    /** 确认收入 */
    ConfirmIncome("ConfirmIncome", "确认收入", ApprovalKind.BusinessForms),

    /** 修改状态审批通过确认收入流程 */
    UpdateStatusConfirmIncome("UpdateStatusConfirmIncome", "修改状态审批通过确认收入流程", ApprovalKind.BusinessForms),

    /** 批量确认收入 */
    ConfirmIncomeBatch("ConfirmIncomeBatch", "批量确认收入", ApprovalKind.BusinessForms),

    /** 批量修改 */
    BatchUpdate("BatchUpdate", "批量修改", ApprovalKind.BusinessForms),

    /** 修改申请 */
    UpdateProject("UpdateProject", "修改申请", ApprovalKind.BusinessForms),

    /** 资源退款 */
    ProjectResourcesRefund("ProjectResourcesRefund", "资源退款", ApprovalKind.BusinessForms,false,true,true),
    /** 资源批量退款 */
    ProjectResourcesRefundBatch("ProjectResourcesRefundBatch", "资源批量退款", ApprovalKind.BusinessForms,false,true,true),
    /** 新增收入坏账 */
    NewBadDebtRevenue("new_bad_debt_revenue","新增收入坏账", ApprovalKind.BusinessForms,false,true,true),
    /** 新增资源返点坏账 */
    NewResourcesBadDebtRebate("new_resources_bad_debt_rebate","新增资源返点坏账", ApprovalKind.BusinessForms,false,true,true),
    /** 新增客户合同 */
    NewCustomerContract("new_customer_contract","新增客户合同", ApprovalKind.BusinessForms),
    /** 新增供应商合同 */
    NewSupplierContract("new_supplier_contract","新增供应商合同", ApprovalKind.BusinessForms),
    /** 项目收款开票申请 */
    ApplyProjectInvoicing("apply_project_invoicing","项目收款开票", ApprovalKind.BusinessForms),
    /** 资源返点开票申请 */
    ApplyInvoicingResource("apply_invoicing_resource","资源返点开票", ApprovalKind.BusinessForms),
    /** 项目返点登记申请 */
    ApplyRebateResource("apply_rebate_resource", "返点登记", ApprovalKind.BusinessForms),

    // ---------- 客户管理 ----------
    /** 新增客户 */
    InsertCustomer("insert_customer","新增客户",ApprovalKind.BusinessForms),
    /** 新增人脉 */
    InsertConnection("insert_connection","新增人脉",ApprovalKind.BusinessForms),

    // ---------- 派OA ----------
    /** 费用 */
    Expense("expense_approval","费用审批", ApprovalKind.NonBusinessForms,true,true),
    /** 日常合同 */
    RoutineContract("routine_contract","日常合同审批", ApprovalKind.NonBusinessForms),
    /** 其他 */
    Other("other_approval","其它审批", ApprovalKind.NonBusinessForms),
    ;

    private final String value;
    private final String label;
    /** 审批分类 */
    private final ApprovalKind kind;
    /** 是否支持金额判断 */
    private final boolean supportMoney;

    /** 是否支持使用项目成员职位选项 */
    private boolean supportProjectPostUser;

    /** 是否支持打印 */
    private final boolean hasSupportPrint;

    ApprovalBizType(String value, String label, ApprovalKind kind) {
        this(value, label, kind, false,false,false);
    }

    ApprovalBizType(String value, String label, ApprovalKind kind, boolean supportMoney, boolean supportProjectPostUser,boolean hasSupportPrint) {
        this.value = value;
        this.label = label;
        this.kind = kind;
        this.supportMoney = supportMoney;
        this.supportProjectPostUser = supportProjectPostUser;
        this.hasSupportPrint = hasSupportPrint;
    }

    ApprovalBizType(String value, String label, ApprovalKind kind, boolean supportMoney,boolean hasSupportPrint) {
        this.value = value;
        this.label = label;
        this.kind = kind;
        this.supportMoney = supportMoney;
        this.hasSupportPrint = hasSupportPrint;
    }

    /**
     * 根据label模糊查询value列表
     * @param label
     * @return
     */
    public static List<String> getValuesByLabel(String label) {
        List<String> valueList = new ArrayList<>();
        if(StringUtils.isEmpty(label)) {
            return valueList;
        }
        for (ApprovalBizType bizType : ApprovalBizType.values()) {
            if (StringUtils.contains(bizType.getLabel() +"申请", label)) {
                valueList.add(bizType.getValue());
            }
        }
        return valueList;
    }
}
