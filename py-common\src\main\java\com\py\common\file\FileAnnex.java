package com.py.common.file;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * 附件实体
 * <AUTHOR>
 */
@Data
public class FileAnnex implements Comparable<FileAnnex> {

    /** 文件名 */
    @ApiModelProperty("文件名")
    private String fileName;

    /** 文件Key */
    @ApiModelProperty("文件Key")
    private String key;


    @Override
    public int compareTo(FileAnnex o) {
        return this.equals(o) ? 0 : -1;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FileAnnex fileAnnex = (FileAnnex) o;
        return Objects.equals(fileName, fileAnnex.fileName) && Objects.equals(key, fileAnnex.key);
    }
}
