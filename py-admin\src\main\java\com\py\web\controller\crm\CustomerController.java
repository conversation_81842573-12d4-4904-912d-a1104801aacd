package com.py.web.controller.crm;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.exception.ServiceException;
import com.py.common.oss.IOssService;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.tools.multisheetexcelexporter.MultiSheetExcelExporter;
import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.customer.domain.dto.CustomerDTO;
import com.py.crm.customer.domain.dto.CustomerExportModel;
import com.py.crm.customer.domain.query.CustomerLineBusinessQuery;
import com.py.crm.customer.domain.query.CustomerNameDownQuery;
import com.py.crm.customer.domain.query.CustomerQuery;
import com.py.crm.customer.domain.vo.*;
import com.py.crm.customer.service.ICustomerService;
import com.py.crm.customer.service.impl.CustomerServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 客户管理-客户Controller
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Api(tags = "客户管理-客户")
@RestController
@RequestMapping("/crm/customer")
public class CustomerController extends BaseController {

    /** 客户管理-客户服务 */
    @Resource
    private ICustomerService customerService;

    /** 对象存储服务 */
    @Resource
    private IOssService ossService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 分页查询客户管理-客户列表
     *
     * @param query 客户管理-客户查询参数
     * @return 客户管理-客户分页
     */
    @ApiOperation("分页查询询客户管理-客户列表")
    @PreAuthorize("@ss.hasPermi('crm:customer:list')")
    @PostMapping("/pageCustomer")
    public R<PageInfo<CustomerListVO>> pageCustomer(@RequestBody CustomerQuery query) {
        PageInfo<CustomerListVO> voList = this.customerService.pageCustomerList(query);
        return R.success(voList);
    }

    /**
     * 客户管理-客户列表,模糊搜索列表,录入人部门集合
     *
     * @param query 客户管理-客户查询参数(录入人部门名称)
     * @return 客户管理-客户列表,部门集合
     */
    @ApiOperation("查询客户管理-客户列表-录入人部门名称")
    @PostMapping("/findCustomerCreateDeptList")
    public R<List<String>> findCustomerCreateDeptList(@RequestBody CustomerQuery query) {
        List<String> voList = this.customerService.findCustomerCreateDeptList(query);
        return R.success(voList);
    }

    /**
     * 客户管理-客户列表,模糊搜索列表,服务人部门集合
     *
     * @param query 客户管理-客户查询参数(服务人部门名称)
     * @return 客户管理-客户列表,部门集合
     */
    @ApiOperation("查询客户管理-客户列表-服务人员部门名称")
    @PostMapping("/findCustomerServiceDeptList")
    public R<List<String>> findCustomerServiceDeptList(@RequestBody CustomerQuery query) {
        List<String> voList = this.customerService.findCustomerServiceDeptList(query);
        return R.success(voList);
    }



    /**
     * 客户名称模糊检索
     * 业务线模糊检索
     * @param query 客户名称,业务线名称
     * @return 客户id,客户名称,业务线
     */
    @ApiOperation("客户名称,业务线模糊检索")
    @PostMapping("/lineBusiness/byCustomer")
    public R<List<CustomerLineBusinessVO>> lineBusinessByCustomer(@RequestBody CustomerLineBusinessQuery query) {
        return R.success(this.customerService.lineBusinessByCustomer(query));
    }

    /**
     * 获取客户管理-客户详细信息
     * @param id 客户管理-客户主键
     * @return 客户管理-客户视图模型
     */
    @ApiOperation("获取客户管理-客户详细信息")
    @PreAuthorize("@ss.hasPermi('crm:customer:query')")
    @GetMapping(value = "/{id}")
    public R<CustomerVO> getInfo(@PathVariable("id") Long id) {
        return R.success(customerService.selectCustomerById(id));
    }

    /**
     * 新增客户管理-客户
     *
     * @param dto 客户管理-客户修改参数
     * @return 是否成功
     */
    @ApiOperation("新增客户管理-客户")
    @PreAuthorize("@ss.hasPermi('crm:customer:add')")
    @Log(title = "客户管理-客户", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Long> add(@Valid @RequestBody CustomerDTO dto) {
        return R.success(customerService.insertCustomer(dto));
    }

    /**
     * 修改客户管理-客户
     *
     * @param dto 客户管理-客户修改参数
     * @return 修改的客户ID
     */
    @ApiOperation("修改客户管理-客户")
    @PreAuthorize("@ss.hasPermi('crm:customer:edit')")
    @Log(title = "客户管理-客户", businessType = BusinessType.UPDATE)
    @PutMapping("/edit")
    public R<Long> edit(@Valid @RequestBody CustomerDTO dto) {
        return R.success(customerService.updateCustomer(dto));
    }

    /**
     * 修改客户管理-客户(审批)
     *
     * @param dto 客户管理-客户修改参数
     * @return 是否成功
     */
    @ApiOperation("修改客户管理-客户(审批)")
    @PreAuthorize("@ss.hasPermi('crm:customer:auditEdit')")
    @Log(title = "客户管理-客户", businessType = BusinessType.UPDATE)
    @PutMapping("/auditEdit")
    public R<Long> auditEdit(@Valid @RequestBody CustomerDTO dto) {
        dto.setIsApprovalEdit(true);
        return R.success(customerService.insertCustomer(dto));
    }

    /**
     * 删除客户管理-客户
     * @param id 需要删除的客户管理-客户主键
     * @return 是否成功
     */
    @ApiOperation("删除客户管理-客户" )
    @PreAuthorize("@ss.hasPermi('crm:customer:remove')")
    @Log(title = "客户管理-客户", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{id}" )
    public R<Boolean> remove(@PathVariable("id") Long id) {
        return R.success(customerService.deleteCustomerById(id));
    }

    /**
     * 客户状态变更
     * @param dto 参数
     * @return 结果
     */
    @ApiOperation("客户管理-客户状态变更" )
    @PreAuthorize("@ss.hasPermi('crm:customer:stateUpdate')")
    @PostMapping("/stateUpdate" )
    public R<Boolean> stateUpdate(@RequestBody CustomerDTO dto){
        return R.success(customerService.stateUpdate(dto));
    }

    /**
     * 客户流转
     * @param dto 参数
     * @return 结果
     */
    @ApiOperation("客户管理-客户分配" )
    @PreAuthorize("@ss.hasPermi('crm:customer:customerCirculation')")
    @PostMapping("/customerCirculation" )
    public R<Boolean> customerCirculation(@RequestBody CustomerDTO dto){
        return R.success(customerService.customerCirculation(dto));
    }

    /**
     * 客户下拉
     * @param query 参数
     * @return 下拉
     */
    @ApiOperation("客户管理-客户下拉" )
    @PreAuthorize("@ss.hasPermi('crm:customer:pollDown')")
    @GetMapping("/pollDown" )
    public R<List<CustomerListVO>> pollDown(CustomerQuery query){
        return R.success(customerService.pollDown(query));
    }

    /**
     * 客户下拉模糊搜索
     * @param query 客户名称
     * @return 下拉
     */
    @ApiOperation("客户管理-客户下拉模糊搜索" )
    @PostMapping("/pollDown/select" )
    public R<List<CustomerListVO>> pollDownSelect(@RequestBody CustomerNameDownQuery query){
        return R.success(customerService.pollDownSelect(query));
    }

    /**
     * 客户下拉
     * 展示当前登录用户云客户中的数据权限中的客户
     * @param query 参数
     * @return 下拉
     */
    @ApiOperation("客户管理-客户下拉(权限)" )
    @GetMapping("/pollDownUserDataScope" )
    public R<List<CustomerListVO>> pollDownUserDataScope(CustomerQuery query){
        return R.success(customerService.pollDownUserDataScope(query));
    }

    /**
     * 客户审核修改状态
     * @param dto 参数
     * @return 结果
     */
    @ApiOperation("客户管理-客户审核修改状态" )
    @PostMapping("/auditAfter" )
    public R<Boolean> auditAfter(@RequestBody CustomerDTO dto){
        return R.success(customerService.auditAfter(dto));
    }

    /**
     * 客户统计
     * @return 客户统计
     */
    @ApiOperation("客户管理-客户统计" )
    @GetMapping("/customerCount" )
    public R<CustomerCountVO> customerCount(CustomerQuery query){
        return R.success(customerService.customerCount(query));
    }

    /**
     * 人脉客户下拉
     * @param query 参数
     * @return 下拉
     */
    @ApiOperation("客户管理-客户下拉" )
    @PreAuthorize("@ss.hasPermi('crm:customer:pollConnectionDown')")
    @GetMapping("/pollConnectionDown" )
    public R<List<CustomerConnectionVO>> pollConnectionDown(CustomerQuery query){
        return R.success(customerService.pollConnectionDown(query));
    }

    /**
     * 人脉客户下拉(包括从人脉中获取的客户数据)
     * @param query 参数
     * @return 下拉
     */
    @ApiOperation("客户管理-客户下拉" )
    @PreAuthorize("@ss.hasPermi('crm:customer:pollConnectionDown')")
    @GetMapping("/getPollConnectionDown" )
    public R<List<String>> getPollConnectionDown(CustomerQuery query){
        return R.success(customerService.getPollConnectionDown(query));
    }

    /**
     * 客户管理-下载模版
     * @return 是否成功
     */
    @ApiOperation("客户管理-下载模版" )
    @PreAuthorize("@ss.hasPermi('crm:customerLaundry:exportTemplate')")
    @PostMapping("/exportTemplate" )
    public void exportTemplate (HttpServletResponse response){
        MultiSheetExcelExporter excelExporter = new MultiSheetExcelExporter();
        ExcelSheetConfig<CustomerExportModel> customerTemplateConfig = customerService.getCustomerTemplateConfig();
        excelExporter.addSheetConfig(ListUtil.emptyList(), customerTemplateConfig, "客户批量分配模版");
        excelExporter.exportExcel(response,"客户批量分配模版.xlsx");
    }

    /**
     * 客户管理-批量分配
     * @return 是否成功
     */
    @ApiOperation("客户管理-批量分配" )
    @PreAuthorize("@ss.hasPermi('crm:customerLaundry:exportAssignment')")
    @PostMapping("/exportAssignment" )
    public R<String> exportAssignment(MultipartFile file) throws Exception{
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xls"))) {
            throw new ServiceException("请上传正确的文件格式");
        }
        ExcelUtil<CustomerExportModel> util = new ExcelUtil<>(CustomerExportModel.class);
        List<CustomerExportModel> customerExportModelList = util.importExcel(file.getInputStream(),0);
        if(ListUtil.isEmpty(customerExportModelList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        OssUploadResult ossUploadResult = ossService.upload(file.getInputStream(), file.getOriginalFilename(), false);
        CustomerQuery query = new CustomerQuery();
        query.setFileKey(ossUploadResult.getOssKey());
        query.setFileName(file.getOriginalFilename());
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("客户管理-批量分配", TaskType.Import,query, CustomerServiceImpl.class);
        return R.success("提交成功");
    }

}
