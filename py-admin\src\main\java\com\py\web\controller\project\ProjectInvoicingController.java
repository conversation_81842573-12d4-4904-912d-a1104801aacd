package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.SecurityUtils;
import com.py.project.enums.ExportType;
import com.py.project.projectinvoicing.ProjectInvoicingInfoQuery;
import com.py.project.projectinvoicing.domain.query.ProjectInvoicingApprovalQuery;
import com.py.project.projectinvoicing.domain.query.ProjectInvoicingCountQuery;
import com.py.project.projectinvoicing.domain.query.ProjectInvoicingPageQuery;
import com.py.project.projectinvoicing.domain.vo.*;
import com.py.project.projectinvoicing.service.IProjectInvoicingService;
import com.py.project.projectinvoicing.service.impl.ProjectInvoicingDownloadServiceImpl;
import com.py.project.projectinvoicing.service.impl.ProjectInvoicingServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 项目收款开票表Controller
 *
 * <AUTHOR>
 * @date 2023-08-01
 */
@Api(tags = "项目收款开票表")
@RestController
@RequestMapping("/projectInvoicing")
public class ProjectInvoicingController extends BaseController {

    /** 项目收款开票表服务 */
    @Resource
    private IProjectInvoicingService projectInvoicingService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 查询各派芽主体下数量
     *
     * @return 项目-收票管理表列表
     */
    @ApiOperation("查询各派芽主体下数量")
    @GetMapping("/selectProjectItemCount")
    public R<List<ProjectInvoicingMainstayCountVO>> selectProjectItemCount() {
        return R.success(this.projectInvoicingService.selectProjectItemCount());
    }

    /**
     * 查询项目收款开票数量
     * @param query
     * @return 项目收款开票数量
     */
    @ApiOperation("项目收款、资源开票数量")
    @GetMapping("/selectInvoicingCount")
    public R<InvoicingTotalCountVO> selectInvoicingCount(ProjectInvoicingCountQuery query) {
        return R.success(projectInvoicingService.selectInvoicingCount(query));
    }

    /**
     * 查询项目收款各个状态开票数量
     * @param query
     * @return 项目收款各个状态开票数量
     */
    @ApiOperation("查询项目收款各个状态开票数量")
    @GetMapping("/selectStatusTotalCount")
    public R<InvoicingStatusCountVO> selectStatusTotalCount(ProjectInvoicingCountQuery query) {
        return R.success(projectInvoicingService.selectStatusTotalCount(query));
    }

    /**
     * 分页查询项目收款开票表列表
     *
     * @param query 项目收款开票表查询参数
     * @return 项目收款开票表分页
     */
    @ApiOperation("分页查询询项目收款开票表列表")
    @PreAuthorize("@ss.hasPermi('projectinvoicing:projectinvoicing:list')")
    @GetMapping("/pageProjectInvoicing")
    public R<PageInfo<ProjectInvoicingListVO>> pageProjectInvoicing(@Validated ProjectInvoicingPageQuery query) {
        PageInfo<ProjectInvoicingListVO> voList = this.projectInvoicingService.pageProjectInvoicingList(query, true, true);
        return R.success(voList);
    }

    /**
     * 勾选-分页查询项目收款开票表列表
     *
     * @param query 项目收款开票表查询参数
     * @return 项目收款开票表分页
     */
    @ApiOperation("勾选-分页查询询项目收款开票表列表")
    @PreAuthorize("@ss.hasPermi('projectinvoicing:projectinvoicing:list')")
    @GetMapping("/pageProjectInvoicingSelect")
    public R<ProjectInvoicingSelectedListVO> pageProjectInvoicingSelect(@Validated ProjectInvoicingPageQuery query) {
        return R.success(this.projectInvoicingService.pageProjectInvoicingSelect(query));
    }

    /**
     * 勾选-项目收款开票申请校验
     *
     * @param query 项目收款开票表查询参数
     * @return 项目收款开票表分页
     */
    @ApiOperation("勾选-项目收款开票申请校验")
    @PreAuthorize("@ss.hasPermi('projectinvoicing:projectinvoicing:list')")
    @GetMapping("/checkProjectInvoicing")
    public R<Boolean> checkProjectInvoicing(@Validated ProjectInvoicingPageQuery query) {
        return R.success(this.projectInvoicingService.checkProjectInvoicing(query));
    }

    /**
     * 勾选合计
     *
     * @return 勾选合计金额
     */
    @ApiOperation("勾选合计")
    @GetMapping("/selectTotalStatistics")
    public R<InvoicingAmountTotalVO> selectTotalStatistics(ProjectInvoicingCountQuery query) {
        return R.success(projectInvoicingService.selectTotalStatistics(query));
    }

    /**
     * 获取项目收款开票申请记录
     * @param query 项目收款开票表主键
     * @return 项目收款开票表视图模型
     */
    @ApiOperation("获取项目收款开票申请记录")
    @PreAuthorize("@ss.hasPermi('projectinvoicing:projectinvoicing:query')")
    @GetMapping(value = "/selectInfo")
    public R<ProjectInvoicingInfoVO> getInfo(ProjectInvoicingInfoQuery query) {
        return R.success(projectInvoicingService.selectProjectInvoicingById(query));
    }

    /**
     * 导出项目收款开票表
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目收款开票表")
    @PreAuthorize("@ss.hasPermi('projectinvoicing:projectinvoicing:export')")
    @Log(title = "项目收款开票表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@Validated @RequestBody ProjectInvoicingPageQuery query) throws IOException {
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("项目收款开票数据", TaskType.Export, query, ProjectInvoicingServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 审批查询-项目收款开票记录列表
     *
     * @param query 查询条件
     * @return 数据集合
     */
    @ApiOperation("审批查询-项目收款开票-分页列表")
    @PreAuthorize("@ss.hasPermi('projectinvoicing:projectinvoicing:approval:list')")
    @GetMapping("/pageInvoicingProjectApproval")
    public R<PageInfo<ProjectInvoicingApprovalVO>> pageInvoicingProjectApproval(@Validated ProjectInvoicingApprovalQuery query) {
        PageInfo<ProjectInvoicingApprovalVO> pageInfo = this.projectInvoicingService.pageInvoicingProjectApproval(query);
        return R.success(pageInfo);
    }

    /**
     * 审批查询-主体数量统计
     * @return 数据集合
     */
    @ApiOperation("审批查询-项目收款开票-主体数量统计")
    @PreAuthorize("@ss.hasPermi('projectinvoicing:projectinvoicing:approval:sumlist')")
    @GetMapping("/sumAddProjectApproval")
    public R<List<ProjectInvoicingApprovalSumVO>> sumAddProjectApproval() {
        List<ProjectInvoicingApprovalSumVO> list = this.projectInvoicingService.sumAddProjectApproval();
        return R.success(list);
    }

    /**
     * 审批查询-统计选择项目的本次开票金额总数
     * @param query 需要统计的数据
     * @return 统计的数量
     */
    @ApiOperation("审批查询-项目收款开票-统计本次开票金额总数")
    @PreAuthorize("@ss.hasPermi('projectinvoicing:projectinvoicing:approval:sumIds')")
    @GetMapping("/sumInvoicingApprovalByIds")
    public R<ProjectInvoicingApprovalSumVO> sumInvoicingApprovalByIds(@Validated ProjectInvoicingApprovalQuery query){
        ProjectInvoicingApprovalSumVO vo = this.projectInvoicingService.sumInvoicingApprovalByIds(query);
        return R.success(vo);
    }

    /**
     * 导出下载修改申请项目审批查询列表
     * @param query 导出查询参数
     */
    @ApiOperation("审批查询-项目收款开票-下载审批查询列表")
    @PreAuthorize("@ss.hasPermi('projectinvoicing:projectinvoicing:approval:export')")
    @Log(title = "项目管理-项目表", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadProjectInvoicingApproval")
    public R<String> downloadProjectInvoicingApproval(@Validated @RequestBody ProjectInvoicingApprovalQuery query) {
        query.setExportType(ExportType.PROJECT_INVOICING_APPROVAL_LIST);
        reusableAsyncTaskService.addTask("项目收款开票审批查询",
                TaskType.Export,
                query,
                ProjectInvoicingDownloadServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 查询项目收款开票审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询项目收款开票审批列表上的创建部门下拉" )
    @GetMapping("/listProjectInvoicingApprovalDept" )
    public R<List<String>> listProjectInvoicingApprovalDept(ProjectInvoicingApprovalQuery query){
        return R.success(projectInvoicingService.listProjectInvoicingApprovalDept(query));
    }
}


