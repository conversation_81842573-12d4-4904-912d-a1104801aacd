package com.py.common.utils;

import org.springframework.http.MediaType;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 压缩包
 *
 * @author: XXH
 * @date: 2022/3/16 17:35
 */
public class ZipUtils {

    private static final String FORMAT_NAME = "JPG";

    /**
     * 将图片导出压缩包
     *
     * @param response
     * @param imageList     需要导出的图片
     * @param fileName      下载默认框显示的文件名
     * @param imageNameList 图片的名字
     */
    public static void downloadCode(HttpServletResponse response, List<BufferedImage> imageList, List<String> imageNameList, String fileName) {
        try {

            String downloadFilename = URLEncoder.encode(fileName, "UTF-8");
            // 指明response的返回对象是文件流
            response.setContentType("application/octet-stream");
            // 设置在下载框默认显示的文件名
            response.setHeader("Content-Disposition", "attachment;filename=" + downloadFilename + ".zip");

            ZipOutputStream zos = new ZipOutputStream(response.getOutputStream());
            BufferedImage[] files = new BufferedImage[imageList.size()];
            imageList.toArray(files);
            BufferedImage[] bufferedImages = imageList.toArray(files);
            for (int i = 0; i < files.length; i++) {
                BufferedImage img = files[i];
                String name = imageNameList.get(i);
                // fileName  压缩包里面文件夹名称  name 图片名称
                zos.putNextEntry(new ZipEntry(fileName + File.separator + name + ".jpg"));
                ImageIO.write(img, FORMAT_NAME, zos);
            }
            zos.flush();
            zos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    /**
     *
     * @param response
     * @param zipFileName zip名称
     * @return 流
     * @throws Exception
     */
    public static OutputStream setResponse(HttpServletResponse response, String zipFileName) throws Exception{
        zipFileName = URLEncoder.encode(zipFileName, "UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment; filename=" + zipFileName);
        response.setHeader("Pragma", "public");
        response.setHeader("Cache-Control", "no-store");
        response.addHeader("Cache-Control", "max-age=0");
        return response.getOutputStream();
    }
}
