package com.py.common.tools.infocompletionratecalculator;

import com.py.common.tools.infocompletionratecalculator.impl.DefaultInfoCompletionRateCalculator;
import com.py.common.utils.collection.ListUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;

/**
 * 信息完整度计算器
 * <AUTHOR>
 */
@Component
public class InfoCompletionRateCalculator {

    /** 类型计算器对应MAP */
    private final Map<Class<?>, IInfoCompletionRateCalculator<?>> calculatorTypeMap;

    public InfoCompletionRateCalculator(Collection<IInfoCompletionRateCalculator<?>> calculatorImplCollection) {
        this.calculatorTypeMap = ListUtil.toMap(calculatorImplCollection, IInfoCompletionRateCalculator::supportedClass);
    }

    /**
     * 计算指定类型的信息完整度
     * @param target 需计算的对象
     * @param clazz 计算的目标类型
     * @param <T> 计算的目标类型
     * @return 信息完整度
     */
    public <T> BigDecimal calculation(T target, Class<T> clazz) {
        IInfoCompletionRateCalculator<T> completionRateMetadata = this.getCalculator(clazz);
        return completionRateMetadata.calculation(target);
    }

    /**
     * 获取对应类型的信息完整度计算器
     * @param clazz 请求类型实例
     * @param <T> 请求类型
     * @return 信息完整度计算器
     */
    private <T> IInfoCompletionRateCalculator<T> getCalculator(Class<T> clazz) {
        Assert.notNull(clazz, "请求的类型不能为null");

        IInfoCompletionRateCalculator<?> completionRateCalculator = calculatorTypeMap.get(clazz);

        // 请求类型为配置信息完整度计算器实现使, 添加默认实现
        if(completionRateCalculator == null) {
            synchronized(this) {
                completionRateCalculator = calculatorTypeMap.get(clazz);
                if(completionRateCalculator == null) {
                    completionRateCalculator = new DefaultInfoCompletionRateCalculator<>(clazz);
                    this.calculatorTypeMap.put(clazz, completionRateCalculator);
                }
            }
        }

        //noinspection unchecked
        return (IInfoCompletionRateCalculator<T>) completionRateCalculator;
    }

}
