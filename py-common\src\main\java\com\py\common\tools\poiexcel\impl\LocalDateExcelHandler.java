package com.py.common.tools.poiexcel.impl;

import com.py.common.tools.poiexcel.ExcelHandlerAdapter;
import com.py.common.utils.DateUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 时间转换器
 * <AUTHOR>
 */
public class LocalDateExcelHandler implements ExcelHandlerAdapter {


    @Override
    public Object format(Object value, String[] args) {

        if (value == null){
            return null;
        }
        if (value instanceof LocalDate){
            return DateUtils.format((LocalDate)value,"yyyy-MM-dd");
        }
        if (value instanceof Date){
            return DateUtils.format((Date)value,"yyyy-MM-dd");
        }

        return null;
    }
}
