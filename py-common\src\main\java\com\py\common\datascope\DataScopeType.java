package com.py.common.datascope;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Set;

/**
 * 数据权限枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DataScopeType implements IDict<Integer>{

    /** 全部数据权限 */
    ALL(0, "全部数据"),
    /** 本人数据权限 */
    SELF(1, "本人数据"),
    /** 本人及下属数据 */
    SELF_AND_SUBSIDIARY(2, "本人及下属数据"),
    /** 本部门数据权限 */
    DEPT(3, "本部门数据"),
    /** 本部门及下级部门数据 */
    DEPT_AND_CHILD(4, "本部门及下级部门数据");

    private final Integer value;
    private final String label;

    /** 人员数据权限集合 */
    public static Set<DataScopeType> MemberScopeSet = EnumSet.of(SELF, SELF_AND_SUBSIDIARY);

    /** 部门数据权限集合 */
    public static Set<DataScopeType> DeptScopeSet = EnumSet.of(DEPT, DEPT_AND_CHILD);

    @Override
    public Integer getValue() {
        return this.value;
    }

    @Override
    public String getLabel() {
        return this.label;
    }
}
