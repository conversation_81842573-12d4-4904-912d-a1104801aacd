package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.project.projectpaymentrecord.domain.dto.ProjectPaymentRecordDTO;
import com.py.project.projectpaymentrecord.domain.dto.ProjectPaymentRecordResourceDTO;
import com.py.project.projectpaymentrecord.domain.query.ProjectPaymentAuditSelectQuery;
import com.py.project.projectpaymentrecord.domain.query.ProjectPaymentExportQuery;
import com.py.project.projectpaymentrecord.domain.query.ProjectPaymentRecordQuery;
import com.py.project.projectpaymentrecord.domain.vo.MainstayTotalCountVO;
import com.py.project.projectpaymentrecord.domain.vo.ProjectPaymentAuditSelectListVO;
import com.py.project.projectpaymentrecord.domain.vo.ProjectPaymentRecordListVO;
import com.py.project.projectpaymentrecord.domain.vo.ProjectPaymentRecordResourceVO;
import com.py.project.projectpaymentrecord.service.IProjectPaymentRecordService;
import com.py.project.projectpaymentrecord.service.impl.ProjectPaymentAuditApprovalServiceImpl;
import com.py.project.projectpaymentrecord.service.impl.ProjectPaymentRecordAuditSelectServiceImpl;
import com.py.project.projectpaymentrecord.service.impl.ProjectPaymentRecordServiceImpl;
import com.py.project.projectresource.domain.vo.ProjectPaymentInfoVO;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目管理-付款记录Controller
 *
 * <AUTHOR>
 * @date 2023-08-01
 */
@Api(tags = "项目管理-付款记录")
@RestController
@RequestMapping("/project/projectPaymentRecord")
public class ProjectPaymentRecordController extends BaseController {

    /** 项目管理-付款记录服务 */
    @Resource
    private IProjectPaymentRecordService projectPaymentRecordService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 系统设置-主体参数设置服务 */
    @Resource
    private ISystemMainstayParamService systemMainstayParamService;

    /**
     * 查询项目管理-付款记录列表
     *
     * @param query 项目管理-付款记录查询参数
     * @return 项目管理-付款记录列表
     */
    @ApiOperation("查询项目管理-付款记录列表")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentRecord:list')")
    @GetMapping("/listProjectPaymentRecord")
    public R<List<ProjectPaymentRecordListVO>> listProjectPaymentRecord(ProjectPaymentRecordQuery query) {
        List<ProjectPaymentRecordListVO> voList = this.projectPaymentRecordService.listProjectPaymentRecord(query);
        return R.success(voList);
    }

    /**
     * 分页查询项目管理-付款记录列表
     *
     * @param query 项目管理-付款记录查询参数
     * @return 项目管理-付款记录分页
     */
    @ApiOperation("分页查询询项目管理-付款记录列表")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentRecord:list')")
    @GetMapping("/pageProjectPaymentRecord")
    public R<PageInfo<ProjectPaymentRecordListVO>> pageProjectPaymentRecord(ProjectPaymentRecordQuery query) {
        PageInfo<ProjectPaymentRecordListVO> voList = this.projectPaymentRecordService.pageProjectPaymentRecordList(query);
        return R.success(voList);
    }

    /**
     * 审批查询-分页查询项目管理-付款审批列表
     *
     * @param query 项目管理-付款记录查询参数
     * @return 项目管理-付款记录分页
     */
    @ApiOperation("分页查询询项目管理-付款审批列表")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentRecord:list')")
    @GetMapping("/pageProjectPaymentAudit")
    public R<PageInfo<ProjectPaymentAuditSelectListVO>> pageProjectPaymentAudit(@Validated ProjectPaymentAuditSelectQuery query) {

        PageInfo<ProjectPaymentAuditSelectListVO> voList = this.projectPaymentRecordService.pageProjectPaymentAudit(query);
        return R.success(voList);
    }

    /**
     * 导出付款审批列表
     *
     * @param query 项目管理-付款记录查询参数
     * @return 项目管理-付款记录分页
     */
    @ApiOperation("导出付款审批列表")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentRecord:list')")
    @PostMapping("/exportProjectPaymentAudit")
    public R<String> exportProjectPaymentAudit(@Validated @RequestBody ProjectPaymentAuditSelectQuery query) {
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPaiyaMainstayId());
        String fileName = "付款审批查询列表数据-"+systemMainstayParamVO.getMainstayName()+"-"+ DateUtils.format(LocalDateTime.now(),"yyyy-MM-dd-HH：mm：ss")+".xlsx";
        query.setFileName(fileName);
        reusableAsyncTaskService.addTask("付款审批查询下载", TaskType.Export,query, ProjectPaymentRecordAuditSelectServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 获取项目管理-付款记录详细信息
     * @param id 项目管理-付款记录主键
     * @return 项目管理-付款记录视图模型
     */
    @ApiOperation("获取项目管理-付款记录详细信息")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentRecord:query')")
    @GetMapping(value = "/{id}")
    public R<ProjectPaymentInfoVO> getInfo(@PathVariable("id") Long id) {
        return R.success(projectPaymentRecordService.selectProjectPaymentRecordById(id));
    }


    /**
     * 新增项目管理-付款记录-校验付款资源是否在成本列表
     *
     * @param dto 本次付款需要校验的项目资源id
     * @return 校验信息结果
     */
    @ApiOperation("新增项目管理-付款记录-校验付款资源是否在成本列表")
    @PostMapping("/check/projectResourceId")
    public R<ProjectPaymentRecordResourceVO> checkProjectResourceId(@RequestBody ProjectPaymentRecordResourceDTO dto) {
        return R.success(projectPaymentRecordService.checkProjectResourceId(dto));
    }

    /**
     * 新增项目管理-付款记录
     *
     * @param dto 项目管理-付款记录修改参数
     * @return 是否成功
     */
    @ApiOperation("新增项目管理-付款记录")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentRecord:add')")
    @Log(title = "项目管理-付款记录", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Long> add(@RequestBody ProjectPaymentRecordDTO dto) {
        return R.success(projectPaymentRecordService.insertProjectPaymentRecord(dto));
    }

    /**
     * 修改项目管理-付款记录
     *
     * @param dto 项目管理-付款记录修改参数
     * @return 是否成功
     */
    @ApiOperation("修改项目管理-付款记录")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentRecord:edit')")
    @Log(title = "项目管理-付款记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody ProjectPaymentRecordDTO dto) {
        return R.success(projectPaymentRecordService.updateProjectPaymentRecord(dto));
    }

    /**
     * 审批退回/撤回付款信息
     *
     * @param dto 项目管理-付款记录修改参数
     * @return 是否成功
     */
    @ApiOperation("审批退回/撤回付款信息")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentRecord:edit')")
    @Log(title = "项目管理-付款记录", businessType = BusinessType.UPDATE)
    @PutMapping("/approvePaymentRecord")
    public R<Long> approvePaymentRecord(@RequestBody ProjectPaymentRecordDTO dto) {
        return R.success(projectPaymentRecordService.approvePaymentRecord(dto));
    }

    /**
     * 导出项目管理-付款信息
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目管理-退款记录")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:export')")
    @Log(title = "项目管理-退款记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@RequestBody ProjectPaymentExportQuery query) {
        reusableAsyncTaskService.addTask("资源付款执行表单下载", TaskType.Export,query, ProjectPaymentRecordServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 审批查询-本次付款金额合计
     * @param query 导出查询参数
     * @return 合计
     */
    @ApiOperation("本次付款金额合计")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:list')")
    @GetMapping("/currentAmountCount")
    public R<String> currentAmountCount(@Validated ProjectPaymentAuditSelectQuery query) {
        return R.success("操作成功",this.projectPaymentRecordService.currentAmountCount(query));
    }

    /**
     * 审批查询-付款查询项目主体统计
     * @param query 查询参数
     * @return 项目主体统计
     */
    @ApiOperation("付款查询项目主体统计")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:list')")
    @PostMapping("/totalCount")
    public R<List<MainstayTotalCountVO>> totalCount(@Validated ProjectPaymentAuditSelectQuery query) {
        return R.success(this.projectPaymentRecordService.totalCount(query));

    }

    /**
     *  导出项目管理-成本管理-批量付款审批页-执行表单
     * @param query 付款记录id
     * @return 操作结果
     */
    @ApiOperation("导出项目管理-成本管理-批量付款审批页执行表单")
    @Log(title = "项目管理-成本管理-批量付款执行表单下载", businessType = BusinessType.EXPORT)
    @PostMapping("/exportProjectPaymentApproval")
    public R<String> exportProjectPaymentApproval(@RequestBody ProjectPaymentExportQuery query){
        reusableAsyncTaskService.addTask("批量付款执行表单下载",TaskType.Export,query, ProjectPaymentAuditApprovalServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 查询项目付款审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询项目付款审批列表上的创建部门下拉" )
    @GetMapping("/listProjectPaymentApprovalDept" )
    public R<List<String>> listProjectPaymentApprovalDept(ProjectPaymentAuditSelectQuery query){
        return R.success(projectPaymentRecordService.listProjectPaymentApprovalDept(query));
    }

    /**
     * 处理项目资源的渠道老数据问题（渠道id为空）
     * 使用后注释掉
     * @return 是否成功
     */
    @ApiOperation("处理项目资源的渠道老数据问题" )
    @GetMapping("/dispose/oldDate" )
    public R<Integer> disposeOldDate(){
        return R.success(projectPaymentRecordService.disposeOldDate());
    }
}
