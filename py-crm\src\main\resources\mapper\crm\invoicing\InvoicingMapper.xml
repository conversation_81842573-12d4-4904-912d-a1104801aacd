<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.py.crm.customer.invoicing.mapper.InvoicingMapper">

    <select id="getShowId" resultType="java.lang.Integer">
        SELECT
            show_id + 1
        FROM
            `py_crm_invoicing`
        WHERE
            customer_id = #{customerId}
        ORDER BY
            show_id desc
        LIMIT 1
    </select>
</mapper>
