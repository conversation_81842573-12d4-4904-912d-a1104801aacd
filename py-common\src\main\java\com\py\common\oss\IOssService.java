package com.py.common.oss;

import com.py.common.file.FileAnnex;
import com.py.common.file.FileInfoVO;
import com.py.common.oss.model.OssDownloadQuery;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.oss.model.TempToken;
import org.springframework.web.multipart.MultipartFile;
import rx.functions.Action2;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.function.Function;

/**
 * 对象存储服务
 * <AUTHOR> pyr
 **/
public interface IOssService {

    /**
     * 根据文件key集合批量获取访问url  有时间限制
     * @param keyList ossKey列表
     * @return URL列表
     */
    List<String> listUrlByKey(List<String> keyList);

    /**
     * 根据文件key获取访问url  有时间限制
     * @param key ossKey
     * @return fileUrl
     */
    String getUrlByKey(String key);

    /**
     * 批量关联Oss访问地址
     * @param list 数据源列表
     * @param ossKeySelector ossKey选择器
     * @param ossUrlSetCallback ossUrl设置回调
     * @param <T> 元素类型
     */
    <T> void relationOssUrl(List<T> list, Function<T, String> ossKeySelector, Action2<T, String> ossUrlSetCallback);

    /**
     * 上传输入流
     * @param inputStream 输入流
     * @param fileName 文件名称
     * @param needUrl 是否需要返回访问Url
     * @return 上传结果
     */
    OssUploadResult upload(InputStream inputStream, String fileName, boolean needUrl);

    /**
     * 文件上传
     * @param fileList 文件列表
     * @param needUrl 是否需要返回访问Url
     * @return 上传结果
     */
    List<OssUploadResult> upload(List<MultipartFile> fileList, boolean needUrl);

    /**
     * 获取oss临时凭证
     * @return 临时凭证视图模型
     */
    TempToken getAliYunOssConfig();

    /**
     * 转换富文本中的Oss多媒体链接
     * @param richText 富文本
     * @return 转换后的富文本
     */
    String convertRichTextMultimediaKey(String richText);

    /**
     * 下载Oss文件
     * @param query Oss下载请求
     * @param response 请求响应, 文件流将放置响应内
     */
    void download(OssDownloadQuery query, HttpServletResponse response);

    /**
     * 下载Oss文件
     * @param ossKey OssKey
     * @return 文件字节数组
     */
    byte[] download(String ossKey);

    /**
     * 下载Oss文件
     * @param ossKey OssKey
     * @param fileName 文件路径
     */
    void download(String ossKey, String fileName);

    /**
     * 转换输入对象字段上标记 RichText 注解字段的 富文本中的Oss多媒体链接
     * @param richTextBearer 富文本载体
     */
    <T> void convertRichTextMultimediaKey(T richTextBearer);

    /**
     * 填充标记 OssUrl 注解的字段
     * @param ossUrlBearer url字段载体
     * @param <T> 载体类型
     */
    <T> void populateUrlField(T ossUrlBearer);

    /**
     * 根据key获取文件对象信息
     * @param fileAnnexList 文件附件列表
     * @return 文件信息列表
     */
    List<FileInfoVO> getFileInfoByKey(List<FileAnnex> fileAnnexList);

    /**
     * 上传文件至Oss
     * @param imageData 文件流
     * @param fileName 文件名
     * @return key
     */
    String uploadFileToOss(byte[] imageData,String fileName);

    /**
     * 设置Oss上传结果Url
     * @param ossUploadResultList 上传结果列表
     */
    void setUploadResultUrl(List<OssUploadResult> ossUploadResultList);

    /**
     * 删除文件oss
     * @param ossKeyList ossKey
     */
    void deleteUpload(List<String> ossKeyList);
}
