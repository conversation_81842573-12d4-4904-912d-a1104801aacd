package com.py.flow.domain.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 审批执行表单信息DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("审批执行表单信息DTO")
public class PrintFlowDataDTO {

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "派芽合作主体")
    private String mainstayName;

    @ApiModelProperty(value = "资源ID")
    private String resourceCode;

    @ApiModelProperty(value = "资源名称")
    private String resourceName;

    @ApiModelProperty(value = "付款渠道")
    private String channelsName;

    @ApiModelProperty(value = "付款账户")
    private String paymentAccount;

    @ApiModelProperty(value = "执行未付金额")
    private String unpaidAmount;

    @ApiModelProperty(value = "本次付款金额")
    private String currentPaymentAmount;

}
