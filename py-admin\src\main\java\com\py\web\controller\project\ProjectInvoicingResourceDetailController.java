package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.project.projectinvoicingresource.domain.query.ProjectInvoicingResourceDetailQuery;
import com.py.project.projectinvoicingresourcedetail.domain.vo.ProjectInvoicingResourceDetailInfoVO;
import com.py.project.projectinvoicingresourcedetail.service.IProjectInvoicingResourceDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 资源返点开票详情表Controller
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Api(tags = "资源返点开票详情表")
@RestController
@RequestMapping("/invoicingResourceDetail")
public class ProjectInvoicingResourceDetailController extends BaseController {

    /** 资源返点开票详情表服务 */
    @Resource
    private IProjectInvoicingResourceDetailService projectInvoicingResourceDetailService;

    /**
     * 分页查询资源返点开票详情表列表
     *
     * @param query 资源返点开票详情表查询参数
     * @return 资源返点开票详情表分页
     */
    @ApiOperation("分页查询资源返点开票详情表列表~~~~~~~~~~")
    @GetMapping("/pageProjectInvoicingDetail")
    public R<PageInfo<ProjectInvoicingResourceDetailInfoVO>> pageProjectInvoicingDetail(ProjectInvoicingResourceDetailQuery query) {
        return R.success(this.projectInvoicingResourceDetailService.pageProjectInvoicingDetail(query));
    }
}
