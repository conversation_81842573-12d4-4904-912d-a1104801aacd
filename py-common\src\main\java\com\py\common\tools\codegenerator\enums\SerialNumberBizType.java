package com.py.common.tools.codegenerator.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;

/**
 * 序列化业务类型
 * <AUTHOR>
 * @version SerialNumberBizType 2023/5/5 16:51
 */
@AllArgsConstructor
public enum SerialNumberBizType implements IDict<String> {

    /** 无 */
    None("0", "无"),
    /** 下载记录 */
    DOWNLOAD("1","下载记录"),

    /** 资源服务商 */
    RESOURCE_SERVICE("2","资源服务商"),

    /** 资源服务商下载 */
    DOWNLOAD_RESOURCE_SERVICE("3","资源服务商下载"),

    /** 资源红人下载 */
    DOWNLOAD_RESOURCE_SENSATION("4","资源服务商下载"),

    /** 项目 */
    PROJECT("6","项目"),

    /** 日常合同 */
    OA_DAILY("7","日常合同"),

    /** 供应商合同 */
    SUPPLIER("8","供应商合同"),

    /** 客户合同 */
    CUSTOMER("9","客户合同"),

    /** 客户下载记录 */
    CUSTOMER_DOWNLOAD("10","客户下载记录"),

    /** 人脉下载记录 */
    CONNECTION_DOWNLOAD("11","人脉下载记录"),
    ;
    private final String value;

    private final String label;


    @Override
    public String getValue() {
        return this.value;
    }

    @Override
    public String getLabel() {
        return this.label;
    }
}
