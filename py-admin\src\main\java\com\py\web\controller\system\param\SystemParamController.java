package com.py.web.controller.system.param;


import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.system.param.domain.dto.SystemParamDTO;
import com.py.common.file.FileInfoVO;
import com.py.system.param.domain.vo.SystemParamVO;
import com.py.system.param.service.ISystemParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统设置-系统参数设置Controller
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@Api(tags = "系统设置-系统参数设置")
@RestController
@RequestMapping("/system/systemParam")
public class SystemParamController extends BaseController {

    /** 系统设置-系统参数设置服务 */
    @Resource
    private ISystemParamService systemParamService;

    /**
     * 获取登录页背景图
     *
     * @return 登录页背景图
     */
    @ApiOperation("获取登录页背景图")
    @GetMapping("/queryLoginImage")
    public R<List<FileInfoVO>> queryLoginImage() {
        return R.success(this.systemParamService.queryLoginImage());
    }


    /**
     * 获取系统设置-系统参数设置详细信息
     * @return 系统设置-系统参数设置视图模型
     */
    @ApiOperation("获取系统设置-系统参数设置详细信息")
    @PreAuthorize("@ss.hasPermi('system:systemParam:query')")
    @GetMapping(value = "/getInfo")
    public R<SystemParamVO> getInfo() {
        return R.success(systemParamService.selectSystemParam());
    }

    /**
     * 保存系统设置-系统参数设置
     *
     * @param dto 系统设置-系统参数设置修改参数
     * @return 是否成功
     */
    @ApiOperation("保存系统设置-系统参数设置")
    @PreAuthorize("@ss.hasPermi('system:systemParam:add')")
    @Log(title = "保存系统设置-系统参数设置", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> save(@RequestBody SystemParamDTO dto) {
        return R.success(systemParamService.saveSystemParam(dto));
    }


}
