package com.py.common.core.domain.query;

import com.py.common.utils.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * 基础更新信息查询接口
 * <AUTHOR>
 */
@Data
public class BaseUpdateQuery implements IUpdateQuery {

    /** 更新用户ID */
    @ApiModelProperty("更新用户ID")
    private List<Long> updateUser;

    /** 更新用户部门ID */
    @ApiModelProperty("更新用户部门ID")
    private List<Long> updateDept;

    /** 更新时间-开始 */
    @ApiModelProperty("更新时间-开始")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate startUpdateDate;

    /** 更新时间-结束 */
    @ApiModelProperty("更新时间-结束")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate endUpdateDate;

}
