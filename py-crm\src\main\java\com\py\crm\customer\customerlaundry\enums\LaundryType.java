package com.py.crm.customer.customerlaundry.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @version LaundryType 2023/9/8 17:40
 */
@AllArgsConstructor
public enum LaundryType implements IDict<Integer> {
    /** 0客户 */
    CUSTOMER(0,"客户"),
    /** 1人脉 */
    CONNECTION(1,"人脉");

    private final Integer value;

    private final String label;

    /**
     * 获取字典说明
     * @return 字典说明
     */
    @Override
    public String getLabel() {
        return label;
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public Integer getValue() {
        return value;
    }
}
