package com.py.common.typehandler.impl;

import com.py.common.typehandler.BaseSetTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.util.function.Function;

/**
 * 长整型基础集合类型处理器
 * <AUTHOR>
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
public class LongSetTypeHandler extends BaseSetTypeHandler<Long> {
    /**
     * 初始化内容序列化器, 将序列元素转化为字符串
     * <p>输入不会为null</p>
     * @return 内容序列化器
     */
    @Override
    protected Function<Long, String> initSerializer() {
        return x -> x.toString();
    }

    /**
     * 初始化内容反序列化器, 从字符串还原为序列元素
     * @return 内容反序列化器
     */
    @Override
    protected Function<String, Long> initDeserializer() {
        return Long::valueOf;
    }
}
