package com.py.common.utils;

import com.alibaba.fastjson.JSON;
import org.apache.commons.beanutils.BeanMap;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 实体转换
 * <AUTHOR>
 */
public class BeanConvertUtils {

    /**
     * 对象转换
     * @param source 原数据
     * @param clazz 目标类
     * @return 入参不合法时返回null
     */
    public static <T> T convert(Object source, Class<T> clazz) {
        if(source == null || clazz == null) {
            return null;
        }

        try {
            T target = clazz.newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch(InstantiationException | IllegalAccessException e) {
            throw new RuntimeException("对象转换异常");
        }
    }

    /**
     * 对象转换
     * @param source 原数据
     * @param clazz 目标类
     * @return 入参不合法时返回null
     */
    public static <T> T jsonConvert(Object source, Class<T> clazz) {
        if(source == null || clazz == null) {
            return null;
        }

        return JSON.parseObject(JSON.toJSONString(source), clazz);
    }

    /**
     * 对象list转换
     * @param sources 原数据
     * @param clazz 目标类
     * @return 入参不合法时返回空集合
     */
    public static <T> List<T> convertList(List<?> sources, Class<T> clazz) {
        if(CollectionUtils.isEmpty(sources) || clazz == null) {
            return new ArrayList<>();
        }
        List<T> targets = new ArrayList<>(sources.size());
        sources.forEach(source -> targets.add(convert(source, clazz)));
        return targets;
    }

    /**
     * 对象list转换
     * @param sources 原数据
     * @param clazz 目标类
     * @return 入参不合法时返回空集合
     */
    public static <T> List<T> jsonConvertList(List<?> sources, Class<T> clazz) {
        if(CollectionUtils.isEmpty(sources) || clazz == null) {
            return new ArrayList<>();
        }
        return JSON.parseArray(JSON.toJSONString(sources), clazz);
    }

    /**
     * 对象转map
     */
    public static Map<?, ?> objectToMap(Object obj) {
        if(obj == null) {
            return null;
        }
        return new BeanMap(obj);
    }
}
