package com.py.common.datascope;

import com.py.common.enums.IDict;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

/**
 * 数据权限页面类型
 * <AUTHOR>
 */
@Getter
@ApiModel("数据权限页面类型")
public enum DataScopePageType implements IDict<Integer> {

    // ------ SED项目管理 100 --------
    /** SED - 项目管理 */
    SED_ProjectManage(101, "SED - 项目管理"),
    /** SED - 收入管理 */
    SED_IncomeManage(102, "SED - 收入管理"),
    /** SED - 成本管理 */
    SED_CostManage(103, "SED - 成本管理"),
    /** SED - 收票管理 */
    SED_Ticket(104, "SED - 收票管理"),
    /** SED - 资源坏账管理 */
    SED_BadDept(105,"SED - 资源坏账管理"),
    /** SED - 合同管理 */
    SED_Contract(106,"SED -合同管理"),
    /** SED - 项目收款开票 */
    SED_Invoicing(107, "SED - 项目收款开票"),
    /** SED - 资源返点开票 */
    SED_Invoicing_Resource(108, "SED - 资源返点开票"),
    /** SED -返点管理 */
    SED_REBATE(109,"SED -返点管理"),

    /** SED - 管理及销售人员业绩 */
    SED_SaleManageSourceFlow(110, "SED - 管理及销售人员业绩"),

    /** SED - 执行人员业绩 */
    SED_ExecutiveFlow(111, "SED - 执行人员业绩"),

    /** SED -收入坏账管理 */
    SED_income_bad_debt(112,"SED - 收入坏账管理"),

    /** 业务财报 */
    SED_BUSINESS_FINANCIAL_REPORT(113, "SED - 业务财报"),


    // ------ 派财务管理 200 --------
    /** 派财务 - 收入管理 */
    Finance_IncomeManage(201, "派财务 - 收入管理", true),
    /** 派财务 - 成本管理 */
    Finance_CostManage(202, "派财务 - 成本管理", true),
    /** 派财务 - 资源坏账管理 */
    Finance_BadDept(203, "派财务 - 资源坏账管理", true),

    /** 派财务 - 项目收款开票 */
    Finance_Invoicing(204, "派财务 - 项目收款开票", true),
    /** SED - 资源返点开票 */
    Finance_Invoicing_Resource(205, "派财务 - 资源返点开票", true),
    /** 派财务 - 合同管理 */
    Finance_Contract(206,"派财务 -合同管理",true),
    /** 派财务 -返点管理 */
    Finance_REBATE(207,"派财务 -返点管理", true),
    /** 派财务 - 成本管理 */
    Finance_Ticket(208, "派财务 - 收票管理", true),

    /** 派财务 -OA管理 */
    Finance_OaCost(209,"派财务 -OA管理", true),
    /** 派财务 -审批查询-费用审批查询 */
    Finance_COST_CONTRACT(233,"派财务 - 财务审批查询 -费用审批查询", true),
    /** 派财务 -审批查询-日常合同审批查询 */
    Finance_DAILY_CONTRACT(232,"派财务 - 财务审批查询 -日常合同审批查询", true),
    /** 派财务 -审批查询-其他审批查询 */
    Finance_OTHER_CONTRACT(231,"派财务 - 财务审批查询 -其他审批查询", true),
    /** 派财务 -审批查询 */
    Finance_ADD_PROJECT(210,"派财务 - 财务审批查询 -新增项目查询", true),
    /** 派财务 - 财务审批查询-修改申请查询 */
    Finance_UPDATE_PROJECT(211,"派财务 - 财务审批查询 -修改申请查询", true),
    /** 派财务 - 财务审批查询-付款审批查询 */
    Finance_Payment(212,"派财务 - 财务审批查询 -付款查询", true),
    /** 派财务 - 财务审批查询-退款审批查询 */
    Finance_Refund(213,"派财务 - 财务审批查询 -退款查询", true),



    /** 派财务 - 财务审批查询-结案查询 */
    Finance_Close_Case(216,"派财务 - 财务审批查询 -结案查询", true),
    /** 派财务 - 项目收款开票 审批查询*/
    Finance_Invoicing_Approval_List(217, "派财务 - 财务审批查询 - 项目收款开票查询", true),
    /** 派财务 - 资源返点开票 审批查询*/
    Finance_Invoicing_Resource_Approval_List(218, "派财务 - 财务审批查询 - 资源返点开票查询", true),
    /** 派财务 - 确认收入 审批查询*/
    Finance_Project_Confirm_Record_Approval_List(219, "派财务 - 财务审批查询 - 确认收入查询", true),
    /** 派财务 - 修改状态 审批查询*/
    Finance_Project_Update_Status_Approval_List(230,"派财务 - 财务审批查询 - 修改状态查询", true),
    /** 派财务 - 返点登记 审批查询 */
    Finance_Project_Rebate_Resource_Approval_List(234,"派财务 - 财务审批查询 - 返点登记查询", true),
    /** 派财务 - 批量修改 审批查询*/
    Finance_Project_Batch_update_Record_Approval_List(235, "派财务 - 财务审批查询 - 批量修改查询", true),

    /** 派财务 -收入坏账管理 */
    Finance_income_bad_debt(220,"派财务 - 收入坏账管理",true),

    /** 派财务 - 财务审批查询- 合同管理审批列表 */
    Finance_Audit_Contract(221,"派财务 - 财务审批查询- 合同管理审批列表",true),
    /** 派财务 - 收入坏账管理审批列表 */
    Finance_Audit_BadDept(222, "派财务 - 财务审批查询 - 收入坏账查询", true),
    /**派财务 - 资源返点坏账审批查询列表*/
    Finance_Audit_BadRebates(223,"派财务 - 财务审批查询 - 返点坏账审批查询", true),

    /**派财务 - 企业财报 */
    Finance_Corporate_Report(224,"派财务 - 企业财报", true),

    // ------ CRM云客户 300 --------
    /** CRM - 客户管理 */
    CRM_CustomerManage(301, "CRM - 客户管理"),
    /** CRM - 人脉管理 */
    CRM_ConnectionManage(302, "CRM - 人脉管理"),
    /** CRM - 客户贡献管理 */
    CRM_CustomerContribute(303, "CRM - 客户贡献管理"),
    /** CRM - 比稿管理 */
    CRM_ComparedManage(304, "CRM - 比稿管理"),

    ;

    private final Integer value;
    private final String label;
    /** 是否有主体条件 */
    private final Boolean hasMainstay;
    DataScopePageType(Integer value, String label) {
        this(value, label, false);
    }
    DataScopePageType(Integer value, String label, Boolean hasMainstay) {
        this.value = value;
        this.label = label;
        this.hasMainstay = hasMainstay;
    }
}
