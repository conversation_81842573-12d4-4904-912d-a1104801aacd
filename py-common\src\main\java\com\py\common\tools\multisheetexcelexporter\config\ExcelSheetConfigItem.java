package com.py.common.tools.multisheetexcelexporter.config;

import com.py.common.tools.multisheetexcelexporter.enums.CellType;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

/**
 * Excel表导出配置项
 * @param <T> 输出对象的类型
 * <AUTHOR>
 */
public class ExcelSheetConfigItem<T> {


    /** 提取导出内容的函数 */
    @Getter
    private final Function<T, Object> contextSelector;
    /** 内容的Excel标题 */
    @Getter
    private final List<String> headerList;
    /** 内容是否为列表 */
    @Getter
    private final boolean isList;

    /** 合计数据 */
    @Getter
    List<Integer> totalData;

    /** 输出的下拉框内容 */
    @Getter
    @Setter
    private CellType cellType = CellType.GENERAL;
    /** 单元格样式调整者 */
    @Getter
    @Setter
    private CellStyleModifier styleModifier = null;
    /** 下拉框选项内容获取函数 */
    @Getter
    private Function<T, List<String>> dropDownOptionSelector;
    /** 表头区间 */
    private int[] headerRang;

    /** 高 */
    @Getter
    @Setter
    private Integer height = 14;

    /** 宽 */
    @Getter
    @Setter
    private Integer width;

    /**
     * 基础表头配置
     * @param contextSelector 提取输出内容的函数
     * @param headerList 内容的Excel表头列表
     * @param isList 字段是否为列表
     */
    public ExcelSheetConfigItem(Function<T, Object> contextSelector, List<String> headerList, boolean isList) {
        this.contextSelector = contextSelector;
        this.headerList = headerList;
        this.isList = isList;
    }

    /**
     * 创建顶部表头配置
     * @param headerName 顶部表头名
     * @param startPoint 顶部表头起始点 从0开始
     * @param endPoint 顶部表头结束点
     * @return 顶部表头配置
     */
    public static <T> ExcelSheetConfigItem<T> createTopHeaderConfig(String headerName, int startPoint, int endPoint) {
        ExcelSheetConfigItem<T> configItem = new ExcelSheetConfigItem<>(null, Collections.singletonList(headerName), false);
        configItem.headerRang = new int[]{startPoint, endPoint};
        return configItem;
    }

    /**
     * 创建下拉框单元格的配置项
     * @param optionSelector 选项内容获取函数
     * @param titleName 内容的Excel标题
     * @return 下拉框单元格的配置项
     */
    public static <T> ExcelSheetConfigItem<T> createDropDownCellConfig(Function<T, List<String>> optionSelector, String titleName) {
        ExcelSheetConfigItem<T> configItem = new ExcelSheetConfigItem<>(null, Collections.singletonList(titleName), false);
        configItem.cellType = CellType.DROP_DOWN;
        configItem.dropDownOptionSelector = optionSelector;
        return configItem;
    }

    /**
     * 合计行配置
     * @param headerName 合计名
     * @param startPoint 合计名起始点 从0开始
     * @param endPoint 合计名结束点
     * @param totalData 合计数据
     */
    public ExcelSheetConfigItem(String headerName, int startPoint, int endPoint, List<Integer> totalData) {
        this.headerList = new ArrayList<>();
        this.headerList.add(headerName);
        this.headerRang = new int[]{startPoint, endPoint};
        this.contextSelector = null;
        this.isList = false;
        this.totalData = totalData;
    }

    /** 取得表头起始点*/
    public int getHeaderStartPoint() {
        return headerRang[0];
    }

    /** 取得表头结束点*/
    public int getHeaderEndPoint() {
        return headerRang[1];
    }

    /**
     * 移动表头位置
     * @param shiftNumber 移动数量, 数组为正时: 向右移动, 数组为负时:向左移动
     */
    public void shiftHeader(int shiftNumber) {
        this.headerRang[0] += shiftNumber;
        this.headerRang[1] += shiftNumber;
    }

    @Override
    public String toString() {
        return "ExcelSheetConfigItem{" +
                "headerList=" + headerList +
                '}';
    }
}
