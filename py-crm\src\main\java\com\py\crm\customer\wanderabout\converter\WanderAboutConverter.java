package com.py.crm.customer.wanderabout.converter;

import com.py.common.core.converter.BaseDomainModelConverter;
import com.py.crm.customer.wanderabout.domain.WanderAbout;
import com.py.crm.customer.wanderabout.domain.dto.WanderAboutDTO;
import com.py.crm.customer.wanderabout.domain.vo.WanderAboutVO;
import org.mapstruct.Mapper;

/**
 * 客户管理-客户流转模型转换器
 *
 * <AUTHOR>
 * @date 2023-08-16
 */
@Mapper(componentModel = "spring")
public interface WanderAboutConverter extends BaseDomainModelConverter<WanderAbout, WanderAboutVO, WanderAboutDTO> {

}
