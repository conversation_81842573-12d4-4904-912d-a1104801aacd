package com.py.common.filter;

import com.py.common.utils.StringUtils;
import com.py.common.utils.NullMergeUtils;
import org.springframework.http.MediaType;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static com.py.common.utils.NullMergeUtils.nullMerge;

/**
 * Repeatable 过滤器
 * <AUTHOR>
 */
public class RepeatableFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        ServletRequest requestWrapper = null;
        ServletResponse responseWrapper = new RepeatedlyResponseWrapper((HttpServletResponse) response);
        if(request instanceof HttpServletRequest
                && StringUtils.startsWithIgnoreCase(request.getContentType(), MediaType.APPLICATION_JSON_VALUE)) {
            requestWrapper = new RepeatedlyRequestWrapper((HttpServletRequest) request, response);
        }

        chain.doFilter(NullMergeUtils.nullMerge(requestWrapper, request), NullMergeUtils.nullMerge(responseWrapper, response));
    }

    @Override
    public void destroy() {

    }
}
