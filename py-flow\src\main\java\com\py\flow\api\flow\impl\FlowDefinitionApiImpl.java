package com.py.flow.api.flow.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.py.common.constant.FlowConstant;
import com.py.common.exception.ServiceException;
import com.py.flow.api.flow.IFlowDefinitionApi;
import com.py.flow.domain.enums.ApprovalBizType;
import com.py.flow.flowversion.domain.FlowVersion;
import com.py.flow.flowversion.mapper.FlowVersionMapper;
import com.py.flow.tools.factory.FlowServiceFactory;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.image.impl.DefaultProcessDiagramGenerator;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collections;

/**
 * 流程定义服务实现类
 * <AUTHOR>
 * @date 2023/7/14 13:52
 */
@Service
@Log4j2
public class FlowDefinitionApiImpl extends FlowServiceFactory implements IFlowDefinitionApi {

    @Resource
    private FlowVersionMapper flowVersionMapper;

    /**
     * 新增流程定义
     * @param deploymentName 部署的流程名称
     * @param approvalBizType 流程关联的审批业务
     * @param flowVersionId 关联的流程版本ID
     * @param bpmnXml 流程定义的xml
     * @return 流程定义
     */
    @Override
    public ProcessDefinition addProcessDefinition(String deploymentName, ApprovalBizType approvalBizType, Long flowVersionId, String bpmnXml){
        log.info("准备部署流程名称：{}", deploymentName);

        //部署成功会返回一个部署的id存储在模板表当做流程的唯一id deployment.getId().
        Deployment deployment = repositoryService.createDeployment()
                //key 是 流程的 id 属性，表示是流程的唯一标识，一个key 可以对应多个version版本的流程定义
                .name(deploymentName)
                .key(flowVersionId.toString())
                .addString(deploymentName + FlowConstant.BPMN_FILE_SUFFIX, bpmnXml)
                .category(approvalBizType.getValue())
                .deploy();
        log.info("部署流程名称：{}成功", deploymentName);
        return repositoryService.createProcessDefinitionQuery().deploymentId(deployment.getId()).singleResult();
    }

    @Override
    public InputStream getFlowVersionFlowImage(Long flowManageVersionId) {
        LambdaQueryWrapper<FlowVersion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowVersion::getFlowVersionId, flowManageVersionId);

        FlowVersion flowVersion = flowVersionMapper.selectOne(queryWrapper);

        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .deploymentId(flowVersion.getProcessDeploymentId())
                .singleResult();
        //获得图片流
        DefaultProcessDiagramGenerator diagramGenerator = new DefaultProcessDiagramGenerator();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
        //输出为图片
        return diagramGenerator.generateDiagram(
                bpmnModel,
                "png",
                Collections.emptyList(),
                Collections.emptyList(),
                "宋体",
                "宋体",
                "宋体",
                null,
                1.0,
                false);
    }

    /**
     * 获取流程设计xml文件
     * @param processDeploymentId 流程部署ID
     * @return 流程Xml
     */
    @Override
    public String getFlowBpmnXml(String processDeploymentId){
        Assert.hasText(processDeploymentId, "流程部署ID不能为空");

        //获取流程定义
        ProcessDefinition definition = repositoryService.createProcessDefinitionQuery()
                .deploymentId(processDeploymentId)
                .singleResult();
        InputStream inputStream = repositoryService.getResourceAsStream(definition.getDeploymentId(), definition.getResourceName());
        try {
            return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        } catch(IOException e) {
            log.error("获取流程设计xml文件失败", e);
            throw new ServiceException("获取流程设计xml文件失败");
        }
    }

    /**
     * 获取流程Bpmn模型
     * @param processDefinitionId 流程定义ID
     * @return 流程Bpmn模型
     */
    @Override
    public BpmnModel getProcessBpmnModel(String processDefinitionId) {
        Assert.notNull(processDefinitionId, "流程定义ID不能为空");
        return repositoryService.getBpmnModel(processDefinitionId);
    }

}
