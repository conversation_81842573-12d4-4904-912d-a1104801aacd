package com.py.common.tools.modifycomparator.contexthandler;

import java.lang.reflect.Field;

/**
 * 自定义比较内容处理器接口
 * <AUTHOR>
 */
public interface ICompareContextHandler {

    /**
     * 自定义处理逻辑
     * @param fieldValue 字段值
     * @param field 字段定义
     * @return 处理结果值
     */
    String handle(Object fieldValue, Field field);

    /** 空自定义比较内容处理器接口 */
    interface Void extends ICompareContextHandler {
    }
}
