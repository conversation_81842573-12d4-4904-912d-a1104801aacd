package com.py.common.utils;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import com.py.common.utils.collection.ListUtil;
import org.hibernate.validator.internal.engine.ConstraintViolationImpl;
import org.hibernate.validator.internal.engine.path.PathImpl;

import javax.validation.ConstraintViolation;
import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 验证错误工具类
 * <AUTHOR>
 */
public class ConstraintViolationUtils {

    /**
     * 创建验证错误结果
     * @param property 错误字段
     * @param beanClass 错误Bean类型
     * @param errorMsg 错误信息
     * @param args 错误信息格式化参数
     * @return 验证错误结果
     */
    public static ConstraintViolation<?> createConstraintViolation(String property, Class<?> beanClass, String errorMsg, Object... args) {
        errorMsg = String.format(errorMsg, args);
        PathImpl propertyPath = PathImpl.createPathFromString(property);
        return ConstraintViolationImpl.forParameterValidation(
                errorMsg, null, null, null,
                beanClass, null, null, null, propertyPath,
                null, null, null);
    }

    /**
     * 为验证错误结果的错误消息追加前缀
     * @param violation 验证错误结果
     * @param errorPrefix 要追加的错误前缀
     * @return 修改后的验证错误结果
     */
    public static ConstraintViolation<?> addErrorPrefix(ConstraintViolation<?> violation, String errorPrefix) {
        return ConstraintViolationImpl.forParameterValidation(
                errorPrefix + violation.getMessageTemplate(), null, null, null,
                violation.getRootBeanClass(), null, null, null, violation.getPropertyPath(),
                null, null, null);
    }

    /**
     * 为验证错误结果的错误消息追加前缀
     * @param violationSet 验证错误结果
     * @param errorPrefix 要追加的错误前缀
     * @return 修改后的验证错误结果
     */
    public static Set<ConstraintViolation<?>> addErrorPrefix(Set<ConstraintViolation<?>> violationSet, String errorPrefix) {
        if(ListUtil.isEmpty(violationSet)) {
            return ListUtil.emptySet();
        }
        return violationSet.stream()
                .map(violation -> addErrorPrefix(violation, errorPrefix))
                .collect(Collectors.toSet());
    }

    /**
     * 将验证错误转换(错误字段,错误信息)Map
     * @param violationSet 验证错误集合
     * @return (错误字段, 错误信息)Map
     */
    public static <T extends ConstraintViolation<?>> ListMultimap<String, String> convertToErrorMap(Collection<T> violationSet) {
        ListMultimap<String, String> resultMap = ArrayListMultimap.create();

        for(T violation : violationSet) {
            String path = violation.getPropertyPath().toString();
            String field = path.substring(path.indexOf(".") + 1);
            if(resultMap.containsKey(field) == false) {
                resultMap.put(field, violation.getMessageTemplate());
            } else {
                resultMap.put(field, ", " + violation.getMessageTemplate());
            }
        }
        return resultMap;
    }
}
