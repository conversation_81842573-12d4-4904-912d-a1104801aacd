<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.py.crm.connectionwander.mapper.ConnectionWanderMapper">

    <resultMap type="com.py.crm.connectionwander.domain.ConnectionWander" id="ConnectionWanderResult">
        <result property="id" column="id" />
        <result property="connectionId" column="connection_id" />
        <result property="serviceUserId" column="service_user_id" />
        <result property="serviceUserDept" column="service_user_dept" />
        <result property="isHidden" column="is_hidden" />
        <result property="createId" column="create_id" />
        <result property="createBy" column="create_by" />
        <result property="createDept" column="create_dept" />
        <result property="createTime" column="create_time" />
        <result property="updateId" column="update_id" />
        <result property="updateBy" column="update_by" />
        <result property="updateDept" column="update_dept" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
    </resultMap>

</mapper>
