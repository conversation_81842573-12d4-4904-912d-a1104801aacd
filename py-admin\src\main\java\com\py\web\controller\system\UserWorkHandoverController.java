package com.py.web.controller.system;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.system.userworkhandover.domain.dto.AddWorkHandoverDTO;
import com.py.system.userworkhandover.domain.query.UserWorkHandoverQuery;
import com.py.system.userworkhandover.domain.vo.UserWorkHandoverListVO;
import com.py.system.userworkhandover.service.IUserWorkHandoverService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 组织权限管理 - 工作交接记录Controller
 */
@Api(tags = "组织权限管理 -  工作交接记录")
@RestController
@RequestMapping("/system/user/workHandover")
@Validated
public class UserWorkHandoverController extends BaseController {

    /** 组织权限管理 -  工作交接记录服务 */
    @Resource
    private IUserWorkHandoverService userWorkHandoverService;

    /**
     * 分页查询组织权限管理 -  工作交接记录列表
     *
     * @param query 组织权限管理 -  工作交接记录查询参数
     * @return 组织权限管理 -  工作交接记录分页
     */
    @ApiOperation("分页查询询组织权限管理 -  工作交接记录列表")
    @GetMapping("/pageWorkHandover")
    public R<PageInfo<UserWorkHandoverListVO>> pageWorkHandover(UserWorkHandoverQuery query) {
        PageInfo<UserWorkHandoverListVO> voList = this.userWorkHandoverService.pageUserWorkHandoverList(query);
        return R.success(voList);
    }

    /**
     * 获取组织权限管理 -  工作交接记录详细信息
     *
     * @return 组织权限管理 -  工作交接记录视图模型
     */
    @ApiOperation("获取组织权限管理 -  检查是否有进行中的工作交接记录")
    @GetMapping(value = "/check")
    public R<Boolean> check() {
        return R.success(userWorkHandoverService.check());
    }

    /**
     * 新增组织权限管理 -  工作交接记录
     *
     * @param dto 组织权限管理 -  工作交接记录修改参数
     * @return 是否成功
     */
    @ApiOperation("新增组织权限管理 -  新增工作交接记录")
    @Log(title = "组织权限管理 - 新增工作交接记录", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public R<Boolean> add(@Validated @RequestBody AddWorkHandoverDTO dto) {
        return R.success(userWorkHandoverService.insertWorkHandover(dto));
    }
}
