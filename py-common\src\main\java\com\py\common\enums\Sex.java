package com.py.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;

/**
 * 性别
 * <AUTHOR>
 */
@AllArgsConstructor
public enum Sex implements IEnum<Integer> {

    /** 男 */
    MAN(0),
    /** 女 */
    WOMAN(1),
    /** 未知 */
    UNKNOWN(2);

    private final Integer value;


    @Override
    public Integer getValue() {
        return this.value;
    }
}
