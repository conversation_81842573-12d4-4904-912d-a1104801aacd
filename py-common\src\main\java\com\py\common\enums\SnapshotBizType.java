package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 快照类型
 * <AUTHOR>
 */
@AllArgsConstructor
public enum SnapshotBizType implements IDict<Integer>{
    /**
     * 付款渠道
     */
    CHANNELS(0,"付款渠道"),
    /**
     * 资源管理(红人)
     */
    RESOURCE_SENSATION(1,"资源管理(红人)"),
    /**
     * 资源管理(服务商)
     */
    RESOURCE_SERVICE(2,"资源管理(服务商)"),

    /** 客户 */
    CUSTOMER(3,"客户"),

    /** 人脉 */
    CONNECTION(4,"人脉"),

    /** 资源返点开票 */
    INVOICING_RESOURCE(5,"资源返点开票"),

    /** 项目收款开票 */
    PROJECT_INVOICING(6,"项目收款开票"),

    ;
    private final Integer values;
    private final String label;

    @Override
    public String getLabel() {
        return this.label;
    }

    @Override
    public Integer getValue() {
        return this.values;
    }
}
