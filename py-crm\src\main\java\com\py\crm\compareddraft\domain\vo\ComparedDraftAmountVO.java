package com.py.crm.compareddraft.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.py.common.utils.MathUtils;
import com.py.framework.config.jackson.serializer.IntegerJsonSerializer;
import com.py.framework.config.jackson.serializer.MoneyJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 合计参数
 * <AUTHOR>
 * @version ComparedDraftAmountVO 2023/7/17 14:33
 */
@Data
public class ComparedDraftAmountVO {
    /** 比稿次数 */
    @ApiModelProperty("比稿次数")
    @JsonSerialize(using = IntegerJsonSerializer.class)
    private Integer comparedDraftNum;
    /** 商务标成功数 */
    @ApiModelProperty("商务标成功数")
    @JsonSerialize(using = IntegerJsonSerializer.class)
    private Integer commercialBidSuccessNum;
    /** 商务标成功率 */
    @ApiModelProperty("商务标成功率")
    private String commercialBidSuccessRate;
    /** 技术标成功数 */
    @ApiModelProperty("技术标成功数")
    @JsonSerialize(using = IntegerJsonSerializer.class)
    private Integer techniqueBidSuccessNum;
    /** 技术标成功率 */
    @ApiModelProperty("技术标成功率")
    private String techniqueBidSuccessRate;
    /** 最终比稿成功数 */
    @ApiModelProperty("最终比稿成功数")
    @JsonSerialize(using = IntegerJsonSerializer.class)
    private Integer finalSuccessNum;
    /** 最终比稿成功率 */
    @ApiModelProperty("最终比稿成功率")
    private String finalSuccessRate;

    public String getCommercialBidSuccessRate() {
        if(this.commercialBidSuccessNum == null || this.commercialBidSuccessNum == 0){
            return "0%";
        }

        BigDecimal divide = BigDecimal.valueOf(commercialBidSuccessNum)
                .divide(MathUtils.toBigDecimal(String.valueOf(comparedDraftNum)), 4, RoundingMode.HALF_UP);
        return divide.multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString() + "%";
    }

    public String getTechniqueBidSuccessRate() {
        if(this.techniqueBidSuccessNum == null || this.techniqueBidSuccessNum == 0){
            return "0%";
        }

        BigDecimal divide = BigDecimal.valueOf(techniqueBidSuccessNum)
                .divide(MathUtils.toBigDecimal(String.valueOf(comparedDraftNum)), 4, RoundingMode.HALF_UP);
        return divide.multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString() + "%";
    }

    public String getFinalSuccessRate() {
        if(this.finalSuccessNum == null || this.finalSuccessNum == 0){
            return "0%";
        }

        BigDecimal divide = BigDecimal.valueOf(finalSuccessNum)
                .divide(MathUtils.toBigDecimal(String.valueOf(comparedDraftNum)), 4, RoundingMode.HALF_UP);
        return divide.multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString() + "%";
    }
}
