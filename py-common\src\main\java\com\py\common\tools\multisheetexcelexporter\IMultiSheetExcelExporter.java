package com.py.common.tools.multisheetexcelexporter;

import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

/**
 * 提供导出多个sheet的Excel文件的接口
 * <AUTHOR>
 */
public interface IMultiSheetExcelExporter {

    /**
     * 添加 sheet 配置
     * @param <T> 导出 sheet 类型
     * @param list 导出数据源
     * @param config 导出配置
     * @param sheetName 导出sheet名
     */
    <T> void addSheetConfig(List<T> list, ExcelSheetConfig<T> config, String sheetName);

    /**
     * 导出至指定响应
     * @param response 导出Excel返回的http响应
     * @param excelName 导出Excel返回的文件名
     */
    void exportExcel(HttpServletResponse response, String excelName);

    /**
     * 导出至指定响应并转换为byte
     * @param excelName 导出Excel返回的文件名
     * @return byte数组
     */
    byte[] exportExcelToByte(String excelName);

    /**
     * 导出至指定响应并转换为byte
     * @return byte数组
     */
    InputStream exportExcelToStream();

    /**
     * 写入既有Excel然后导出至指定响应
     * @param response 导出Excel返回的http响应
     * @param excelInputStream Excel输入流
     * @param excelName 导出Excel返回的文件名
     */
    void exportExcel(HttpServletResponse response, InputStream excelInputStream, String excelName);

    /**
     * 写入既有Excel然后导出字节数组
     * @param excelInputStream Excel输入流
     * @return Excel字节数组
     */
    byte[] exportExcel(InputStream excelInputStream);
}
