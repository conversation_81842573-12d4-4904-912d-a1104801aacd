package com.py.flow.flowdatauser.domain.dto;

import com.py.common.datascope.DataScopePageType;
import com.py.flow.domain.enums.ApprovalBizType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import java.time.LocalDate;

/**
 * 审批数据与人员关联数据传输模型
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@Data
@ApiModel("审批数据与人员关联数据传输模型" )
public class FlowDataUserDTO {
    private static final long serialVersionUID = 1L;

    /** 业务id*/
    @ApiModelProperty("业务id" )
    private Long bizId;

    /** 业务类型*/
    @ApiModelProperty("业务类型" )
    private ApprovalBizType bizType;

    /** 用户id*/
    @ApiModelProperty("用户id" )
    private Long userId;

}
