package com.py.common.core.domain.query;

import com.py.common.utils.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 基础创建信息查询接口
 * <AUTHOR>
 */
@Data
public class BaseCreateQuery implements ICreateQuery{

    /** 创建人 */
    @ApiModelProperty("创建人")
    private String createUser;

    /** 创建人部门 */
    @ApiModelProperty("创建人部门")
    private String createDept;

    /** 创建时间-开始 */
    @ApiModelProperty("创建时间-开始")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate startCreateDate;

    /** 创建时间-结束 */
    @ApiModelProperty("创建时间-结束")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate endCreateDate;

}
