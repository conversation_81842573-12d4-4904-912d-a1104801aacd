package com.py.common.tools.modifycomparator.annotation;

import com.py.common.tools.modifycomparator.contexthandler.ICompareContextHandler;
import com.py.common.tools.modifycomparator.enums.CompareFieldType;
import com.py.common.tools.modifycomparator.enums.ModifyContextType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 对象比较需比较的字段标记注解
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CompareField {

    /** 字段名称 */
    String value();

    /** 字典名称 */
    String dictName() default "";

    /** 是否为字典列表 */
    boolean isListDict() default false;

    String delimiter() default ",";

    /** 修改内容类型 */
    ModifyContextType modifyContextType() default ModifyContextType.STRING;

    /** 列表项参数类型 */
    Class<?> typeArgument() default Void.class;

    /** 自定义比较内容处理器接口-自定义设置值修改前的处理逻辑 */
    Class<? extends ICompareContextHandler> customContextHandler() default ICompareContextHandler.Void.class;

    /**
     * 字段类型
     * @return 类型
     */
    CompareFieldType fieldType() default CompareFieldType.Simple;
}
