package com.py.crm.customer.customercontribute.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.py.crm.customer.customercontribute.domain.ComparedDraftUser;

import java.util.List;

/**
 * 客户管理-比稿策划人关联Service接口
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
public interface IComparedDraftUserService extends IService<ComparedDraftUser>{

    /**
     * 新增客户管理-比稿策划人关联
     *
     * @param comparedDraftId 客户管理-比稿id
     * @param userIdList 客户管理-比稿策划人id
     * @return 是否成功
     */
    boolean insertComparedDraftUser(Long comparedDraftId,List<Long> userIdList);

    /**
     * 批量修改客户管理-比稿策划人关联
     *
     * @param comparedDraftId 客户管理-比稿id
     * @param userIdList 客户管理-比稿策划人id
     * @return 是否成功
     */
    boolean updateComparedDraftUserByIds(Long comparedDraftId,List<Long> userIdList);

    /**
     * 根据策划人id查询比稿项目id
     * @param userIdList 策划人id
     * @param pageNum 页码
     * @param pageSize 页数
     * @return 比稿项目id
     */
    List<Long> listComparedDraftIdByUserIds(List<Long> userIdList,Integer pageNum,Integer pageSize);

    /**
     * 根据比稿项目id查询策划人
     * @param comparedDraftIdList 比稿项目id
     * @return 策划人
     */
    List<ComparedDraftUser> listComparedDraftIds(List<Long> comparedDraftIdList);

    /**
     * 根据比稿项目id查询策划人
     * @param comparedDraftId 比稿项目id
     */
    void removeComparedDraftId(Long comparedDraftId);
}
