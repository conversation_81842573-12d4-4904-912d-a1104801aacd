package com.py.web.controller.resources;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.EnumUtils;
import com.py.project.enums.ExportType;
import com.py.project.pycollaborativeanalysis.domain.query.AnalysisSensationQuery;
import com.py.project.pycollaborativeanalysis.domain.query.AnalysisTotalSumQuery;
import com.py.project.pycollaborativeanalysis.domain.vo.*;
import com.py.project.pycollaborativeanalysis.export.AnalysisSensationRedsExport;
import com.py.project.pycollaborativeanalysis.service.ICollaborativeAnalysisSensation;
import com.py.project.pycollaborativeanalysis.service.impl.CollaborativeAnalysisSensationImpl;
import com.py.resources.enums.ResourceSensationType;
import com.py.resources.mediumresourcesensation.service.IMediumResourceSensationService;
import com.py.resources.mediumresourceservice.service.IMediumResourceServiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 合作分析
 * @date 2023/9/7 18:16
 */
@Api(tags = "媒介合作分析-合作分析")
@RestController
@RequestMapping("/resource/analysis")
public class CollaborativeAnalysisController {

    /**
     * 合作分析-红人服务
     */
    @Resource
    private ICollaborativeAnalysisSensation collaborativeAnalysisSensation;

    /**
     * 可视化异步任务服务
     */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 媒介资源管理-媒介资源红人Service接口 */
    @Resource
    private IMediumResourceSensationService mediumResourceSensationService;

    /** 媒介资源管理-媒介资服务商Service接口 */
    @Resource
    private IMediumResourceServiceService mediumResourceServiceService;

    /**
     * 模块统计
     *
     * @param query 项目管理-收入管理查询参数
     * @return 项目管理-收入管理分页
     */
    @ApiOperation("合作分析-模块统计")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:sum')")
    @GetMapping("/sumTotal")
    public R<AnalysisTotalSumVO> sumTotal(AnalysisTotalSumQuery query) {
        AnalysisTotalSumVO analysisTotalSumVO = this.collaborativeAnalysisSensation.sumTotal(query);
        return R.success(analysisTotalSumVO);
    }

    /**
     * 分页查询项目管理-收入管理列表
     *
     * @param query 项目管理-收入管理查询参数
     * @return 项目管理-收入管理分页
     */
    @ApiOperation("合作分析-红人列表")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:sensation:list')")
    @GetMapping("/collaborativeAnalysisSensation")
    public R<PageInfo<AnalysisSensationVO>> pageCollaborativeAnalysisSensation(AnalysisSensationQuery query) {
        PageInfo<AnalysisSensationVO> voList = this.collaborativeAnalysisSensation.collaborativeAnalysisSensation(query);
        return R.success(voList);
    }

    /**
     * 合作分析-红人明细基本信息
     *
     * @param query 项目管理-收入管理查询参数
     * @return 项目管理-收入管理分页
     */
    @ApiOperation("合作分析-红人明细基本信息")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:sensation:list')")
    @GetMapping("/queryAnalysisSensation")
    public R<AnalysisSensationVO> queryAnalysisSensation(AnalysisSensationQuery query) {
        AnalysisSensationVO voList = this.collaborativeAnalysisSensation.queryAnalysisSensation(query);
        return R.success(voList);
    }

    /**
     * 分页查询项目管理-收入管理列表
     *
     * @param query 查询参数
     * @return 项目管理-收入管理分页
     */
    @ApiOperation("合作分析-红人明细分析")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:sensation:details')")
    @GetMapping("/selectResourceDetails")
    public R<PageInfo<AnalysisResourceDetails>> selectResourceDetails(AnalysisSensationQuery query) {
        PageInfo<AnalysisResourceDetails> voList = this.collaborativeAnalysisSensation.selectResourceDetails(query);
        return R.success(voList);
    }

    /**
     * 导出合作分析红人明细
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出合作分析红人列表")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:sensation:list:download')")
    @Log(title = "合作分析", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCollaborativeSensationList")
    public R<String> exportCollaborativeSensationList(@RequestBody AnalysisSensationQuery query) {
        query.setExportType(ExportType.COLLABORATIVE_ANALYSIS_SENSATION_LIST);
        String platform = EnumUtils.getLabelByValue(query.getType(), ResourceSensationType.class);
        String fileName = String.format("合作分析(红人)-%s-%s.xlsx", platform, DateUtils.getTimeCn());
        query.setFileName(fileName);
        reusableAsyncTaskService.addTask("合作分析（红人）列表下载", TaskType.Export,
                query, AnalysisSensationRedsExport.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 导出合作分析红人明细
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出合作分析红人明细")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:sensation:details:download')")
    @Log(title = "合作分析", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCollaborativeSensation")
    public R<String> exportProjectResource(@RequestBody AnalysisSensationQuery query) {
        //查询资源名
        String resourceName = mediumResourceSensationService.selectMediumResourceSensationInfoById(query.getResourceId());
        query.setExportType(ExportType.COLLABORATIVE_ANALYSIS_SENSATION_DETAILS);
        query.setModelType(0);
        query.setFileName(String.format("合作明细(%s)-%s.xlsx",resourceName, DateUtils.getTimeCn()));
        reusableAsyncTaskService.addTask("合作分析（红人）明细下载", TaskType.Export,
                query, CollaborativeAnalysisSensationImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }


    /**
     * 分页查询服务商列表
     *
     * @param query 项目管理-收入管理查询参数
     * @return 项目管理-收入管理分页
     */
    @ApiOperation("合作分析-服务商列表")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:service:list')")
    @GetMapping("/collaborativeAnalysisService")
    public R<PageInfo<AnalysisServiceVO>> pageCollaborativeAnalysisService(AnalysisSensationQuery query) {
        PageInfo<AnalysisServiceVO> voList = this.collaborativeAnalysisSensation.selectAnalysisService(query);
        return R.success(voList);
    }

    /**
     * 服务商基本信息详情
     *
     * @param query 项目管理-收入管理查询参数
     * @return 项目管理-收入管理分页
     */
    @ApiOperation("合作分析-服务商基本信息详情")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:service:list')")
    @GetMapping("/queryAnalysisService")
    public R<AnalysisServiceVO> queryAnalysisService(AnalysisSensationQuery query) {
        AnalysisServiceVO analysisServiceVO = this.collaborativeAnalysisSensation.queryAnalysisService(query);
        return R.success(analysisServiceVO);
    }

    /**
     * 分页查询项目管理-收入管理列表
     *
     * @param query 项目管理-收入管理查询参数
     * @return 项目管理-收入管理分页
     */
    @ApiOperation("合作分析-服务商明细分析")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:service:details')")
    @GetMapping("/selectResourceDetailsService")
    public R<PageInfo<AnalysisResourceDetails>> selectResourceDetailsService(AnalysisSensationQuery query) {
        PageInfo<AnalysisResourceDetails> voList = this.collaborativeAnalysisSensation.selectResourceDetailsService(query);
        return R.success(voList);
    }

    /**
     * 导出合作分析服务商列表
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出合作分析服务商列表")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:service:list:download')")
    @Log(title = "合作分析", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCollaborativeServiceList")
    public R<String> exportCollaborativeServiceList(@RequestBody AnalysisSensationQuery query) {
        query.setExportType(ExportType.COLLABORATIVE_ANALYSIS_SERVICE_LIST);
        reusableAsyncTaskService.addTask("合作分析（服务商）列表下载", TaskType.Export,
                query, CollaborativeAnalysisSensationImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 导出合作分析服务商明细
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出合作分析服务商明细")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:sensation:details:download')")
    @Log(title = "合作分析", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCollaborativeService")
    public R<String> exportCollaborativeService(@RequestBody AnalysisSensationQuery query) {
        //服务商名
        String resourceName = mediumResourceServiceService.selectMediumResourceServiceInfoById(query.getResourceId());
        query.setExportType(ExportType.COLLABORATIVE_ANALYSIS_SENSATION_DETAILS);
        query.setFileName(String.format("合作明细(%s)-%s.xlsx",resourceName, DateUtils.getTimeCn()));
        query.setModelType(1);
        reusableAsyncTaskService.addTask("合作分析(服务商)明细下载", TaskType.Export,
                query, CollaborativeAnalysisSensationImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 分页查询合作分析-MCN列表
     *
     * @param query 项目管理-收入管理查询参数
     * @return 项目管理-收入管理分页
     */
    @ApiOperation("合作分析-MCN列表")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:mcn:list')")
    @GetMapping("/selectResourceListMcn")
    public R<PageInfo<AnalysisMcnVO>> pageCollaborativeAnalysisMcn(AnalysisSensationQuery query) {
        PageInfo<AnalysisMcnVO> voList = this.collaborativeAnalysisSensation.selectAnalysisMcn(query);
        return R.success(voList);
    }

    /**
     * 合作分析-MCN明细基本信息
     *
     * @param query 项目管理-收入管理查询参数
     * @return 项目管理-收入管理分页
     */
    @ApiOperation("合作分析-MCN明细基本信息")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:mcn:list')")
    @GetMapping("/queryAnalysisMcn")
    public R<AnalysisMcnVO> queryAnalysisMcn(AnalysisSensationQuery query) {
        AnalysisMcnVO voList = this.collaborativeAnalysisSensation.queryAnalysisMcn(query);
        return R.success(voList);
    }

    /**
     * 根据机构id查询合作分析明细（MCN）
     *
     * @param query 查询参数
     * @return 机构项目资源明细集合
     */
    @ApiOperation("合作分析-MCN明细")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:mcn:list')")
    @GetMapping("/selectResourceDetailsMcn")
    public R<PageInfo<AnalysisResourceDetails>> pageResourceDetailsMcn(AnalysisSensationQuery query) {
        PageInfo<AnalysisResourceDetails> voList = this.collaborativeAnalysisSensation.selectResourceDetailsMcn(query);
        return R.success(voList);
    }


    /**
     * 导出MCN列表
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出MCN列表")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:mcn:list:download')")
    @Log(title = "合作分析", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCollaborativeMcn")
    public R<String> exportCollaborativeMcn(@RequestBody AnalysisSensationQuery query) {
        query.setExportType(ExportType.COLLABORATIVE_ANALYSIS_MCN_List);
        String fileName = String.format("合作分析(MCN)-%s.xlsx", DateUtils.getTimeCn());
        query.setFileName(fileName);
        reusableAsyncTaskService.addTask("合作分析(MCN)列表下载", TaskType.Export,
                query, CollaborativeAnalysisSensationImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 导出MCN明细
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出MCN明细")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:mcn:details:download')")
    @Log(title = "合作分析", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCollaborativeDetailsMcn")
    public R<String> exportCollaborativeDetailsMcn(@RequestBody AnalysisSensationQuery query) {
        query.setExportType(ExportType.COLLABORATIVE_ANALYSIS_MCN_DETAILS);
        String institutionName = mediumResourceSensationService.listInstitutionNameByInstitutionId(query.getInstitutionId());
        String fileName = String.format("合作明细(" + institutionName + ")-%s.xlsx", DateUtils.getTimeCn());
        query.setFileName(fileName);
        reusableAsyncTaskService.addTask("合作分析（MCN）明细下载", TaskType.Export,
                query, CollaborativeAnalysisSensationImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 查询派芽星球列表明细
     * @param query 查询参数
     * @return 数据集合
     */
    @ApiOperation("合作分析-派芽星球")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:star:list')")
    @GetMapping("/pagePaiYaStarDetail")
    public R<PageInfo<AnalysisResourceDetails>> pagePaiYaStarDetail(AnalysisSensationQuery query) {
        PageInfo<AnalysisResourceDetails> voList = this.collaborativeAnalysisSensation.pagePaiYaStarResource(query);
        return R.success(voList);
    }

    /**
     * 查询派芽星球列表明细-合作部门
     * @param query 查询参数(合作部门)
     * @return 合作部门
     */
    @ApiOperation("合作分析-派芽星球-列表合作部门")
    @PostMapping("/findPaiYaStarDetailDept")
    public R<List<String>> findPaiYaStarDetailDept(@RequestBody AnalysisSensationQuery query) {
        List<String> voList = this.collaborativeAnalysisSensation.findPaiYaStarDetailDept(query);
        return R.success(voList);
    }

    /**
     * 导出派芽星球明细
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出派芽星球明细")
    @PreAuthorize("@ss.hasPermi('com.py.project:collaborative:star:details:download')")
    @Log(title = "合作分析", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCollaborativeDetailsPaiYaStar")
    public R<String> exportCollaborativeDetailsPaiYaStar(@RequestBody AnalysisSensationQuery query) {
        query.setExportType(ExportType.COLLABORATIVE_ANALYSIS_PAI_YA_STAR_DETAILS);
        reusableAsyncTaskService.addTask("合作分析（派芽星球）明细下载", TaskType.Export,
                query, CollaborativeAnalysisSensationImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }
}
