package com.py.crm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户统计
 * <AUTHOR>
 * @version CustomerCountVO 2023/8/4 10:02
 */
@Data
public class CustomerCountVO {
    /** 合作中 */
    @ApiModelProperty("合作中")
    private Integer cooperation;
    /** 意向合作 */
    @ApiModelProperty("意向合作")
    private Integer intentionCooperate;
    /** 暂停合作 */
    @ApiModelProperty("暂停合作")
    private Integer suspensionCooperation;
    /** 总数 */
    @ApiModelProperty("总数")
    private Integer total;
}
