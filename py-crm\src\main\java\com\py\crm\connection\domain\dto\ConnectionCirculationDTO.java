package com.py.crm.connection.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Set;

/**
 * 人脉管理流转数据传输模型
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
@ApiModel("人脉管理流转数据传输模型" )
public class ConnectionCirculationDTO {
    private static final long serialVersionUID = 1L;

    /** 人脉Id */
    @ApiModelProperty("人脉Id" )
    @NotNull(message = "人脉Id不能为空")
    private Long connectionId;

    /** 目标服务人员id */
    @NotEmpty(message = "目标服务人员不能为空")
    @ApiModelProperty("目标服务人员id" )
    private Set<Long> serviceUserIdList;

    /** 目标服务人员id(废弃) */
    private Long serviceUserId;

    /** 备注 */
    private String remark;
}
