package com.py.web.controller.resources;


import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.resources.mediumlaundrylist.domain.dto.LaundryListDTO;
import com.py.resources.mediumlaundrylist.domain.dto.LaundryListRemoveDTO;
import com.py.resources.mediumlaundrylist.domain.dto.MediumLaundryListDTO;
import com.py.resources.mediumlaundrylist.domain.dto.MediumLaundryListExportModel;
import com.py.resources.mediumlaundrylist.domain.query.MediumLaundryListQuery;
import com.py.resources.mediumlaundrylist.domain.vo.MediumLaundryListListVO;
import com.py.resources.mediumlaundrylist.domain.vo.MediumLaundryListVO;
import com.py.resources.mediumlaundrylist.domain.vo.MediumResourceListVO;
import com.py.resources.mediumlaundrylist.service.IMediumLaundryListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 媒介资源-资源清单Controller
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Api(tags = "媒介资源-资源清单")
@RestController
@RequestMapping("/resources/mediumLaundryList")
public class MediumLaundryListController extends BaseController {

    /** 媒介资源-资源清单服务 */
    @Resource
    private IMediumLaundryListService mediumLaundryListService;

    /**
     * 查询媒介资源-资源清单列表
     *
     * @param query 媒介资源-资源清单查询参数
     * @return 媒介资源-资源清单列表
     */
    @ApiOperation("查询媒介资源-资源清单列表")
    @PreAuthorize("@ss.hasPermi('resources:mediumLaundryList:list')")
    @GetMapping("/listMediumLaundryList")
    public R<List<MediumLaundryListListVO>> listMediumLaundryList(MediumLaundryListQuery query) {
        List<MediumLaundryListListVO> voList = this.mediumLaundryListService.listMediumLaundryList(query);
        return R.success(voList);
    }

    /**
     * 分页查询媒介资源-资源清单列表
     *
     * @param query 媒介资源-资源清单查询参数
     * @return 媒介资源-资源清单分页
     */
    @ApiOperation("分页查询询媒介资源-资源清单列表")
    @PreAuthorize("@ss.hasPermi('resources:mediumLaundryList:list')")
    @GetMapping("/pageMediumLaundryList")
    public R<PageInfo<MediumResourceListVO>> pageMediumLaundryList(MediumLaundryListQuery query) {
        PageInfo<MediumResourceListVO> voList = this.mediumLaundryListService.pageMediumLaundryListList(query);
        return R.success(voList);
    }

    /**
     * 获取媒介资源-资源清单详细信息
     * @param id 媒介资源-资源清单主键
     * @return 媒介资源-资源清单视图模型
     */
    @ApiOperation("获取媒介资源-资源清单详细信息")
    @PreAuthorize("@ss.hasPermi('resources:mediumLaundryList:query')")
    @GetMapping(value = "/{id}")
    public R<MediumLaundryListVO> getInfo(@PathVariable("id") Long id) {
        return R.success(mediumLaundryListService.selectMediumLaundryListById(id));
    }

    /**
     * 加入清单
     *
     * @param dto 媒介资源-资源清单修改参数
     * @return 是否成功
     */
    @ApiOperation("加入清单")
    @PreAuthorize("@ss.hasPermi('resources:mediumLaundryList:add')")
    @Log(title = "加入清单", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody LaundryListDTO dto) {
        return R.success(mediumLaundryListService.insertMediumLaundryList(dto));
    }


    /**
     * 取消加入
     * @param dto 资源清单删除实体
     * @return 是否成功
     */
    @ApiOperation("取消加入" )
    @PreAuthorize("@ss.hasPermi('resources:mediumLaundryList:remove')")
    @Log(title = "取消加入", businessType = BusinessType.DELETE)
    @PostMapping("/remove" )
    public R<Boolean> remove(@RequestBody LaundryListRemoveDTO dto) {
        return R.success(mediumLaundryListService.deleteMediumLaundryListByIds(dto));
    }

    /**
     * 导出媒介资源-资源清单
     * @param response 请求响应
     * @param query 导出查询参数
     */
    @ApiOperation("导出媒介资源-资源清单")
    @PreAuthorize("@ss.hasPermi('resources:mediumLaundryList:export')")
    @Log(title = "媒介资源-资源清单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MediumLaundryListQuery query) {
        List<MediumLaundryListExportModel> exportList = this.mediumLaundryListService.exportMediumLaundryList(query);

        ExcelUtil<MediumLaundryListExportModel> util = new ExcelUtil<>(MediumLaundryListExportModel. class);
        util.exportExcel(response, exportList, "媒介资源-资源清单数据" );
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumLaundryList:import')" )
    @Log(title = "媒介资源-资源清单" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<MediumLaundryListExportModel> util = new ExcelUtil<>(MediumLaundryListExportModel.class);
        List<MediumLaundryListExportModel> mediumLaundryListList = util.importExcel(file.getInputStream());
        String message = this.mediumLaundryListService.importMediumLaundryList(mediumLaundryListList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumLaundryList:import')" )
    @Log(title = "媒介资源-资源清单" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<MediumLaundryListExportModel> util = new ExcelUtil<>(MediumLaundryListExportModel.class);
        util.importTemplateExcel(response, "媒介资源-资源清单数据" );
    }

    /**
     * 获取选中的数量
     * @param dto 菜单类型、类型
     * @return 媒介资源-资源清单视图模型
     */
    @ApiOperation("获取选中的数量")
    @PreAuthorize("@ss.hasPermi('resources:mediumLaundryList:query')")
    @GetMapping(value = "/querySelectCount")
    public R<Integer> querySelectCount(MediumLaundryListDTO dto) {
        List<Long> resourceIdList = mediumLaundryListService.queryByResourceIdList(dto.getMenuType(), dto.getType(), dto.getStatus(),dto.getResourceType());
        return R.success(resourceIdList.size());
    }

    /**
     * 确认选择删除
     * @param dto 资源清单删除实体
     * @return 是否成功
     */
    @ApiOperation("确认选择删除" )
    @PreAuthorize("@ss.hasPermi('resources:mediumLaundryList:remove')")
    @Log(title = "确认选择删除", businessType = BusinessType.DELETE)
    @PostMapping("/confirmSelectRemove" )
    public R<Boolean> confirmSelectRemove(@RequestBody LaundryListRemoveDTO dto) {
        return R.success(mediumLaundryListService.confirmSelectRemove(dto));
    }

    /**
     * 移除清单
     * @param dto 资源清单删除实体
     * @return 是否成功
     */
    @ApiOperation("移除清单" )
    @PreAuthorize("@ss.hasPermi('resources:mediumLaundryList:remove')")
    @Log(title = "移出清单", businessType = BusinessType.DELETE)
    @PostMapping("/deleteLaundryList" )
    public R<Boolean> deleteLaundryList(@RequestBody LaundryListRemoveDTO dto) {
        return R.success(mediumLaundryListService.deleteLaundryListInfo(dto));
    }
}
