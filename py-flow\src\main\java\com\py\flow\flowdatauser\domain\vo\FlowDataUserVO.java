package com.py.flow.flowdatauser.domain.vo;

import com.py.common.core.domain.vo.IUpdateInfoVO;
import com.py.common.datascope.DataScopePageType;
import com.py.flow.domain.enums.ApprovalBizType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import java.time.LocalDate;

/**
 * 审批数据与人员关联视图模型
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@Data
@ApiModel("审批数据与人员关联视图模型" )
public class FlowDataUserVO implements IUpdateInfoVO {
    private static final long serialVersionUID = 1L;

    /** 自增主键*/
    @ApiModelProperty("自增主键")
    private Long id;

    /** 业务id*/
    @ApiModelProperty("业务id")
    private Long bizId;

    /** 业务类型*/
    @ApiModelProperty("业务类型")
    private ApprovalBizType bizType;

    /** 用户id*/
    @ApiModelProperty("用户id")
    private Long userId;

    /** 用户名*/
    @ApiModelProperty("用户名")
    private String userName;

    /** 更新人部门*/
    @ApiModelProperty("更新人部门")
    private String updateDept;

    /**
     * 获取更新者ID
     * @return 更新者ID
     */
    @Override
    public Long getUpdateId() {
        return userId;
    }

    /**
     * 设置更新者名字
     * @param updateBy 更新者名字
     */
    @Override
    public void setUpdateBy(String updateBy) {
        this.userName = updateBy;
    }
}
