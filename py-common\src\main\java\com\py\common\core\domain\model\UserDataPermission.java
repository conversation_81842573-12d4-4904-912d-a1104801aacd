package com.py.common.core.domain.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.py.common.datascope.DataScopePageType;
import com.py.common.datascope.DataScopeType;
import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.collection.ListUtil;
import lombok.Getter;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.util.*;

/**
 * 用户数据权限
 * <AUTHOR>
 */
public class UserDataPermission {

    /** 用户页面数据权限Map */
    @Getter
    private final Map<DataScopePageType, Set<DataScopeType>> pageDataScopeMap;

    /** 用户页面主体权限Map */
    @Getter
    private final Map<DataScopePageType, List<Long>> pageMainstayScopeMap;

    /** 用户页面日期权限Map */
    @Getter
    private final Map<DataScopePageType, LocalDate> pageDateTimeScopeMap;

    /**
     * 构造用户数据权限
     * @param dataPermissionList 用户数据权限列表
     */
    public UserDataPermission(List<DataPermission> dataPermissionList) {
        this.pageDataScopeMap = new HashMap<>();
        this.pageMainstayScopeMap = new HashMap<>();
        this.pageDateTimeScopeMap = new HashMap<>();
        for(DataPermission dataPermission : dataPermissionList) {
            DataScopeType dataScopeType = dataPermission.getPermissionType();
            for(DataScopePageType dataScopePageType : dataPermission.getPageIdList()) {
                // 设置页面数据权限
                this.setPageDataScope(dataScopeType, dataScopePageType);
                // 设置页面主体权限
                this.setPageMainstayScope(dataPermission, dataScopePageType);
                // 设置页面时间权限
                this.setPageDateTimeScope(dataPermission, dataScopePageType);
            }
        }
    }

    /** 序列化构造函数 */
    @JsonCreator
    public UserDataPermission(
            @JsonProperty("pageDataScopeMap") Map<DataScopePageType, Set<DataScopeType>> pageDataScopeMap,
            @JsonProperty("pageMainstayScopeMap") Map<DataScopePageType, List<Long>> pageMainstayScopeMap,
            @JsonProperty("pageDateTimeScopeMap") Map<DataScopePageType, LocalDate> pageDateTimeScopeMap) {
        this.pageDataScopeMap = NullMergeUtils.nullMerge(pageDataScopeMap, ListUtil.emptyMap());
        this.pageMainstayScopeMap = NullMergeUtils.nullMerge(pageMainstayScopeMap, ListUtil.emptyMap());
        this.pageDateTimeScopeMap = NullMergeUtils.nullMerge(pageDateTimeScopeMap, ListUtil.emptyMap());
    }

    /**
     * 设置业务数据权限
     * @param dataScopeType 数据权限类型
     * @param dataScopePageType 数据权限页面类型
     */
    private void setPageDataScope(DataScopeType dataScopeType, DataScopePageType dataScopePageType) {
        if(this.pageDataScopeMap.containsKey(dataScopePageType) == false) {
            this.pageDataScopeMap.put(dataScopePageType, EnumSet.of(dataScopeType));
        }else {
            // 存在重复的页面类型，合并数据权限
            Set<DataScopeType> alreadyDataScopeTypeSet = this.pageDataScopeMap.get(dataScopePageType);
            this.pageDataScopeMap.put(dataScopePageType, this.mergeDataScope(alreadyDataScopeTypeSet, dataScopeType));
        }
    }

    /**
     * 设置页面主体权限
     * @param dataPermission 数据权限
     * @param dataScopePageType 数据权限页面类型
     */
    private void setPageMainstayScope(DataPermission dataPermission, DataScopePageType dataScopePageType) {
        if(dataScopePageType.getHasMainstay() == false) {
            return;
        }
        if(this.pageMainstayScopeMap.containsKey(dataScopePageType) == false){
            this.pageMainstayScopeMap.put(dataScopePageType, dataPermission.getMainstayIdList());
        }else{
            // 存在重复的页面类型，合并主体权限
            List<Long> mainstayIdList = ListUtil.mergeAndDistinct(this.pageMainstayScopeMap.get(dataScopePageType), dataPermission.getMainstayIdList());
            this.pageMainstayScopeMap.put(dataScopePageType, mainstayIdList);
        }
    }

    /**
     * 设置页面时间权限
     * @param dataPermission 数据权限
     * @param dataScopePageType 数据权限页面类型
     */
    private void setPageDateTimeScope(DataPermission dataPermission, DataScopePageType dataScopePageType) {
        if(dataPermission.getStartDate() == null){
            this.pageDateTimeScopeMap.put(dataScopePageType, LocalDate.MIN);
            return;
        }

        LocalDate currentStartDate = this.pageDateTimeScopeMap.get(dataScopePageType);
        if(currentStartDate == null || currentStartDate.isAfter(dataPermission.getStartDate())){
            this.pageDateTimeScopeMap.put(dataScopePageType, dataPermission.getStartDate());
        }
    }

    /**
     * 合并数据权限
     * @param alreadyDataScopeTypeSet 已有的数据权限集合
     * @param dataScopeType 新的数据权限
     * @return 合并后的数据权限集合
     */
    private Set<DataScopeType> mergeDataScope(Set<DataScopeType> alreadyDataScopeTypeSet, DataScopeType dataScopeType) {
        if(alreadyDataScopeTypeSet.contains(dataScopeType)) {
            return alreadyDataScopeTypeSet;
        }else if(alreadyDataScopeTypeSet.contains(DataScopeType.ALL) || dataScopeType == DataScopeType.ALL) {
            return ListUtil.singletonSet(DataScopeType.ALL);
        }else {
            alreadyDataScopeTypeSet.add(dataScopeType);

            // 去除有包含关系的重复数据权限
            if(alreadyDataScopeTypeSet.containsAll(DataScopeType.MemberScopeSet)){
                alreadyDataScopeTypeSet.remove(DataScopeType.SELF);
            }
            if(alreadyDataScopeTypeSet.containsAll(DataScopeType.DeptScopeSet)){
                alreadyDataScopeTypeSet.remove(DataScopeType.DEPT);
            }
        }
        return alreadyDataScopeTypeSet;
    }

    /**
     * 获取页面数据权限类型
     * @param pageType 页面类型
     * @return 数据权限类型
     */
    public Set<DataScopeType> getPageDataScope(DataScopePageType pageType){
        Assert.notNull(pageType, "页面类型不能为null");
        return NullMergeUtils.nullMerge(this.pageDataScopeMap.get(pageType), ListUtil.emptySet());
    }

    /**
     * 获取页面主体权限
     * @param pageType 页面类型
     * @return 主体ID列表
     */
    public List<Long> getPageMainstayScope(DataScopePageType pageType){
        Assert.notNull(pageType, "页面类型不能为null");
        Assert.isTrue(pageType.getHasMainstay(), "页面类型必须有主体权限");
        return NullMergeUtils.nullMerge(this.pageMainstayScopeMap.get(pageType), ListUtil.emptyList());
    }

    /**
     * 获取页面时间权限
     * @param pageType 页面类型
     * @return 权限开始时间
     */
    public LocalDate getPageDateTimeScope(DataScopePageType pageType) {
        Assert.notNull(pageType, "页面类型不能为null");
        return this.pageDateTimeScopeMap.get(pageType);
    }
}
