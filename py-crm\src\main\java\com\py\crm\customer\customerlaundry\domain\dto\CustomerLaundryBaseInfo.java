package com.py.crm.customer.customerlaundry.domain.dto;

import java.util.List;

/**
 * 行业类目和服务人员的基类
 * <AUTHOR>
 * @version CustomerLaundryBaseInfo 2023/9/7 15:10
 */
public interface CustomerLaundryBaseInfo {

    /**
     * 行业类目ID
     * @param industryCategoryIdList 行业类目ID
     */
    void setIndustryCategoryIdList(List<Long> industryCategoryIdList);

    /**
     * 服务人员id
     * @param serviceUserIdList 服务人员id
     */
    void setServiceUserIdList(List<Long> serviceUserIdList);

    /**
     * 获取服务人员id列表
     * @return
     */
    List<Long> getServiceUserIdList();

    /**
     * 服务人员
     * @param serviceUserName 服务人员
     */
    void setServiceUserName(String serviceUserName);

    /**
     * 服务人员
     * @param serviceUserNameList 服务人员
     */
    void setServiceUserNameList(List<String> serviceUserNameList);

    /**
     * 客户ID
     * @return 客户ID
     */
    Long getCustomerId();

    /**
     * 创建者id
     * @return 创建者id
     */
    Long getCreateId();
}
