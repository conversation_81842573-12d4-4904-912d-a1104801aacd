package com.py.crm.compareddraft.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.core.domain.vo.BaseUpdateInfoVO;
import com.py.common.oss.model.OssObjectInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version ComparedDraftListVO 2023/8/9 18:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ComparedDraftListVO extends BaseUpdateInfoVO {

    /**
     * 自增主键
     */
    @ApiModelProperty("自增主键")
    private Long id;

    /**
     * 比稿id
     */
    @ApiModelProperty("比稿id")
    private Long comparedDraftId;

    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    private Long customerId;

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    private String customerName;

    /**
     * 客户合作主体ID
     */
    @ApiModelProperty("客户合作主体ID")
    private Long customerCooperateId;

    /**
     * 客户合作主体
     */
    @ApiModelProperty("客户合作主体")
    private String customerCooperateName;

    /**
     * 品牌/业务线名
     */
    @ApiModelProperty("品牌/业务线名")
    private String brandName;

    /**
     * 行业类目
     */
    @ApiModelProperty("行业类目（数据字典Id）")
    private List<String> categoryId;

    /**
     * 行业类目名称
     */
    @ApiModelProperty("行业类目名称")
    private List<String> categoryName;

    /**
     * 品牌阶段
     */
    @ApiModelProperty("品牌阶段")
    private Integer brandStage;

    /**
     * 合作部门
     */
    @ApiModelProperty("合作部门")
    private String collaborateDapt;

    /**
     * 比稿项目名称
     */
    @ApiModelProperty("比稿项目名称")
    private String comparedDraftName;

    /**
     * 商务标比稿结果(0失败，1成功)
     */
    @ApiModelProperty("商务标比稿结果(0失败，1成功)")
    private Integer commercialBidFruits;

    /**
     * 技术标比稿结果(0失败，1成功)
     */
    @ApiModelProperty("技术标比稿结果(0失败，1成功)")
    private Integer techniqueBidFruits;

    /**
     * 最终比稿结果(0失败，1成功)
     */
    @ApiModelProperty("最终比稿结果(0失败，1成功)")
    private Integer finalFruits;

    /**
     * 比稿时间
     */
    @ApiModelProperty("比稿时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate comparedDraftTime;

    /**
     * 录入时间
     */
    @ApiModelProperty("录入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enteringTime;

    /**
     * 比稿金额(元)
     */
    @ApiModelProperty("比稿金额(元)")
    private BigDecimal comparedDraftMoney;

    /**
     * 成交金额(元)
     */
    @ApiModelProperty("成交金额(元)")
    private BigDecimal turnoverMoney;

    /**
     * 派芽业务类型
     */
    @ApiModelProperty("派芽业务类型")
    private Integer pyType;

    private String pyTypeStr;

    /**
     * 方案类型
     */
    @ApiModelProperty("方案类型")
    private Integer planType;

    private String planTypeStr;

    /**
     * 比稿方案
     */
    @ApiModelProperty("比稿方案")
    private List<OssObjectInfo> comparedDraftPlan;

    /**
     * 比稿策略复盘
     */
    @ApiModelProperty("比稿策略复盘")
    private String strategyReview;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /** 策划人 */
    @ApiModelProperty("策划人")
    private List<String> plotterNameList;

    /** 策划人id */
    @ApiModelProperty("策划人id")
    private List<Long> plotterIdList;
}
