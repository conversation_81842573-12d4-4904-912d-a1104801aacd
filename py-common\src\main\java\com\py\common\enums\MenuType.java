package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 菜单类型：1项目；2-财务
 * <AUTHOR>
 */
@AllArgsConstructor
public enum MenuType implements IDict<Integer> {

    /** 项目 */
    PROJECT(1, "项目"),

    /** 财务 */
    FINANCE(2, "财务");

    private final Integer value;

    private final String label;


    @Override
    public Integer getValue() {
        return this.value;
    }

    @Override
    public String getLabel() {
        return this.label;
    }

}
