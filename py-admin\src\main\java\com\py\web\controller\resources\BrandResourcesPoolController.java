package com.py.web.controller.resources;

import com.github.pagehelper.PageInfo;
import com.py.common.constant.Constants;
import com.py.common.core.domain.R;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.project.enums.ExportType;
import com.py.project.projectresourcestatics.domain.query.ResourcesPoolQuery;
import com.py.project.projectresourcestatics.domain.vo.ResourcesPoolOverviewVO;
import com.py.project.projectresourcestatics.domain.vo.ResourcesPoolProjectDetailVO;
import com.py.project.projectstatics.domain.vo.ResourcesPoolProjectListVO;
import com.py.project.projectresourcestatics.service.IProjectResourceStaticsService;
import com.py.project.projectresourcestatics.service.impl.ProjectResourceStaticsServiceImpl;
import com.py.project.projectstatics.service.IProjectStaticsService;
import com.py.project.projectstatics.service.impl.ProjectStaticsServiceImpl;
import com.py.system.mainstayparam.domain.vo.MainstayParamListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> @description 品牌资源池
 * @date
 */
@Api(tags = "媒介合作分析-品牌资源池")
@RestController
@RequestMapping("/resources/brandResourcePool")
public class BrandResourcesPoolController {

    /**
     * 可视化异步任务服务
     */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    @Resource
    private IProjectResourceStaticsService projectResourceStaticsService;

    @Resource
    private IProjectStaticsService projectStaticsService;


    @ApiOperation("数据-品牌资源池公司主体")
    @PreAuthorize("@ss.hasPermi('system:systemMainstayParam:list')")
    @GetMapping("/listSystemMainstayParam")
    public R<List<MainstayParamListVO>> listSystemMainstayParam() {
        return R.success(projectResourceStaticsService.listSystemMainstayParam());
    }


    @ApiOperation("品牌资源池-总览")
    @PreAuthorize("@ss.hasPermi('resources:pool:list')")
    @GetMapping("/overview")
    public R<ResourcesPoolOverviewVO> overview(ResourcesPoolQuery query) {
        return R.success(projectResourceStaticsService.overview(query));
    }

    @ApiOperation("品牌资源池-项目列表")
    @PreAuthorize("@ss.hasPermi('resources:pool:list')")
    @GetMapping("/pageProjectList")
    public R<PageInfo<ResourcesPoolProjectListVO>> pageProjectList(ResourcesPoolQuery query) {
        return R.success(projectStaticsService.pageProjectStatics(query));
    }

    @ApiOperation("品牌资源池-资源列表")
    @PreAuthorize("@ss.hasPermi('resources:pool:list')")
    @GetMapping("/pageProjectResourceList")
    public R<PageInfo<ResourcesPoolProjectDetailVO>> pageProjectResourceList(ResourcesPoolQuery query) {
        return R.success(projectResourceStaticsService.pageResourceStatics(query));
    }

    @ApiOperation("品牌资源池-下载总览")
    @PreAuthorize("@ss.hasPermi('resources:pool:list')")
    @PostMapping("/downloadOverview")
    public R<String> downloadOverview(@RequestBody ResourcesPoolQuery query) {
        query.setExportType(ExportType.RESOURCES_POOL_OVERVIEW);
        reusableAsyncTaskService.addTask("品牌资源池-下载总览", TaskType.Export, query, ProjectResourceStaticsServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }


    @ApiOperation("品牌资源池-下载项目")
    @PreAuthorize("@ss.hasPermi('resources:pool:list')")
    @PostMapping("/downloadProject")
    public R<String> downloadProject(@RequestBody ResourcesPoolQuery query) {
        query.setExportType(ExportType.RESOURCES_POOL_PROJECT);
        reusableAsyncTaskService.addTask("品牌资源池-下载项目", TaskType.Export, query, ProjectStaticsServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }


    @ApiOperation("品牌资源池-下载资源")
    @PreAuthorize("@ss.hasPermi('resources:pool:list')")
    @PostMapping("/downloadProjectResource")
    public R<String> downloadProjectResource(@RequestBody ResourcesPoolQuery query) {
        query.setExportType(ExportType.RESOURCES_POOL_PROJECT_RESOURCE);
        reusableAsyncTaskService.addTask("品牌资源池-下载资源", TaskType.Export, query, ProjectResourceStaticsServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

}
