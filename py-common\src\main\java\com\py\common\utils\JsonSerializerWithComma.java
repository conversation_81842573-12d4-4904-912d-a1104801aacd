package com.py.common.utils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/18
 * 向前端返回时将数字从右至左每三位加一逗号
 */
public class JsonSerializerWithComma extends JsonSerializer<Long> {

    @Override
    public void serialize(Long value, JsonGenerator gen,
                          SerializerProvider serializers) throws IOException {
        if (Objects.nonNull(value)) {
            //将数字从右至左每三位加一逗号
            gen.writeString(StringUtil.displayWithComma(value.toString()));
        }
    }

}

