package com.py.crm.compareddraft.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;
import com.py.crm.compareddraft.domain.ComparedDraft;
import com.py.crm.compareddraft.domain.dto.ComparedDraftDTO;
import com.py.crm.compareddraft.domain.dto.ComparedDraftExportModel;
import com.py.crm.compareddraft.domain.query.ComparedDraftQuery;
import com.py.crm.compareddraft.domain.vo.ComparedDraftAmountVO;
import com.py.crm.compareddraft.domain.vo.ComparedDraftListVO;
import com.py.crm.compareddraft.domain.vo.ComparedDraftVO;

import java.util.List;

/**
 * 客户管理-比稿管理服务
 *
 * <AUTHOR>
 * @date 2023/07/17 09:36
 */
public interface IComparedDraftService extends IService<ComparedDraft> {

    /**
    * 分页查询客户管理-比稿管理
    *
    * @param comparedDraftQuery 客户管理-比稿管理查询条件
    * @return 客户管理-比稿管理查询内容
    */
    PageInfo<ComparedDraftListVO> listComparedDraft(ComparedDraftQuery comparedDraftQuery);

    /**
     * 查询客户管理-比稿管理
     *
     * @param id 客户管理-比稿管理查询条件
     * @return 客户管理-比稿管理查询内容
     */
    ComparedDraftVO queryComparedDraft(Long id);

    /**
     * 查询客户管理-比稿管理详细信息
     *
     * @param id 客户管理-比稿管理查询条件
     * @param customerCooperateId 客户合作主题
     * @param projectId 项目ID
     * @return 客户管理-比稿管理查询内容
     */
    PageInfo<ComparedDraftVO> queryComparedDraftDetailById(Long id, Long customerCooperateId, Long projectId);

    /**
     * 添加客户管理-比稿管理
     *
     * @param draftDTO 客户管理-比稿管理Param
     * @return 是否成功
     */
    Boolean addComparedDraft(ComparedDraftDTO draftDTO);

    /**
     * 修改客户管理-比稿管理
     *
     * @param draftDTO 客户管理-比稿管理Param
     * @return 是否成功
     */
    Boolean updateComparedDraft(ComparedDraftDTO draftDTO);

    /**
     * 删除客户管理-比稿管理
     *
     * @param draftDTO 要删除的客户管理-比稿管理Id
     * @return 是否成功
     */
    Boolean deleteComparedDraft(ComparedDraftDTO draftDTO);

    /**
     * 客户管理-比稿管理合计
     *
     * @param query 客户管理-比稿管理查询内容
     * @return 合计参数
     */
    ComparedDraftAmountVO amount(ComparedDraftQuery query);

    /**
     * 下载比稿
     * @param query 查询条件
     */
    void download(ComparedDraftQuery query);

    /**
     * 导出模版
     * @param exportModelList 导出信息
     * @return 模版
     */
    ExcelSheetConfig<ComparedDraftExportModel> getComparedDraftExportConfig(List<ComparedDraftExportModel> exportModelList);

    /**
     * 查询客户管理-比稿管理详细信息
     *
     * @param idList 客户id
     * @param customerCooperateId 客户合作主题
     * @return 客户管理-比稿管理查询内容
     */
    List<ComparedDraftVO> queryComparedDraftDetailByIds(List<Long> idList, Long customerCooperateId);

    /**
     * 获取所有的客户有比稿明细的客户id列表
     * @param customerIds
     * @return
     */
    List<Long> listExistedCustomerIds(List<Long> customerIds);


    /**
     * 根据客户id查询比搞数据
     * @param customerId 客户id
     * @return 比搞详情
     */
    List<ComparedDraft> queryComparedDraftByCustomerId(Long customerId);


    /**
     * 批量更新客户的比稿信息
     * @param comparedDrafts 客户比稿详情集合
     * @return 批量更新结果
     */
   Boolean updateComparedDraftByCustomerId( List<ComparedDraft> comparedDrafts);
}
