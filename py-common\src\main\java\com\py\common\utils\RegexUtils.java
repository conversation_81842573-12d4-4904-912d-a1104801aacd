package com.py.common.utils;

import lombok.NonNull;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 正则表达式共通
 * <AUTHOR> pyr
 **/
@SuppressWarnings("AlibabaConstantFieldShouldBeUpperCase")
public class RegexUtils {
    /**输入金额正则表达式*/
    public static final String CONTRACT_AMOUNT="(^-?(\\d)+(\\.(\\d+))?$)|(^$)";

    /** 手机号正则表达式 */
    public static final String PHONE = "^1[3456789]\\d{9}$";

    /** 手机号正则表达式 */
    public static final Pattern PHONE_REGEX = Pattern.compile(PHONE);

    /** 邮箱正则表达式 */
    public static final String EMAIL = "^([a-zA-Z0-9_-]+@)([a-zA-Z0-9_-]+\\.)([a-zA-Z0-9_-]+)+$";

    /** 邮箱正则表达式 */
    public static final Pattern EMAIL_REGEX = Pattern.compile(EMAIL);

    /** 身份证正则表达式 */
    public static final String IDENTITY_REGEX = "^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";

    /** 密码验证正则表达式 */
    @SuppressWarnings("HardcodedCredentials")
    public static final String PASSWORD_VERIFY = "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,20}$";

    /**
     * 正则表达式文本回调替换
     * <p>实现正则表达式可变长度回调替换</p>
     * @param source 需要被正则表达式替换的文本
     * @param regex 正则表达式
     * @param replaceCallback 替换回调(匹配结果:Matcher, 替换值:String)
     * @return 替换后的文本
     * @throws IllegalArgumentException 替换回调不能为null
     */
    public static String replace(String source, @NonNull Pattern regex, @NonNull Function<Matcher, String> replaceCallback) {
        if(StringUtils.isBlank(source)) {
            return source;
        }

        StringBuilder temp = new StringBuilder(source);
        Matcher matcher = regex.matcher(temp);

        int matcherPointer = 0;
        while(matcher.find(matcherPointer)) {
            matcherPointer = matcher.end();

            String replaceStr = replaceCallback.apply(matcher);
            if(replaceStr == null) {
                throw new IllegalArgumentException("替换回调不能为null");
            }
            if(StringUtils.isBlank(replaceStr)) {
                continue;
            }

            temp.replace(matcher.start(), matcher.end(), replaceStr);
            // 修正替换后匹配开始的位置
            matcherPointer += replaceStr.length() - matcher.group().length();
        }
        return temp.toString();
    }

    /**
     * 生成集合匹配正则表达式
     * <p>用于列表项多值匹配已 ',' 分隔的集合</p>
     * @param valueList 匹配值列表
     * @param <TValue> 值类型
     * @return 匹配正则表达式
     */
    public static <TValue> String generateSetInRegex(List<TValue> valueList) {
        String regex = valueList.stream()
                .map(Objects::toString)
                .collect(Collectors.joining("|"));
        return String.format("(^|,)(%s)(,|$)", regex);
    }
}
