package com.py.common.oss.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * Oss下载请求
 * <AUTHOR>
 */
@Data
@ApiModel("Oss下载请求")
public class OssDownloadQuery {

    /** 文件名 */
    @ApiModelProperty("文件名")
    @NotBlank(message = "文件名不能为空")
    private String uploadFileName;

    /** 对象存储地址 */
    @ApiModelProperty("对象存储地址")
    @NotBlank(message = "对象存储地址不能为空")
    private String ossKey;
}
