package com.py.web.controller.system;

import com.py.common.core.domain.R;
import com.py.common.core.domain.model.LoginUser;
import com.py.common.utils.SecurityUtils;
import com.py.system.user.button.domain.dto.SysUserApprovalButtonDTO;
import com.py.system.user.button.domain.vo.SysUserApprovalButtonVO;
import com.py.system.user.button.service.ISysUserApprovalButtonService;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 首页-个人设置.自动打开下一条审批按钮控制层
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user/approval/button")
@ApiModel("首页-个人设置")
public class SysUserButtonController {

    /** 用户自动打开下一条审批按钮服务层  */
    @Resource
    private ISysUserApprovalButtonService sysUserApprovalButtonService;



    /**
     * 编辑本人下一条审批按钮
     * @param dto 修改参数
     * @return 修改结果
     */
    @ApiOperation("编辑本人审批按钮")
    @PostMapping("/editButtonStatus")
    public R<Void> editButtonStatus(@Validated @RequestBody SysUserApprovalButtonDTO dto ){
        sysUserApprovalButtonService.editUserApprovalButton(dto);
        return  R.success();
    }



    /**
     * 获取当前登录人按钮状态
     * @return 个人按钮状态
     */
    @ApiOperation("获取当前登录人按钮状态")
    @GetMapping("/getUserStatus")
    public R<SysUserApprovalButtonVO> getUserStatus(){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Assert.notNull(loginUser, "获取用户信息失败, 请重新登录");
        return R.success(sysUserApprovalButtonService.getUserNextApprovalButtonStatus(loginUser.getUserId()));
    }


}
