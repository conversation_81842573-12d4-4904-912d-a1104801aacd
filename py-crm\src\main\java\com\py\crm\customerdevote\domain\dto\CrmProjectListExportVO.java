package com.py.crm.customerdevote.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.py.common.tools.poiexcel.annotation.Excel;
import com.py.common.utils.DateUtils;
import com.py.common.utils.StringUtils;
import com.py.framework.config.jackson.serializer.MoneyJsonSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 项目管理-项目表列表视图模型
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
@Data
@ApiModel("客户贡献管理-项目表列表视图模型")
public class CrmProjectListExportVO {
    private static final long serialVersionUID = 1L;

    /** 项目名称 */
    @ApiModelProperty("项目名称")
    @Excel(name = "项目名称")
    private String projectName;

    /** 项目状态 */
    @Excel(name = "项目状态",readConverterExp = "0=项目中,1=已完结")
    @ApiModelProperty("项目状态")
    private Integer projectStatus;

    /** 立项项目金额 */
    @ApiModelProperty("立项项目金额(元)")
    @JsonSerialize(using = MoneyJsonSerializer.class)
    private BigDecimal projectAmount;
    @Excel(name = "立项项目金额(元)")
    private String projectAmountStr;


    /** 立项毛利率预估 */
    @ApiModelProperty("立项毛利率预估")
    @JsonSerialize(using = MoneyJsonSerializer.class)
    private BigDecimal grossMargin;
    @Excel(name = "立项毛利率预估(%)")
    private String grossMarginStr;

    /** 已完成项目收入 */
    @ApiModelProperty("已完成项目收入(元)")
    @JsonSerialize(using = MoneyJsonSerializer.class)
    private BigDecimal finishProjectIncome;
    @Excel(name = "已完成项目收入(元)")
    private String finishProjectIncomeStr;

    /** 已完成项目成本 */
    @ApiModelProperty("已完成项目成本(元)")
    @JsonSerialize(using = MoneyJsonSerializer.class)
    private BigDecimal finishProjectCost;
    @Excel(name = "已完成项目成本(元)")
    private String finishProjectCostStr;

    /** 已完成项目利润 */
    @ApiModelProperty("已完成项目利润(元)")
    @JsonSerialize(using = MoneyJsonSerializer.class)
    private BigDecimal finishProjectProfit;
    @Excel(name = "已完成项目利润(元)")
    private String finishProjectProfitStr;

    /** 已完成项目毛利率 */
    @ApiModelProperty("已完成项目毛利率(%)")
    private BigDecimal finishProjectGrossMargin;
    @Excel(name = "已完成项目毛利率(%)")
    private String finishProjectGrossMarginStr;

    /** 客户名称 */
    @Excel(name = "客户名称")
    @ApiModelProperty("客户名称")
    private String customerName;


    /** 客户id */
    @ApiModelProperty("客户id")
    private Long customerId;


    /** 客户合作主体名称 */
    @Excel(name = "客户合作主体")
    @ApiModelProperty("客户合作主体名称")
    private String customerMainstayName;

    /** 品牌/业务线 */
    @Excel(name = "品牌/业务线")
    @ApiModelProperty("品牌/业务线")
    private String lineBusiness;

    /** 派芽合作主体名称 */
    @Excel(name = "派芽合作主体")
    @ApiModelProperty("派芽合作主体名称")
    private String mainstayName;

    /** 创建者 */
    @ApiModelProperty("立项人")
    @Excel(name = "立项人")
    private String createBy;

    /** 创建部门 */
    @ApiModelProperty("立项人部门")
    @Excel(name = "立项人部门")
    private String createDept;

    /** 立项时间 */
    @ApiModelProperty("立项时间")
    @Excel(name = "立项时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDate projectApprovalTime;
    private String projectApprovalTimeStr;

    /** 项目开始时间 */
    @ApiModelProperty("项目开始时间")
    @Excel(name = "项目开始时间",dateFormat = "yyyy-MM-dd")
    private LocalDate projectStartTime;
    private String projectStartTimeStr;
    /** 预计结束时间 */
    @Excel(name = "预计结束时间",dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("预计结束时间")
    private LocalDate expectedEndTime;
    private String expectedEndTimeStr;
    /** 结案时间 */
    @Excel(name = "结案时间",dateFormat = "yyyy-MM-dd")
    @ApiModelProperty("结案时间")
    private LocalDate closeCaseTime;
    private String closeCaseTimeStr;
    /** 自增id */
    @ApiModelProperty("自增id")
    private Long id;

    /** 项目id */
    @ApiModelProperty("项目id")
    private Long projectId;


    public String getProjectStatusStr() {
        if(projectStatus == null){
            return null;
        }
        if(projectStatus == 0){
            return "项目中";
        }
        if(projectStatus == 1){
            return "已完结";
        }
        return null;
    }
    /** 立项项目金额 */
    public String getProjectAmountStr() {
        if(projectAmount == null){
            return null;
        }
        return StringUtils.formatBigDecimal(projectAmount);
    }
    /** 立项毛利率预估 */
    public String getGrossMarginStr() {
        if(grossMargin == null){
            return StringUtils.EMPTY;
        }
        return StringUtils.formatBigDecimal(grossMargin);
    }
    /** 已完成项目收入 */
    public String getFinishProjectIncomeStr() {
        if(finishProjectIncome == null){
            return StringUtils.EMPTY;
        }
        return StringUtils.formatBigDecimal(finishProjectIncome);
    }
    /** 已完成项目成本 */
    public String getFinishProjectCostStr() {
        if(finishProjectCost == null){
            return StringUtils.EMPTY;
        }
        return StringUtils.formatBigDecimal(finishProjectCost);
    }
    /** 已完成项目利润 */
    public String getFinishProjectProfitStr() {
        if(finishProjectProfit == null){
            return StringUtils.EMPTY;
        }
        return StringUtils.formatBigDecimal(finishProjectProfit);
    }
    /** 已完成项目毛利率 */
    public String getFinishProjectGrossMarginStr() {
        if(finishProjectGrossMargin == null){
            return StringUtils.EMPTY;
        }
        return StringUtils.formatBigDecimal(finishProjectGrossMargin);
    }
    /** 立项时间 */
    public String getProjectApprovalTimeStr() {
        if(projectApprovalTime == null){
            return StringUtils.EMPTY;
        }
        return DateUtils.format(projectApprovalTime,"yyyy-MM-dd HH:mm:ss");
    }
    /** 项目开始时间 */
    public String getProjectStartTimeStr() {
        if(projectStartTime == null){
            return StringUtils.EMPTY;
        }
        return DateUtils.format(projectStartTime,"yyyy-MM-dd");
    }
    /** 预计结束时间 */
    public String getExpectedEndTimeStr() {
        if(expectedEndTime == null){
            return StringUtils.EMPTY;
        }
        return DateUtils.format(expectedEndTime,"yyyy-MM-dd");
    }
    /** 结案时间 */
    public String getCloseCaseTimeStr() {
        if(closeCaseTime == null){
            return StringUtils.EMPTY;
        }
        return DateUtils.format(closeCaseTime,"yyyy-MM-dd");
    }
}
