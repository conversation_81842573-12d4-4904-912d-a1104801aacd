package com.py.crm.customer.customerlaundry.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import java.time.LocalDate;

/**
 * 客户管理-查看清单数据传输模型
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Data
@ApiModel("客户管理-查看清单数据传输模型" )
public class CustomerLaundryDTO {
    private static final long serialVersionUID = 1L;

    /** 自增id*/
    @ApiModelProperty("自增id" )
    private Long id;

    /** 清单id*/
    @ApiModelProperty("清单id" )
    private Long laundryId;

    /** 客户id*/
    @ApiModelProperty("业务id" )
    private Long bizId;

    /** 业务类型（0客户，1人脉） */
    @ApiModelProperty("业务类型（0客户，1人脉）")
    private Integer bizType;
}
