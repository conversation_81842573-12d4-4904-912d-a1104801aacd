package com.py.common.filter;

import com.alibaba.fastjson.JSONObject;
import com.py.common.utils.http.HttpHelper;
import lombok.AccessLevel;
import lombok.Getter;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 构建可重复读取inputStream的request
 *
 * <AUTHOR>
 */
public class RepeatedlyRequestWrapper extends HttpServletRequestWrapper
{
    /** 请求体 */
    private final byte[] body;

    /** 请求提JSON对象 */
    @Getter(value =  AccessLevel.PRIVATE,lazy = true)
    private final JSONObject bodyJsonObject = this.initBodyJsonObject();

    /**
     * 初始化请求体JSON对象
     * @return 请求体JSON对象
     */
    private JSONObject initBodyJsonObject() {
        if(this.body == null || this.body.length == 0){
            return new JSONObject();
        }

        String bodyStr = new String(this.body, StandardCharsets.UTF_8);
        //如果请求是数组不处理
        if (bodyStr.trim().startsWith("[")){
            return new JSONObject();
        }
        return JSONObject.parseObject(bodyStr);
    }

    public RepeatedlyRequestWrapper(HttpServletRequest request, ServletResponse response) throws IOException
    {
        super(request);
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");

        this.body = HttpHelper.getBodyString(request).getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 获取 body 的Json值
     * @param name 字段名
     * @return Json值
     */
    public Object getBodyJsonValue(String name){
        return this.getBodyJsonObject().get(name);
    }


    @Override
    public BufferedReader getReader() throws IOException
    {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException
    {
        final ByteArrayInputStream bais = new ByteArrayInputStream(body);
        return new ServletInputStream()
        {
            @Override
            public int read() throws IOException
            {
                return bais.read();
            }

            @Override
            public int available() throws IOException
            {
                return body.length;
            }

            @Override
            public boolean isFinished()
            {
                return false;
            }

            @Override
            public boolean isReady()
            {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener)
            {

            }
        };
    }
}
