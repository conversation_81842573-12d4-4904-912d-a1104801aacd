package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 合作状态
 * <AUTHOR>
 * @version CooperationStatus 2023/8/1 16:54
 */
@AllArgsConstructor
public enum CooperationStatus implements IDict<Integer>{
    /** 合作中 */
    COOPERATION(0,"合作中"),

    /** 暂停合作 */
    SUSPENSION_COOPERATION(1,"暂停合作"),

    /** 意向合作 */
    INTENTION_COOPERATE(2,"意向合作"),

    /** 全部 */
    ALL(3,"全部"),
    ;


    private final Integer value;
    private final String label;

    @Override
    public String getLabel() {
        return this.label;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
