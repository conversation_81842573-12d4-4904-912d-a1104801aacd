package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 行业平台枚举
 * <AUTHOR>
 * @version PlatformType 2023/7/18 15:28
 */
@AllArgsConstructor
public enum PlatformType implements IDict<Integer>{
   /** 小红书 */
    LITTLE_RED_BOOK(0,"小红书"),
    /** 抖音 */
    TIK_TOK(1,"抖音"),
    /** B站 */
    BILI_BILI(2,"B站"),
    /** 微博 */
    WEIBO(3,"微博"),
    /** 淘宝 */
    TAO_BAO(4,"淘宝");

    private final Integer value;

    private final String label;

    /**
     * 获取字典说明
     * @return 字典说明
     */
    @Override
    public String getLabel() {
        return label;
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public Integer getValue() {
        return value;
    }
}
