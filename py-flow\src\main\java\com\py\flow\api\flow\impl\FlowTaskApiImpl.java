package com.py.flow.api.flow.impl;

import com.py.common.enums.flow.FlowResultCode;
import com.py.common.exception.ServiceException;
import com.py.common.utils.collection.ListUtil;
import com.py.flow.api.flow.IFlowTaskApi;
import com.py.flow.constant.FlowVariablesConstant;
import com.py.flow.domain.enums.FlowOperate;
import com.py.flow.flowinstance.domain.enums.ApprovalTaskType;
import com.py.flow.flowinstance.domain.enums.ApproverNodeStatus;
import com.py.flow.tools.FlowableUtils;
import com.py.flow.tools.aftersign.AfterSignCmd;
import com.py.flow.tools.aftersign.model.AfterSignDTO;
import com.py.flow.tools.aftersign.model.AfterSignNodeInfo;
import com.py.flow.tools.bpmnconverter.constant.BpmnConverterConstant;
import com.py.flow.tools.factory.FlowServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.event.FlowableActivityEvent;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程任务服务实现类
 * <AUTHOR>
 * @date 2023/7/14 14:51
 */
@Service
@Slf4j
public class FlowTaskApiImpl extends FlowServiceFactory implements IFlowTaskApi {

    /** 允许的最大加签层级 */
    private int MAX_AFTER_SIGN_LEVEL = 3;

    /**
     * 同意审批流程任务
     * @param userId 审批用户
     * @param taskId 任务ID
     */
    @Override
    public void passApproval(Long userId, String taskId) {
        Assert.notNull(userId, "审批用户不能为空");
        Assert.hasText(taskId, "任务ID不能为空");
        log.info("开始向审批 flowable 引擎提交审批通过信息, 任务ID: {} 用户ID: {}", taskId, userId);

        Task task = this.selectTask(taskId);
        this.canOperateTask(userId, task);

        // 在流程本地变量中设置审批状态, 后续的详情查询时读取
        Map<String, Object> taskVariables = new HashMap<>(2);
        taskVariables.put(FlowVariablesConstant.NODE_STATUS, ApproverNodeStatus.Pass);
        taskVariables.put(FlowVariablesConstant.OPERATE, FlowOperate.AGREE);
        // 设置审批人并完成任务
        this.taskService.setAssignee(taskId, task.getAssignee());
        this.taskService.complete(taskId, taskVariables, true);
        log.info("flowable 引擎审批通过信息提交成功, 任务ID: {} 用户ID: {}", taskId, userId);
    }

    /**
      * 拒绝审批流程任务
     * @param userId 审批用户
     * @param taskId 任务ID
     */
    @Override
    public void rejectApproval(Long userId, String taskId) {
        Assert.notNull(userId, "审批用户不能为空");
        Assert.hasText(taskId, "任务ID不能为空");
        log.info("开始向审批 flowable 引擎提交审批拒绝信息, 任务ID: {} 用户ID: {}", taskId, userId);

        Task task = this.selectTask(taskId);
        this.canOperateTask(userId, task);

        // 在流程本地变量中设置审批状态, 后续的详情查询时读取
        Map<String, Object> taskVariables = new HashMap<>(2);
        taskVariables.put(FlowVariablesConstant.NODE_STATUS, ApproverNodeStatus.Reject);
        taskVariables.put(FlowVariablesConstant.OPERATE, FlowOperate.REJECT);
        // 设置审批人并完成任务
        this.taskService.setAssignee(taskId, task.getAssignee());
        this.taskService.complete(taskId, taskVariables, true);
        // 结束流程
        List<Execution> executionList = this.runtimeService.createExecutionQuery()
                .processInstanceId(task.getProcessInstanceId())
                .list();
        if(ListUtil.any(executionList)){
            List<String> executionIdList = executionList
                    .stream()
                    .filter(execution -> execution.getActivityId() != null)
                    .limit(1)
                    .map(Execution::getId)
                    .collect(Collectors.toList());
            this.runtimeService.createChangeActivityStateBuilder()
                    .moveExecutionsToSingleActivityId(executionIdList, BpmnConverterConstant.END_EVENT_ID)
                    .changeState();
        }
        log.info("flowable 引擎审批拒绝信息提交成功, 任务ID: {} 用户ID: {}", taskId, userId);
    }

    /**
     * 流程撤回
     * @param processInstanceId 流程实例ID
     */
    @Override
    public void cancelApproval(String processInstanceId) {
        Assert.hasText(processInstanceId, "流程实例ID不能为空");
        log.info("开始向审批 flowable 引擎提交流程撤回信息, 流程实例ID: {}", processInstanceId);
        // 新需求，流程只有一条
        this.runtimeService.deleteProcessInstance(processInstanceId, "流程发起人撤回流程");
        log.info("flowable 引擎流程撤回信息提交成功, 流程实例ID: {}", processInstanceId);
    }

    /**
     * 流程作废
     * @param processInstanceId 流程实例ID
     */
    @Override
    public void invalidatedApproval(String processInstanceId) {
        Assert.hasText(processInstanceId, "流程实例ID不能为空");
        log.info("开始向审批 flowable 引擎提交流程作废信息, 流程实例ID: {}", processInstanceId);
        this.runtimeService.deleteProcessInstance(processInstanceId, "流程发起人作废流程");
        log.info("flowable 引擎流程作废信息提交成功, 流程实例ID: {}", processInstanceId);
    }

    /**
     * 流程转交
     * @param taskId 需转交的任务
     * @param userId 转交人
     * @param forwardUserId 被转交人
     */
    @SuppressWarnings("unchecked")
    @Override
    public void forwardApproval(String taskId, Long userId, Long forwardUserId) {
        Assert.notNull(userId, "审批用户不能为空");
        Assert.hasText(taskId, "任务ID不能为空");
        Assert.notNull(forwardUserId, "被转交人不能为空");

        log.info("开始向审批 flowable 引擎提交流程转交信息, 任务ID: {} 用户ID: {} 转交人ID: {}", taskId, userId, forwardUserId);
        Task task = this.selectTask(taskId);
        this.canOperateTask(userId, task);

        // 设置任务的归属者, 仅第一次转交时设置
        if(task.getOwner() == null) {
            this.taskService.setOwner(taskId, userId.toString());
        }
        // 设置任务的办理者
        this.taskService.setAssignee(taskId, forwardUserId.toString());
        // 设置任务类型为转交
        this.setTaskType(taskId, ApprovalTaskType.Forward);

        // 设置转交人员记录
        Set<Long> forwardUserSet = (Set<Long>) this.taskService.getVariableLocal(taskId, FlowVariablesConstant.FORWARD_USER_SET);
        if(ListUtil.isEmpty(forwardUserSet)){
            forwardUserSet = new HashSet<>();
        }
        forwardUserSet.add(userId);
        this.taskService.setVariableLocal(taskId, FlowVariablesConstant.FORWARD_USER_SET, forwardUserSet);

        log.info("flowable 引擎流程转办完成, 任务ID: {}", taskId);
    }

    /**
     * 向后加签
     * @param afterSignDto 向后加签DTO
     * @param userId 操作人ID
     * @return 加签成功的节点信息
     */
    @Override
    public AfterSignNodeInfo afterSign(AfterSignDTO afterSignDto, Long userId) {
        Assert.notNull(afterSignDto, "向后加签DTO不能为空");

        ProcessInstance processInstance = this.selectProcessInstance(afterSignDto.getProcessInstanceId());
        List<AfterSignNodeInfo> afterSignNodeInfoList = FlowableUtils.getVariable(processInstance, FlowVariablesConstant.AFTER_SIGN_NODE_INFO);
        AfterSignNodeInfo afterSignNodeInfo = ListUtil.first(afterSignNodeInfoList,
                signNode -> signNode.getNewBeSignTaskId().equals(afterSignDto.getTaskId()));
        // 存在向后加签信息则说明该节点已经加签
        if(afterSignNodeInfo != null){
            this.appendUserToSignNode(afterSignDto.getSignUserId(), afterSignNodeInfo, processInstance);
            return afterSignNodeInfo;
        }else {
        // 不存在时, 新增加签节点
            return this.newAfterSignNode(afterSignDto, afterSignNodeInfoList);
        }
    }

    /**
     * 在任务局部变量上设置任务类型
     * @param taskId 任务ID
     * @param taskType 要设置的任务类型
     */
    @SuppressWarnings("unchecked")
    private void setTaskType(String taskId, ApprovalTaskType taskType) {
        Assert.hasText(taskId, "任务ID不能为空");
        Assert.notNull(taskType, "任务类型不能为空");

        Set<ApprovalTaskType> taskTypeSet = (Set<ApprovalTaskType>) this.taskService.getVariableLocal(taskId, FlowVariablesConstant.NODE_TYPE);
        if(ListUtil.isEmpty(taskTypeSet)){
            taskTypeSet = EnumSet.noneOf(ApprovalTaskType.class);
        }
        taskTypeSet.add(taskType);
        this.taskService.setVariableLocal(taskId, FlowVariablesConstant.NODE_TYPE, taskTypeSet);
    }

    /**
     * 新增加签节点
     * @param afterSignDto 向后加签DTO
     * @param afterSignNodeInfoList 流程已存在的向后加签信息列表
     * @return 加签成功的节点信息
     */
    private AfterSignNodeInfo newAfterSignNode(AfterSignDTO afterSignDto, List<AfterSignNodeInfo> afterSignNodeInfoList) {
        Task task = this.selectTask(afterSignDto.getTaskId());
        this.canOperateTask(afterSignDto.getBeSignUserId(), task);
        // 查找被加签节点, 当被加签节点也是加签节点时: 判断加签深度
        AfterSignNodeInfo beAfterSignNodeInfo = ListUtil.first(afterSignNodeInfoList, nodeInfo -> nodeInfo.getSignActivityId().equals(task.getTaskDefinitionKey()));
        if(beAfterSignNodeInfo != null
        && beAfterSignNodeInfo.getSignLevel() != null
        && beAfterSignNodeInfo.getSignLevel() >= MAX_AFTER_SIGN_LEVEL){
            throw new ServiceException("节点加签最大仅支持三层加签");
        }

        // 执行向后加签命令, 新增加签节点
        AfterSignCmd afterSignCmd = new AfterSignCmd(afterSignDto, beAfterSignNodeInfo);
        AfterSignNodeInfo afterSignNodeInfo = this.managementService.executeCommand(afterSignCmd);
        // 获取新建的被加签任务
        Task newBeSignTask = this.taskService.createTaskQuery()
                .taskDefinitionKey(afterSignNodeInfo.getNewBeSignActivityId())
                .singleResult();
        // 为新建的任务继承原有任务的本地变量, 并设置新的任务类型(加签)
        this.taskService.setVariablesLocal(newBeSignTask.getId(), task.getTaskLocalVariables());
        this.setTaskType(newBeSignTask.getId(), ApprovalTaskType.AfterSign);
        // 设置原来的任务为被加签任务
        this.setTaskType(afterSignDto.getTaskId(), ApprovalTaskType.BeSign);

        // 记录新增加签任务ID
        this.recordBeAfterSignTaskId(newBeSignTask);
        afterSignNodeInfo.setNewBeSignTaskId(newBeSignTask.getId());

        return afterSignNodeInfo;
    }

    /**
     * 记录向后加签的被加签任务ID
     * @param newBeSignTask 新增加签的被加签任务
     */
    private void recordBeAfterSignTaskId(Task newBeSignTask) {
        String processInstanceId = newBeSignTask.getProcessInstanceId();
        ProcessInstance processInstance = selectProcessInstance(processInstanceId);
        List<AfterSignNodeInfo> afterSignNodeInfoList = FlowableUtils.getVariable(processInstance, FlowVariablesConstant.AFTER_SIGN_NODE_INFO);
        AfterSignNodeInfo afterSignNodeInfo = ListUtil.firstOrThrow(afterSignNodeInfoList,
                signNode -> signNode.getNewBeSignActivityId().equals(newBeSignTask.getTaskDefinitionKey()));
        afterSignNodeInfo.setNewBeSignTaskId(newBeSignTask.getId());
        this.runtimeService.setVariable(processInstance.getId(), FlowVariablesConstant.AFTER_SIGN_NODE_INFO, afterSignNodeInfoList);
    }

    /**
     * 查询Flowable流程实例
     * @param processInstanceId 流程实例ID
     * @return Flowable流程实例
     */
    @Override
    public ProcessInstance selectProcessInstance(String processInstanceId) {
        return this.runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .includeProcessVariables()
                .singleResult();
    }

    /**
     * 查询指定ID的任务
     * @param taskId 任务ID
     * @return 任务
     */
    @Override
    public Task selectTask(String taskId) {
        Assert.hasText(taskId, "任务ID不能为空");

        return this.taskService.createTaskQuery()
                .taskId(taskId)
                .singleResult();
    }

    /**
     * 判断当前用户是否可以操作任务
     * @param userId 用户ID
     * @param task 任务
     */
    private void canOperateTask(Long userId, Task task) {
        if(task == null){
            throw new ServiceException(FlowResultCode.ACTIVITY_VETTING_TASK_NOT_EXIST);
        }else if(task.isSuspended()){
            throw new ServiceException(FlowResultCode.ACTIVITY_VETTING_TASK_FREEZE);
        }
        else if(task.getAssignee().equals(userId.toString()) == false){
            throw new ServiceException("审批操作错误: 您不是当前节点的审批人");
        }
    }

    /**
     * 设置流程实例变量
     * @param processInstanceId 流程实例ID
     * @param variableName 变量名
     * @param value 变量值
     */
    @Override
    public void setProcessInstanceVariable(String processInstanceId, String variableName, Object value) {
        ProcessInstance processInstance = this.selectProcessInstance(processInstanceId);
        this.setProcessInstanceVariable(processInstance, variableName, value);
    }

    /**
     * 设置流程实例变量
     * @param processInstance 流程实例
     * @param variableName 变量名
     * @param value 变量值
     */
    @Override
    public void setProcessInstanceVariable(ProcessInstance processInstance, String variableName, Object value) {
        this.runtimeService.setVariable(processInstance.getId(), variableName, value);
    }

    /**
     * 通知子流程完成
     * @param activityCompletedEvent 子流程完成事件
     */
    @Override
    public void notifySubProcessCompleted(FlowableActivityEvent activityCompletedEvent) {
        ProcessInstance processInstance = this.selectProcessInstance(activityCompletedEvent.getProcessInstanceId());
        List<AfterSignNodeInfo> afterSignNodeInfoList = FlowableUtils.getVariable(processInstance, FlowVariablesConstant.AFTER_SIGN_NODE_INFO);
        AfterSignNodeInfo afterSignNodeInfo = AfterSignNodeInfo.findAfterSignSubProcess(activityCompletedEvent, afterSignNodeInfoList);
        if(afterSignNodeInfo == null){
            return;
        }
        // 完成向后加签子流程顶替掉的被加签任务
        Task task = this.selectTask(afterSignNodeInfo.getBeSignTaskId());
        this.passApproval(Long.valueOf(task.getAssignee()), task.getId());
        log.info("已完成被加签任务 {} 的补偿审批任务", afterSignNodeInfo.getBeSignTaskId());
    }

    /**
     * 向加签节点添加审批用户
     * @param signUserId 加签用户
     * @param afterSignNodeInfo 向后加签节点信息
     * @param processInstance 加签的流程实例
     */
    private void appendUserToSignNode(Long signUserId, AfterSignNodeInfo afterSignNodeInfo, ProcessInstance processInstance) {
        Task task = this.selectTask(afterSignNodeInfo.getNewBeSignTaskId());
        this.canOperateTask(afterSignNodeInfo.getBeSignUserId(), task);

        List<Long> signUserAssigneeList = FlowableUtils.getVariable(processInstance, afterSignNodeInfo.getSignUserAssignee());
        Assert.notNull(signUserAssigneeList, "加签用户列表不存在");
        signUserAssigneeList.add(signUserId);
        this.runtimeService.setVariable(processInstance.getId(), afterSignNodeInfo.getSignUserAssignee(), signUserAssigneeList);
    }
}
