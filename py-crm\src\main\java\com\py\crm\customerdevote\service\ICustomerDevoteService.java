package com.py.crm.customerdevote.service;

import com.github.pagehelper.PageInfo;
import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;
import com.py.crm.compareddraft.domain.dto.CustomerDevoteComparedDraftExportModel;
import com.py.crm.compareddraft.domain.vo.ComparedDraftListVO;
import com.py.crm.customerdevote.domain.dto.CrmProjectListExportVO;
import com.py.crm.customerdevote.domain.dto.CustomerDevoteBadDebtExportModel;
import com.py.crm.customerdevote.domain.query.CustomerDevoteQuery;
import com.py.crm.customerdevote.domain.vo.CustomerDevoteListVO;
import com.py.crm.customerdevote.domain.vo.CustomerDevoteTotal;

import java.util.List;

/**
 * 客户贡献管理
 * <AUTHOR>
 * @version ICustomerDevoteService 2023/8/9 11:21
 */
public interface ICustomerDevoteService {

    /**
     * 查询客户贡献管理列表
     * @param query 查询
     * @return 客户贡献管理列表
     */
    PageInfo<CustomerDevoteListVO> pageCustomerDevote(CustomerDevoteQuery query);

    /**
     * 比稿列表
     * @param query 查询
     * @return 客户贡献管理列表
     */
    PageInfo<ComparedDraftListVO> listComparedDraft(CustomerDevoteQuery query);

    /**
     * 导出比稿数据
     * @param query
     * @return
     */
    List<CustomerDevoteComparedDraftExportModel> queryComparedDraftList(CustomerDevoteQuery query);

    /**
     * 勾选客户贡献合计
     * @param query
     * @return
     */
    CustomerDevoteTotal getCustomerDevoteTotal(CustomerDevoteQuery query);

    /**
     * 客户贡献管理选中的
     * @param query
     * @return
     */
    List<CustomerDevoteListVO> listCustomerDevote(CustomerDevoteQuery query);
    /**
     * 客户贡献管理明细表头
     * @return
     */
    ExcelSheetConfig<CustomerDevoteListVO> getCustomerDevoteConfig();

    /**
     * 客户贡献比稿明细表头
     * @return
     */
    ExcelSheetConfig<CustomerDevoteComparedDraftExportModel> getComparedDraftConfig();
    /**
     * 客户贡献项目明细表头
     * @return
     */
    ExcelSheetConfig<CrmProjectListExportVO> getCrmProjectConfig();
    /**
     * 客户贡献坏账明细表头
     * @return
     */
    ExcelSheetConfig<CustomerDevoteBadDebtExportModel> getBadDebtConfig();

    /**
     * 导出比稿明细
     *
     * @param query 导出查询参数
     * @return 导出数据
     */
    void exportComparedDraft(CustomerDevoteQuery query);

    /**
     * 导出坏账明细
     *
     * @param query 导出查询参数
     * @return 导出数据
     */
    void exportBadDebt(CustomerDevoteQuery query);

    /**
     * 导出项目明细
     *
     * @param query 导出查询参数
     * @return 导出数据
     */
    void exportProject(CustomerDevoteQuery query);

    /**
     * 导出结案明细
     *
     * @param query 导出查询参数
     * @return 导出数据
     */
    void exportCloseCaseProject(CustomerDevoteQuery query);

    /**
     * 导出客户贡献列表数据
     *
     * @param query 导出查询参数
     * @return 导出数据
     */
    void exportCustomerDevote(CustomerDevoteQuery query);
}
