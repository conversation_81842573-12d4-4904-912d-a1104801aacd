package com.py.common.tools.modifycomparator.contexthandler;

import com.py.common.utils.StringUtils;
import org.springframework.util.Assert;

import java.lang.reflect.Field;

/**
 * 基础比较内容处理器
 * <AUTHOR>
 */
public abstract class BaseCompareContextHandler<T> implements ICompareContextHandler {

    /** 处理的内容类型 */
    private final Class<T> contextType;

    public BaseCompareContextHandler(Class<T> contextType) {
        this.contextType = contextType;
    }

    /**
     * 自定义处理逻辑
     * @param fieldValue 字段值
     * @param field 字段定义
     * @return 处理结果值
     */
    @Override
    public String handle(Object fieldValue, Field field) {
        if(fieldValue == null) {
            return StringUtils.EMPTY;
        }
        Assert.isInstanceOf(contextType, fieldValue, "类型错误: 处理器配置的类型必须为 " + this.contextType.getName());
        //noinspection unchecked
        return this.handleNonNullContext((T) fieldValue, field);
    }

    /**
     * 处理不为空的比较内容
     * @param fieldValue 字段值
     * @param field 字段定义
     * @return 处理结果值
     */
    protected abstract String handleNonNullContext(T fieldValue, Field field);
}
