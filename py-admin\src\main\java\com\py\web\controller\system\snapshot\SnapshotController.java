package com.py.web.controller.system.snapshot;


import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.system.snapshot.domain.Snapshot;
import com.py.system.snapshot.domain.dto.SnapshotDTO;
import com.py.system.snapshot.domain.dto.SnapshotExportModel;
import com.py.system.snapshot.domain.query.SnapshotQuery;
import com.py.system.snapshot.domain.vo.SnapshotListVO;
import com.py.system.snapshot.domain.vo.SnapshotVO;
import com.py.system.snapshot.service.ISnapshotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 快照Controller
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@Api(tags = "快照")
@RestController
@RequestMapping("/snapshot/snapshot")
public class SnapshotController extends BaseController {

    /** 快照服务 */
    @Resource
    private ISnapshotService snapshotService;

    /**
     * 查询快照列表
     *
     * @param query 快照查询参数
     * @return 快照列表
     */
    @ApiOperation("查询快照列表")
    @PreAuthorize("@ss.hasPermi('snapshot:snapshot:list')")
    @GetMapping("/listSnapshot")
    public R<List<SnapshotListVO>> listSnapshot(SnapshotQuery query) {
        List<SnapshotListVO> voList = this.snapshotService.listSnapshot(query);
        return R.success(voList);
    }

    /**
     * 分页查询快照列表
     *
     * @param query 快照查询参数
     * @return 快照分页
     */
    @ApiOperation("分页查询询快照列表")
    @PreAuthorize("@ss.hasPermi('snapshot:snapshot:list')")
    @GetMapping("/pageSnapshot")
    public R<PageInfo<SnapshotListVO>> pageSnapshot(SnapshotQuery query) {
        PageInfo<SnapshotListVO> voList = this.snapshotService.pageSnapshotList(query);
        return R.success(voList);
    }

    /**
     * 获取快照详细信息
     * @param id 快照主键
     * @return 快照视图模型
     */
    @ApiOperation("获取快照详细信息")
    @PreAuthorize("@ss.hasPermi('snapshot:snapshot:query')")
    @GetMapping(value = "/{id}")
    public R<SnapshotVO> getInfo(@PathVariable("id") Long id) {
        return R.success(snapshotService.selectSnapshotById(id));
    }

    /**
     * 新增快照
     *
     * @param snapshotList 快照修改参数
     * @return 是否成功
     */
    @ApiOperation("新增快照")
    @PreAuthorize("@ss.hasPermi('snapshot:snapshot:add')")
    @Log(title = "快照", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody List<Snapshot> snapshotList) {
        return R.success(snapshotService.insertSnapshot(snapshotList));
    }

    /**
     * 修改快照
     *
     * @param dto 快照修改参数
     * @return 是否成功
     */
    @ApiOperation("修改快照")
    @PreAuthorize("@ss.hasPermi('snapshot:snapshot:edit')")
    @Log(title = "快照", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody SnapshotDTO dto) {
        return R.success(snapshotService.updateSnapshot(dto));
    }

    /**
     * 删除快照
     * @param ids 需要删除的快照主键集合
     * @return 是否成功
     */
    @ApiOperation("删除快照" )
    @PreAuthorize("@ss.hasPermi('snapshot:snapshot:remove')")
    @Log(title = "快照", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(snapshotService.deleteSnapshotByIds(ids));
    }

    /**
     * 导出快照
     * @param response 请求响应
     * @param query 导出查询参数
     */
    @ApiOperation("导出快照")
    @PreAuthorize("@ss.hasPermi('snapshot:snapshot:export')")
    @Log(title = "快照", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SnapshotQuery query) {
        List<SnapshotExportModel> exportList = this.snapshotService.exportSnapshot(query);

        ExcelUtil<SnapshotExportModel> util = new ExcelUtil<>(SnapshotExportModel. class);
        util.exportExcel(response, exportList, "快照数据" );
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('snapshot:snapshot:import')" )
    @Log(title = "快照" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<SnapshotExportModel> util = new ExcelUtil<>(SnapshotExportModel.class);
        List<SnapshotExportModel> snapshotList = util.importExcel(file.getInputStream());
        String message = this.snapshotService.importSnapshot(snapshotList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('snapshot:snapshot:import')" )
    @Log(title = "快照" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<SnapshotExportModel> util = new ExcelUtil<>(SnapshotExportModel.class);
        util.importTemplateExcel(response, "快照数据" );
    }

}
