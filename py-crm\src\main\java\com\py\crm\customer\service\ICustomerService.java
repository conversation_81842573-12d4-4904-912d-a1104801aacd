package com.py.crm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.py.common.core.domain.vo.user.AuthUser;
import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryBaseInfo;
import com.py.crm.customer.domain.SupCustomer;
import com.py.crm.customer.domain.dto.CustomerDTO;
import com.py.crm.customer.domain.dto.CustomerExportModel;
import com.py.crm.customer.domain.query.CustomerLineBusinessQuery;
import com.py.crm.customer.domain.query.CustomerNameDownQuery;
import com.py.crm.customer.domain.query.CustomerQuery;
import com.py.crm.customer.domain.vo.*;
import com.py.crm.customerdevote.domain.query.CustomerDevoteQuery;
import com.py.crm.customerdevote.domain.vo.CloseCaseMoneyListVO;
import com.py.crm.customerdevote.domain.vo.CustomerDevoteListVO;

import java.util.List;
import java.util.Map;

/**
 * 客户管理-客户Service接口
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
public interface ICustomerService extends IService<SupCustomer> {
    boolean deleteCustomerByBizIds (List<Long> bizIds);

    /**
     * 分页查询客户管理-客户列表
     * @param query 客户管理-客户查询参数
     * @return 客户管理-客户分页
     */
    PageInfo<CustomerListVO> pageCustomerList(CustomerQuery query);

    /**
     * 设置目标服务人员的名和部门
     * @param customerVoList 客户列表
     */
    void setServiceUserCategory(List<? extends CustomerLaundryBaseInfo> customerVoList);

    /**
     * 查询客户管理-客户
     * @param id 客户管理-客户主键
     * @return 客户管理-客户视图模型
     */
     CustomerVO selectCustomerById(Long id);

    /**
     * 获取客户快照信息
     * @param customerVO 客户基本信息
     * @return 客户快照信息
     */
    CustomerSnapshotVO getCustomerSnapshot(CustomerVO customerVO);

    /**
     * 新增客户管理-客户
     * @param dto 客户管理-客户修改参数
     * @return 是否成功
     */
    Long insertCustomer(CustomerDTO dto);

    /**
     * 修改客户管理-客户
     * @param dto 客户管理-客户修改参数
     * @return 修改的客户ID
     */
    Long updateCustomer(CustomerDTO dto);

    /**
     * 批量删除客户管理-客户
     * @param id 需要删除的客户管理-客户主键集合
     * @return 是否成功
     */
    boolean deleteCustomerById(Long id);

    /**
     * 客户状态变更
     * @param dto 参数
     * @return 结果
     */
    Boolean stateUpdate(CustomerDTO dto);

    /**
     * 客户流转
     * @param dto 参数
     * @return 结果
     */
    Boolean customerCirculation(CustomerDTO dto);

    /**
     * 客户下拉
     * @param query 参数
     * @return 下拉
     */
    List<CustomerListVO> pollDown(CustomerQuery query);


    /**
     * 客户下拉模糊搜索
     * @param query 客户名称
     * @return 下拉
     */
    List<CustomerListVO> pollDownSelect(CustomerNameDownQuery query);


    /**
     * 根据用户对应的云客户数据权限获取客户下拉
     * @param query 参数
     * @return 下拉数据
     */
    List<CustomerListVO> pollDownUserDataScope(CustomerQuery query);

    /**
     * 获取详情
     * @param id 客户id
     * @return 客户
     */
    SupCustomer getInfo(Long id);

    /**
     * 客户审核修改状态
     * @param dto 参数
     * @return 结果
     */
    Boolean auditAfter(CustomerDTO dto);

    /**
     * 客户统计
     * @param query 查询权限条件
     * @return 客户统计
     */
    CustomerCountVO customerCount(CustomerQuery query);

    /**
     * 客户贡献列表
     * @param query 客户贡献管理查询模型
     * @return 客户对象
     */
    List<SupCustomer> getCustomerDevoteList(CustomerDevoteQuery query);

    /**
     * 获取比稿金额
     * @param query 客户id列表-结案时间-合作时间
     * @return 客户比稿金额
     */
    List<ComparedDraftMoneyVO> getCustomerDevoteProjectList(CustomerDevoteQuery query);

    /**
     * 查询项目ID
     * @param query 查询条件
     * @return 项目ID
     */
    List<Long> getCustomerDevoteProjectIdList(CustomerDevoteQuery query);

    /**
     * 客户金额统计
     * @param projectIds 项目ID
     * @return 客户金额统计
     */
    List<CloseCaseMoneyListVO>  getCloseCaseMoneyMap(List<Long> projectIds);

    /**
     * 坏账金额统计
     * @param query 查询条件
     * @return 坏账金额
     */
    List<CustomerDevoteListVO> getBadeMoneyMap(CustomerDevoteQuery query);

    /**
     * 人脉客户下拉
     * @param query 参数
     * @return 下拉
     */
    List<CustomerConnectionVO> pollConnectionDown(CustomerQuery query);

    /**
     * 全选时根据状态和客户id查询客户id
     * @param customerIdList 客户id
     * @param query 查询条件
     * @return customerId
     */
    List<Long> listAllCustomerIdByStatus(List<Long> customerIdList, CustomerQuery query);

    /**
     * 根据客户ID查询客户信息
     * @param customerIdList 客户ID
     * @return 客户信息
     */
    List<SupCustomer> listCustomerIdByCustomerIds(List<Long> customerIdList);

    /**
     * 根据用户信息组装服务人员
     * @param authUsers 用户信息用户信息
     * @param delimiter 分隔符
     * @return 服务人员
     */
    String getServiceUserName(List<AuthUser> authUsers, String delimiter);

    /**
     * 批量分配模版
     * @return 模版
     */
    ExcelSheetConfig<CustomerExportModel> getCustomerTemplateConfig();

    /**
     * 人脉客户下拉(包括从人脉中获取的客户数据)
     * @param query
     * @return
     */
    List<String> getPollConnectionDown(CustomerQuery query);

    /**
     * 获取客户管理中该客户下的品牌/业务线列表
     * @param name
     * @param responsibleBrand
     * @return
     */
    List<String> listResponsibleBrandByName(String name, String responsibleBrand);

    /**
     * 根据企业名称和负责品牌业务线获取企业id
     * @param name
     * @param lineBusinessList
     * @return
     */
    Long getCustomerId(String name, List<String> lineBusinessList);

    /**
     * 根据客户ID和在职手机号查询其他关联的在职的客户并将在职改为离职
     * @param customerId 客户ID
     * @param phoneList 手机号
     * @return 客户信息
     */
    Map<String,SupCustomer> listCustomerByPhoneId(Long customerId, List<String> phoneList);

    /**
     * 客户名称模糊检索
     * 业务线模糊检索
     * @param query 客户名称,业务线名称
     * @return 客户id,客户名称,业务线
     */
    List<CustomerLineBusinessVO> lineBusinessByCustomer(CustomerLineBusinessQuery query);

    /**
     * 客户管理-客户列表,模糊搜索列表,录入人部门集合
     *
     * @param query 客户管理-客户查询参数(录入人部门名称)
     * @return 客户管理-客户列表,部门集合
     */
    List<String> findCustomerCreateDeptList(CustomerQuery query);

    /**
     * 客户管理-客户列表,模糊搜索列表,服务人部门集合
     *
     * @param query 客户管理-客户查询参数(服务人部门名称)
     * @return 客户管理-客户列表,部门集合
     */
    List<String> findCustomerServiceDeptList(CustomerQuery query);
}
