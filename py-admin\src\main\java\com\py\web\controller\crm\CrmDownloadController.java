package com.py.web.controller.crm;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.core.domain.model.LoginUser;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.codegenerator.IBusinessSerialNumberGenerator;
import com.py.common.tools.codegenerator.enums.SerialNumberBizType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.crm.crmdownload.domain.query.CrmDownloadQuery;
import com.py.crm.crmdownload.domain.vo.CrmDownloadListVO;
import com.py.crm.crmdownload.service.ICrmDownloadService;
import com.py.crm.crmdownload.service.impl.CrmDownloadServiceImpl;
import com.py.crm.crmupdaterecord.domain.enums.RecordBizType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 客户/人脉清单下载记录Controller
 *
 * <AUTHOR>
 * @date 2023-09-26
 */
@Api(tags = "客户-人脉清单下载记录")
@RestController
@RequestMapping("/crmDownload")
public class CrmDownloadController extends BaseController {

    /** 客户/人脉清单下载记录服务 */
    @Resource
    private ICrmDownloadService crmDownloadService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 业务序列号生成器 */
    @Resource
    private IBusinessSerialNumberGenerator businessSerialNumberGenerator;

    /**
     * 分页查询客户/人脉清单下载记录列表
     *
     * @param query 客户/人脉清单下载记录查询参数
     * @return 客户/人脉清单下载记录分页
     */
    @ApiOperation("分页查询客户人脉清单下载记录列表")
    @PreAuthorize("@ss.hasPermi('crmdownload:crmdownload:list')")
    @GetMapping("/pageCrmDownload")
    public R<PageInfo<CrmDownloadListVO>> pageCrmDownload(CrmDownloadQuery query) {
        PageInfo<CrmDownloadListVO> voList = this.crmDownloadService.pageCrmDownloadList(query);
        return R.success(voList);
    }

    /**
     * 查询客户/人脉清单下载记录列表,部门信息
     *
     * @param query 客户/人脉清单下载记录查询参数(部门信息)
     * @return 客户/人脉清单下载记录,部门信息
     */
    @ApiOperation("查询客户人脉清单下载记录列表,部门信息")
    @PostMapping("/findCrmDownloadDeptList")
    public R<List<String>> findCrmDownloadDeptList(@RequestBody CrmDownloadQuery query) {
        List<String> voList = this.crmDownloadService.findCrmDownloadDeptList(query);
        return R.success(voList);
    }

    /**
     * 客户-人脉下载明细
     * @param query 导出查询参数
     */
    @ApiOperation("客户-人脉下载明细")
    @PreAuthorize("@ss.hasPermi('crmdownload:download:export')")
    @Log(title = "客户-人脉下载明细", businessType = BusinessType.EXPORT)
    @PostMapping("/batchDownload")
    public R<String> batchDetail(@Validated @RequestBody CrmDownloadQuery query) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        query.setLoginUser(loginUser);
        String time = DateUtils.dateTime();

        //区分是客户还是人脉
        SerialNumberBizType bizType;
        if(Objects.equals(RecordBizType.CUSTOMER.getValue(), query.getBizType())) {
            bizType = SerialNumberBizType.CUSTOMER_DOWNLOAD;
        } else {
            bizType = SerialNumberBizType.CONNECTION_DOWNLOAD;
        }
        long serialNo = businessSerialNumberGenerator.generateSerialNumber(bizType,
                Constants.DOWNLOAD_NAME_NUMBER + time, () -> 1L, DateUtils.getTodayToTomorrowTime(), TimeUnit.MINUTES);
        String filename = time + "-" + serialNo + ".zip";
        query.setFileName(filename);
        reusableAsyncTaskService.addTask("客户下载管理", TaskType.Export, query, CrmDownloadServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }
}
