package com.py.crm.connection.domain.dto;

import com.py.common.tools.poiexcel.annotation.Excel;
import com.py.common.tools.verify.config.VerifyConfig;
import com.py.common.tools.verify.domain.ISerialNumber;
import com.py.common.tools.verify.enums.VerifyType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;


/**
 * 人脉管理表导出模型
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
@ApiModel("人脉管理表导出模型" )
public class ConnectionImportModel implements ISerialNumber {
    private static final long serialVersionUID = 1L;

    /** 数据行号 */
    @Excel(setRowIndex = true)
    private Integer serialNumber;

    /** 客户名称 */
    @Excel(name = "*人脉姓名")
    @ApiModelProperty("人脉姓名" )
    private String connectionName;

    /** 电话 */
    @Excel(name = "电话")
    @ApiModelProperty("电话" )
    private String phone;

    /** *服务人员 */
    @Excel(name = "*服务人员")
    @ApiModelProperty("*服务人员" )
    private String serviceUser;

    /** 服务人员的姓名和手机号 */
    private List<ConnectionImportModel.ServiceUser> serviceUserList;
    /** 服务人员id列表 */
    private Set<Long> serviceUserIdList;

    /** 人脉id */
    @ApiModelProperty("人脉id" )
    private Long connectionId;
    /**
     * 获取验证配置
     * @return 验证配置
     */
    public static VerifyConfig<ConnectionImportModel> getVerifyConfig() {
        VerifyConfig<ConnectionImportModel> config = new VerifyConfig<>();
        // 项目名称校验
        config.addConfig(VerifyType.NotBlank, ConnectionImportModel::getConnectionName, "人脉姓名为空")
                //资源ID校验
                .addConfig(VerifyType.NotBlank, ConnectionImportModel::getServiceUser, "服务人员为空");
        return config;
    }

    /** 服务人员 */
    @Data
    public static class ServiceUser {
        /** 姓名 */
        private String serviceUserName;

        /** 手机号 */
        private String serviceUserPhone;
    }

}
