package com.py.crm.customer.invoicing.service;

import com.py.crm.customer.invoicing.domain.dto.InvoicingDTO;
import com.py.crm.customer.invoicing.domain.dto.InvoicingExportModel;
import com.py.crm.customer.invoicing.domain.vo.InvoicingVO;
import com.py.crm.customer.invoicing.domain.vo.InvoicingListVO;
import com.py.crm.customer.invoicing.domain.query.InvoicingQuery;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 客户管理-客户-开票信息Service接口
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
public interface IInvoicingService {

    /**
     * 分页查询客户管理-客户-开票信息列表
     * @param query 客户管理-客户-开票信息查询参数
     * @return 客户管理-客户-开票信息分页
     */
    PageInfo<InvoicingListVO>pageinvoicingList(InvoicingQuery query);

    /**
     * 查询客户管理-客户-开票信息
     * @param id 客户管理-客户-开票信息主键
     * @return 客户管理-客户-开票信息视图模型
     */
     InvoicingVO selectinvoicingById(Long id);

    /**
     * 查询客户管理-客户-开票信息详细信息
     * @param id 客户管理-客户-开票信息主键
     * @return 客户管理-客户-开票信息视图模型
     */
    List<InvoicingVO> selectinvoicingDetailById(Long id);

    /**
     * 新增客户管理-客户-开票信息
     * @param dto 客户管理-客户-开票信息修改参数
     * @return 是否成功
     */
    boolean insertinvoicing(InvoicingDTO dto);

    /**
     * 修改客户管理-客户-开票信息
     * @param dto 客户管理-客户-开票信息修改参数
     * @return 是否成功
     */
    boolean updateinvoicing(InvoicingDTO dto);

    /**
     * 批量删除客户管理-客户-开票信息
     * @param idList 需要删除的客户管理-客户-开票信息主键集合
     * @return 是否成功
     */
    boolean deleteinvoicingByIds(List<Long> idList);

    /**
     * 导出客户管理-客户-开票信息
     * @param query 导出查询参数
     * @return 导出数据
     */
    List<InvoicingExportModel> exportinvoicing(InvoicingQuery query);

    /**
     * 导入客户管理-客户-开票信息
     * @param exportList 导入数据
     * @return 导入结果
     */
    String importinvoicing(List<InvoicingExportModel> exportList);

    /**
     * 批量新增开票信息数据
     * @param dtoList 开票信息数据传输模型
     * @param customerId 客户ID
     * @param isApprovalEdit 是否是审批驳回修改
     */
    void insertBatchInvoicing(List<InvoicingDTO> dtoList, Long customerId, Boolean isApprovalEdit);

    /**
     * 批量修改开票信息数据
     * @param invoicingDTOList 开票信息数据传输模型
     * @param customerId 客户ID
     * @param name 客户名称
     */
    void updateBatchInvoicing(List<InvoicingDTO> invoicingDTOList, Long customerId, String name);

    /**
     * 分页查询客户管理-客户-开票信息列表--下拉列表
     *
     * @param query 客户管理-客户-开票信息查询参数
     * @return 客户管理-客户-开票信息分页
     */
    PageInfo<InvoicingListVO> pageInvoicingSelected(InvoicingQuery query);

    /**
     * 根据客户id查看开票信息
     * @param customerIdList 客户id
     * @return 开票信息
     */
    List<InvoicingVO> listInvoicingByCustomerId(List<Long> customerIdList);

    /**
     * 编辑客户时，如果未传开票信息数据，则需删除旧的开票信息数据
     * @param customerId
     * @param name
     */
    void deleteBatchInvoicing(Long customerId, String name);

    /**
     * 根据客户ID删除
     * @param bizIds 客户ID
     */
    void deleteByCustomerIds(List<Long> bizIds);
}
