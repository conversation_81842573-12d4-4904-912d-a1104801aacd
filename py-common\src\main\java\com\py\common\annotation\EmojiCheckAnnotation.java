package com.py.common.annotation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
/**
 * <AUTHOR>
 * 表情符号校验
 */
@Target( {ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EmojiCheckAnnotationImpl.class)
public @interface EmojiCheckAnnotation {
    String message() default "参数中请勿包含表情";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
