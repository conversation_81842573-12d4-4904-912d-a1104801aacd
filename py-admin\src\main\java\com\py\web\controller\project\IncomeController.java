package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.exception.ServiceException;
import com.py.common.oss.IOssService;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.project.income.domain.dto.IncomeExportModel;
import com.py.project.income.domain.eunm.MethodType;
import com.py.project.income.domain.query.IncomeQuery;
import com.py.project.income.domain.vo.IncomeCountVO;
import com.py.project.income.domain.vo.IncomeListVO;
import com.py.project.income.domain.vo.IncomeVO;
import com.py.project.income.service.IIncomeService;
import com.py.project.income.service.impl.IncomeServiceImpl;
import com.py.project.incomeenregister.domain.dto.IncomeEnregisterBatchDTO;
import com.py.project.incomeenregister.domain.dto.IncomeEnregisterDTO;
import com.py.project.incomeenregister.service.IIncomeEnregisterService;
import com.py.project.primecost.domin.vo.PaymentTypeCountVO;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 项目管理-收入管理Controller
 *
 * <AUTHOR>
 * @date 2023-08-11
 */
@Api(tags = "项目管理-收入管理")
@RestController
@RequestMapping("/project/income")
public class IncomeController extends BaseController {

    /** 项目管理-收入管理服务 */
    @Resource
    private IIncomeService incomeService;

    /** 项目管理-收入登记Service接口 */
    @Resource
    private IIncomeEnregisterService iIncomeEnregisterService;

    /** 系统设置-主体参数设置服务 */
    @Resource
    private ISystemMainstayParamService systemMainstayParamService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 对象存储服务 */
    @Resource
    private IOssService ossService;

    /**
     * 分页查询项目管理-收入管理列表
     *
     * @param query 项目管理-收入管理查询参数
     * @return 项目管理-收入管理分页
     */
    @ApiOperation("分页查询询项目管理-收入管理列表")
    @PreAuthorize("@ss.hasPermi('com.py.project:income:list')")
    @GetMapping("/pageIncome")
    public R<PageInfo<IncomeListVO>> pageIncome(@Validated IncomeQuery query) {
        PageInfo<IncomeListVO> voList = this.incomeService.pageIncomeList(query);
        return R.success(voList);
    }

    /**
     * 查询项目/财务管理-收入管理列表,查询立项人部门
     *
     * @param query 立项人部门
     * @return 项目/财务管理-收入管理(立项人部门)
     */
    @ApiOperation("分页查询询项目管理-收入管理列表")
    @PostMapping("/findIncomeCreateDept")
    public R<List<String>> findIncomeCreateDept(@RequestBody IncomeQuery query) {
        return R.success(this.incomeService.findIncomeCreateDept(query));
    }

    /**
     * 获取项目管理-收入管理详细信息
     * @param query 项目管理-收入管理主键
     * @return 项目管理-收入管理视图模型
     */
    @ApiOperation("获取项目管理-收入管理详细信息")
    @PreAuthorize("@ss.hasPermi(' com.py.project:income:query')")
    @GetMapping(value = "/getInfo")
    public R<IncomeVO> getInfo(IncomeQuery query) {
        return R.success(incomeService.selectIncomeById(query));
    }

    /**
     * 项目管理-下载
     * @param query 导出查询参数
     * @return 文件key
     */
    @ApiOperation("项目管理-下载")
    @PreAuthorize("@ss.hasPermi('com.py.project:income:export')")
    @Log(title = "项目管理-收入管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@Validated @RequestBody IncomeQuery query) {
        String fileName = "收入管理列表数据-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setFinanceSedMenu(1);
        query.setMethod(MethodType.DOWNLOAD);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("项目管理-收入管理", TaskType.Export,query, IncomeServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 项目管理-下载(财务)
     * @param query 导出查询参数
     * @return 文件key
     */
    @ApiOperation("项目管理-下载(财务)")
    @PreAuthorize("@ss.hasPermi('com.py.project:income:exporFinancet')")
    @Log(title = "项目管理-收入管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exporFinancet")
    public R<OssUploadResult> exporFinancet(@Validated @RequestBody IncomeQuery query) {
        if(query.getPaiyaMainstayId() == null){
            throw new ServiceException("派芽合作主体为空");
        }
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPaiyaMainstayId());
        String fileName = "收入管理列表数据-" + systemMainstayParamVO.getMainstayName() + "-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setFinanceSedMenu(0);
        query.setMethod(MethodType.DOWNLOAD);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("收入管理(财务)", TaskType.Export,query, IncomeServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 收入管理-收入登记
     * @param dto 需要删除的项目管理-收入管理主键集合
     * @return 是否成功
     */
    @ApiOperation("收入管理-收入登记")
    @PreAuthorize("@ss.hasPermi('project:income:insertIncomeEnregister')")
    @Log(title = "项目管理-收入管理", businessType = BusinessType.INSERT)
    @PostMapping("/insertIncomeEnregister" )
    public R<Boolean> insertIncomeEnregister(@Valid @RequestBody IncomeEnregisterBatchDTO dto) {
        return R.success(iIncomeEnregisterService.insertIncomeEnregister(dto));
    }

    /**
     * 收入管理-收入登记更新
     * @param dto 需要删除的项目管理-收入管理主键集合
     * @return 是否成功
     */
    @ApiOperation("收入管理-收入登记更新")
    @PreAuthorize("@ss.hasPermi('project:income:editIncomeEnregister')")
    @Log(title = "项目管理-收入管理", businessType = BusinessType.UPDATE)
    @PostMapping("/editIncomeEnregister" )
    public R<Boolean> editIncomeEnregister(@Valid @RequestBody IncomeEnregisterDTO dto) {
        return R.success(iIncomeEnregisterService.updateIncomeEnregister(dto));
    }

    /**
     * 收入管理-收入登记列表
     * @param query 需要删除的项目管理-收入管理主键集合
     * @return 是否成功
     */
    @ApiOperation("收入管理-收入登记列表")
    @PreAuthorize("@ss.hasPermi('project:income:listIncomeEnregister')")
    @GetMapping("/listIncomeEnregister" )
    public R<PageInfo<IncomeListVO>> listIncomeEnregister(@Validated IncomeQuery query) {
        return R.success(incomeService.listIncomeEnregister(query));
    }

    /**
     * 收入管理-合计
     * @param query 需要删除的项目管理-收入管理查询参数
     * @return 合计
     */
    @ApiOperation("收入管理-合计")
    @PreAuthorize("@ss.hasPermi('project:income:countIncomeCount')")
    @PostMapping("/countIncomeCount" )
    public R<IncomeCountVO> countIncomeCount(@Validated @RequestBody IncomeQuery query){
        return R.success(incomeService.countIncomeCount(query));
    }

    /**
     * 头部统计
     *
     * @return 头部统计
     */
    @ApiOperation("头部统计")
    @PreAuthorize("@ss.hasPermi('prime:cost:getIncomePaymentCount')")
    @GetMapping("/getIncomePaymentCount")
    public R<PaymentTypeCountVO> getIncomePaymentCount(@Validated IncomeQuery query){
        return R.success(incomeService.getIncomePaymentCount(query));
    }

    /**
     * 项目管理-下载导入模板
     * @param response 导出查询参数
     * @return 文件key
     */
    @ApiOperation("项目管理-下载导入模板")
    @PreAuthorize("@ss.hasPermi('project:income:downloadTemplate')")
    @Log(title = "项目管理-收入管理", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadTemplate")
    public void downloadTemplate( HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/收入登记批量导入模板.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("收入管理导入模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = null;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 项目管理-导入收入登记
     * @param file 文件
     * @return 文件key
     */
    @ApiOperation("项目管理-导入收入登记")
    @PreAuthorize("@ss.hasPermi('project:income:importIncome')")
    @Log(title = "项目管理-收入管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importIncome")
    public R<String> importIncome(MultipartFile file) throws Exception  {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xls"))) {
            throw new ServiceException("请上传正确的文件格式");
        }
        String originalFilename = file.getOriginalFilename();
        ExcelUtil<IncomeExportModel> util = new ExcelUtil<>(IncomeExportModel.class);
        List<IncomeExportModel> incomeExportModelList = util.importExcel(file.getInputStream(),0);
        if(ListUtil.isEmpty(incomeExportModelList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }

        OssUploadResult ossUploadResult = ossService.upload(file.getInputStream(), "收入管理导入.xlsx", false);
        IncomeQuery query = new IncomeQuery();
        query.setFileKey(ossUploadResult.getOssKey());
        query.setFileName(originalFilename);
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setMethod(MethodType.IMPORT);
        reusableAsyncTaskService.addTask("收入管理(财务)", TaskType.Import, query, IncomeServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 头部统计
     *
     * @return 头部统计
     */
    @GetMapping("/edit")
    public R<Boolean> edit(@Validated IncomeQuery query) {
        return R.success(incomeService.updateProjectIncome(query.getProjectIdList().get(0)));
    }
}
