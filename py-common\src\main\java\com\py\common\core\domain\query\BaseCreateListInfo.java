package com.py.common.core.domain.query;

import com.py.common.core.domain.query.BaseUpdateQuery;
import com.py.common.utils.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * 创建者
 * <AUTHOR>
 * @version BaseCreateListInfo 2023/8/2 14:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseCreateListInfo extends BaseUpdateQuery {
    /** 创建人 */
    @ApiModelProperty("创建人")
    private List<Long> createUserIdList;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private List<String> createUserList;

    /** 创建人部门 */
    @ApiModelProperty("创建人部门")
    private List<Long> createDeptIdList;

    /** 创建人部门 */
    @ApiModelProperty("创建人部门")
    private List<String> createDeptList;

    /** 创建时间-开始 */
    @ApiModelProperty("创建时间-开始")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate startCreateDate;

    /** 创建时间-结束 */
    @ApiModelProperty("创建时间-结束")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate endCreateDate;
}
