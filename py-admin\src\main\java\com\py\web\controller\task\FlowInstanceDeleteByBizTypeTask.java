package com.py.web.controller.task;

import com.py.common.utils.collection.ListUtil;
import com.py.flow.flowinstance.domain.FlowInstance;
import com.py.flow.flowinstance.service.IFlowInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 删除作废记录-定时任务
 * @date 2023/9/18 11:05
 */
@Slf4j
@Component
@EnableScheduling
public class FlowInstanceDeleteByBizTypeTask {

    /**
     * 流程记录服务
     */
    @Resource
    private IFlowInstanceService flowInstanceService;

    /**
     * 删除30天前审批作废的数据
     */
    @Scheduled(cron = "0 10 0 * * ?")
    public void deleteFlow() {
        //30天  作废的数据
        int day = 30;
        // 测试数据删除今天以前所有作废数据
//        int day = 0;
        List<FlowInstance> list = this.flowInstanceService.selectDeleteFlow(day);
        log.info("每月审批作废删除：记录当前开启时间");
        List<Long> idList = ListUtil.distinctMap(list,FlowInstance::getBizId);
        log.info("审批作废审批流id"+idList+"有"+list.size()+"条数据");
        if (ListUtil.isNotEmpty(list)){
            this.flowInstanceService.deleteFlowData(list);
        }
        log.info("每月审批作废删除：记录当前结束时间");
    }
}
