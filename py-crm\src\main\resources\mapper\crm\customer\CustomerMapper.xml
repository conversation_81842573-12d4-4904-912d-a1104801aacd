<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.py.crm.customer.mapper.CustomerMapper">

    <resultMap type="com.py.crm.customer.domain.SupCustomer" id="CrmCustomerResult">
        <result property="id" column="id" />
        <result property="customerId" column="customer_id" />
        <result property="name" column="name" />
        <result property="lineBusiness" column="line_business" />
        <result property="cooperationStatus" column="cooperation_status" />
        <result property="changeStatusReason" column="change_status_reason" />
        <result property="customerSource" column="customer_source" />
        <result property="customerType" column="customer_type" />
        <result property="brandStage" column="brand_stage" />
        <result property="cooperativeSector" column="cooperative_sector" />
        <result property="businessSource" column="business_source" />
        <result property="serviceProduct" column="service_product"
                typeHandler="com.py.common.typehandler.impl.StringSetTypeHandler" />
        <result property="biddingStrategy" column="bidding_strategy" />
        <result property="decisionLink" column="decision_link" />
        <result property="remark" column="remark" />
        <result property="version" column="version" />
        <result property="auditStatus" column="audit_status" />
        <result property="auditTime" column="audit_time" />
        <result property="serviceUserId" column="service_user_id" />
        <result property="createId" column="create_id" />
        <result property="createBy" column="create_by" />
        <result property="createDept" column="create_dept" />
        <result property="createTime" column="create_time" />
        <result property="updateId" column="update_id" />
        <result property="updateBy" column="update_by" />
        <result property="updateDept" column="update_dept" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
    </resultMap>

<!--  客户统计  -->
    <select id="customerCount" resultType="com.py.crm.customer.domain.vo.CustomerCountVO">
        SELECT
            count(Distinct customer_id) as total,
            count(Distinct IF (cooperation_status = 0 ,customer_id,null )) as cooperation,
            count(Distinct IF (cooperation_status = 1 ,customer_id,null )) as suspensionCooperation,
            count(Distinct IF (cooperation_status = 2 ,customer_id,null )) as intentionCooperate
        FROM
            `py_crm_customer`
        where
            del_flag = 0
          and audit_status = 1
        <if test="dataScope != null and dataScope != ''">
            and customer_id IN (select Distinct customer_id from py_crm_wander_about where del_flag = 0 and is_distribution = 1  ${dataScope} )
        </if>
        <if test="dataScope == null || dataScope == ''">
            and customer_id IN (select Distinct customer_id from py_crm_wander_about where del_flag = 0)
        </if>
    </select>
<!--  品牌/业务线唯一校验  -->
    <select id="unique" resultType="com.py.crm.customer.domain.SupCustomer">
        SELECT
               customer_id,
               name,
               line_business,
               industry_category_id,
               cooperation_status,
               change_status_reason
        FROM
            `py_crm_customer`
        where
            line_business = #{lineBusiness}
        and del_flag = 0
        and audit_status in (-1,0,1)
        limit
        1
    </select>
<!--    客户贡献列表按成交金额倒序-->
    <select id="getCustomerDevoteList" resultType="com.py.crm.customer.domain.SupCustomer">
        Select
            customer.customer_id,
            COALESCE ( sum( project.project_amount ), 0 ) AS transactionMoney,
            min(customer.`name`) as name,
            min(customer.`line_business`) as lineBusiness,
            min(customer.`industry_category_id`) as industryCategorId
        From py_crm_customer customer
        Left join py_project project on project.customer_id = customer.customer_id
        where customer.del_flag = '0' and project.del_flag='0'
        <!--  客户名称 -->
        <if test="query.name != null and query.name != ''">
            AND customer.name LIKE CONCAT('%', #{query.name}, '%')
        </if>
        <!-- 品牌业务线 -->
        <if test="query.lineBusiness != null and query.lineBusiness != ''">
            AND customer.line_business LIKE CONCAT('%', #{query.lineBusiness}, '%')
        </if>
        <if test="query.cooperateBeginDate != null and query.cooperateEndDate != null">
            AND project.project_approval_time &gt;= #{query.cooperateBeginDate}
            AND project.project_approval_time &lt;= #{query.cooperateEndDate}
        </if>
        <if test="query.closeCaseBeginDate != null and query.closeCaseEndDate != null">
            AND project.close_case_time &gt;= #{query.closeCaseBeginDate}
            AND project.close_case_time &lt;= #{query.closeCaseEndDate}
        </if>

        <if test="query.customerIds != null and query.customerIds.size()>0">
              and project.customer_id in
              <foreach collection="query.customerIds" item="customerIid" open="(" close=")" separator=",">
                  #{customerIid}
              </foreach>
         </if>
        <if test="query.notCustomerIds != null and query.notCustomerIds.size()>0">
            and project.customer_id not in
            <foreach collection="query.notCustomerIds" item="customerIid" open="(" close=")" separator=",">
                #{customerIid}
            </foreach>
        </if>
        <if test="query.dataScope != null and query.dataScope != ''">
            ${query.dataScope}
        </if>
        <if test="customerDataScopeWrapper.nonEmptyOfWhere()">
            and project.customer_id IN (
                select customer_id
                from py_crm_wander_about
                ${customerDataScopeWrapper.customSqlSegment}
                and del_flag = 0 and is_distribution = 1
            )
        </if>
        Group by customer.customer_id
        Order by transactionMoney desc
    </select>
    <!--    比稿金额附带项目结案合作时间-->
    <select id="getCustomerDevoteProjectList" resultType="com.py.crm.customer.domain.vo.ComparedDraftMoneyVO">
        select draft.customer_id customerId,
               COALESCE(sum(compared_draft_money),0) comparedDraftMoneySum
        from py_crm_compared_draft draft
            join py_project project on draft.compared_draft_id = project.compared_draft_id
        where draft.del_flag = '0'
          and project.del_flag = '0'
        <if test="query.customerIds != null and query.customerIds.size > 0 ">
            and draft.customer_id in
            <foreach collection="query.customerIds" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
        <!-- 合作时间 -->
        <if test="query.cooperateBeginDate != null and query.cooperateEndDate != null">
            AND project.project_approval_time &gt;= #{query.cooperateBeginDate}
            AND project.project_approval_time &lt;= #{query.cooperateEndDate}
        </if>
        <!-- 结案时间 -->
        <if test="query.closeCaseBeginDate != null and query.closeCaseEndDate != null">
            AND project.close_case_time &gt;= #{query.closeCaseBeginDate}
            AND project.close_case_time &lt;= #{query.closeCaseEndDate}
        </if>
        group by draft.customer_id
    </select>

    <!--  客户比稿金额 -->
    <select id="getCustomerComparedList" resultType="com.py.crm.customer.domain.vo.ComparedDraftMoneyVO">
        select draft.customer_id customerId,
                COALESCE(sum(compared_draft_money),0) comparedDraftMoneySum
        from py_crm_compared_draft draft
        where draft.del_flag = '0'
        <if test="query.customerIds != null and query.customerIds.size > 0 ">
            and draft.customer_id in
            <foreach collection="query.customerIds" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
        group by draft.customer_id
    </select>
<!--    查询项目id-->
    <select id="getCustomerDevoteProjectIdList" resultType="java.lang.Long">
        SELECT
        project.project_id
        FROM
        py_project project
        where project.del_flag ='0'
        and project.audit_status = 1
        and project.customer_id in
        <foreach collection="customerIds" item="customerId" open="(" close=")" separator=",">
            #{customerId}
        </foreach>
        <if test="cooperateBeginDate != null and cooperateEndDate != null">
            AND project.project_approval_time &gt;= #{cooperateBeginDate}
            AND project.project_approval_time &lt;= #{cooperateEndDate}
        </if>
        <if test="closeCaseBeginDate != null and closeCaseEndDate != null">
            AND project.project_id IN (
            select project_id
            from py_project_close_case_details
            where del_flag = 0
            AND update_time &gt;= #{closeCaseBeginDate}
            AND update_time &lt;= #{closeCaseEndDate}
            AND update_time &gt;= #{closeCaseBeginDate}
            AND update_time &lt;= #{closeCaseEndDate}
            )
        </if>
    </select>

    <!--坏账金额-->
    <select id="getBadeMoneyMap" resultType="com.py.crm.customerdevote.domain.vo.CustomerDevoteListVO">
        SELECT
            project.customer_id as customerId,
            COALESCE(SUM( bad.bad_debt_amount ),0) as badDebtMoney
        FROM
            `py_income_bad_debt` bad
            LEFT JOIN py_project project ON bad.project_id = project.project_id
        WHERE
               bad.del_flag = 0
          AND  bad.approval_date IS NOT NULL
        <if test="query.projectIds != null and !query.projectIds.isEmpty()">
            and project.project_id in
            <foreach collection="query.projectIds" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
        <!-- 合作时间 -->
        <if test="query.cooperateBeginDate != null and query.cooperateEndDate != null">
            AND project.project_approval_time &gt;= #{query.cooperateBeginDate}
            AND project.project_approval_time &lt;= #{query.cooperateEndDate}
        </if>
        <!-- 结案时间 -->
        <if test="query.closeCaseBeginDate != null and query.closeCaseEndDate != null">
            AND project.close_case_time &gt;= #{query.closeCaseBeginDate}
            AND project.close_case_time &lt;= #{query.closeCaseEndDate}
        </if>
        GROUP BY
            project.customer_id
    </select>
<!--毛利率-->
    <select id="getCloseProfitMarginList" resultType="com.py.crm.customerdevote.domain.vo.CloseCaseMoneyListVO">
        SELECT
        customer_id, sum(margin) /count(0) as margin, count(0) as num
        from
        (SELECT
        project.project_id,
        project.customer_id,
        IF(COALESCE(SUM(resource.including_tax_price), 0) + COALESCE(SUM(resource.service_profit), 0) = 0, 0,
        (
        COALESCE(SUM(resource.resource_profit), 0) + COALESCE(SUM(resource.service_profit), 0) +
        COALESCE(SUM(resource.rebate_profit), 0)
        )
            /
        ( COALESCE(SUM(resource.including_tax_price), 0) + COALESCE(SUM(resource.service_profit), 0) )) AS margin
        FROM
        `py_project_close_case_details` colse
        LEFT JOIN py_project_resource resource ON colse.project_id = resource.project_id
        LEFT JOIN py_project project ON colse.project_id = project.project_id
        WHERE
        colse.del_flag = 0
        AND colse.audit_status = 1
        AND resource.del_flag = 0
        AND project.project_id
        in
        <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
            #{projectId}
        </foreach>
        GROUP BY
        project.project_id , project.customer_id) temp group by customer_id
    </select>
<!--  分页客户管理-客户列表  -->
    <select id="pageCustomerList" resultType="com.py.crm.customer.domain.SupCustomer">
        SELECT DISTINCT
            customer.id, customer.customer_id, customer.name, customer.line_business, customer.industry_category_id,
            customer.cooperation_status, customer.change_status_reason, customer.customer_source,
            customer.customer_type, customer.brand_stage, customer.cooperative_sector,
            customer.business_source, customer.service_product, customer.bidding_strategy,
            customer.decision_link, customer.remark, customer.version, customer.audit_status,customer.audit_time,
            customer.service_user_id, customer.create_id, customer.create_by,
            customer.create_dept, customer.create_time, customer.update_id,
            customer.update_by, customer.update_dept, customer.update_time, customer.del_flag
        FROM
            `py_crm_customer` customer
        Left join `py_crm_customer_category` category on customer.customer_id = category.customer_id
        Left join py_crm_wander_about wanderAbout on customer.customer_id = wanderAbout.customer_id
        where
        customer.del_flag = 0
          and category.del_flag = 0
          and wanderAbout.del_flag = 0
          and customer.audit_status = 1
        <if test="query.serviceUserIdList != null and query.serviceUserIdList.size() > 0">
            and wanderAbout.is_create = 0
            and wanderAbout.user_id in
            (Select user_id From sys_user where del_flag = 0 and user_id in
            <foreach collection="query.serviceUserIdList" item="serviceUserId" open="(" close=")" separator=",">
                #{serviceUserId}
            </foreach>)
        </if>
        <if test="query.serviceDeptIdList != null and query.serviceDeptIdList.size() > 0">
            and wanderAbout.is_create = 0
            and wanderAbout.is_distribution = 1
            and wanderAbout.user_id in (Select Distinct ud.user_id From sys_user_dept ud join sys_dept dept on ud.dept_id = dept.dept_id
            WHERE
            dept.del_flag = 0
            and dept.dept_name in
              <foreach collection="query.serviceDeptIdList" item="serviceDept" open="(" close=")" separator=",">
                    #{serviceDept}
              </foreach>
            )
        </if>
        <if test="query.dataScope != null and query.dataScope != ''">
            and wanderAbout.is_distribution = 1
            ${query.dataScope}
        </if>
        <if test="query.industryCategoryIdList != null and query.industryCategoryIdList.size() > 0">
            AND category.industry_category_id IN
            <foreach collection="query.industryCategoryIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="ew.isEmptyOfNormal() != true">
            and
            <trim prefixOverrides="WHERE">
                ${ew.customSqlSegment}
            </trim>
        </if>

        <!-- 云客户-客户管理列表搜索客户名称,业务线下拉框 -->
        <if test="query.downStatus">
            and
            (
            <trim prefixOverrides="or">
                <if test="query.downCustomerIdList != null and query.downCustomerIdList.size>0">
                    customer.customer_id in
                    <foreach collection="query.downCustomerIdList" item="customerId" open="(" close=")" separator=",">
                        #{customerId}
                    </foreach>
                </if>
                <if test="query.downCustomerNameList != null and query.downCustomerNameList.size>0">
                    or customer.name in
                    <foreach collection="query.downCustomerNameList" item="customerName" open="(" close=")" separator=",">
                        #{customerName}
                    </foreach>
                </if>
                <if test="query.downBusinessLineNameList != null and query.downBusinessLineNameList.size>0">
                    or customer.line_business in
                    <foreach collection="query.downBusinessLineNameList" item="businessLineName" open="(" close=")" separator=",">
                        #{businessLineName}
                    </foreach>
                </if>
            </trim>
            )
        </if>
        ORDER BY
        customer.create_time desc
    </select>
<!--  客户下拉  -->
    <select id="pollDown" resultType="com.py.crm.customer.domain.SupCustomer">
        SELECT
            id, customer_id, name, line_business, industry_category_id,
            cooperation_status, change_status_reason, customer_source,
            customer_type, brand_stage, cooperative_sector,
            business_source, service_product, bidding_strategy,
            decision_link, remark, version, audit_status,audit_time,
            service_user_id, create_id, create_by,
            create_dept, create_time, update_id,
            update_by, update_dept, update_time
        FROM
            `py_crm_customer`
            where del_flag = 0
        <if test="query.dataScope != null and query.dataScope != ''">
            and customer_id IN (select customer_id from py_crm_wander_about where del_flag = 0 and is_distribution = 1  ${query.dataScope} )
        </if>
        <if test="ew.isEmptyOfNormal() != true">
            and
            <trim prefixOverrides="WHERE">
                ${ew.customSqlSegment}
            </trim>
        </if>
    </select>
<!--  根据权限查询当前角色权限范围内的客户ID  -->
    <select id="getJudgmentCustomerId" resultType="java.lang.Long">
        select wanderAbout.customer_id from py_crm_wander_about wanderAbout
        LEFT Join  `py_crm_customer` customer on wanderAbout.customer_id = customer.customer_id
        where wanderAbout.del_flag = 0 and customer.del_flag = 0 and is_distribution = 1 and customer.audit_status = 1
        <if test="customerIdList != null and customerIdList.size() > 0">
            and wanderAbout.customer_id in
            <foreach collection="customerIdList" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
        <if test="dataScope != null and dataScope != ''">
            ${dataScope}
        </if>

    </select>

    <!--根据客户id查询客户信息-->
    <select id="selectByCustomerId" resultMap="CrmCustomerResult">
        select customer_id, line_business
        from py_crm_customer
        where customer_id = #{customerId}
    </select>
    <!-- 结案金额 -->
    <select id="getCloseCaseMoneyList" resultType="com.py.crm.customerdevote.domain.vo.CloseCaseMoneyListVO">
        SELECT
        project.customer_id,
        COALESCE(SUM(ifnull(resource.including_tax_price,0)+ ifnull(resource.service_profit,0)),0) as closeCaseMoney ,
        COALESCE(SUM(ifnull(resource.resource_profit,0)+ifnull(resource.service_profit,0)+ifnull(resource.rebate_profit,0)),0) as profitMoney
        FROM
        `py_project_close_case_details`colse
        LEFT JOIN py_project_resource resource ON colse.project_id=resource.project_id
        LEFT JOIN py_project project on colse.project_id=project.project_id
        WHERE
        colse.del_flag=0
        AND resource.del_flag=0
        AND colse.audit_status=1
        and project.project_id
        in
        <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
            #{projectId}
        </foreach>
        GROUP BY
        project.customer_id
    </select>

    <select id="getCustomerByProjectId" resultType="com.py.crm.customer.domain.vo.CrmCustomerProjectVO">
        select
            draft.customer_id customerId,
            compared_draft_money,
            project_id,
            project.compared_draft_id
        from py_crm_compared_draft draft
        join py_project project on draft.compared_draft_id = project.compared_draft_id
        where draft.del_flag = '0'
        and project.del_flag = '0'
        <if test="projectIdList != null and projectIdList.size > 0 ">
            and project.project_id in
            <foreach collection="projectIdList" item="projectId" open="(" close=")" separator=",">
                #{projectId}
            </foreach>
        </if>
    </select>


    <select id="getCustomerComparedInfoList" resultType="com.py.crm.customer.domain.vo.ComparedDraftMoneyVO">
        select
            draft.customer_id customerId,
            compared_draft_money,
            draft.compared_draft_id
        from py_crm_compared_draft draft
        where draft.del_flag = '0'
        <if test="query.customerIds != null and query.customerIds.size > 0 ">
            and draft.customer_id in
            <foreach collection="query.customerIds" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
    </select>

    <!-- 根据客户ID查询客户信息 -->
    <select id="listCustomerIdByCustomerIds" resultType="com.py.crm.customer.domain.SupCustomer">
        SELECT
            customer.*
        FROM
            py_crm_customer customer
                JOIN py_crm_customer_laundry laundry ON customer.customer_id = laundry.biz_id
        WHERE
        <if test="customerIdList != null and customerIdList.size > 0">
             laundry.laundry_id in
            <foreach collection="customerIdList" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>
        ORDER BY
            CASE
                cooperation_status
                WHEN 0 THEN
                    0
                WHEN 1 THEN
                    2
                WHEN 2 THEN
                    1 ELSE 0
                END,
            customer.create_time DESC
    </select>

    <select id="userCustomerList" resultType="com.py.crm.customer.domain.SupCustomer">
        SELECT DISTINCT
        customer.id, customer.customer_id, customer.name, customer.line_business, customer.industry_category_id,
        customer.cooperation_status, customer.change_status_reason, customer.customer_source,
        customer.customer_type, customer.brand_stage, customer.cooperative_sector,
        customer.business_source, customer.service_product, customer.bidding_strategy,
        customer.decision_link, customer.remark, customer.version, customer.audit_status,customer.audit_time,
        customer.service_user_id, customer.create_id, customer.create_by,
        customer.create_dept, customer.create_time, customer.update_id,
        customer.update_by, customer.update_dept, customer.update_time, customer.del_flag
        FROM
        `py_crm_customer` customer
        Left join py_crm_wander_about wanderAbout on customer.customer_id = wanderAbout.customer_id
        where
        customer.del_flag = 0
        and wanderAbout.del_flag = 0
        and customer.audit_status = 1
        <if test="query.dataScope != null and query.dataScope != ''">
            and wanderAbout.is_distribution = 1
            ${query.dataScope}
        </if>
        <if test="ew.isEmptyOfNormal() != true">
            and
            <trim prefixOverrides="WHERE">
                ${ew.customSqlSegment}
            </trim>
        </if>

    </select>

    <select id="findCustomerCreateDeptList" resultType="com.py.crm.customer.domain.SupCustomer">
        SELECT DISTINCT  customer.create_dept
        FROM
        `py_crm_customer` customer
        Left join `py_crm_customer_category` category on customer.customer_id = category.customer_id
        Left join py_crm_wander_about wanderAbout on customer.customer_id = wanderAbout.customer_id
        where
        customer.del_flag = 0
        and category.del_flag = 0
        and wanderAbout.del_flag = 0
        and customer.audit_status = 1
        <if test="query.dataScope != null and query.dataScope != ''">
            and wanderAbout.is_distribution = 1
            ${query.dataScope}
        </if>
        <if test="ew.isEmptyOfNormal() != true">
            and
            <trim prefixOverrides="WHERE">
                ${ew.customSqlSegment}
            </trim>
        </if>
    </select>

    <select id="findCustomerServiceDeptList" resultType="com.py.crm.customer.domain.SupCustomer">
        SELECT DISTINCT  dept.dept_name as createDept
        FROM
        `py_crm_customer` customer
        LEFT JOIN `py_crm_customer_category` category ON customer.customer_id = category.customer_id
        LEFT JOIN py_crm_wander_about wanderAbout ON customer.customer_id = wanderAbout.customer_id
        left join sys_user_dept ud on wanderAbout.user_id = ud.user_id
        LEFT JOIN sys_dept AS dept ON ud.dept_id = dept.dept_id
        where
        customer.del_flag = 0
        and category.del_flag = 0
        and wanderAbout.del_flag = 0
        and customer.audit_status = 1
        and wanderAbout.is_create = 0
        and wanderAbout.is_distribution = 1
        and dept.del_flag = 0
        and dept.dept_name is not null
        <if test="query.dataScope != null and query.dataScope != ''">
            and wanderAbout.is_distribution = 1
            ${query.dataScope}
        </if>
        <if test="ew.isEmptyOfNormal() != true">
            and
            <trim prefixOverrides="WHERE">
                ${ew.customSqlSegment}
            </trim>
        </if>
    </select>

</mapper>
