package com.py.crm.connection.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人脉管理表列表数量视图模型
 * <AUTHOR>
 */
@Data
@ApiModel("人脉管理表列表数量视图模型")
public class ConnectionCount {
    /**
     * 无效数
     */
    @ApiModelProperty("无效数")
    private Long invalidCount;
    /**
     * 有效数
     */
    @ApiModelProperty("有效数")
    private Long validCount;
}
