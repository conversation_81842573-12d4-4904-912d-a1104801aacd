package com.py.web.controller.project;


import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.exception.ServiceException;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.collection.ListUtil;
import com.py.project.enums.CooperationStatus;
import com.py.project.enums.ExportType;
import com.py.project.project.domain.query.ProjectQuery;
import com.py.project.projectbatchupdaterecord.domain.query.BatchUpdateApprovalListQuery;
import com.py.project.projectbatchupdaterecord.domain.vo.ProjectBatchUpdateAddRecordVO;
import com.py.project.projectbatchupdaterecord.domain.vo.ProjectBatchUpdateRecordVO;
import com.py.project.projectbatchupdaterecord.domain.vo.ResourceBatchUpdateDetailsAddVO;
import com.py.project.projectbatchupdaterecord.domain.vo.ResourceBatchUpdateVO;
import com.py.project.projectresource.batchupdate.domain.vo.CommitBatchUpdateVO;
import com.py.project.projectresource.comfirmation.excel.search.model.ImportResourceSelectModel;
import com.py.project.projectresource.comfirmation.excel.search.model.ImportResourceSelectVO;
import com.py.project.projectresource.domain.dto.*;
import com.py.project.projectresource.domain.query.*;
import com.py.project.projectresource.domain.vo.*;
import com.py.project.projectresource.excel.search.ProjectResourceSearchImporter;
import com.py.project.projectresource.excel.search.model.ImportProjectResourceQuery;
import com.py.project.projectresource.excel.search.model.ImportProjectResourceSelectModel;
import com.py.project.projectresource.excel.search.model.ImportProjectResourceSelectVO;
import com.py.project.projectresource.passInfo.domain.excel.ImportProjectResourcePassInfoModel;
import com.py.project.projectresource.passInfo.domain.vo.ResourcePassInfoVO;
import com.py.project.projectresource.passInfo.search.ResourcePassInfoSearchImporter;
import com.py.project.projectresource.passInfo.service.IProjectResourcePassInfoService;
import com.py.project.projectresource.service.IProjectResourceService;
import com.py.project.projectresource.service.impl.ProjectResourceServiceImpl;
import com.py.project.projectupdatestatusdetails.service.impl.ProjectUpdateStatusDetailsServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

/**
 * 项目资源表Controller
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@Api(tags = "项目资源表")
@RestController
@RequestMapping("/projectresource")
public class ProjectResourceController extends BaseController {

    /**
     * 项目资源表服务
     */
    @Resource
    private IProjectResourceService projectResourceService;

    /**项目资源表导入查询*/
    @Resource
    private ProjectResourceSearchImporter projectResourceSearchImporter;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 批量确认收入service */
    @Resource
    private IProjectResourcePassInfoService projectResourcePassInfoService;

    /**批量通过导入赋值凭证 */
    @Resource
    private ResourcePassInfoSearchImporter resourcePassInfoSearchImporter;

    /**
     * 查询项目资源表列表
     *
     * @param query 项目资源表查询参数
     * @return 项目资源表列表
     */
    @ApiOperation("查询项目资源表列表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:list')")
    @GetMapping("/listProjectResource")
    public R<List<ProjectResourceListVO>> listProjectResource(ProjectResourceQuery query) {
        List<ProjectResourceListVO> voList = this.projectResourceService.listProjectResource(query);
        return R.success(voList);
    }

    /**
     * 分页查询项目资源表列表
     *
     * @param query 项目资源表查询参数
     * @return 项目资源表分页
     */
    @ApiOperation("分页查询询项目资源表列表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:list')")
    @PostMapping("/pageProjectResource")
    public R<PageInfo<ProjectResourceListVO>> pageProjectResource(@RequestBody ProjectResourceQuery query) {
        PageInfo<ProjectResourceListVO> voList = this.projectResourceService.pageProjectResourceList(query);
        return R.success(voList);
    }

    /**
     * 执行交接:分页查询项目资源表列表(执行表单信息)
     *
     * @param query 项目查询参数
     * @return 项目资源表分页
     */
    @ApiOperation("分页查询询项目资源表列表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:list')")
    @PostMapping("/pageProjectResourceForWorkHandover")
    public R<PageInfo<ProjectResourceListVO>> pageProjectResource(@RequestBody ProjectQuery query) {
        PageInfo<ProjectResourceListVO> voList = this.projectResourceService.queryResourceListByProjectId(query);
        return R.success(voList);
    }

    /**
     * 获取项目资源表详细信息
     *
     * @param id 项目资源表主键
     * @return 项目资源表视图模型
     */
    @ApiOperation("获取项目资源表详细信息")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @GetMapping(value = "/{id}")
    public R<ProjectResourceVO> getInfo(@PathVariable("id") Long id) {
        return R.success(projectResourceService.selectProjectResourceById(id));
    }

    /**
     * 执行表单合计
     *
     * @param query 查询参数
     * @return 项目资源表视图模型
     */
    @ApiOperation("执行表单合计")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @GetMapping(value = "/countResourceVO")
    public R<ResourceCountVO> countResourceVO(ResourceCountQuery query) {
        return R.success(projectResourceService.countResourceVO(query));
    }

    /**
     *  打印执行表单合计数据
     *
     * @param query 查询参数
     * @return 项目资源表视图模型
     */
    @ApiOperation("执行表单合计打印数据")
    @GetMapping(value = "/print/countResourceVO")
    public R<ResourceCountVO> printCountResourceVO(ResourceCountQuery query) {
        return R.success(projectResourceService.countResourceVO(query));
    }


    /**
     * 保存项目资源表
     *
     * @param dto 项目资源表修改参数
     * @return 是否成功
     */
    @ApiOperation("保存项目资源表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:add')")
    @Log(title = "项目资源表", businessType = BusinessType.INSERT)
    @PostMapping("/saveProjectResource")
    public R<List<ProjectResourceDTO>> saveProjectResource(@RequestBody List<ProjectResourceDTO> dto) {
        return R.success(projectResourceService.saveProjectResource(dto));
    }

    /**
     * 修改项目资源表
     *
     * @param dto 项目资源表修改参数
     * @return 是否成功
     */
    @ApiOperation("修改项目资源表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:edit')")
    @Log(title = "项目资源表", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody List<ProjectResourceDTO> dto) {
        return R.success();
    }

    /**
     * 删除项目资源表
     *
     * @param dto 需要删除的项目资源表主键集合
     * @return 是否成功
     */
    @ApiOperation("删除项目资源表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:remove')")
    @Log(title = "项目资源表", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public R<Boolean> remove(@RequestBody ConfirmIncomeDTO dto) {
        return R.success(projectResourceService.deleteProjectResourceByIds(dto.getProjectResourceIdList()));
    }

    /**
     * 导出项目资源表
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目资源表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:export')")
    @Log(title = "项目资源表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@RequestBody ProjectResourceQuery query) {
        query.setExportType(ExportType.PROJECT_RESOURCE_LIST_DETAILS);
        reusableAsyncTaskService.addTask("项目详情执行表单下载", TaskType.Export, query, ProjectResourceServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 导出进入项目-执行表单
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出进入项目-执行表单")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:export')")
    @Log(title = "项目资源表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportProjectResource")
    public R<String> exportProjectResource(@RequestBody ProjectResourceQuery query) {
        query.setExportType(ExportType.PROJECT_RESOURCE_LIST);
        reusableAsyncTaskService.addTask("进入项目执行表单下载", TaskType.Export,query, ProjectResourceServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 导入
     *
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:import')")
    @Log(title = "项目资源表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<ProjectResourceExportModel> util = new ExcelUtil<>(ProjectResourceExportModel.class);
        List<ProjectResourceExportModel> projectResourceList = util.importExcel(file.getInputStream());
        String message = this.projectResourceService.importProjectResource(projectResourceList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     *
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:import')")
    @Log(title = "项目资源表", businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ProjectResourceExportModel> util = new ExcelUtil<>(ProjectResourceExportModel.class);
        util.importTemplateExcel(response, "项目资源表数据");
    }

    /**
     * 获取执行表单导入模板
     *
     * @param response 请求响应
     */
    @ApiOperation("获取执行表单导入模板")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:import')")
    @Log(title = "项目执行表单信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importExecFormTemplate")
    public void importExecFormTemplate(HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_project_execute_form.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("项目管理(执行表单)-下载模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入
     *
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @ApiOperation("批量导入")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:import')")
    @Log(title = "项目管理-执行表单导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importExecFormData")
    public R<ImportExecFormErrorVO> importExecFormData(MultipartFile file, Long projectId) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        ExcelUtil<ProjectExecFormExportModel> util = new ExcelUtil<>(ProjectExecFormExportModel.class);
        List<ProjectExecFormExportModel> projectExecFormExportModelList = util.importExcel(file.getInputStream());
        if (ListUtil.isEmpty(projectExecFormExportModelList)) {
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        return R.success(this.projectResourceService.importExecFormData(projectExecFormExportModelList,projectId));
    }

    /**
     * 校验确认收入信息
     *
     * @param dto 确认收入参数
     * @return 是否成功
     */
    @ApiOperation("校验确认收入信息")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/checkResourcePassed")
    public R<List<CheckConfirmInfoVO>> checkResourcePassed(@RequestBody ConfirmIncomeDTO dto) {
        return R.success(projectResourceService.checkResourcePassed(dto));
    }

    /**
     * 确认收入
     *
     * @param dto 确认收入参数
     * @return 是否成功
     */
    @ApiOperation("确认收入")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:edit')")
    @Log(title = "项目资源表", businessType = BusinessType.UPDATE)
    @PostMapping("/confirmIncome")
    public R<Long> confirmIncome(@RequestBody ConfirmIncomeDTO dto) {
        return R.success(projectResourceService.confirmIncome(dto));
    }

    /**
     * 批量确认收入提交
     *
     * @param query 确认收入参数
     * @return 是否成功
     */
    @ApiOperation("批量确认收入提交")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:edit')")
    @Log(title = "项目资源表", businessType = BusinessType.INSERT)
    @PostMapping("/confirmIncomeList")
    public R<Long> confirmIncomeList(@RequestBody ProjectQuery query) {
        return R.success(projectResourceService.confirmIncomeList(query));
    }

    /**
     * 确认收入提交审批列表
     * @param query 确认收入审批列表查询参数
     * @return 是否成功
     */
    @ApiOperation("确认收入提交审批列表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:edit')")
    @GetMapping("/queryConfirmIncome")
    public R<CommitConfirmIncomeVO> queryConfirmIncome(ConfirmIncomeApprovalListQuery query) {
        return R.success(projectResourceService.queryConfirmIncome(query));
    }

    /**
     * 确认收入信息列表
     *
     * @param dto 确认收入参数
     * @return 是否成功
     */
    @ApiOperation("确认收入信息列表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/getConfirmIncomeInfo")
    public R<ConfirmIncomeInfoVO> getConfirmIncomeInfo(@RequestBody ConfirmIncomeDTO dto) {
        return R.success(projectResourceService.getConfirmIncomeInfo(dto));
    }

    /**
     * 校验修改状态
     *
     * @param dto 确认收入参数
     * @return 是否成功
     */
    @ApiOperation("校验修改状态")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/checkStatus")
    public R<CheckErrorInfoVO> checkStatus(@RequestBody UpdateStatusDTO dto) {
        return R.success(projectResourceService.checkStatus(dto));
    }

    /**
     * 获取修改状态信息
     *
     * @param dto 确认收入参数
     * @return 是否成功
     */
    @ApiOperation("获取修改状态信息")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/queryStatusInfo")
    public R<ProjectStatusInfoVO> queryStatusInfo(@RequestBody UpdateStatusDTO dto) {
        return R.success(projectResourceService.queryStatusInfo(dto));
    }

    /**
     * 修改状态
     *
     * @param dto 确认收入参数
     * @return 是否成功
     */
    @ApiOperation("修改状态")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:edit')")
    @Log(title = "项目资源表", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    public R<Long> updateStatus(@RequestBody UpdateStatusDTO dto) {
        return R.success(projectResourceService.updateStatus(dto));
    }

    /**
     * 审批撤回/退回修改状态记录
     *
     * @param dto 项目管理-项目保存记录修改参数
     * @return 是否成功
     */
    @ApiOperation("审批撤回/退回修改状态记录")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:edit')")
    @Log(title = "项目管理-项目保存记录", businessType = BusinessType.UPDATE)
    @PutMapping("/approveUpdateStatusRecord")
    public R<Long> approveUpdateStatusRecord(@RequestBody UpdateStatusDTO dto) {
        return R.success(projectResourceService.approveUpdateStatusRecord(dto));
    }

    /**
     * 批量付款列表信息
     *
     * @param query 付款渠道查询参数
     * @return 是否成功
     */
    @ApiOperation("批量付款列表信息")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/queryPaymentInfo")
    public R<ProjectPaymentInfoVO> queryPaymentInfo(@RequestBody PaymentQuery query) {
        return R.success(projectResourceService.queryPaymentInfo(query));
    }

    /**
     * 校验批量付款信息
     *
     * @param query 付款渠道查询参数
     * @return 是否成功
     */
    @ApiOperation("校验批量付款信息")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/checkPaymentList")
    public R<CheckErrorInfoVO> checkPaymentList(@RequestBody PaymentQuery query) {
        return R.success(projectResourceService.checkPaymentList(query));
    }

    /**
     * 校验付款信息
     *
     * @param query 付款渠道查询参数
     * @return 是否成功
     */
    @ApiOperation("校验付款信息")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/checkPaymentInfo")
    public R<Boolean> checkPaymentInfo(@RequestBody PaymentQuery query) {
        return R.success(projectResourceService.checkPaymentInfo(query));
    }

    /**
     * 批量付款合计
     *
     * @param query 付款渠道查询参数
     * @return 数量
     */
    @ApiOperation("批量付款合计")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/queryPaymentCount")
    public R<PaymentCountVO> queryPaymentCount(@RequestBody PaymentQuery query) {
        return R.success(projectResourceService.queryPaymentCount(query));
    }

    /**
     * 批量付款提交审批列表
     *
     * @param query 付款渠道查询参数
     * @return 是否成功
     */
    @ApiOperation("批量付款提交审批列表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @GetMapping("/queryCommitPaymentInfo")
    public R<ProjectPaymentInfoVO> queryCommitPaymentInfo(PaymentQuery query) {
        return R.success(projectResourceService.queryCommitPaymentInfo(query));
    }

    /**
     * 批量付款提交审批列表
     *
     * @param query 付款渠道查询参数
     * @return 是否成功
     */
    @ApiOperation("批量付款提交审批列表-成本-再次发起获取最新资源信息")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @GetMapping("/queryCommitPaymentInfo/again/initiate")
    public R<ProjectPaymentInfoVO> queryCommitPaymentInfoAgainInitiate(PaymentQuery query) {
        return R.success(projectResourceService.queryCommitPaymentInfoAgainInitiate(query));
    }

    /**
     * 批量付款提交审批列表
     *
     * @param query 付款渠道查询参数
     * @return 是否成功
     */
    @ApiOperation("批量付款提交审批列表付款分组合计")
    @GetMapping("/printCommitPaymentInfoTotal")
    public R<PaymentCountVO> printCommitPaymentInfoTotal(PaymentQuery query) {
        return R.success(projectResourceService.printCommitPaymentInfoTotal(query));
    }


    /**
     * 成本批量付款合计打印数据
     *
     * @param query 付款渠道查询参数
     * @return 数量
     */
    @ApiOperation("成本批量付款合计打印数据")
    @PostMapping("/print/queryPaymentCount")
    public R<PaymentCountVO> printQueryPaymentCount(@RequestBody PaymentQuery query) {
        return R.success(projectResourceService.queryPaymentCount(query));
    }

    /**
     * 批量付款提交审批列表打印数据
     *
     * @param query 付款渠道查询参数
     * @return 是否成功
     */
    @ApiOperation("批量付款提交审批列表打印数据")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @GetMapping("/print/queryCommitPaymentInfo")
    public R<ProjectPaymentInfoVO> printQueryCommitPaymentInfo(PaymentQuery query) {
        return R.success(projectResourceService.queryCommitPaymentInfo(query));
    }

    /**
     * 校验退款信息
     *
     * @param query 付款渠道查询参数
     * @return 退款信息
     */
    @ApiOperation("校验退款信息")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @GetMapping("/checkRefundInfo")
    public R<CheckErrorInfoVO> checkRefundInfo(PaymentQuery query) {
        return R.success(projectResourceService.checkRefundInfo(query));
    }

    /**
     * 获取退款信息
     *
     * @param query 付款渠道查询参数
     * @return 退款信息
     */
    @ApiOperation("获取退款信息")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @GetMapping("/queryRefundInfo")
    public R<ProjectRefundInfoVO> queryRefundInfo(PaymentQuery query) {
        return R.success(projectResourceService.queryRefundInfo(query));
    }

    /**
     * 退款提交审批信息
     *
     * @param query 付款渠道查询参数
     * @return 退款信息
     */
    @ApiOperation("退款提交审批信息")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @GetMapping("/queryCommitRefundInfo")
    public R<ProjectRefundInfoVO> queryCommitRefundInfo(PaymentQuery query) {
        return R.success(projectResourceService.queryCommitRefundInfo(query));
    }

    /**
     * 退款提交审批信息打印数据
     *
     * @param query 付款渠道查询参数
     * @return 退款信息
     */
    @ApiOperation("退款提交审批信息打印数据")
    @GetMapping("/print/queryCommitRefundInfo")
    public R<ProjectRefundInfoVO> printQueryCommitRefundInfo(PaymentQuery query) {
        return R.success(projectResourceService.queryCommitRefundInfo(query));
    }

    /**
     * 批量通过列表
     *
     * @param query 查询参数
     * @return 是否成功
     */
    @ApiOperation("批量通过列表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/pagePassedInfo")
    public R<PageInfo<ProjectResourcePassedInfoVO>> pagePassedInfo(@RequestBody PassedQuery query) {
        return R.success(projectResourceService.pagePassedInfo(query,true));
    }

    /**
     * 批量通过弹窗数据下载
     * @param query 查询参数
     * @param response 响应流
     * @return 成功失败
     */
    @ApiOperation("批量通过弹窗数据下载")
    @PostMapping("/batchPassed/export")
    public R<String> passInfoExport(@RequestBody PassedQuery query,HttpServletResponse response) {
        return R.success("",projectResourcePassInfoService.passInfoExport(query,response));
    }

    /**
     * 批量通过列表,查询通过人部门
     *
     * @param query 查询参数(通过人部门)
     * @return 是否成功(通过人部门)
     */
    @ApiOperation("批量通过列表")
    @PostMapping("/findPassedInfoDept")
    public R<List<String>> findPassedInfoDept(@RequestBody PassedQuery query) {
        return R.success(projectResourceService.findPassedInfoDept(query));
    }

    /**
     * 批量通过确认
     *
     * @param query 确认通过实体
     * @return 是否成功
     */
    @ApiOperation("批量通过确认")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/passedConfirm")
    public R<List<ConfirmPassedDTO>> passedConfirm(@RequestBody PassedQuery query) {
        return R.success(projectResourceService.passedConfirm(query));
    }

    /**
     * 获取未通过数量
     *
     * @return 是否成功
     */
    @ApiOperation("获取未通过数量")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/getFailPassedCount")
    public R<PassedCountVO> getFailPassedCount(PassedQuery query) {

        PassedCountVO passedCountVO = new PassedCountVO();
        query.setTabType(CooperationStatus.FAIL.getValue());
        int unPassedCount = this.projectResourceService.getPassedOrAllCount(query);
        passedCountVO.setUnPassedCount(unPassedCount);
        return R.success(passedCountVO);
    }

    /**
     * 获取通过数量
     *
     * @return 是否成功
     */
    @ApiOperation("获取通过数量")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/getPassedCount")
    public R<PassedCountVO> getPassedCount(PassedQuery query) {

        PassedCountVO passedCountVO = new PassedCountVO();
        query.setTabType(CooperationStatus.PASSED.getValue());
        int passedCount = this.projectResourceService.getPassedOrAllCount(query);
        passedCountVO.setPassedCount(passedCount);
        return R.success(passedCountVO);
    }

    /**
     * 获取全部数量
     *
     * @return 是否成功
     */
    @ApiOperation("获取全部数量")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/getTotalCount")
    public R<PassedCountVO> getTotalCount(PassedQuery query) {
        PassedCountVO passedCountVO = new PassedCountVO();
        int totalCount = this.projectResourceService.getPassedOrAllCount(query);
        passedCountVO.setTotalCount(totalCount);
        return R.success(passedCountVO);
    }

    /**
     * 批量通过-获取导入查询模板
     * @param response 请求响应
     */
    @ApiOperation("批量通过-获取导入查询模板")
    @Log(title = "批量通过-获取导入查询模板" , businessType = BusinessType.IMPORT)
    @PostMapping("/passed/importSelectTemplate" )
    public void importSelectTemplate(HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_project_pass_select.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("批量通过导入查询模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = null;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     *  项目管理-批量通过导入查询
     * @param file 导入文件
     * @param tabType 项目状态
     * @return 导入结果
     */
    @ApiOperation("批量通过-导入查询")
    @Log(title = "批量通过-导入查询" , businessType = BusinessType.IMPORT)
    @PostMapping("/passed/import/select/data")
    public R<ImportProjectResourceSelectVO> passedImportSelectDate(MultipartFile file, Integer tabType) throws Exception {
        // Assert.notNull(tabType, "请选择资源合作状态");
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        //校验是否有数据
        ExcelUtil<ImportProjectResourceSelectModel> importUtils = new ExcelUtil<>(ImportProjectResourceSelectModel.class);
        List<ImportProjectResourceSelectModel> importModelList = importUtils.importExcel(file.getInputStream());
        if (ListUtil.isEmpty(importModelList)) {
            throw new ServiceException("导入失败,无法读取数据,请检查");
        }

        ImportProjectResourceQuery query = new ImportProjectResourceQuery();
        if (!Objects.isNull(tabType)){
            query.setTabType(tabType);
        }
        return R.success(this.projectResourceSearchImporter.importSelectData(importModelList, query));
    }


    /**
     *  批量通过弹窗批量填充通过凭证
     * @param file 导入文件
     * @return 导入赋值结果
     */
    @ApiOperation("批量通过-批量赋值凭证")
    @Log(title = "批量通过-批量赋值凭证", businessType = BusinessType.IMPORT)
    @PostMapping("/batchPassed/import")
    public R<ResourcePassInfoVO> PassInfoImport(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        //校验是否有数据
        ExcelUtil<ImportProjectResourcePassInfoModel> importUtils = new ExcelUtil<>(ImportProjectResourcePassInfoModel.class);
        List<ImportProjectResourcePassInfoModel> importModelList = importUtils.importExcel(file.getInputStream());
        if (ListUtil.isEmpty(importModelList)) {
            throw new ServiceException("导入失败,无法读取数据,请检查");
        }
        return R.success(resourcePassInfoSearchImporter.importPassInfo(importModelList));
    }

    /**
     *  批量通过下载模板
     * @param response 导入文件
     * @return 导入赋值结果
     */
    @ApiOperation("批量通过下载模板")
    @Log(title = "批量通过下载模板", businessType = BusinessType.IMPORT)
    @PostMapping("/batchPassed/download")
    public void download(HttpServletResponse response) throws Exception {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/batch_pass_info.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("项目管理(批量通过)-下载模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 校验批量修改状态
     *
     * @param query 修改状态查询参数
     * @return 是否成功
     */
    @ApiOperation("校验批量修改状态")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/checkStatusBatch")
    public R<CheckErrorInfoVO> checkStatusBatch(@RequestBody ProjectResourceQuery query) {
        return R.success(projectResourceService.checkStatusBatch(query));
    }

    /**
     * 修改状态下载
     *
     * @param query 导出查询参数
     */
    @ApiOperation("修改状态下载")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:export')")
    @Log(title = "修改状态下载", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadUpdateStatus")
    public R<String> downloadUpdateStatus(@RequestBody UpdateStatusDTO query) {
        reusableAsyncTaskService.addTask("修改状态下载", TaskType.Export, query, ProjectUpdateStatusDetailsServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 修改状态获取导入查询模板
     * @param response 请求响应
     */
    @ApiOperation("修改状态-获取导入查询模板")
    @Log(title = "修改状态-获取导入查询模板" , businessType = BusinessType.IMPORT)
    @PostMapping("/updateStatus/importSelectTemplate" )
    public void batchConfirmationImportSelectTemplate(HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_project_resource_update_status_import_select.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("SED项目管理-批量修改状态导入查询模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = null;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量通过-导入查询
     * @param file 导入文件
     * @return 导入结果
     */
    @ApiOperation("修改状态-导入查询")
    @Log(title = "修改状态-导入查询", businessType = BusinessType.IMPORT)
    @PostMapping("/updateStatus/import/select/data")
    public R<ImportResourceSelectVO> batchConfirmationImportSelectDate(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        //校验是否有数据
        ExcelUtil<ImportResourceSelectModel> importUtils = new ExcelUtil<>(ImportResourceSelectModel.class);
        List<ImportResourceSelectModel> importModelList = importUtils.importExcel(file.getInputStream());
        if (ListUtil.isEmpty(importModelList)) {
            throw new ServiceException("导入失败,无法读取数据,请检查");
        }

        return R.success(projectResourceService.importSelectData(importModelList));
    }


    /**
     * 新增批量修改记录
     * @param vo
     * @return
     */
    @ApiOperation("批量修改提交")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:edit')")
    @Log(title = "项目资源表", businessType = BusinessType.INSERT)
    @PostMapping("/addBatchUpdate")
    public R<Long> addBatchUpdate(@RequestBody ProjectBatchUpdateAddRecordVO vo) {
        return R.success(projectResourceService.addBatchUpdate(vo));
    }

    @ApiOperation("批量修改提交审批列表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:edit')")
    @GetMapping("/queryBatchUpdate")
    public R<CommitBatchUpdateVO> queryBatchUpdate(BatchUpdateApprovalListQuery query) {
        return R.success(projectResourceService.queryBatchUpdateDetailVO(query.getBatchUpdateId(),query.getFirstProjectId()));
    }

}
