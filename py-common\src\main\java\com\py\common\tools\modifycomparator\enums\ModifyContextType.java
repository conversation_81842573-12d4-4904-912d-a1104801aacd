package com.py.common.tools.modifycomparator.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;

/**
 * 修改内容类型
 * <AUTHOR>
 */
@AllArgsConstructor
public enum ModifyContextType implements IDict<String> {

    /** 纯文本 */
    STRING("String", "纯文本"),
    /** Oss文件 */
    OSS_File("OssFile", "Oss文件"),
    /** 列表Oss文件 */
    LIST_OSS_FILE("ListOssFile", "列表Oss文件");

    private final String value;

    private final String label;

    @Override
    public String getValue() {
        return this.value;
    }

    @Override
    public String getLabel() {
        return this.label;
    }
}
