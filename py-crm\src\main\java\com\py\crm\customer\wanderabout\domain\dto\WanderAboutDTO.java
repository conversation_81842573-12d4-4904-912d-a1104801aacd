package com.py.crm.customer.wanderabout.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户管理-客户流转数据传输模型
 *
 * <AUTHOR>
 * @date 2023-08-16
 */
@Data
@ApiModel("客户管理-客户流转数据传输模型" )
public class WanderAboutDTO {
    private static final long serialVersionUID = 1L;

    /** 自增主键*/
    @ApiModelProperty("自增主键" )
    private Long id;

    /** 客户id*/
    @ApiModelProperty("客户id" )
    private Long customerId;

    /** 服务人员id*/
    @ApiModelProperty("服务人员id" )
    private Long userId;

    /** 是否被分配（0否,1是） */
    @ApiModelProperty("是否被分配（0否,1是）")
    private Boolean isDistribution;


    /** 是否市创建者（0否,1是） */
    @ApiModelProperty("是否市创建者（0否,1是）")
    private Boolean isCreate;
}
