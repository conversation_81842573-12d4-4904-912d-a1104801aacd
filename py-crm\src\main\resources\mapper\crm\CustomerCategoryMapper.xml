<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.py.crm.customer.customercategory.mapper.CustomerCategoryMapper">
<!--  查询客户id  -->
    <select id="listCustomerIdByCategoryId" resultType="java.lang.Long">
        SELECT DISTINCT
        category.customer_id
        FROM
        `py_crm_customer_category` category
        LEFT JOIN py_crm_customer custome On category.customer_id = custome.customer_id
        WHERE
        category.del_flag = 0
          AND category.industry_category_id IN
        <foreach collection="idList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="query.dataScope != null and query.dataScope != ''">
          and category.customer_id IN (select customer_id from py_crm_wander_about where del_flag = 0 and is_distribution = 1   ${query.dataScope} )
        </if>
        ORDER BY
        custome.update_time
        LIMIT #{pageNum},#{pageSize}
    </select>
</mapper>
