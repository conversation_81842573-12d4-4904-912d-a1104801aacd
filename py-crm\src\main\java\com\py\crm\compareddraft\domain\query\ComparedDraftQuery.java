package com.py.crm.compareddraft.domain.query;

import com.py.common.core.domain.model.LoginUser;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTaskArgs;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * 客户管理-比稿管理
 *
 * <AUTHOR>
 * @date 2023/07/17 09:36
 */
@Data
@ApiModel("客户管理-比稿管理")
public class ComparedDraftQuery implements ReusableAsyncTaskArgs {

    /** 比稿id */
    @ApiModelProperty("比稿id")
    private Long comparedDraftId;

    /**
     * 比稿id集合
     */
    @ApiModelProperty("比稿id集合")
    private List<Long> comparedDraftIdList;

    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    private Long customerId;

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    private String customerName;

    /**
     * 客户合作主体ID
     */
    @ApiModelProperty("客户合作主体ID")
    private Long customerCooperateId;

    /**
     * 客户合作主体
     */
    @ApiModelProperty("客户合作主体")
    private String customerCooperateName;

    /**
     * 品牌/业务线名
     */
    @ApiModelProperty("品牌/业务线名")
    private String brandName;

    /**
     * 比稿项目名称
     */
    @ApiModelProperty("比稿项目名称")
    private String comparedDraftName;

    /**
     * 商务标比稿结果
     */
    @ApiModelProperty("商务标比稿结果")
    private Integer commercialBidFruits;

    /**
     * 技术标比稿结果
     */
    @ApiModelProperty("技术标比稿结果")
    private Integer techniqueBidFruits;

    /**
     * 最终比稿结果
     */
    @ApiModelProperty("最终比稿结果")
    private Integer finalFruits;

    /**
     * 比稿开始时间
     */
    @ApiModelProperty("比稿开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate comparedBeginDraftTime;

    /**
     * 比稿结束时间
     */
    @ApiModelProperty("比稿结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate comparedEndDraftTime;

    /**
     * 录入开始时间
     */
    @ApiModelProperty("录入开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate enteringBeginTime;

    /**
     * 录入结束时间
     */
    @ApiModelProperty("录入结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate enteringEndTime;

    /** 策划人id */
    @ApiModelProperty("策划人id")
    private List<Long> plotterIdList;
    private List<String> plotterList;

    /** 比稿人id */
    @ApiModelProperty("比稿人id")
    private List<Long> userIdList;

    /** 是否全选(false不是true是) */
    @ApiModelProperty("是否全选(false不是true是)")
    private Boolean isAll;

    /** 任务ID*/
    @ApiModelProperty(hidden = true)
    private Long taskId;

    /** 下载文件名 */
    @ApiModelProperty(hidden = true)
    private String fileName;

    /** 登录用户 */
    @ApiModelProperty(hidden = true)
    private LoginUser loginUser;

    /** 项目ID */
    private Long projectId;

}
