package com.py.crm.customer.customerlaundry.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import java.time.LocalDate;

/**
 * 客户管理-查看清单对象
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@TableName("py_crm_customer_laundry" )
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("客户管理-查看清单" )
public class CustomerLaundry extends BaseEntity {
private static final long serialVersionUID=1L;

    /** 自增id*/
    @ApiModelProperty("自增id")
    private Long id;

    /** 清单id*/
    @ApiModelProperty("清单id")
    @TableId
    private Long laundryId;

    /** 业务id*/
    @ApiModelProperty("业务id")
    private Long bizId;

    /** 业务类型（0客户，1人脉） */
    @ApiModelProperty("业务类型（0客户，1人脉）")
    private Integer bizType;
}
