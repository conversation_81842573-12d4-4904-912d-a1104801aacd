package com.py.common.tools.verify.verifyprovider.impl;

import com.py.common.tools.verify.enums.VerifyType;
import com.py.common.tools.verify.verifyprovider.VerifyProvider;
import org.springframework.stereotype.Component;

/**
 * 输入为True验证提供者
 * <AUTHOR>
 */
@Component
public class IsTrueVerifyProvider implements VerifyProvider {

    /**
     * 支持的验证类型
     * @return 支持的验证类型
     */
    @Override
    public VerifyType supportedVerifyType() {
        return VerifyType.IsTrue;
    }

    /**
     * 验证
     * @param target 验证目标
     * @return 验证结果 true:验证通过  false:验证失败
     */
    @Override
    public boolean verify(Object target) throws IllegalArgumentException {
        if(target == null) {
            return false;
        }

        if(target instanceof Boolean == false) {
            throw new IllegalArgumentException("只有布尔值才能使用 VerifyType.IsTrue 验证");
        }
        return (boolean) target == true;
    }
}
