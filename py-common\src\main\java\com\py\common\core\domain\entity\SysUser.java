package com.py.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.py.common.core.domain.BaseEntity;
import com.py.common.enums.EmploymentStatus;
import com.py.common.enums.Enable;
import com.py.common.typehandler.impl.LongSetTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 * <AUTHOR>
 */
@Data
@TableName(value = "sys_user", autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class SysUser extends BaseEntity {

    /** 用户ID */
    @TableId
    private Long userId;

    /** 用户账号 */
    private String userName;

    /** 手机号码 */
    private String phoneNumber;

    /** 直属上级列表 */
    @TableField(typeHandler = LongSetTypeHandler.class)
    private List<Long> directLeaderList;

    /** 用户邮箱 */
    private String email;

    /** 银行卡号 */
    private String account;

    /** 开户行 */
    private String accountBank;

    /** 用户头像 */
    private String avatar;

    /** 密码 */
    private String password;

    /** 盐加密 */
    @TableField(exist = false)
    private String salt;

    /** 帐号状态 */
    private Enable status;

    /** 最后登录IP */
    private String loginIp;

    /** 最后登录时间 */
    private Date loginDate;

    /** 是否初始密码：0否，1是*/
    private Boolean defaultPassword;

    /** 员工状态 */
    private EmploymentStatus employmentStatus;

    public boolean isAdmin() {
        return isAdmin(this.userId);
    }

    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }

    @JsonIgnore
    @JsonProperty
    public String getPassword() {
        return password;
    }

}
