package com.py.web.controller.project;


import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.core.redis.RedisCache;
import com.py.common.enums.BusinessType;
import com.py.common.enums.ProjectStatus;
import com.py.common.enums.TaskType;
import com.py.common.exception.ServiceException;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.customer.domain.vo.CustomerVO;
import com.py.crm.customer.service.ICustomerService;
import com.py.project.enums.ExportType;
import com.py.project.project.domain.Project;
import com.py.project.project.domain.dto.ProjectConfirmRecordDeleteDTO;
import com.py.project.project.domain.dto.ProjectDTO;
import com.py.project.project.domain.dto.ProjectExportModel;
import com.py.project.project.domain.query.CustomerProjectExportQuery;
import com.py.project.project.domain.query.ProjectAddProjectQuery;
import com.py.project.project.domain.query.HandoverProjectQuery;
import com.py.project.project.domain.query.ProjectQuery;
import com.py.project.project.domain.query.ProjectResourceInfoQuery;
import com.py.project.project.domain.vo.*;
import com.py.project.project.excel.search.ProjectSearchImporter;
import com.py.project.project.excel.search.model.ImportProjectSelectModel;
import com.py.project.project.excel.search.model.ImportProjectSelectQuery;
import com.py.project.project.excel.search.model.ImportProjectSelectVO;
import com.py.project.project.service.IProjectService;
import com.py.project.project.service.impl.ProjectDownLoadServiceImpl;
import com.py.project.project.service.impl.ProjectServiceImpl;
import com.py.project.project.sync.ImportProjectCodeModel;
import com.py.project.projectclosecase.domain.vo.ProjectCloseCaseCommitInfoVO;
import com.py.project.projectconfirmrecord.service.IProjectConfirmRecordDeleteService;
import com.py.project.projectresource.comfirmation.domain.query.BatchConfirmationQuery;
import com.py.project.projectresource.domain.vo.CheckErrorInfoVO;
import com.py.project.projectresource.domain.vo.ResourceConfirmIncomeVO;
import com.py.project.projectresource.domain.vo.ResourceCountVO;
import com.py.system.user.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 项目管理-项目表Controller
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
@Api(tags = "项目管理-项目表")
@RestController
@RequestMapping("/project")
public class ProjectController extends BaseController {

    /** 项目管理-项目表服务 */
    @Resource
    private IProjectService projectService;

    /** 客户服务 */
    @Resource
    private ICustomerService customerService;

    /** Redis缓存 */
    @Resource
    private RedisCache redisCache;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    @Resource
    private ISysUserService userService;

    /**项目管理导入查询*/
    @Resource
    private ProjectSearchImporter projectSearchImporter;

    /** 项目确认收入记录删除Service */
    @Resource
    private IProjectConfirmRecordDeleteService projectConfirmRecordDeleteService;

    /**
     * 分页查询项目管理-项目表列表
     *
     * @param query 项目管理-项目表查询参数
     * @return 项目管理-项目表分页
     */
    @ApiOperation("分页查询询项目管理-项目表列表")
    @PreAuthorize("@ss.hasPermi('project:project:list')")
    @GetMapping("/pageProject")
    public R<PageInfo<ProjectListVO>> pageProject(ProjectQuery query) {
        PageInfo<ProjectListVO> voList = this.projectService.pageProjectList(query);
        return R.success(voList);
    }

    /**
     * 查询项目管理-项目表列表,立项部门
     *
     * @param query 项目管理-项目表查询参数(立项部门)
     * @return 项目管理-项目表,立项部门
     */
    @ApiOperation("查询项目管理-项目表列表,立项部门")
    @PostMapping("/findProjectCreateDept")
    public R<List<String>> findProjectCreateDept(@RequestBody ProjectQuery query) {
        List<String> voList = this.projectService.findProjectCreateDept(query);
        return R.success(voList);
    }

    /**
     * 分页查询项目管理-项目表列表
     *
     * @param query 项目管理-项目表查询参数
     * @return 项目管理-项目表分页
     */
    @ApiOperation("分页查询询项目管理-项目表列表")
    @PreAuthorize("@ss.hasPermi('project:project:list')")
    @PostMapping("/pageProject")
    public R<PageInfo<ProjectListVO>> projectList(@RequestBody ProjectQuery query) {
        PageInfo<ProjectListVO> voList = this.projectService.pageProjectList(query);
        return R.success(voList);
    }

    /**
     * 获取项目管理-项目表详细信息
     * @param query 项目管理-项目表主键
     * @return 项目管理-项目表视图模型
     */
    @ApiOperation("获取项目管理-项目表详细信息")
    @PreAuthorize("@ss.hasPermi('project:project:query')")
    @GetMapping(value = "/getInfo")
    public R<ProjectDetailsVO> getInfo(ProjectQuery query) {
        ProjectDetailsVO projectDetails = projectService.selectProjectById(query);
        return R.success(projectDetails);
    }

    /**
     * 获取项目管理-项目表详细信息
     * @param query 项目管理-项目表主键
     * @return 项目管理-项目表视图模型
     */
    @ApiOperation("获取项目管理-项目表详细信息")
    @PreAuthorize("@ss.hasPermi('project:project:query')")
    @PostMapping(value = "/getProjectInfo")
    public R<ProjectDetailsVO> getProjectInfo(@RequestBody ProjectQuery query) {
        ProjectDetailsVO projectDetails = projectService.selectProjectById(query);
        return R.success(projectDetails);
    }

    /**
     * 新增项目管理-项目表
     *
     * @param dto 项目管理-项目表修改参数
     * @return 是否成功
     */
    @ApiOperation("新增项目管理-项目表")
    @PreAuthorize("@ss.hasPermi('project:project:add')")
    @Log(title = "项目管理-项目表", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Long> add(@Validated @RequestBody ProjectDTO dto) {
        return R.success(projectService.insertProject(dto));
    }

    /**
     * 修改申请项目管理-项目表
     *
     * @param dto 项目管理-项目表修改参数
     * @return 是否成功
     */
    @ApiOperation("修改申请项目管理-项目表")
    @PreAuthorize("@ss.hasPermi('project:project:edit')")
    @Log(title = "项目管理-项目表", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Long> edit(@Validated @RequestBody ProjectDTO dto) {
        return R.success(projectService.updateProject(dto));
    }

    /**
     * 项目管理-进入项目修改按钮-按钮校验
     * @param id  项目id
     * @return 校验结果
     */
    @ApiOperation("修改申请项目管理-修改申请按钮校验")
    @PreAuthorize("@ss.hasPermi('project:project:edit')")
    @Log(title = "项目管理-修改申请按钮校验", businessType = BusinessType.UPDATE)
    @GetMapping("/checkProjectApproved/{id}")
    public R<CheckErrorInfoVO> checkProjectApproved(@PathVariable(value = "id") Long id){
        return R.success(projectService.checkProjectApproved(id));
    }

    /**
     * 修改项目
     *
     * @param dto 项目管理-项目表修改参数
     * @return 是否成功
     */
    @ApiOperation("修改项目")
    @PreAuthorize("@ss.hasPermi('project:project:edit')")
    @Log(title = "项目管理-项目表", businessType = BusinessType.UPDATE)
    @PostMapping("/approveUpdateProject")
    public R<Long> approveUpdateProject(@RequestBody ProjectDTO dto) {
        return R.success(projectService.approveUpdateProject(dto));
    }

    /**
     * 校验修改申请是否为审批中
     *
     * @param dto 项目管理-项目表修改参数
     * @return 是否成功
     */
    @ApiOperation("校验修改申请是否为审批中")
    @PreAuthorize("@ss.hasPermi('project:project:edit')")
    @PostMapping("/checkProjectInfo")
    public R<Boolean> checkProjectInfo(@RequestBody ProjectDTO dto) {
        return R.success(projectService.checkProjectInfo(dto));
    }
    /**
     * 删除项目管理-项目表
     * @param ids 需要删除的项目管理-项目表主键集合
     * @return 是否成功
     */
    @ApiOperation("删除项目管理-项目表" )
    @PreAuthorize("@ss.hasPermi('project:project:remove')")
    @Log(title = "项目管理-项目表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(projectService.deleteProjectByIds(ids));
    }

    /**
     * 导出项目管理-项目表
     * @param query 导出查询参数
     * @throws Exception 导入异常
     */
    @ApiOperation("导出项目管理-项目表")
    @PreAuthorize("@ss.hasPermi('project:project:export')")
    @Log(title = "项目管理-项目表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@RequestBody ProjectQuery query) throws Exception {
        query.setExportType(ExportType.PROJECT_LIST);

        String fileName = String.format("项目管理列表数据-%s.xlsx", DateUtils.getTimeCn());
        query.setFileName(fileName);
        reusableAsyncTaskService.addTask("项目管理列表", TaskType.Export,query, ProjectServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 客户管理-导出项目明细
     * @param query 导出查询参数
     * @throws Exception 导入异常
     */
    @ApiOperation("客户管理-导出项目明细")
    @PreAuthorize("@ss.hasPermi('project:project:export')")
    @Log(title = "客户管理-项目明细", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCustomerProjectInfo")
    public R<String> exportCustomerProjectInfo(@Validated @RequestBody CustomerProjectExportQuery query) throws Exception{
        ProjectQuery projectQuery = new ProjectQuery();
        projectQuery.setExportType(ExportType.PROJECT_LIST);
        projectQuery.setProjectIdList(query.getProjectIdList());

        CustomerVO customerVO = this.customerService.selectCustomerById(query.getCustomerId());
        String fileName = String.format("项目明细-%s-%s.xlsx", customerVO.getName(), DateUtils.getTimeCn());
        projectQuery.setFileName(fileName);

        this.reusableAsyncTaskService.addTask("客户管理-项目明细", TaskType.Export,projectQuery, ProjectServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }


    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @ApiModelProperty("进入项目-导入资源")
    @PreAuthorize("@ss.hasPermi(':project:import')" )
    @Log(title = "进入项目-导入资源" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<ProjectExportModel> util = new ExcelUtil<>(ProjectExportModel.class);
        List<ProjectExportModel> projectList = util.importExcel(file.getInputStream());
        String message = this.projectService.importProject(projectList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @ApiModelProperty("进入项目-获取导入资源模板")
    @PreAuthorize("@ss.hasPermi(':project:import')" )
    @Log(title = "进入项目-获取导入资源模板" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ProjectExportModel> util = new ExcelUtil<>(ProjectExportModel.class);
        util.importTemplateExcel(response, "项目管理-项目表数据" );
    }

    /**
     * 获取项目-资源关联信息
     * @param query 项目资源信息查询参数
     * @return 项目管理-项目表视图模型
     */
    @ApiOperation("获取项目管理-资源关联信息")
    @PreAuthorize("@ss.hasPermi('project:project:query')")
    @GetMapping(value = "/queryProjectResourceInfo")
    public R<ProjectResourceInfoVO> queryProjectResourceInfo(ProjectResourceInfoQuery query) {
        return R.success(projectService.queryProjectResourceInfo(query));
    }

    /**
     * 导出项目管理-项目详情
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目管理-项目详情")
    @PreAuthorize("@ss.hasPermi('project:projectInfo:export')")
    @Log(title = "项目管理-项目表", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadProjectInfo")
    public R<String> downloadProjectInfo(@RequestBody ProjectQuery query){
        query.setExportType(ExportType.PROJECT_INFO);
        reusableAsyncTaskService.addTask("项目管理详情", TaskType.Export, query, ProjectServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 导出项目合同记录
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目合同记录")
    @PreAuthorize("@ss.hasPermi('project:projectInfo:export')")
    @Log(title = "导出项目合同记录", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadProjectContractInfo")
    public R<String> downloadProjectContractInfo(@RequestBody ProjectQuery query){
        query.setExportType(ExportType.PROJECT_CONTRACT);
        reusableAsyncTaskService.addTask("合同记录", TaskType.Export, query, ProjectServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 项目管理-批量确认收入列表
     * @param query 查询参数
     * @return 确认收入列表
     */
    @ApiOperation("项目-批量确认收入列表")
    @PreAuthorize("@ss.hasPermi('project:project:query')")
    @GetMapping("/pageConfirmIncomeProject")
    public R<PageInfo<ResourceConfirmIncomeVO>> pageConfirmIncomeProject(ProjectQuery query){
        return R.success(this.projectService.pageConfirmIncomeProject(query));
    }

    /**
     * 分页预览未提交的批量确认收入项目列表
     * @param query 查询参数
     * @return 未提交确认收入列表
     */
    @ApiOperation("项目-分页预览未提交的批量确认收入项目列表")
    @PreAuthorize("@ss.hasPermi('project:project:query')")
    @GetMapping("/previewPageNotSubmittedConfirmIncomeProject")
    public R<PageInfo<ResourceConfirmIncomeVO>> pagePreviewNotSubmittedBatchConfirmIncomeProject(ProjectQuery query){
        return R.success(this.projectService.pagePreviewNotSubmittedBatchConfirmIncomeProject(query));
    }

    /**
     * 项目管理-校验批量确认收入
     * @param query 查询参数
     * @return 确认收入列表
     */
    @ApiOperation("项目-校验批量确认收入")
    @PreAuthorize("@ss.hasPermi('project:project:query')")
    @PostMapping("/checkConfirmIncomeList")
    public R<CheckErrorInfoVO> checkConfirmIncomeList(@RequestBody BatchConfirmationQuery query){
        return this.projectService.checkConfirmIncomeList(query);
    }

    /**
     * 项目管理-校验批量结案
     * @param query 查询参数
     * @return 确认收入列表
     */
    @ApiOperation("项目-校验批量结案")
    @PreAuthorize("@ss.hasPermi('project:project:query')")
    @PostMapping("/checkCloseCaseList")
    public R<CheckErrorInfoVO> checkCloseCaseList(@RequestBody ProjectQuery query){
        return this.projectService.checkCloseCaseList(query);
    }

    /**
     * 项目管理-批量确认收入合计
     * @param query 查询参数
     * @return 确认收入列表
     */
    @ApiOperation("项目-批量确认收入合计")
    @PreAuthorize("@ss.hasPermi('project:project:query')")
    @GetMapping("/getCountConfirmIncome")
    public R<ResourceCountVO> getCountConfirmIncome(ProjectQuery query){
        return R.success(this.projectService.getCountConfirmIncome(query));
    }


    /**
     * 批量结案列表
     *
     * @param query 付款渠道查询参数
     * @return 结案列表
     */
    @ApiOperation("批量结案列表")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @GetMapping("/pageCloseCaseList")
    public R<PageInfo<ProjectCloseCaseInfoVO>> pageCloseCaseList(ProjectQuery query) {
        return R.success(projectService.pageCloseCaseList(query));
    }

    /**
     * 批量结案合计
     *
     * @param query 付款渠道查询参数
     * @return 合计信息
     */
    @ApiOperation("批量结案合计")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @GetMapping("/queryCountCloseCase")
    public R<ProjectCloseCaseCountVO> queryCountCloseCase(ProjectQuery query) {
        return R.success(projectService.queryCountCloseCase(query));
    }

    /**
     * 批量结案提交审批
     *
     * @param query 查询参数
     * @return 是否成功
     */
    @ApiOperation("批量结案提交审批")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @PostMapping("/commitCloseCase")
    public R<Long> commitCloseCase(@RequestBody ProjectQuery query) {
        return R.success(projectService.commitCloseCase(query));
    }

    /**
     * 批量结案审批信息
     *
     * @param pageNum 页码
     * @param pageSize 展示条数
     * @param projectCloseCaseId 项目结案Id
     * @return 批量结案信息
     */
    @ApiOperation("批量结案审批信息")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @GetMapping("/queryCommitCloseCaseInfo")
    public R<ProjectCloseCaseCommitInfoVO> queryCommitCloseCaseInfo(@RequestParam("projectCloseCaseId") Long projectCloseCaseId,
                                                                    @RequestParam("pageNum")Integer pageNum,
                                                                    @RequestParam("pageSize")Integer pageSize) {

        return R.success(projectService.queryCommitCloseCaseInfo(projectCloseCaseId,pageNum,pageSize));
    }

    /**
     * 批量结案审批信息打印数据
     *
     * @param pageNum 页码
     * @param pageSize 展示条数
     * @param projectCloseCaseId 项目结案Id
     * @return 批量结案信息
     */
    @ApiOperation("批量结案审批信息打印数据")
    @PreAuthorize("@ss.hasPermi('projectresource:projectresource:query')")
    @GetMapping("/print/queryCommitCloseCaseInfo")
    public R<ProjectCloseCaseCommitInfoVO> printQueryCommitCloseCaseInfo(@RequestParam("projectCloseCaseId") Long projectCloseCaseId,
                                                                    @RequestParam("pageNum")Integer pageNum,
                                                                    @RequestParam("pageSize")Integer pageSize) {

        return R.success(projectService.queryCommitCloseCaseInfo(projectCloseCaseId,pageNum,pageSize));
    }

    /**
     * 校验同一时间不允许多人修改
     * @param projectId 项目id
     */
    @ApiModelProperty("校验同一时间不允许多人修改")
    @PreAuthorize("@ss.hasPermi(':project:import')" )
    @GetMapping("/checkUpdate" )
    public R<Boolean> checkUpdate(Long projectId) {

        Long cacheValue = redisCache.getCacheObject(setEnterProjectCacheKey(Constants.UPDATE_PROJECT, projectId));
        if (cacheValue == null){
            redisCache.cacheObject(setEnterProjectCacheKey(Constants.UPDATE_PROJECT, projectId),SecurityUtils.getUserId(),30, TimeUnit.MINUTES);
        }
        Long userId = SecurityUtils.getUserId();
        if (cacheValue != null && !cacheValue.equals(userId)){
            throw new ServiceException(String.format("%s 正在填写此表单，请稍等！",this.userService.getInfo(cacheValue).getUserName()));
        }
        redisCache.cacheSet(setEnterProjectCacheKey(Constants.LOGIN_USER_PROJECT_ID, SecurityUtils.getUserId()),projectId);

        return R.success(true);
    }

    /**
     * 退出时删除进入项目的
     * @param projectId 项目id
     */
    @ApiModelProperty("退出时删除进入项目的")
    @PreAuthorize("@ss.hasPermi(':project:import')" )
    @GetMapping("/exitsProjectResource" )
    public R<Boolean> exitsProjectResource(Long projectId) {

        Long cacheValue = redisCache.getCacheObject(setEnterProjectCacheKey(Constants.UPDATE_PROJECT, projectId));
        if (cacheValue != null){
            return R.success(redisCache.deleteObject(setEnterProjectCacheKey(Constants.UPDATE_PROJECT, projectId)));
        }

        return R.success(true);
    }

    /**
     * 关闭全部页签时删除当前登录人的项目id
     */
    @ApiModelProperty("关闭全部页签时删除当前登录人的项目id")
    @PreAuthorize("@ss.hasPermi(':project:import')" )
    @GetMapping("/exitsAllLabel" )
    public R<Boolean> exitsAllLabel() {

        Set<Long> cacheList = redisCache.getCacheSet(setEnterProjectCacheKey(Constants.LOGIN_USER_PROJECT_ID, SecurityUtils.getUserId()));
        if (ListUtil.isNotEmpty(cacheList)){
            redisCache.deleteCacheSetValue(setEnterProjectCacheKey(Constants.LOGIN_USER_PROJECT_ID, SecurityUtils.getUserId()));
            for (Long projectId : cacheList) {
                redisCache.deleteObject(setEnterProjectCacheKey(Constants.UPDATE_PROJECT, projectId));
            }
        }

        return R.success(true);
    }


    /**
     * 设置进入项目缓存key
     * @param  cacheKeyConstants 缓存的key常量
     * @param value 缓存的key值
     * @return 拼接的缓存key
     */
    private String setEnterProjectCacheKey(String cacheKeyConstants, Long value) {
        return cacheKeyConstants + value;
    }

    /**
     * 获取项目统计
     *
     * @param query 项目管理-项目表查询参数
     * @return 统计信息
     */
    @ApiOperation("获取项目中项目统计")
    @PreAuthorize("@ss.hasPermi('project:project:list')")
    @GetMapping("/getInTheProjectCount")
    public R<ProjectCountVO> getInTheProjectCount(ProjectQuery query) {
        ProjectCountVO countVO = new ProjectCountVO();
        query.setProjectStatus(ProjectStatus.IN_PROJECT.getValue());
        List<Project> projectList = this.projectService.getProjectCount(query);
        countVO.setInTheProjectCount(projectList.size());
        return R.success(countVO);
    }

    /**
     * 获取项目统计
     *
     * @param query 项目管理-项目表查询参数
     * @return 统计信息
     */
    @ApiOperation("获取已完结项目统计")
    @PreAuthorize("@ss.hasPermi('project:project:list')")
    @GetMapping("/getCompletedCount")
    public R<ProjectCountVO> getCompletedCount(ProjectQuery query) {
        ProjectCountVO countVO = new ProjectCountVO();
        query.setProjectStatus(ProjectStatus.CLOSED_CASE.getValue());
        List<Project> projectList = this.projectService.getProjectCount(query);
        countVO.setCompletedCount(projectList.size());
        return R.success(countVO);
    }

    /**
     * 获取项目统计
     *
     * @param query 项目管理-项目表查询参数
     * @return 统计信息
     */
    @ApiOperation("获取全部项目统计")
    @PreAuthorize("@ss.hasPermi('project:project:list')")
    @GetMapping("/getTotalCount")
    public R<ProjectCountVO> getTotalCount(ProjectQuery query) {
        ProjectCountVO countVO = new ProjectCountVO();
        List<Project> projectList = this.projectService.getProjectCount(query);
        countVO.setTotal(projectList.size());
        return R.success(countVO);
    }

    /**
     * 分页新增项目审批查询列表
     * @param query 查询条件
     * @return 数据集合
     */
    @ApiOperation("审批查询-新增项目-分页新增项目审批查询列表")
    @PreAuthorize("@ss.hasPermi('project:project:approval:add')")
    @GetMapping("/pageAddProjectApproval")
    public R<PageInfo<ProjectUpdateApprovalListVO>> pageAddProjectApproval(ProjectAddProjectQuery query) {
        PageInfo<ProjectUpdateApprovalListVO> pageInfo = this.projectService.pageAddProjectApproval(query);
        return R.success(pageInfo);
    }

    /**
     * 统计主体-新增项目审批数量
     * @return 数据集合
     */
    @ApiOperation("审批查询-新增项目-统计主题项目数量")
    @PreAuthorize("@ss.hasPermi('project:project:approval:add:sum')")
    @GetMapping("/sumAddProjectApproval")
    public R<List<ProjectAddApprovalSumVO>> sumAddProjectApproval() {
        List<ProjectAddApprovalSumVO> projectAddApprovalSum = this.projectService.sumAddProjectApproval();
        return R.success(projectAddApprovalSum);
    }

    /**
     * 根据项目id统计立项金额和毛利率
     * @param query 查询条件
     * @return 统计的数据
     */
    @ApiOperation("审批查询-新增项目-根据项目id统计立项金额和毛利率")
    @PreAuthorize("@ss.hasPermi('project:project:approval:add:sumIds')")
    @GetMapping("/sumAddProjectApprovalByIds")
    public R<AddProjectApprovalSelectSumVO> sumAddProjectApprovalByIds(ProjectAddProjectQuery query) {
        AddProjectApprovalSelectSumVO list = this.projectService.sumAddProjectApprovalByIds(query);
        return R.success(list);
    }

    /**
     * 导出项目管理-项目表
     * @param query 导出查询参数
     */
    @ApiOperation("审批查询-新增项目-下载新增项目审批查询")
    @PreAuthorize("@ss.hasPermi('project:project:approval:add:export')")
    @Log(title = "项目管理-项目表", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadAddProjectApproval")
    public R<String> downloadAddProjectApproval(@RequestBody ProjectAddProjectQuery query) {
        query.setExportType(ExportType.PROJECT_ADD_APPROVAL_LIST);
        reusableAsyncTaskService.addTask("新增项目审批查询",
                TaskType.Export,
                query,
                ProjectDownLoadServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 分页查询修改申请项目审批查询列表
     * @param query 查询条件
     * @return 数据集合
     */
    @ApiOperation("审批查询-修改申请-分页查询修改申请项目审批查询列表")
    @PreAuthorize("@ss.hasPermi('project:project:approval:update')")
    @GetMapping("/pageUpdateProjectApproval")
    public R<PageInfo<ProjectUpdateApprovalListVO>> pageUpdateProjectApproval(ProjectAddProjectQuery query) {
        PageInfo<ProjectUpdateApprovalListVO> pageInfo = this.projectService.pageUpdateProjectApproval(query);
        return R.success(pageInfo);
    }

    /**
     * 统计主体-修改申请审批数量
     * @return 数据集合
     */
    @ApiOperation("审批查询-修改申请-统计主体项目数量")
    @PreAuthorize("@ss.hasPermi('project:project:approval:update:sum')")
    @GetMapping("/sumUpdateProjectApproval")
    public R<List<ProjectAddApprovalSumVO>> sumUpdateProjectApproval() {
        List<ProjectAddApprovalSumVO> projectAddApprovalSum = this.projectService.sumUpdateProjectApproval();
        return R.success(projectAddApprovalSum);
    }

    /**
     * 修改申请-根据项目id统计立项金额和毛利率
     * @param query 查询参数
     * @return 统计的数据
     */
    @ApiOperation("审批查询-修改申请-根据项目id统计立项金额和毛利率")
    @PreAuthorize("@ss.hasPermi('project:project:approval:update:sumIds')")
    @GetMapping("/sumUpdateProjectApprovalByIds")
    public R<ProjectAddApprovalSumVO> sumUpdateProjectApprovalByIds(ProjectAddProjectQuery query) {
        ProjectAddApprovalSumVO list = this.projectService.sumUpdateProjectApprovalByIds(query);
        return R.success(list);
    }


    /**
     * 导出下载修改申请项目审批查询列表
     * @param query 导出查询参数
     */
    @ApiOperation("审批查询-修改申请-下载修改申请项目审批查询列表")
    @PreAuthorize("@ss.hasPermi('project:project:approval:update:export')")
    @Log(title = "项目管理-项目表", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadUpdateProjectApproval")
    public R<String> downloadUpdateProjectApproval(@RequestBody ProjectAddProjectQuery query) {
        query.setExportType(ExportType.PROJECT_UPDATE_APPROVAL_LIST);
        reusableAsyncTaskService.addTask("修改申请项目审批查询",
                TaskType.Export,
                query,
                ProjectDownLoadServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 项目下拉
     *
     * @param query 项目管理-项目表查询参数
     * @return 项目管理-项目表分页
     */
    @ApiOperation("项目下拉")
    @GetMapping("/listProject")
    public R<PageInfo<ProjectListVO>> listProject(ProjectQuery query) {
        PageInfo<ProjectListVO> voList = this.projectService.listProject(query);
        return R.success(voList);
    }


    /**
     * 获取导入查询模板
     * @param response 请求响应
     */
    @ApiOperation("项目管理-获取导入查询模板")
    @Log(title = "项目管理" , businessType = BusinessType.IMPORT)
    @PostMapping("/importSelectTemplate" )
    public void importSelectTemplate(HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_sed_project_select.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("SED项目管理-导入查询模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = null;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     *  项目管理导入查询
     * @param file 导入文件
     * @param projectStatus 项目状态
     * @return 导入结果
     */
    @ApiOperation("项目管理-导入查询")
    @Log(title = "项目管理-导入查询" , businessType = BusinessType.IMPORT)
    @PostMapping("/import/select/data")
    public R<ImportProjectSelectVO> projectImportSelectDate(MultipartFile file, Integer projectStatus) throws Exception {

        //Assert.notNull(projectStatus, "请选择项目状态");
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        //校验是否有数据
        ExcelUtil<ImportProjectSelectModel> importUtils = new ExcelUtil<>(ImportProjectSelectModel.class);
        List<ImportProjectSelectModel> importProjectSelectModelList = importUtils.importExcel(file.getInputStream());
        if (ListUtil.isEmpty(importProjectSelectModelList)){
            throw new ServiceException("导入失败,无法读取数据,请检查");
        }

        ImportProjectSelectQuery query = new ImportProjectSelectQuery();
        if (!Objects.isNull(projectStatus)){
            query.setProjectStatus(projectStatus);
        }
        return R.success(this.projectSearchImporter.importSelectData(importProjectSelectModelList,query));
    }

    /**
     * 查询修改审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询修改审批列表上的创建部门下拉" )
    @GetMapping("/listUpdateProjectDept" )
    public R<List<String>> listUpdateProjectDept(ProjectAddProjectQuery query){
        return R.success(projectService.listUpdateProjectDept(query));
    }

    /**
     * 查询新增审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询新增审批列表上的创建部门下拉" )
    @GetMapping("/listAddProjectDept" )
    public R<List<String>> listAddProjectDept(ProjectAddProjectQuery query){
        return R.success(projectService.listAddProjectDept(query));
    }


    /**
     * 项目管理-删除确认记录
     * @param deleteDto 删除参数
     * @return 是否成功
     */
    @ApiOperation("项目管理-删除确认记录及其关联数据")
    @PostMapping("/deleteProjectConfirmRecord")
    public R<Void> deleteProjectConfirmRecord(@Validated @RequestBody ProjectConfirmRecordDeleteDTO deleteDto){
        this.projectConfirmRecordDeleteService.deleteProjectConfirmRecord(deleteDto.getConfirmRecordIdList());
        return R.success();
    }


    /**
     * 刷新项目通过率
     * @return 是否成功
     */
    @ApiOperation("项目管理-刷新说有项目通过率")
    @PostMapping("/updateProjectRate")
    public R<Void> updateProjectRebate(){
        this.projectService.updateProjectRebate();
        return R.success();
    }


    @ApiOperation("项目管理-刷新项目立项部门快照信息")
    @PostMapping("/updateProjectSnapshotInfo")
    public R<List<Long>> updateProjectSnapshotInfo(){
        return R.success(this.projectService.updateProjectSnapshotInfo());
    }


    /**
     * 导入更新项目编号文件
     * @param file
     * @return
     */
    @PostMapping("/updateProjectCode")
    public R<Void> updateProjectCode(MultipartFile file) throws Exception {
        //Assert.notNull(projectStatus, "请选择项目状态");
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        //校验是否有数据
        ExcelUtil<ImportProjectCodeModel> importUtils = new ExcelUtil<>(ImportProjectCodeModel.class);
        List<ImportProjectCodeModel> importList = importUtils.importExcel(file.getInputStream());
        if (ListUtil.isEmpty(importList)){
            throw new ServiceException("导入失败,无法读取数据,请检查");
        }
        this.projectService.updateProjectCode(importList);
        return R.success();
    }

    /**
     * 项目管理-校验批量修改
     * @param query
     * @return
     */
    @ApiOperation("项目-校验批量修改")
    @PreAuthorize("@ss.hasPermi('project:project:query')")
    @PostMapping("/checkBatchUpdate")
    public R<CheckErrorInfoVO> checkBatchUpdate(@RequestBody BatchConfirmationQuery query){
        return this.projectService.checkBatchUpdate(query);
    }

    /**
     * 系统管理-项目交接列表
     * @param query
     * @return
     */
    @ApiOperation("系统管理-可交接项目列表")
    @GetMapping("/pageHandoverProject")
    public R<PageInfo<HandoverProjectListVO>> pageHandoverProject(HandoverProjectQuery query){
        PageInfo<HandoverProjectListVO> voList = projectService.pageHandoverProject(query);
        return R.success(voList);
    }
}
