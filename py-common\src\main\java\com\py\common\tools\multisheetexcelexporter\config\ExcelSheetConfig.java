package com.py.common.tools.multisheetexcelexporter.config;


import com.py.common.tools.multisheetexcelexporter.domain.HyperlinkPackage;
import com.py.common.tools.multisheetexcelexporter.domain.HyperlinkPathPackage;
import com.py.common.tools.multisheetexcelexporter.domain.ImagePackage;
import com.py.common.tools.multisheetexcelexporter.enums.CellType;
import com.py.common.tools.multisheetexcelexporter.enums.ExportType;
import com.py.common.tools.poiexcel.annotation.ImageStorageLocation;
import com.py.common.utils.collection.ListUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Excel表导出配置类
 * @param <T> 出力对象的类型
 * <AUTHOR>
 */
@SuppressWarnings({"unused", "UnusedReturnValue"})
public class ExcelSheetConfig<T> {

    /** 顶部表头配置 */
    @Getter
    private final List<ExcelSheetConfigItem<T>> topHeaderConfig = new ArrayList<>();

    /** 次级表头配置 */
    @Getter
    private final List<ExcelSheetConfigItem<T>> subHeaderConfig = new ArrayList<>();

    /** 表格说明, 放在Excel最上面 用于添加说明性文本 */
    private final List<String> description = new ArrayList<>();

    /** 底部文本 */
    private final List<String> bottomText = new ArrayList<>();

    /** 顶部表头配置点列表 用于判断位置上是否已配置顶部表头 */
    private List<Boolean> topHeaderConfigPointList = new ArrayList<>();

    /** 次级表头配置点列表 用于判断位置上是否已配置次级表头 */
    private List<Boolean> subHeaderConfigPointList = new ArrayList<>();

    /** 是否合并没有顶部表头的单元格 */
    @Setter
    private boolean isMergeEmptyHeader = true;

    /** Excel样式类型 */
    @Getter
    @Setter
    private ExcelStyleType styleType = ExcelStyleType.Default;

    /** 基础表头配置*/
    @Getter
    private List<ExcelSheetConfigItem<T>> baseHeaderConfig = new ArrayList<>();

    /** Excel主标题 */
    @Getter
    @Setter
    private String title;

    /** Excel副标题 */
    @Getter
    @Setter
    private String subTitle;

    /** 是否需要生成序列号 */
    @Getter
    private boolean needSerialNumber = false;

    /** 导入类型 */
    @Getter
    private ExportType exportType = ExportType.OVERWRITE;

    /** 导入类型为插入时, 原有表头的行数 */
    @Getter
    private int existHeaderRowNo = 1;

    /** 开始插入的行 */
    @Getter
    @Setter
    private int startInsertRowNo = 0;

    /** 导出行指定器 */
    @Getter
    private Function<T, Integer> rowSpecifier = null;

    /** 需要删除的行号集合 */
    @Getter
    private Set<Integer> removeRowSet = null;

    /** 不删除的行号集合 */
    @Getter
    private Set<Integer> notRemoveRowSet = null;

    /**
     * 是否已配置顶部表头
     * @return true: 已配置 false: 未配置
     */
    public boolean isConfiguredTopHeader() {
        return ListUtil.any(this.topHeaderConfig);
    }

    /**
     * 是否已配置次级表头
     * @return true: 已配置 false: 未配置
     */
    public boolean isConfiguredSubHeader() {
        return ListUtil.any(this.subHeaderConfig);
    }

    /** 是否合并没有顶部表头的单元格*/
    public boolean isMergeEmptyHeader() {
        return this.isMergeEmptyHeader;
    }

    /**
     * 添加输出单个Excel单元格的配置项
     * @param condition 是否添加配置
     * @param contextSelector 提取输出内容的函数
     * @param titleName 内容的Excel标题
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> addConfig(boolean condition, Function<T, Object> contextSelector, String titleName) {
        if(condition == false) {
            return this;
        }
        return this.addConfig(contextSelector, titleName);
    }

    /**
     * 添加输出单个Excel单元格的配置项
     * @param contextSelector 提取输出内容的函数
     * @param titleName 内容的Excel标题
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> addConfig(Function<T, Object> contextSelector, String titleName) {
        List<String> titleList = Collections.singletonList(titleName);
        this.baseHeaderConfig.add(new ExcelSheetConfigItem<>(contextSelector, titleList, false));
        return this;
    }

    /**
     * 为最后一个配置的导出单元格添加样式修改器
     * <p>单元格配置已存在调整者时覆盖原有调整者</p>
     * @param modifier 样式修改器
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> appendStyleModifier(CellStyleModifier modifier) {
        Assert.notEmpty(this.baseHeaderConfig, "配置错误, 未配置单元格");

        ListUtil.lastOrThrow(this.baseHeaderConfig).setStyleModifier(modifier);

        return this;
    }

    /**
     * 添加下拉框单元格的配置项
     * <p>根据导出时的数据生成每列不同的下拉框, 主要用于创建复杂模板</p>
     * <p>如下输入时:</p>
     * <p> 1: A,B,C</p>
     * <p> 2: 甲,乙,丙,丁</p>
     * <p> 3: null</p>
     * <p> 4: []</p>
     * <p>则第一列设置可选内容为A,B,C的下拉框</p>
     * <p>第二列设置可选内容为甲,乙,丙,丁的下拉框</p>
     * <p>第三列, 第四列不设置下拉框</p>
     * @param optionSelector 选项内容获取函数
     * @param titleName 内容的Excel标题
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> addDropDownCellConfig(Function<T, List<String>> optionSelector, String titleName) {
        this.baseHeaderConfig.add(ExcelSheetConfigItem.createDropDownCellConfig(optionSelector, titleName));
        return this;
    }

    /**
     * 添加图片单元格的配置项
     * @param contextSelector 提取输出内容的函数
     * @param titleName 内容的Excel标题
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> addImageCellConfig(Function<T, String> contextSelector, String titleName, ImageStorageLocation storageLocation){
        List<String> titleList = Collections.singletonList(titleName);
        ExcelSheetConfigItem<T> configItem = new ExcelSheetConfigItem<>(item -> new ImagePackage(contextSelector.apply(item), storageLocation), titleList, false);
        configItem.setCellType(CellType.IMAGE);
        this.baseHeaderConfig.add(configItem);

        return this;
    }

    /**
     * 设置高
     * @param height 高
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> addHeight(Integer height){
        Objects.requireNonNull(ListUtil.last(baseHeaderConfig)).setHeight(height);
        return this;
    }

    /**
     * 设置宽
     * @param wide 宽
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> addWidth(Integer wide){
        Objects.requireNonNull(ListUtil.last(baseHeaderConfig)).setWidth(wide);
        return this;
    }

    /**
     * 添加图片单元格
     * @param contextSelector 提取输出内容的函数
     * @param titleName 内容的Excel标题
     * @return 图片
     */
    public ExcelSheetConfig<T> addImageCellConfig(Function<T, String> contextSelector, String titleName){
        return this.addImageCellConfig(contextSelector, titleName, ImageStorageLocation.OSS);
    }

    /**
     * 添加oss附件单元格
     * @param contextSelector 提取输出内容的函数
     * @param titleName 内容的Excel标题
     * @return 图片
     */
    public ExcelSheetConfig<T> addFileCellConfig(Function<T, HyperlinkPackage> contextSelector, String titleName){
        List<String> titleList = Collections.singletonList(titleName);
        ExcelSheetConfigItem<T> configItem = new ExcelSheetConfigItem<>(item-> new HyperlinkPackage(contextSelector.apply(item)), titleList, false);
        configItem.setCellType(CellType.OSS_FILE);
        this.baseHeaderConfig.add(configItem);
        return this;
    }

    /**
     * 添加相对路径附件单元格
     * @param contextSelector 提取输出内容的函数
     * @param titleName 内容的Excel标题
     * @return 图片
     */
    public ExcelSheetConfig<T> addPathFileCellConfig(Function<T, HyperlinkPathPackage> contextSelector, String titleName){
        List<String> titleList = Collections.singletonList(titleName);
        ExcelSheetConfigItem<T> configItem = new ExcelSheetConfigItem<>(item-> new HyperlinkPathPackage(contextSelector.apply(item)), titleList, false);
        configItem.setCellType(CellType.PATH_FILE);
        this.baseHeaderConfig.add(configItem);
        return this;
    }

    /**
     * 获取最后配置表头的位置
     * @param headerConfigPointList 表头配置点列表
     * @return 最后配置表头的位置
     */
    public static int getLastConfigHeaderPoint(List<Boolean> headerConfigPointList) {
        if(ListUtil.isEmpty(headerConfigPointList)) {
            return 0;
        }

        for(int index = headerConfigPointList.size() - 1; index >= 0; index--) {
            if(headerConfigPointList.get(index) == true) {
                return index + 1;
            }
        }
        return 0;
    }

    /**
     * 导入子模型配置
     * @param subSelector 需要配置的子模型选择器
     * @param subConfig 子模型配置
     * @param <TSub> 子模型类型
     * @return 自身 用于链式调用
     */
    public <TSub> ExcelSheetConfig<T> includeSubConfig(Function<T, TSub> subSelector, ExcelSheetConfig<TSub> subConfig) {
        this.includeBaseSubConfig(subSelector, subConfig.getBaseHeaderConfig());
        this.mergeHeader(this.topHeaderConfig, topHeaderConfigPointList, subConfig.getTopHeaderConfig());
        this.mergeHeader(this.subHeaderConfig, subHeaderConfigPointList, subConfig.getSubHeaderConfig());
        return this;
    }

    /**
     * 导入子基础表头配置
     * @param subSelector 需要配置的子模型选择器
     * @param baseHeaderConfig 基础表头配置
     * @param <TSub> 子模型类型
     */
    private <TSub> void includeBaseSubConfig(Function<T, TSub> subSelector, List<ExcelSheetConfigItem<TSub>> baseHeaderConfig) {
        for(ExcelSheetConfigItem<TSub> subConfigItem : baseHeaderConfig) {
            ExcelSheetConfigItem<T> configItem = new ExcelSheetConfigItem<>(
                    mainItem -> subConfigItem.getContextSelector().apply(subSelector.apply(mainItem)),
                    subConfigItem.getHeaderList(),
                    subConfigItem.isList());

            configItem.setCellType(subConfigItem.getCellType());
            configItem.setHeight(subConfigItem.getHeight());
            configItem.setWidth(subConfigItem.getWidth());
            configItem.setStyleModifier(subConfigItem.getStyleModifier());

            this.baseHeaderConfig.add(configItem);
        }
    }

    /**
     * 修改导出配置
     * @param excelHeader 自定义导入表头列表
     */
    public void modifyConfig(List<ExcelHeader> excelHeader) {
        if(ListUtil.isEmpty(excelHeader) == true) {
            return;
        }

        if((this.baseHeaderConfig.size() == excelHeader.size()) == false) {
            throw new IllegalArgumentException();
        }
        // 全部不导出时, 改为导出全部字段
        boolean allFalse = excelHeader.stream().allMatch(item -> item.isExport() == false);
        if(allFalse == true) {
            return;
        }

        List<ExcelSheetConfigItem<T>> temp = new ArrayList<>();

        for(int i = 0; i < this.baseHeaderConfig.size(); i++) {
            if(excelHeader.get(i).isExport() == true) {
                temp.add(this.baseHeaderConfig.get(i));
            }
        }
        // 全选时显示多行表头, 其他情况显示单行表头
        boolean allTrue = excelHeader.stream().allMatch(x -> x.isExport());
        if(allTrue == false) {
            this.topHeaderConfig.clear();
            this.topHeaderConfigPointList.clear();
            this.subHeaderConfig.clear();
            this.subHeaderConfigPointList.clear();
        }

        this.baseHeaderConfig = temp;
    }

    public int countConfig(List<ExcelHeader> excelHeader) {
        int i = 1;
        if(excelHeader != null) {
            i = (int) excelHeader.stream().filter(item -> item.isExport() == true).count();
        }
        return i;
    }

    /**
     * 获取自定义表头
     * @return 自定义表头列表
     */
    public List<ExcelHeader> getCustomizeHeader() {
        if(ListUtil.isEmpty(this.baseHeaderConfig) == true) {
            return Collections.emptyList();
        }

        int index = 0;
        List<ExcelHeader> result = new ArrayList<>(this.baseHeaderConfig.size());
        for(ExcelSheetConfigItem<T> configItem : this.baseHeaderConfig) {
            ExcelHeader excelHeader = new ExcelHeader();
            excelHeader.setTitle(configItem.getHeaderList().toString());
            excelHeader.setIndex(index++);
            excelHeader.setExport(false);
            result.add(excelHeader);
        }

        return result;
    }

    /**
     * 添加输出多个Excel单元格的配置项
     * @param listContextSelector 提取输出List的函数
     * @param titleList 输出的List的Excel标题列表
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> addConfig(Function<T, Object> listContextSelector, List<String> titleList) {
        this.baseHeaderConfig.add(new ExcelSheetConfigItem<>(listContextSelector, titleList, true));
        return this;
    }

    /**
     * 合并表头
     * @param mainHeaderConfig 主表头
     * @param mainHeaderConfigPointList 主表头配置点列表
     * @param subHeaderConfig 副表头
     * @param <TSub> 子模型类型
     */
    private <TSub> void mergeHeader(List<ExcelSheetConfigItem<T>> mainHeaderConfig, List<Boolean> mainHeaderConfigPointList, List<ExcelSheetConfigItem<TSub>> subHeaderConfig) {
        if(ListUtil.isEmpty(subHeaderConfig)) {
            return;
        }
        // 在主表上级表头后追加子模型的上级表头
        int lastConfigHeaderPoint = getLastConfigHeaderPoint(mainHeaderConfigPointList);
        for(ExcelSheetConfigItem<TSub> subConfigItem : subHeaderConfig) {
            this.addHeaderConfig(
                    mainHeaderConfig,
                    mainHeaderConfigPointList,
                    ListUtil.firstOrThrow(subConfigItem.getHeaderList()),
                    lastConfigHeaderPoint + subConfigItem.getHeaderStartPoint(),
                    lastConfigHeaderPoint + subConfigItem.getHeaderEndPoint());
        }
    }

    /**
     * 添加顶部表头配置
     * @param headerName 顶部表头名
     * @param startPoint 顶部表头起始点 从0开始
     * @param endPoint 顶部表头结束点
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> addTopHeaderConfig(String headerName, int startPoint, int endPoint) throws IllegalArgumentException {
        this.addHeaderConfig(this.topHeaderConfig, this.topHeaderConfigPointList, headerName, startPoint, endPoint);
        return this;
    }

    /**
     * 添加次级表头配置
     * @param headerName 次级表头名
     * @param startPoint 次级表头起始点 从0开始
     * @param endPoint 次级表头结束点
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> addSubHeaderConfig(String headerName, int startPoint, int endPoint) throws IllegalArgumentException {
        this.addHeaderConfig(this.subHeaderConfig, this.subHeaderConfigPointList, headerName, startPoint, endPoint);
        return this;
    }

    /**
     * 添加表头配置
     * @param headerConfigList 表头配置列表
     * @param headerConfigPointList 表头配置点位
     * @param headerName 表头名称
     * @param startPoint 顶部表头起始点 从0开始
     * @param endPoint 顶部表头结束点
     */
    private void addHeaderConfig(List<ExcelSheetConfigItem<T>> headerConfigList, List<Boolean> headerConfigPointList, String headerName, int startPoint, int endPoint) {
        // 起始点大于等于结束的的时候抛异常
//        if(startPoint >= endPoint) {
//            throw new IllegalArgumentException(String.format("起始点(%s)不能大于等于结束点(%s)", startPoint, endPoint));
//        }

        // 在已配置顶部表头的地方二次配置的时候抛异常
        if(this.isConfiguredHeader(headerConfigPointList, startPoint, endPoint)) {
            throw new IllegalArgumentException(String.format("区间 (%s, %s) 范围内已配置表头", startPoint, endPoint));
        }
        headerConfigList.add(ExcelSheetConfigItem.createTopHeaderConfig(headerName, startPoint, endPoint));
    }

    /**
     * 追加顶部表头配置
     * @param headerName 顶部表头名
     * @param len 表头长度
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> appendTopHeaderConfig(String headerName, int len) {
        int lastConfigHeaderPoint = getLastConfigHeaderPoint(this.topHeaderConfigPointList);
        this.addTopHeaderConfig(headerName, lastConfigHeaderPoint, lastConfigHeaderPoint + len);

        return this;
    }

    /**
     * 该位置是否包含已配置的顶部表头
     * @param index 需要判定的位置
     * @return true: 已配置 false: 未配置
     */
    public boolean isConfiguredTopHeader(int index) {
        try {
            return this.topHeaderConfigPointList.get(index);
        } catch(IndexOutOfBoundsException e) {
            // 超出列表范围说明没有配置
            return false;
        }
    }

    /**
     * 该位置是否包含已配置的次级表头
     * @param index 需要判定的位置
     * @return true: 已配置 false: 未配置
     */
    public boolean isConfiguredSubHeader(int index) {
        try {
            return this.subHeaderConfigPointList.get(index);
        } catch(IndexOutOfBoundsException e) {
            // 超出列表范围说明没有配置
            return false;
        }
    }

    /**
     * 区间内是否包含已配置的顶部表头
     * @param startPoint 起始点
     * @param endPoint 结束点
     * @return true: 已配置 false: 未配置
     */
    private boolean isConfiguredTopHeader(int startPoint, int endPoint) {
        return isConfiguredHeader(topHeaderConfigPointList, startPoint, endPoint);
    }

    /**
     * 区间内是否包含已配置的次级表头
     * @param startPoint 起始点
     * @param endPoint 结束点
     * @return true: 已配置 false: 未配置
     */
    private boolean isConfiguredSubHeader(int startPoint, int endPoint) {
        return isConfiguredHeader(subHeaderConfigPointList, startPoint, endPoint);
    }

    /**
     * 追加次级表头配置
     * @param headerName 次级表头名
     * @param len 表头长度
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> appendSubHeaderConfig(String headerName, int len) {
        int lastConfigHeaderPoint = getLastConfigHeaderPoint(this.subHeaderConfigPointList);
        this.addSubHeaderConfig(headerName, lastConfigHeaderPoint, lastConfigHeaderPoint + len);

        return this;
    }

    /**
     * 区间内是否包含已配置的顶部表头
     * @param headerConfigPointList 表头配置点列表
     * @param startPoint 起始点
     * @param endPoint 结束点
     * @return true: 已配置 false: 未配置
     */
    private boolean isConfiguredHeader(List<Boolean> headerConfigPointList, int startPoint, int endPoint) {

        // 如果结束点大于原有判定列表,则扩充判定列表
        if(headerConfigPointList.size() < endPoint + 1) {
            for(int i = headerConfigPointList.size(); i <= endPoint + 1; i++) {
                headerConfigPointList.add(false);
            }
        }

        // 检查判定列表中是否包含 true
        boolean hasSet;
        if (startPoint == endPoint){
            hasSet = headerConfigPointList.get(startPoint);
        }else {
            hasSet = headerConfigPointList
                    .subList(startPoint, endPoint + 1)
                    .stream().anyMatch(x -> x);
        }

        if(hasSet) {
            return true;
        }

        for(int i = startPoint; i <= endPoint; i++) {
            headerConfigPointList.set(i, true);
        }
        return false;
    }

    /** 取得基础标题数量*/
    public int getBaseHeaderCount() {
        return this.baseHeaderConfig.stream().mapToInt(x -> x.getHeaderList().size()).sum();
    }

    /**
     * 设置是否需要生成序列号
     * @param value 设置值
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> setNeedSerialNumber(boolean value) {
        this.needSerialNumber = value;

        return this;
    }

    /**
     * 设置自定义导出行的行指定器
     * <p>使数据在行指定器指定的行导出, 而依据默认的顺序导出</p>
     * @param rowSpecifier 行指定器
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> setCustomizationExportRowConfig(Function<T, Integer> rowSpecifier) {
        Assert.notNull(rowSpecifier, "行指定器不能为null");
        Assert.isNull(this.rowSpecifier, "配置错误: 已配置过行指定器");
        this.rowSpecifier = rowSpecifier;

        return this;
    }

    /**
     * 设置导出时需要移除的行号集合
     * <p>该配置仅在写入既有Excel时才生效</p>
     * <p>将在导出时, 删除集合行号指定的行</p>
     * <p>表头所在的行必定不会删除, 表头行由 existHeaderRowNo 字段定义</p>
     * @param notRemoveRowCollection 不删除行号集合
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> setRemoveRowConfig(Collection<Integer> notRemoveRowCollection) {
        Assert.isNull(this.removeRowSet, "配置错误: 已配置过删除行号集合");
        Assert.isNull(this.notRemoveRowSet, "配置错误: 已配置过不删除行号集合, 不能同时配置删除行号集合");
        Assert.notEmpty(notRemoveRowCollection, "配置错误: 删除行号集合不能为空");
        this.removeRowSet = new HashSet<>(notRemoveRowCollection);

        return this;
    }

    /**
     * 设置导出时不需要移除指定行的行指定器
     * <p>该配置仅在写入既有Excel时才生效</p>
     * <p>将在导出时, 删除集合行号外的所有行</p>
     * <p>表头所在的行必定不会删除, 表头行是哪几行由 existHeaderRowNo 字段定义</p>
     * @param notRemoveRowCollection 不删除行号集合
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> setNotRemoveRowConfig(Collection<Integer> notRemoveRowCollection) {
        Assert.isNull(this.notRemoveRowSet, "配置错误: 已配置过不删除行号集合");
        Assert.isNull(this.removeRowSet, "配置错误: 已配置过删除行号集合, 不能同时配置不删除行号集合");
        Assert.notEmpty(notRemoveRowCollection, "配置错误: 不删除行号集合不能为空");
        this.notRemoveRowSet = new HashSet<>(notRemoveRowCollection);

        return this;
    }

    /**
     * 启用插入导入模式
     * <p>当前仅支持最前部插入</p>
     * @param existHeaderRowNo 原有表头行数
     * @return 自身 用于链式调用
     */
    public ExcelSheetConfig<T> useInsertModel(int existHeaderRowNo) {
        this.exportType = ExportType.INSERT;
        this.existHeaderRowNo = existHeaderRowNo;
        return this;
    }

    /**
     * 统一移动表头位置
     * @param shiftNumber 移动数量, 数组为正时: 向右移动, 数组为负时:向左移动
     */
    public void shiftHeader(int shiftNumber) {
        this.getTopHeaderConfig().forEach(configItem -> configItem.shiftHeader(shiftNumber));
        this.getSubHeaderConfig().forEach(configItem -> configItem.shiftHeader(shiftNumber));

        if(shiftNumber > 0) {
            List<Boolean> shiftList = Stream.generate(() -> Boolean.FALSE).limit(shiftNumber).collect(Collectors.toList());
            this.topHeaderConfigPointList = ListUtil.merge(shiftList, this.topHeaderConfigPointList);
            this.subHeaderConfigPointList = ListUtil.merge(shiftList, this.subHeaderConfigPointList);
        } else if(shiftNumber < 0) {
            this.topHeaderConfigPointList = this.topHeaderConfigPointList.subList(Math.abs(shiftNumber), this.topHeaderConfigPointList.size());
            this.subHeaderConfigPointList = this.subHeaderConfigPointList.subList(Math.abs(shiftNumber), this.subHeaderConfigPointList.size());
        }
    }

    /**
     * 是否存在删除行配置
     * @return true: 存在 false: 不存在
     */
    public boolean hasRemoveRowConfig() {
        return this.removeRowSet != null || this.notRemoveRowSet != null;
    }

    /** Excel样式类型 */
    public enum ExcelStyleType {
        /* 黑白 */
        BlackAndWhite,
        /* 默认 */
        Default
    }
}
