package com.py.crm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 根据客户名称精确查询并携带业务线vo
 *
 * <AUTHOR>
 */
@Data
public class CustomerLineBusinessVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**品牌/业务线*/
    @ApiModelProperty("品牌/业务线")
    private String lineBusiness;

    /**客户名称*/
    @ApiModelProperty("客户名称")
    private String name;

    /**客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 合作状态(0.合作中 1.暂停合作 2.意向合作)*/
    @ApiModelProperty(value = "合作状态(0.合作中 1.暂停合作 2.意向合作)")
    private Integer cooperationStatus;

    /**客户名称-品牌业务线*/
    @ApiModelProperty("客户名称-品牌业务线")
    public String getNameWithLineBusiness() {
        return this.getName() + "-" + this.getLineBusiness();
    }
}
