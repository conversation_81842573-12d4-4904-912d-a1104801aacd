package com.py.web.controller.oss;

import com.py.common.core.domain.R;
import com.py.common.oss.IOssService;
import com.py.common.oss.model.TempToken;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * OssController
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Api(tags = "文件上传")
@RestController
@RequestMapping("/oss")
public class OssController {

    @Resource
    private IOssService ossService;


    /**
     * 获取oss临时凭证
     * @return 临时凭证视图模型
     */
    @ApiOperation("获取oss临时凭证")
    @GetMapping("/getAliYunOssConfig")
    public R<TempToken> getAliYunOssConfig(){
        return R.success(ossService.getAliYunOssConfig());
    }
}
