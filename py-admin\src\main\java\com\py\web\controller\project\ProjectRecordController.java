package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.project.enums.ExportType;
import com.py.project.project.domain.vo.ProjectDetailsVO;
import com.py.project.projectrecord.domain.dto.ProjectRecordDTO;
import com.py.project.projectrecord.domain.dto.ProjectRecordExportModel;
import com.py.project.projectrecord.domain.query.ProjectRecordQuery;
import com.py.project.projectrecord.domain.query.ProjectRecordUpdateStatusApprovalQuery;
import com.py.project.projectrecord.domain.vo.*;
import com.py.project.projectrecord.service.IProjectRecordService;
import com.py.project.projectrecord.service.impl.ProjectRecordServiceImpl;
import com.py.project.projectrecord.service.impl.ProjectRecordUpdateStatusDownloadServiceImpl;
import com.py.project.projectresource.domain.dto.UpdateStatusDTO;
import com.py.project.projectresource.domain.query.ResourceCountQuery;
import com.py.project.projectresource.domain.vo.ResourceCountVO;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目管理-项目保存记录Controller
 *
 * <AUTHOR>
 * @date 2023-08-01
 */
@Api(tags = "项目管理-项目保存记录")
@RestController
@RequestMapping("/project/projectRecord")
public class ProjectRecordController extends BaseController {

    /** 项目管理-项目保存记录服务 */
    @Resource
    private IProjectRecordService projectRecordService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 系统设置-主体参数设置服务 */
    @Resource
    private ISystemMainstayParamService systemMainstayParamService;

    /**
     * 查询项目管理-项目保存记录列表
     *
     * @param query 项目管理-项目保存记录查询参数
     * @return 项目管理-项目保存记录列表
     */
    @ApiOperation("查询项目管理-项目保存记录列表")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:list')")
    @GetMapping("/listProjectRecord")
    public R<List<ProjectRecordListVO>> listProjectRecord(ProjectRecordQuery query) {
        List<ProjectRecordListVO> voList = this.projectRecordService.listProjectRecord(query);
        return R.success(voList);
    }

    /**
     * 分页查询项目管理-项目保存记录列表
     *
     * @param query 项目管理-项目保存记录查询参数
     * @return 项目管理-项目保存记录分页
     */
    @ApiOperation("分页查询询项目管理-项目保存记录列表")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:list')")
    @GetMapping("/pageProjectRecord")
    public R<PageInfo<ProjectRecordListVO>> pageProjectRecord(ProjectRecordQuery query) {
        PageInfo<ProjectRecordListVO> voList = this.projectRecordService.pageProjectRecordList(query);
        return R.success(voList);
    }

    /**
     * 获取执行表单保存记录详情
     * @param query 项目管理-项目保存记录主键
     * @return 项目管理-项目保存记录视图模型
     */
    @ApiOperation("获取执行表单保存记录详情")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:query')")
    @GetMapping(value = "/getInfo")
    public R<ProjectResourceRecordVO> getInfo(ProjectRecordQuery query) {
        return R.success(projectRecordService.selectProjectRecordById(query));
    }

    /**
     * 执行表单详情的资源统计
     * @param query 项目管理-项目保存记录主键
     * @return 项目管理-项目保存记录视图模型
     */
    @ApiOperation("执行表单详情的资源统计")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:query')")
    @GetMapping(value = "/resourceRecordCount")
    public R<ResourceCountVO> resourceRecordCount(ProjectRecordQuery query) {
        return R.success(projectRecordService.resourceRecordCount(query));
    }

    /**
     * 获取修改申请记录详情
     * @param query 项目管理-项目保存记录主键
     * @return 项目管理-项目保存记录视图模型
     */
    @ApiOperation("获取修改申请记录详情")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:query')")
    @GetMapping(value = "/getUpdateInfo")
    public R<ProjectUpdateRecordVO> getUpdateInfo(ProjectRecordQuery query) {
        return R.success(projectRecordService.getUpdateInfo(query));
    }

    /**
     * 获取修改状态保存记录详情
     * @param query 项目管理-项目保存记录主键
     * @return 项目管理-项目保存记录视图模型
     */
    @ApiOperation("获取修改状态保存记录详情")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:query')")
    @GetMapping(value = "/getUpdateStatusInfo")
    public R<ProjectResourceRecordVO> getUpdateStatusInfo(ProjectRecordQuery query) {
        return R.success(projectRecordService.getUpdateStatusInfo(query));
    }

    /**
     * 获取新增项目记录详情
     * @param query 项目管理-项目保存记录主键
     * @return 项目管理-项目保存记录视图模型
     */
    @ApiOperation("获取新增项目记录详情")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:query')")
    @GetMapping(value = "/getAddProjectInfo")
    public R<ProjectDetailsVO> getAddProjectInfo(ProjectRecordQuery query) {
        return R.success(projectRecordService.getAddProjectInfo(query));
    }

    /**
     * 新增项目审批页面 - 执行表单合计
     *
     * @param query 查询参数
     * @return 项目资源表视图模型
     */
    @ApiOperation("新增项目审批页面 - 执行表单合计")
    @PostMapping(value = "/sumResource")
    public R<ResourceCountVO> sumResource(@RequestBody ResourceCountQuery query) {
        return R.success(projectRecordService.sumResource(query));
    }

    /**
     * 新增项目管理-项目保存记录
     *
     * @param dto 项目管理-项目保存记录修改参数
     * @return 是否成功
     */
    @ApiOperation("新增项目管理-项目保存记录")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:add')")
    @Log(title = "项目管理-项目保存记录", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody List<ProjectRecordDTO> dto) {
        return R.success(projectRecordService.insertProjectRecord(dto));
    }

    /**
     * 修改项目管理-项目保存记录
     *
     * @param dto 项目管理-项目保存记录修改参数
     * @return 是否成功
     */
    @ApiOperation("修改项目管理-项目保存记录")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:edit')")
    @Log(title = "项目管理-项目保存记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody UpdateStatusDTO dto) {
        return R.success(projectRecordService.updateProjectRecord(dto));
    }

    /**
     * 执行表单保存记录详情表单下载
     * @param query 导出查询参数
     */
    @ApiOperation("执行表单保存记录详情表单下载")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:export')")
    @Log(title = "项目管理-执行表单保存记录详情表单下载", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@RequestBody ProjectRecordQuery query) {
        query.setExportType(ExportType.PROJECT_RESOURCE_LIST_DETAILS_RECORD_INFO);
        reusableAsyncTaskService.addTask("项目详情执行表单下载", TaskType.Export,query, ProjectRecordServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('project:projectRecord:import')" )
    @Log(title = "项目管理-项目保存记录" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<ProjectRecordExportModel> util = new ExcelUtil<>(ProjectRecordExportModel.class);
        List<ProjectRecordExportModel> projectRecordList = util.importExcel(file.getInputStream());
        String message = this.projectRecordService.importProjectRecord(projectRecordList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('project:projectRecord:import')" )
    @Log(title = "项目管理-项目保存记录" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ProjectRecordExportModel> util = new ExcelUtil<>(ProjectRecordExportModel.class);
        util.importTemplateExcel(response, "项目管理-项目保存记录数据" );
    }

    /**
     * 审批查询-修改状态-分页列表
     * @param query 查询参数
     * @return 数据集合
     */
    @ApiOperation("审批查询-修改状态-分页列表")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:approval:list')")
    @GetMapping("/pageUpdateStatusApprovalList")
    public R<PageInfo<ProjectRecordUpdateStatusApprovalVO>> pageUpdateStatusApprovalList(ProjectRecordUpdateStatusApprovalQuery query) {
        PageInfo<ProjectRecordUpdateStatusApprovalVO> voList = this.projectRecordService.pageUpdateStatusApprovalList(query);
        return R.success(voList);
    }

    /**
     * 审批查询-修改状态-主体统计
     * @return 数据集合
     */
    @ApiOperation("审批查询-修改状态-主体统计")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:approval:mainstay:sumList')")
    @GetMapping("/sumUpdateStatusApprovalByMainstayId")
    public R<List<ProjectRecordUpdateStatusApprovalSumVO>> sumUpdateStatusApprovalByMainstayId() {
        List<ProjectRecordUpdateStatusApprovalSumVO> voList = this.projectRecordService.sumUpdateStatusApprovalByMainstayId();
        return R.success(voList);
    }

    /**
     * 审批查询-修改状态-列表合计
     * @param query 查询参数
     * @return 数据集合
     */
    @ApiOperation("审批查询-修改状态-列表合计")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:approval:mainstay:sumIds')")
    @GetMapping("/sumUpdateStatusApprovalByIds")
    public R<ProjectRecordUpdateStatusApprovalSumVO> sumUpdateStatusApprovalByIds(ProjectRecordUpdateStatusApprovalQuery query) {
        ProjectRecordUpdateStatusApprovalSumVO vo = this.projectRecordService.sumUpdateStatusApprovalByIds(query);
        return R.success(vo);
    }

    /**
     * 导出下载修改状态审批查询列表
     * @param query 导出查询参数
     */
    @ApiOperation("审批查询-修改状态-下载")
    @PreAuthorize("@ss.hasPermi('project:projectRecord:approval:mainstay:export')")
    @Log(title = "项目管理-项目保存记录", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadUpdateStatusApprovalList")
    public R<String> downloadUpdateStatusApprovalList(@RequestBody ProjectRecordUpdateStatusApprovalQuery query) {
        query.setExportType(ExportType.PROJECT_RECORD_UPDATE_STATUS_APPROVAL_LIST);
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPyMainstayId());
        String fileName = "修改状态审批查询列表-" +systemMainstayParamVO.getMainstayName()+"-"+ DateUtils.format(LocalDateTime.now(),"yyyy-MM-dd-HH：mm：ss")+".xlsx";
        query.setFileName(fileName);
        this.reusableAsyncTaskService.addTask("修改状态审批查询",
                TaskType.Export,
                query,
                ProjectRecordUpdateStatusDownloadServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 查询审批列表上的更新部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询审批列表上的更新部门下拉" )
    @GetMapping("/listUpdateStatusDept" )
    public R<List<String>> listUpdateStatusDept(ProjectRecordUpdateStatusApprovalQuery query){
        return R.success(projectRecordService.listUpdateProjectDept(query));
    }
}
