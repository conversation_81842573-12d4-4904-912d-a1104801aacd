package com.py.common.tools.verify.config;

import com.py.common.tools.verify.enums.VerifyType;
import lombok.Getter;
import lombok.Setter;

import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class VerifyItem<T> {

    /** 验证类型 */
    private VerifyType verifyType;

    /** 提取验证内容的函数 */
    private Function<T, Object> contextSelector;

    /** 错误消息生成器 */
    private Function<T, String> errorMessageGenerator;
}
