package com.py.common.utils.tree;

import com.py.common.utils.collection.ListUtil;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.py.common.utils.NullMergeUtils.nullMerge;

/**
 * 树工具类
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class TreeUtil {

    /**
     * 树根节点
     */
    public static final Long TREE_ROOT = 0L;

    /**
     * 构建前端所需要下拉树结构
     * @param treeNodeList 树节点列表
     * @return 下拉树结构列表
     */
    public static <T extends ITreeNode<T>> List<TreeSelect<T>> buildDeptTreeSelect(List<T> treeNodeList) {
        List<T> treeList = buildDeptTree(treeNodeList);
        return ListUtil.map(treeList, treeNode -> new TreeSelect<>(treeNode));
    }

    /**
     * 使用默认根节点构建树
     * @param treeNodeList 树节点列表
     * @param <T> 树元素类型
     * @return 树结构列表
     */
    public static <T extends ITreeNode<T>> List<T> buildTree(List<T> treeNodeList) {
        return buildTree(treeNodeList, TREE_ROOT);
    }

    /**
     * 使用指定的根节点构建树
     * @param treeNodeList 树节点列表
     * @param rootNodeId 根节点Id
     * @param <T> 树元素类型
     * @return 树结构列表
     */
    public static <T extends ITreeNode<T>> List<T> buildTree(List<T> treeNodeList, Long rootNodeId) {
        Map<Long, List<T>> childrenMap = ListUtil.toGroup(treeNodeList, ITreeNode::getParentId);
        for(T node : treeNodeList) {
            List<T> childrenList = nullMerge(childrenMap.get(node.getTreeId()), ListUtil.emptyList());
            node.setChildren(childrenList);
        }

        if(TREE_ROOT.equals(rootNodeId)) {
            T rootNode = ListUtil.first(treeNodeList, node -> TREE_ROOT.equals(node.getTreeId()));
            if(rootNode != null){
                return ListUtil.singletonList(rootNode);
            }

            return treeNodeList.stream()
                    .filter(orgNode -> rootNodeId.equals(orgNode.getParentId()))
                    .collect(Collectors.toList());
        } else {
            return treeNodeList.stream()
                    .filter(orgNode -> rootNodeId.equals(orgNode.getParentId()))
                    .collect(Collectors.toList());
        }
    }

    /**
     * 构建前端所需要树结构
     * @param treeNodeList 树节点列表
     * @return 树结构列表
     */
    private static <T extends ITreeNode<T>> List<T> buildDeptTree(List<T> treeNodeList) {
        List<T> returnList = new ArrayList<>();
        List<Long> treeIdListList = ListUtil.map(treeNodeList, ITreeNode::getTreeId);
        for(T node : treeNodeList) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if(TREE_ROOT.equals(node.getParentId()) == true) {
                recursionFn(treeNodeList, node);
                returnList.add(node);
            }
        }

        if(returnList.isEmpty()) {
            returnList = treeNodeList;
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private static <T extends ITreeNode<T>> void recursionFn(List<T> treeNodeList, T treeParent) {
        // 得到子节点列表
        List<T> childList = getChildList(treeNodeList, treeParent);
        if(ListUtil.isEmpty(childList)) {
            return;
        }
        treeParent.setChildren(childList);
        for(T treeChild : childList) {
            recursionFn(treeNodeList, treeChild);
        }
    }


    /**
     * 得到子节点列表
     * @param treeNodeList 可能的子节点列表
     * @param treeParent 父节点
     * @param <T> 节点类型
     * @return 子节点列表
     */
    private static <T extends ITreeNode<T>> List<T> getChildList(List<T> treeNodeList, T treeParent) {
        return treeNodeList.stream()
                .filter(treeNode -> treeNode.getParentId() != null && treeNode.getParentId().equals(treeParent.getTreeId()))
                .collect(Collectors.toList());
    }

    /**
     * 设置树节点相对关系
     * @param rootTreeList 树列表
     */
    public static <T extends ITreeNode<T>> void setRelativeLocation(List<TreeSelect<T>> rootTreeList) {
        if(ListUtil.isEmpty(rootTreeList) == true) {
            return;
        }
        // 设置前后关系
        List<TreeSelect<T>> treeList = rootTreeList.stream()
                .flatMap(node -> node.getAllNode().stream())
                .collect(Collectors.toList());
        TreeSelect<T> previous = ListUtil.firstOrThrow(treeList);
        for(int index = 1; index < treeList.size(); index++) {
            TreeSelect<T> current = treeList.get(index);
            previous.setNext(current.getId());
            current.setPrevious(previous.getId());
            previous.setNextName(current.getLabel());
            current.setPreviousName(previous.getLabel());
            previous = current;
        }
    }

    /**
     * 设置树节点是否能上下移动
     * @param treeList 树列表
     */
    public static <T extends ITreeNode<T>> void setCanUpDown(List<TreeSelect<T>> treeList) {
        if(ListUtil.isEmpty(treeList) == true) {
            return;
        }

        int index = 0, treeListSize = treeList.size();
        for(TreeSelect<T> treeNode : treeList) {
            treeNode.setNodeIndex(index++);
            treeNode.setCanUp(treeNode.getNodeIndex() != 0);
            treeNode.setCanDown(treeNode.getNodeIndex() != treeListSize - 1);

            setCanUpDown(treeNode.getChildren());
        }
    }

    /**
     * 获取树的所有节点
     * <p>包括自身节点</p>
     * @param treeNodeList 树节点列表
     * @return 所有节点
     */
    public static <T extends ITreeNode<T>> List<T> getAllNode(List<T> treeNodeList) {
        Assert.notEmpty(treeNodeList, "树节点列表不能为空");

        return treeNodeList.stream()
                .flatMap(node -> TreeUtil.getAllNode(node).stream())
                .collect(Collectors.toList());
    }

    /**
     * 获取树的所有节点
     * <p>包括自身节点</p>
     * @param tree 树节点
     * @return 所有节点
     */
    public static <T extends ITreeNode<T>> List<T> getAllNode(T tree) {
        Assert.notNull(tree, "树节点不能为空");

        if(ListUtil.isEmpty(tree.getChildren())) {
            return Collections.singletonList(tree);
        }

        List<T> result = tree.getChildren().stream()
                .flatMap(node -> getAllNode(node).stream())
                .collect(Collectors.toList());
        result.add(tree);
        return result;
    }

    /**
     * 初始化树的祖籍列表
     * <p>必须使用 TreeUtil.buildTree 或相似方法构建树</p>
     * @param tree 树
     * @param <T> 树元素类型
     */
    public static <T extends IAncestors<T>> void initTreeAncestors(List<T> tree) {
        initTreeAncestors(tree, null);
    }

    /**
     * 初始化树的祖籍列表
     * <p>必须使用 TreeUtil.buildTree 或相似方法构建树</p>
     * @param tree 树
     * @param parent 父节点
     * @param <T> 树元素类型
     */
    public static <T extends IAncestors<T>> void initTreeAncestors(List<T> tree, T parent) {
        if(ListUtil.isEmpty(tree)) {
            return;
        }

        for(T node : tree) {
            node.initAncestors(parent);
            initTreeAncestors(node.getChildren(), node);
        }
    }
}
