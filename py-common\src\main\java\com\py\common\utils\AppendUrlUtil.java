package com.py.common.utils;

import java.util.Iterator;
import java.util.Map;

/**
 * 地址拼接
 * <AUTHOR> pyr
 * @date : 2022-03-16 14:39
 **/
public class AppendUrlUtil {

    /**
     * url拼接
     * @param url
     * @param map
     * @return
     */
    public static String getAppendUrl(String url, Map<String, Object> map) {
        if (map != null && !map.isEmpty()) {
            StringBuffer buffer = new StringBuffer();
            Iterator<Map.Entry<String, Object>> iterator = map.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Object> entry = iterator.next();
                if (StringUtils.isEmpty(buffer.toString())) {
                    buffer.append("?");
                } else {
                    buffer.append("&");
                }
                buffer.append(entry.getKey()).append("=").append(entry.getValue());
            }
            url += buffer.toString();
        }
        return url;
    }
}
