package com.py.web.controller.crm;

import com.github.pagehelper.PageInfo;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.project.project.domain.vo.ProjectListVO;
import com.py.project.project.service.IProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 客户管理-客户-项目Controller
 *
 * <AUTHOR>
 * @date 2023-08-04
 */
@Api(tags = "客户管理-客户-项目")
@RestController
@RequestMapping("/com.py.web/project")

public class CustomerProjectController extends BaseController {
    /** 客户管理-客户-项目服务 */
    @Resource
    private IProjectService projectService;

    /**
     * 获取客户管理-客户-项目详细信息
     * @param id 客户管理-客户-主键
     * @return 客户管理-客户-项目视图模型
     */
    @ApiOperation("获取客户管理-客户-项目详细信息")
    @PreAuthorize("@ss.hasPermi('com.py.web:project:query')")
    @GetMapping(value = "/{id}")
    public R<PageInfo<ProjectListVO>> getInfo(@PathVariable("id") Long id) {
        return R.success(projectService.selectProjectDetailById(id));
    }
}
