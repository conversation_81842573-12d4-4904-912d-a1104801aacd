package com.py.crm.customer.customercontribute.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户管理-比稿策划人关联列表视图模型
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@Data
@ApiModel("客户管理-比稿策划人关联列表视图模型")
public class ComparedDraftUserListVO {
    private static final long serialVersionUID = 1L;

    /** 自增主键*/
    @ApiModelProperty("自增主键")
    private Long id;

    /** 关联id*/
    @ApiModelProperty("关联id")
    private Long relevanceId;

    /** 比稿id*/
    @ApiModelProperty("比稿id")
    private Long comparedDraftId;

    /** 策划人id*/
    @ApiModelProperty("策划人id")
    private Long userId;

    /** 策划人名称*/
    @ApiModelProperty("策划人名称")
    private String userName;

    /** 创建者Id*/
    @ApiModelProperty("创建者Id")
    private Long createId;

    /** 创建者*/
    @ApiModelProperty("创建者")
    private String createBy;

    /** 创建时间*/
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /** 更新者Id*/
    @ApiModelProperty("更新者Id")
    private Long updateId;

    /** 更新者*/
    @ApiModelProperty("更新者")
    private String updateBy;

    /** 更新时间*/
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /** 删除标志*/
    @ApiModelProperty("删除标志")
    private String delFlag;


}
