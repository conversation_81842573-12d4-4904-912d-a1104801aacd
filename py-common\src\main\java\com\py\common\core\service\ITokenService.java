package com.py.common.core.service;

import com.py.common.core.domain.model.LoginUser;

import javax.servlet.http.HttpServletRequest;

/**
 * token验证处理
 * <AUTHOR>
 * @version ITokenService 2023/11/22 14:40
 */
public interface ITokenService {

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    LoginUser getLoginUser(HttpServletRequest request);

    /**
     * 通过登录令牌获取登录用户
     * @param token 登录令牌
     * @return 登录用户
     */
    LoginUser getLoginUser(String token);
}
