package com.py.web.controller.crm;

import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.crm.customer.cooperatemainstay.domain.vo.CooperateMainstayVO;
import com.py.crm.customer.cooperatemainstay.domain.vo.CooperateMainstayListVO;
import com.py.crm.customer.cooperatemainstay.domain.query.CooperateMainstayQuery;
import com.py.crm.customer.cooperatemainstay.domain.dto.CooperateMainstayDTO;
import com.py.crm.customer.cooperatemainstay.domain.dto.CooperateMainstayExportModel;
import com.py.crm.customer.cooperatemainstay.service.ICooperateMainstayService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户管理-客户-合作主体Controller
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Api(tags = "客户管理-客户-合作主体")
@RestController
@RequestMapping("/com.py.web/cooperateMainstay")
public class CooperateMainstayController extends BaseController {

    /** 客户管理-客户-合作主体服务 */
    @Resource
    private ICooperateMainstayService cooperateMainstayService;

    /**
     * 分页查询客户管理-客户-合作主体列表
     *
     * @param query 客户管理-客户-合作主体查询参数
     * @return 客户管理-客户-合作主体分页
     */
    @ApiOperation("分页查询询客户管理-客户-合作主体列表")
    @PreAuthorize("@ss.hasPermi('com.py.web:cooperateMainstay:list')")
    @GetMapping("/pageCooperateMainstay")
    public R<PageInfo<CooperateMainstayListVO>> pageCooperateMainstay(CooperateMainstayQuery query) {
        PageInfo<CooperateMainstayListVO> voList = this.cooperateMainstayService.pageCooperateMainstayList(query);
        return R.success(voList);
    }

    /**
     * 获取客户管理-客户-合作主体详细信息
     * @param id 客户管理-客户-合作主体主键
     * @return 客户管理-客户-合作主体视图模型
     */
    @ApiOperation("获取客户管理-客户-合作主体详细信息")
    @PreAuthorize("@ss.hasPermi('com.py.web:cooperateMainstay:query')")
    @GetMapping(value = "/{id}")
    public R<List<CooperateMainstayVO>> getInfo(@PathVariable("id") Long id) {
        return R.success(cooperateMainstayService.selectCooperateMainstayDetailById(id));
    }

    /**
     * 新增客户管理-客户-合作主体
     *
     * @param dto 客户管理-客户-合作主体修改参数
     * @return 是否成功
     */
    @ApiOperation("新增客户管理-客户-合作主体")
    @PreAuthorize("@ss.hasPermi('com.py.web:cooperateMainstay:add')")
    @Log(title = "客户管理-客户-合作主体", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody CooperateMainstayDTO dto) {
        return R.success(cooperateMainstayService.insertCooperateMainstay(dto));
    }

    /**
     * 修改客户管理-客户-合作主体
     *
     * @param dto 客户管理-客户-合作主体修改参数
     * @return 是否成功
     */
    @ApiOperation("修改客户管理-客户-合作主体")
    @PreAuthorize("@ss.hasPermi('com.py.web:cooperateMainstay:edit')")
    @Log(title = "客户管理-客户-合作主体", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody CooperateMainstayDTO dto) {
        return R.success(cooperateMainstayService.updateCooperateMainstay(dto));
    }

    /**
     * 删除客户管理-客户-合作主体
     * @param ids 需要删除的客户管理-客户-合作主体主键集合
     * @return 是否成功
     */
    @ApiOperation("删除客户管理-客户-合作主体" )
    @PreAuthorize("@ss.hasPermi('com.py.web:cooperateMainstay:remove')")
    @Log(title = "客户管理-客户-合作主体", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(cooperateMainstayService.deleteCooperateMainstayByIds(ids));
    }

    /**
     * 导出客户管理-客户-合作主体
     * @param response 请求响应
     * @param query 导出查询参数
     */
    @ApiOperation("导出客户管理-客户-合作主体")
    @PreAuthorize("@ss.hasPermi('com.py.web:cooperateMainstay:export')")
    @Log(title = "客户管理-客户-合作主体", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CooperateMainstayQuery query) {
        List<CooperateMainstayExportModel> exportList = this.cooperateMainstayService.exportCooperateMainstay(query);

        ExcelUtil<CooperateMainstayExportModel> util = new ExcelUtil<>(CooperateMainstayExportModel. class);
        util.exportExcel(response, exportList, "客户管理-客户-合作主体数据" );
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('com.py.web:cooperateMainstay:import')" )
    @Log(title = "客户管理-客户-合作主体" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<CooperateMainstayExportModel> util = new ExcelUtil<>(CooperateMainstayExportModel.class);
        List<CooperateMainstayExportModel> cooperateMainstayList = util.importExcel(file.getInputStream());
        String message = this.cooperateMainstayService.importCooperateMainstay(cooperateMainstayList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('com.py.web:cooperateMainstay:import')" )
    @Log(title = "客户管理-客户-合作主体" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<CooperateMainstayExportModel> util = new ExcelUtil<>(CooperateMainstayExportModel.class);
        util.importTemplateExcel(response, "客户管理-客户-合作主体数据" );
    }

    /**
     * 根据客户id查询客户合作主体列表
     * @param customerId 客户id
     * @return 客户管理-客户-合作主体
     */
    @ApiOperation("根据客户id查询客户合作主体列表")
    @PreAuthorize("@ss.hasPermi('com.py.web:cooperateMainstay:import')" )
    @GetMapping("/list/{customerId}" )
    public R<List<CooperateMainstayListVO>> listCooperateMainstayList(@PathVariable("customerId") Long customerId){
        return R.success(cooperateMainstayService.listCooperateMainstayList(customerId));
    }

}
