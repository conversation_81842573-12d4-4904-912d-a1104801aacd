package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 删除标志
 * <AUTHOR>
 */
@AllArgsConstructor
public enum DelFlag implements IDict<String> {

    /** 未删除 */
    NOT_DELETED("0", "未删除"),
    /** 已删除 */
    DELETE("1", "已删除");

    private final String value;

    private final String label;


    @Override
    public String getValue() {
        return this.value;
    }

    @Override
    public String getLabel() {
        return this.label;
    }
}
