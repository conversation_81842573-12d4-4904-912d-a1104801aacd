package com.py.common.tools.multisheetexcelexporter.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 表头对象
 * <AUTHOR>
 */
@Data
@ApiModel("表头对象")
public class ExcelHeader {

    /** 表头 */
    @ApiModelProperty("表头")
    private String title;

    /** 索引 */
    @ApiModelProperty("索引")
    private Integer index;

    /** 是否导出 */
    @ApiModelProperty("是否导出")
    private boolean isExport = true;
}
