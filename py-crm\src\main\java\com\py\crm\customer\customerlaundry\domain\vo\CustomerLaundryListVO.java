package com.py.crm.customer.customerlaundry.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.utils.DateUtils;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户管理-查看清单列表视图模型
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Data
@ApiModel("客户管理-查看清单列表视图模型")
public class CustomerLaundryListVO implements CustomerLaundryBaseInfo {
    private static final long serialVersionUID = 1L;

    /** 自增id*/
    @ApiModelProperty("自增id")
    private Long id;

    /** 清单id*/
    @ApiModelProperty("清单id")
    private Long laundryId;

    /** 客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 客户名称*/
    @ApiModelProperty("客户名称")
    private String name;

    /** 合作状态(0.合作中 1.暂停合作 2.意向合作)*/
    @ApiModelProperty("合作状态(0.合作中 1.暂停合作 2.意向合作)")
    private Integer cooperationStatus;

    /** 品牌/业务线*/
    @ApiModelProperty("品牌/业务线")
    private String lineBusiness;

    /** 行业类目id*/
    @ApiModelProperty("行业类目id")
    private List<Long> industryCategoryIdList;

    /** 目标服务人员 */
    @ApiModelProperty("目标服务人员")
    private List<Long> serviceUserIdList;

    /** 目标服务人员id*/
    @ApiModelProperty("目标服务人员id")
    private String serviceUserName;
    private List<String> serviceUserNameList;

    /** 创建者Id*/
    @ApiModelProperty("创建者Id")
    private Long createId;

    /** 创建者*/
    @ApiModelProperty("创建者")
    private String createBy;

    /** 创建人部门*/
    @ApiModelProperty("创建人部门")
    private String createDept;

    /** 创建时间*/
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime createTime;

    /** 更新者Id*/
    @ApiModelProperty("更新者Id")
    private Long updateId;

    /** 更新者*/
    @ApiModelProperty("更新者")
    private String updateBy;

    /** 更新人部门*/
    @ApiModelProperty("更新人部门")
    private String updateDept;

    /** 更新时间*/
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /** 业务类型（0客户，1人脉） */
    @ApiModelProperty("业务类型（0客户，1人脉）")
    private Integer bizType;

    /** 业务id*/
    @ApiModelProperty("业务id")
    private Long bizId;
}
