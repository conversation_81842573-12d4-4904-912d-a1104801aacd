package com.py.common.core.domain.vo.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.py.common.core.domain.entity.SysUser;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 鉴权用户
 * <AUTHOR>
 */
@Data
@ApiModel("鉴权用户")
public class AuthUser {

    /** 用户ID */
    @ApiModelProperty("用户ID")
    private Long userId;

    /** 用户账号 */
    @ApiModelProperty("用户账号")
    private String userName;

    /** 用户直属主管 */
    @ApiModelProperty("用户直属主管")
    private List<Long> directLeaderList;

    /** 银行卡号 */
    @ApiModelProperty("银行卡号")
    private String account;

    /** 开户行 */
    @ApiModelProperty("开户行")
    private String accountBank;

    /** 用户邮箱 */
    @ApiModelProperty("用户邮箱")
    private String email;

    /** 手机号码 */
    @ApiModelProperty("手机号码")
    private String phoneNumber;

    /** 用户头像 */
    @ApiModelProperty("用户头像")
    private String avatar;

    /** 密码 */
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String password;

    /** 用户部门列表 */
    @ApiModelProperty("用户部门列表")
    private List<AuthUserDeptVO> deptList;

    /** 用户岗位列表 */
    @ApiModelProperty("用户岗位列表")
    private List<AuthUserPostVO> postList;

    /** 用户所有部门名称列表 - 仅登录时返回*/
    @ApiModelProperty("用户所有部门名称列表")
    private List<String> fullDeptNameList;

    /** 是否删除 */
    @ApiModelProperty("是否删除")
    private Boolean delFlag;

    /** 最后登录IP */
    @ApiModelProperty("最后登录IP")
    private String loginIp;

    /** 最后登录时间 */
    @ApiModelProperty("最后登录时间")
    private LocalDateTime loginDate;

    /** 是否初始密码：0否，1是*/
    @ApiModelProperty("是否初始密码：0否，1是")
    private Boolean defaultPassword;

    /**
     * 当前是否为管理员
     * @return true: 是管理员
     */
    public boolean isAdmin() {
        return SysUser.isAdmin(this.getUserId());
    }

    /**
     * 获取部门ID列表
     * @return 部门ID列表
     */
    @JsonIgnore
    public List<Long> getDeptIdList() {
        return ListUtil.map(this.getDeptList(), AuthUserDeptVO::getDeptId);
    }

    /**
     * 获取用户的部门和其所有上级部门的ID列表
     * @return 部门ID列表
     */
    @ApiModelProperty("用户的部门和其所有上级部门的ID列表")
    @JsonIgnore
    public List<Long> getFullDeptIdList(){
        List<Long> ancestors = this.getDeptList().stream()
                .map(AuthUserDeptVO::getAncestors)
                .map(StringUtils::convertLongList)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        return ListUtil.mergeAndDistinct(this.getDeptIdList(), ancestors);
    }

    /**
     * 获取岗位ID列表
     * @return 岗位ID列表
     */
    @JsonIgnore
    public List<Long> getPostIdList(){
        return ListUtil.map(this.getPostList(), AuthUserPostVO::getPostId);
    }

    /**
     * 获取用户部门名称
     * @return 用户部门名称
     */
    public String getDeptName(){
        if(ListUtil.isEmpty(this.getDeptList())){
            return StringUtils.EMPTY;
        }
        this.getDeptList().sort(Comparator.comparing(AuthUserDeptVO::getDeptId));
        return ListUtil.joining(this.getDeptList(), ",", AuthUserDeptVO::getDeptName);
    }

    /**
     * 获取用户职位名称
     * @return 用户职位名称
     */
    public String getPostName(){
        if(ListUtil.isEmpty(this.getPostList())){
            return StringUtils.EMPTY;
        }
        this.getPostList().sort(Comparator.comparing(AuthUserPostVO::getPostId));
        return ListUtil.joining(this.getPostList(), ",", AuthUserPostVO::getPostName);
    }
}
