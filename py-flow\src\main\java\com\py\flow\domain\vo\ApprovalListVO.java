package com.py.flow.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.enums.flow.FlowApprovalStatus;
import com.py.common.utils.DateUtils;
import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.flow.domain.dto.flow.ExtraField;
import com.py.flow.domain.enums.ApprovalBizType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 审批列表视图
 * <AUTHOR>
 */
@Data
@ApiModel("审批列表视图")
public class ApprovalListVO {

    /** 流程实例ID */
    @ApiModelProperty("流程实例ID")
    private Long flowInstanceId;

    /** 审批业务类型 */
    @ApiModelProperty("审批业务类型")
    private ApprovalBizType bizType;

    /** 业务ID */
    @ApiModelProperty("业务ID")
    private Long bizId;

    /** 审批名称 */
    @ApiModelProperty("审批名称")
    private String processName;

    /** 项目名称 */
    @ApiModelProperty("项目名称")
    private String projectName;

    /** 流程状态 */
    @ApiModelProperty("流程状态")
    private FlowApprovalStatus approvalStatus;

    /** 发起人 */
    @ApiModelProperty("发起人")
    private String launcher;

    /** 发起人部门 */
    @ApiModelProperty("发起人部门")
    private String launcherDept;

    /** 发起时间 */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime launchTime;

    /** 审批时间 */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime approvalTime;

    /**
     * 审批完结时间(流程状态不等于审批中,待审批)
     * (废弃)
     */
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty("审批完结时间-废弃)")
    private LocalDateTime updateTime;

    /** 手机端首页展示时间 */
    @ApiModelProperty("手机端首页展示时间")
    private String showTime;

    /** 新增的字段 */
    private ExtraField extraField;

    /**当前用户是否已读这条消息*/
    @ApiModelProperty("当前用户是否已读这条消息")
    private Boolean hasRead = false;

    /**用户消息id*/
    @ApiModelProperty("用户消息id")
    private Long flowInstanceReadId;
    /**
     * 非业务审批主体
     */
    @ApiModelProperty("非业务审批主体")
    private String nonBusinessMain;

    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty("审批到达时间")
    private LocalDateTime transferTime;

    public String getNonBusinessMain(){
        if (this.getExtraField() == null) {
            return null;
        }
        if (this.getExtraField().getPyMainstayName() != null) {
            return this.getExtraField().getPyMainstayName();
        }
        return nonBusinessMain;
    }

    /**
     * 派芽合作主体名称(业务审批)
     */
    @ApiModelProperty("派芽合作主体名称(业务审批)")
    private List<String> paiyaMainstayNameList;

    /**
     * 获取表单名字
     * @return 表单名字
     */
    @ApiModelProperty("表单名字")
    public String getFromName(){
        return NullMergeUtils.nullMerge(this.getBizType(), dict -> dict.getLabel() + "申请");
    }

    /**
     * 派芽合作主体名称(业务审批)
     */
    @ApiModelProperty("派芽合作主体名称(业务审批)")
    private String paiyaMainstayName;

    public String getPaiyaMainstayName() {
        if (ListUtil.isEmpty(this.getPaiyaMainstayNameList())){
            return null;
        }
        return this.getPaiyaMainstayNameList().stream()
                .collect(Collectors.joining(","));
    }


    /**
     * 审批完结时间
     */
    @ApiModelProperty("审批完结时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approvalCompletionTime;


}
