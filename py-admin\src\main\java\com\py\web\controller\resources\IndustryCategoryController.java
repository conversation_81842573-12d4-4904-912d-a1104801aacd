package com.py.web.controller.resources;

import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.resources.historyindustrycategory.domain.query.HistoryIndustryCategoryQuery;
import com.py.resources.historyindustrycategory.domain.vo.HistoryIndustryCategoryListVO;
import com.py.resources.historyindustrycategory.service.IHistoryIndustryCategoryService;
import com.py.resources.industrycategory.domain.dto.IndustryCategoryDTO;
import com.py.resources.industrycategory.domain.query.IndustryCategoryQuery;
import com.py.resources.industrycategory.domain.vo.IndustryCategoryDeptVO;
import com.py.resources.industrycategory.domain.vo.IndustryCategoryListVO;
import com.py.resources.industrycategory.domain.vo.IndustryCategoryVO;
import com.py.resources.industrycategory.service.IIndustryCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 媒介资源管理-行业类目Controller
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Api(tags = "媒介资源管理-行业类目")
@RestController
@RequestMapping("/industryCategory")
public class IndustryCategoryController extends BaseController {

    /** 媒介资源管理-行业类目服务 */
    @Resource
    private IIndustryCategoryService dustryCategoryService;

    /** 媒介资源管理-行业类目-历史Service接口 */
    @Resource
    private IHistoryIndustryCategoryService historyIndustryCategoryService;

    /**
     * 分页查询媒介资源管理-行业类目列表
     *
     * @param query 媒介资源管理-行业类目查询参数
     * @return 媒介资源管理-行业类目分页
     */
    @ApiOperation("分页查询询媒介资源管理-行业类目列表")
    @PreAuthorize("@ss.hasPermi('com.py.resources:dustryCategory:list')")
    @GetMapping("/queryTree")
    public R<List<IndustryCategoryListVO>> queryTree(IndustryCategoryQuery query) {
        List<IndustryCategoryListVO> voList = this.dustryCategoryService.pageDustryCategoryList(query);
        return R.success(voList);
    }

    /**
     * 查询媒介资源管理-行业类目列表,更新部门
     *
     * @param query 更新部门
     * @return 媒介资源管理-行业类目,部门信息
     */
    @ApiOperation("分页查询询媒介资源管理-行业类目列表,更新部门")
    @PostMapping("/findTreeUpdateDept")
    public R<List<IndustryCategoryDeptVO>> findTreeUpdateDept(@RequestBody IndustryCategoryQuery query) {
        List<IndustryCategoryDeptVO> voList = this.dustryCategoryService.findTreeUpdateDept(query);
        return R.success(voList);
    }

    /**
     * 分页查询媒介资源管理-历史行业类目列表
     *
     * @param query 媒介资源管理-行业类目查询参数
     * @return 媒介资源管理-行业类目分页
     */
    @ApiOperation("分页查询询媒介资源管理-历史行业类目列表")
    @GetMapping("/listTree")
    public R<List<HistoryIndustryCategoryListVO>> listAllTree(HistoryIndustryCategoryQuery query) {
        return R.success(historyIndustryCategoryService.listAllTree(query));
    }

    /**
     * 获取媒介资源管理-行业类目详细信息
     * @param id 媒介资源管理-行业类目主键
     * @return 媒介资源管理-行业类目视图模型
     */
    @ApiOperation("获取媒介资源管理-行业类目详细信息")
    @GetMapping(value = "/query/{id}")
    public R<IndustryCategoryVO> getInfo(@PathVariable("id") Long id) {
        return R.success(dustryCategoryService.selectDustryCategoryById(id));
    }

    /**
     * 新增媒介资源管理-行业类目
     *
     * @param dto 媒介资源管理-行业类目修改参数
     * @return 是否成功
     */
    @ApiOperation("新增媒介资源管理-行业类目")
    @PreAuthorize("@ss.hasPermi('com.py.resources:dustryCategory:add')")
    @Log(title = "媒介资源管理-行业类目", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Boolean> add(@Validated @RequestBody IndustryCategoryDTO dto) {
        return R.success(dustryCategoryService.insertIndustryCategory(dto));
    }

    /**
     * 修改媒介资源管理-行业类目
     *
     * @param dto 媒介资源管理-行业类目修改参数
     * @return 是否成功
     */
    @ApiOperation("修改媒介资源管理-行业类目")
    @PreAuthorize("@ss.hasPermi('com.py.resources:dustryCategory:edit')")
    @Log(title = "媒介资源管理-行业类目", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R<Boolean> edit(@Validated @RequestBody IndustryCategoryDTO dto) {
        return R.success(dustryCategoryService.updateDustryCategory(dto));
    }

    /**
     * 删除媒介资源管理-行业类目
     * @param dto 需要删除的媒介资源管理-行业类目主键集合
     * @return 是否成功
     */
    @ApiOperation("删除媒介资源管理-行业类目" )
    @PreAuthorize("@ss.hasPermi('com.py.resources:dustryCategory:remove')")
    @Log(title = "媒介资源管理-行业类目", businessType = BusinessType.DELETE)
    @PostMapping("/remove" )
    public R<Boolean> remove(@RequestBody IndustryCategoryDTO dto) {
        return R.success(dustryCategoryService.deleteDustryCategoryByIds(dto.getIndustryCategoryId()));
    }

    /**
     * 拖拽媒介资源管理-行业类目
     * @param dto 媒介资源管理-行业类目参数
     * @return 是否成功
     */
    @ApiOperation("拖拽媒介资源管理-行业类目" )
    @PreAuthorize("@ss.hasPermi('com.py.resources:dustryCategory:drag')")
    @Log(title = "媒介资源管理-行业类目", businessType = BusinessType.OTHER)
    @PostMapping("/drag" )
    public R<Boolean> drag(@RequestBody IndustryCategoryDTO dto) {
        return R.success(dustryCategoryService.drag(dto));
    }

}
