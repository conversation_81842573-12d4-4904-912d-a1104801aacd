package com.py.crm.customer.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.py.common.file.FileAnnex;
import com.py.common.file.FileInfoVO;
import com.py.crm.customer.contact.domain.vo.ContactVO;
import com.py.crm.customer.cooperatemainstay.domain.vo.CooperateMainstayVO;
import com.py.crm.customer.customeraccount.domain.vo.CustomerAccountVO;
import com.py.crm.customer.customeraddress.domain.vo.CustomerAddressVO;
import com.py.crm.customer.customervisit.domian.vo.CustomerVisitVO;
import com.py.crm.customer.invoicing.domain.vo.InvoicingVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 快照信息
 * <AUTHOR>
 * @version CustomerSnapshotVO 2023/8/4 17:56
 */
@Data
public class CustomerSnapshotVO {
    private static final long serialVersionUID = 1L;

    /** 审批状态(0.待审批 1.审批中 2.审批完成)*/
    @ApiModelProperty("审批状态(0.待审批 1.审批中 2.审批完成)")
    private Integer auditStatus;

    /** 报价策略(0.资源差价模式 1.拆分模式（策略费+服务费+资源返点）2.资源差价+拆分模式)*/
    @ApiModelProperty("报价策略(0.资源差价模式 1.拆分模式（策略费+服务费+资源返点）2.资源差价+拆分模式)")
    private Integer biddingStrategy;

    /** 品牌阶段(0.初创品牌 1.成长期品牌 2.成熟品牌)*/
    @ApiModelProperty("品牌阶段(0.初创品牌 1.成长期品牌 2.成熟品牌)")
    private Integer brandStage;

    /** 品牌核心生意来源(0.全渠道销售（线下强，线上辅助）1.全渠道销售（线上强，线下辅助）2. 纯线上渠道)*/
    @ApiModelProperty("品牌核心生意来源(0.全渠道销售（线下强，线上辅助）1.全渠道销售（线上强，线下辅助）2. 纯线上渠道)")
    private Integer businessSource;

    /** 状态变更原因*/
    @ApiModelProperty("状态变更原因")
    private String changeStatusReason;

    /** 合作状态(0.合作中 1.暂停合作 2.意向合作)*/
    @ApiModelProperty("合作状态(0.合作中 1.暂停合作 2.意向合作)")
    private Integer cooperationStatus;

    /** 合作部门*/
    @ApiModelProperty("合作部门")
    private String cooperativeSector;

    /** 创建者*/
    @ApiModelProperty("创建者")
    private String createBy;

    /** 创建人部门*/
    @ApiModelProperty("创建人部门")
    private String createDept;

    /** 创建者Id*/
    @ApiModelProperty("创建者Id")
    private Long createId;

    /** 创建时间*/
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 客户来源(0.老客推荐 1.熟人介绍 2.主动BD 3.客户直联)*/
    @ApiModelProperty("客户来源(0.老客推荐 1.熟人介绍 2.主动BD 3.客户直联)")
    private Integer customerSource;

    /** 客户类型(0.品牌客户 1.平台客户 2.集团客户 3.渠道客户)*/
    @ApiModelProperty("客户类型(0.品牌客户 1.平台客户 2.集团客户 3.渠道客户)")
    private Integer customerType;

    /** 决策链路*/
    @ApiModelProperty("决策链路")
    private String decisionLink;

    /** 删除标志*/
    @ApiModelProperty("删除标志")
    private String delFlag;

    /** 自增id*/
    @ApiModelProperty("自增id")
    private Long id;

    /** 行业类目id*/
    @ApiModelProperty("行业类目id")
    private List<Long> industryCategoryIdList;

    /** 品牌/业务线*/
    @ApiModelProperty("品牌/业务线")
    private String lineBusiness;

    /** 客户名称*/
    @ApiModelProperty("客户名称")
    private String name;

    /** 备注*/
    @ApiModelProperty("备注")
    private String remark;

    /** 派芽服务产品(0.内容营销全链路服务 1.整合营销服务 2.内容营销策略及执行服务 3.媒介投放服务 4.MCN服务)*/
    @ApiModelProperty("派芽服务产品(0.内容营销全链路服务 1.整合营销服务 2.内容营销策略及执行服务 3.媒介投放服务 4.MCN服务)")
    private List<String> serviceProduct;

    /** 目标服务人员id*/
    @ApiModelProperty("目标服务人员id")
    private Long serviceUserId;

    /** 更新者*/
    @ApiModelProperty("更新者")
    private String updateBy;

    /** 更新人部门*/
    @ApiModelProperty("更新人部门")
    private String updateDept;

    /** 更新者Id*/
    @ApiModelProperty("更新者Id")
    private Long updateId;

    /** 更新时间*/
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 版本*/
    @ApiModelProperty("版本")
    private Integer version;

    /** 企业联系人 */
    private List<ContactVO> contactVOList;

    /** 客户管理-客户-合作主体 */
    private List<CooperateMainstayVO> cooperateMainstayVOList;

    /** 客户管理-客户-客户地址 */
    private List<CustomerAddressVO> customerAddressVOList;

    /** 账户信息数据 */
    private List<CustomerAccountVO> customerAccountVOList;

    /** 开票信息数据 */
    private List<InvoicingVO> invoicingVOList;

    /** 附件地址 {filename  ossKy}*/
    @ApiModelProperty("附件地址 {filename  ossKy}")
    private List<FileInfoVO> annexList;

    @JsonIgnore
    private List<FileAnnex> annex;

    /**客户拜访日志数据*/
    private List<CustomerVisitVO> customerVisitVOList;
}
