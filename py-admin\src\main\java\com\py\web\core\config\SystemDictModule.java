package com.py.web.core.config;

import com.py.common.config.dict.AbstractDictModule;
import com.py.common.datascope.DataScopePageType;
import com.py.common.datascope.DataScopeType;
import com.py.system.recyclebin.domain.enums.RecycleBizType;
import org.springframework.stereotype.Component;

/**
 * 系统字典模组
 * <AUTHOR>
 */
@Component
public class SystemDictModule extends AbstractDictModule {

    /** 初始化 */
    @Override
    protected void init() {
        // 注册字典
        registered(DataScopeType.class);
        registered(DataScopePageType.class);
        registered(RecycleBizType.class);
    }
}
