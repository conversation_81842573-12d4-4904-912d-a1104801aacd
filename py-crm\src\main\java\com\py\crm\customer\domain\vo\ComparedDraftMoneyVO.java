package com.py.crm.customer.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 客户比稿金额
 *
 * <AUTHOR>
 * @date 2023-08-23
 */
@Data
@ApiModel("客户比稿金额" )
public class ComparedDraftMoneyVO {
    private static final long serialVersionUID = 1L;

    /** 客户id */
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 比稿总金额 */
    @ApiModelProperty("比稿总金额")
    private BigDecimal comparedDraftMoneySum;

    /** 比稿金额 */
    private BigDecimal comparedDraftMoney;

    /** 比稿项目id */
    @ApiModelProperty("比稿项目id")
    private Long comparedDraftId;
}
