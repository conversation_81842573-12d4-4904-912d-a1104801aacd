package com.py.common.enums;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/7/28 14:29
 */
@AllArgsConstructor
@ApiModel("业绩类型" )
public enum PerformanceType  implements IDict<Integer>{

    /** 销售和管理人员 */
    SALESANDMANAGE(0,"销售和管理人员"),
    /** 执行人员*/
    EXECUTE(1,"执行人员类型");

    private final Integer value;

    private final String label;

    /**
     * 获取字典说明
     * @return 字典说明
     */
    @Override
    public String getLabel() {
        return label;
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public Integer getValue() {
        return value;
    }
}
