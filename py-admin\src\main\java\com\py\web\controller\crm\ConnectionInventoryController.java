package com.py.web.controller.crm;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.connectioninventory.domain.dto.ConnectionInventoryDTO;
import com.py.crm.connectioninventory.domain.dto.ConnectionInventoryDeleteDTO;
import com.py.crm.connectioninventory.domain.dto.ConnectionInventoryExportDTO;
import com.py.crm.connectioninventory.domain.query.ConnectionInventoryQuery;
import com.py.crm.connectioninventory.domain.vo.ConnectionInventoryCountVO;
import com.py.crm.connectioninventory.domain.vo.ConnectionInventoryListVO;
import com.py.crm.connectioninventory.service.IConnectionInventoryService;
import com.py.crm.connectioninventory.service.impl.ConnectionInventoryServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 人脉管理-清单Controller
 *
 * <AUTHOR>
 * @date 2023-09-26
 */
@Api(tags = "人脉管理-清单")
@RestController
@RequestMapping("/connectionInventory")
public class ConnectionInventoryController extends BaseController {

    /** 人脉管理-清单服务 */
    @Resource
    private IConnectionInventoryService connectionInventoryService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 人脉 - 加入清单
     *
     * @param dto 人脉管理-清单修改参数
     * @return 是否成功
     */
    @ApiOperation("人脉-加入清单")
    @PreAuthorize("@ss.hasPermi('connectioninventory:connectioninventory:add')")
    @Log(title = "人脉-加入清单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Boolean> add(@Validated @RequestBody ConnectionInventoryDTO dto) {
        return R.success(connectionInventoryService.insertConnectionInventory(dto));
    }

    /**
     * 查询清单有效、无效数量
     *
     * @return 查询人脉管理表列表有效无效数量
     */
    @ApiOperation("查询清单有效、无效数量")
    @PreAuthorize("@ss.hasPermi('connectioninventory:connectioninventory:list')")
    @GetMapping("/selectInventoryCount")
    public R<ConnectionInventoryCountVO> selectInventoryCount() {
        return R.success(connectionInventoryService.selectInventoryCount());
    }

    /**
     * 分页查询人脉管理-清单列表
     *
     * @param query 人脉管理-清单查询参数
     * @return 人脉管理-清单分页
     */
    @ApiOperation("查询人脉清单分页列表")
    @PreAuthorize("@ss.hasPermi('connectioninventory:connectioninventory:list')")
    @GetMapping("/pageConnectionInventory")
    public R<PageInfo<ConnectionInventoryListVO>> pageConnectionInventory(ConnectionInventoryQuery query) {
        PageInfo<ConnectionInventoryListVO> voList = this.connectionInventoryService.pageConnectionInventoryList(query, true, true);
        return R.success(voList);
    }

    /**
     * 清单页面-下载人脉
     * @param exportDTO 导出查询参数
     */
    @ApiOperation("清单页面-下载人脉")
    @PreAuthorize("@ss.hasPermi('connectioninventory:connectioninventory:export')")
    @Log(title = "清单页面-下载人脉", businessType = BusinessType.EXPORT)
    @PostMapping("/exportConnection")
    public R<String> exportConnection(@Validated @RequestBody ConnectionInventoryExportDTO exportDTO) {
        String fileName = "人脉管理-" + DateUtils.getTimeCn() + ".xlsx";
        exportDTO.setFileName(fileName);
        //获取所有的人脉清单数据
        ConnectionInventoryQuery inventoryQuery = new ConnectionInventoryQuery();
        inventoryQuery.setStatus(null);

        //获取人脉基本信息列表
        PageInfo<ConnectionInventoryListVO> inventoryPage = connectionInventoryService.pageConnectionInventoryList(inventoryQuery, false, false);
        List<ConnectionInventoryListVO> inventoryList = inventoryPage.getList();
        List<Long> connectionIds = new ArrayList<>();
        if(ListUtil.isNotEmpty(inventoryList)) {
            connectionIds = ListUtil.map(inventoryList, ConnectionInventoryListVO::getConnectionId);
            exportDTO.setConnectionIds(connectionIds);
        }

        //移除人脉的所有清单数据
        ConnectionInventoryDeleteDTO inventoryDeleteDTO = new ConnectionInventoryDeleteDTO();
        inventoryDeleteDTO.setConnectionIds(connectionIds);
        deleteInventory(inventoryDeleteDTO);

        reusableAsyncTaskService.addTask("人脉", TaskType.Export, exportDTO, ConnectionInventoryServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 移除清单
     * @param dto 人脉清单删除实体
     * @return 是否成功
     */
    @ApiOperation("移除/批量移除" )
    @PreAuthorize("@ss.hasPermi('connectioninventory:connectioninventory:remove')")
    @Log(title = "移除/批量移除", businessType = BusinessType.DELETE)
    @PostMapping("/deleteInventory" )
    public R<Boolean> deleteInventory(@Validated @RequestBody ConnectionInventoryDeleteDTO dto) {
        return R.success(this.connectionInventoryService.deleteInventory(dto));
    }
}
