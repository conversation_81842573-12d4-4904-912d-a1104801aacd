package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.project.projectclosecasedetails.domain.dto.ProjectCloseCaseDetailsDTO;
import com.py.project.projectclosecasedetails.domain.dto.ProjectCloseCaseDetailsExportModel;
import com.py.project.projectclosecasedetails.domain.query.ProjectCloseCaseDetailsQuery;
import com.py.project.projectclosecasedetails.domain.vo.ProjectCloseCaseDetailsListVO;
import com.py.project.projectclosecasedetails.domain.vo.ProjectCloseCaseDetailsVO;
import com.py.project.projectclosecasedetails.service.IProjectCloseCaseDetailsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 项目管理-项目结案详情Controller
 *
 * <AUTHOR>
 * @date 2023-08-17
 */
@Api(tags = "项目管理-项目结案详情")
@RestController
@RequestMapping("/project/pyProjectCloseCaseDetails")
public class ProjectCloseCaseDetailsController extends BaseController {

    /** 项目管理-项目结案详情服务 */
    @Resource
    private IProjectCloseCaseDetailsService pyProjectCloseCaseDetailsService;

    /**
     * 分页查询项目管理-项目结案详情列表
     *
     * @param query 项目管理-项目结案详情查询参数
     * @return 项目管理-项目结案详情分页
     */
    @ApiOperation("分页查询询项目管理-项目结案详情列表")
    @PreAuthorize("@ss.hasPermi('project:pyProjectCloseCaseDetails:list')")
    @GetMapping("/pagePyProjectCloseCaseDetails")
    public R<PageInfo<ProjectCloseCaseDetailsListVO>> pagePyProjectCloseCaseDetails(ProjectCloseCaseDetailsQuery query) {
        PageInfo<ProjectCloseCaseDetailsListVO> voList = this.pyProjectCloseCaseDetailsService.pagePyProjectCloseCaseDetailsList(query);
        return R.success(voList);
    }

    /**
     * 获取项目管理-项目结案详情详细信息
     * @param id 项目管理-项目结案详情主键
     * @return 项目管理-项目结案详情视图模型
     */
    @ApiOperation("获取项目管理-项目结案详情详细信息")
    @PreAuthorize("@ss.hasPermi('project:pyProjectCloseCaseDetails:query')")
    @GetMapping(value = "/{id}")
    public R<ProjectCloseCaseDetailsVO> getInfo(@PathVariable("id") Long id) {
        return R.success(pyProjectCloseCaseDetailsService.selectPyProjectCloseCaseDetailsById(id));
    }

    /**
     * 新增项目管理-项目结案详情
     *
     * @param dto 项目管理-项目结案详情修改参数
     * @return 是否成功
     */
    @ApiOperation("新增项目管理-项目结案详情")
    @PreAuthorize("@ss.hasPermi('project:pyProjectCloseCaseDetails:add')")
    @Log(title = "项目管理-项目结案详情", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody List<ProjectCloseCaseDetailsDTO> dto) {
        return R.success(pyProjectCloseCaseDetailsService.insertPyProjectCloseCaseDetails(dto));
    }

    /**
     * 修改项目管理-项目结案详情
     *
     * @param dto 项目管理-项目结案详情修改参数
     * @return 是否成功
     */
    @ApiOperation("修改项目管理-项目结案详情")
    @PreAuthorize("@ss.hasPermi('project:pyProjectCloseCaseDetails:edit')")
    @Log(title = "项目管理-项目结案详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody List<ProjectCloseCaseDetailsDTO> dto) {
        return R.success(pyProjectCloseCaseDetailsService.updateProjectCloseCaseDetails(dto));
    }

    /**
     * 删除项目管理-项目结案详情
     * @param ids 需要删除的项目管理-项目结案详情主键集合
     * @return 是否成功
     */
    @ApiOperation("删除项目管理-项目结案详情" )
    @PreAuthorize("@ss.hasPermi('project:pyProjectCloseCaseDetails:remove')")
    @Log(title = "项目管理-项目结案详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(pyProjectCloseCaseDetailsService.deletePyProjectCloseCaseDetailsByIds(ids));
    }

    /**
     * 导出项目管理-项目结案详情
     * @param response 请求响应
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目管理-项目结案详情")
    @PreAuthorize("@ss.hasPermi('project:pyProjectCloseCaseDetails:export')")
    @Log(title = "项目管理-项目结案详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectCloseCaseDetailsQuery query) {
        List<ProjectCloseCaseDetailsExportModel> exportList = this.pyProjectCloseCaseDetailsService.exportPyProjectCloseCaseDetails(query);

        ExcelUtil<ProjectCloseCaseDetailsExportModel> util = new ExcelUtil<>(ProjectCloseCaseDetailsExportModel. class);
        util.exportExcel(response, exportList, "项目管理-项目结案详情数据" );
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('project:pyProjectCloseCaseDetails:import')" )
    @Log(title = "项目管理-项目结案详情" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<ProjectCloseCaseDetailsExportModel> util = new ExcelUtil<>(ProjectCloseCaseDetailsExportModel.class);
        List<ProjectCloseCaseDetailsExportModel> pyProjectCloseCaseDetailsList = util.importExcel(file.getInputStream());
        String message = this.pyProjectCloseCaseDetailsService.importPyProjectCloseCaseDetails(pyProjectCloseCaseDetailsList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('project:pyProjectCloseCaseDetails:import')" )
    @Log(title = "项目管理-项目结案详情" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ProjectCloseCaseDetailsExportModel> util = new ExcelUtil<>(ProjectCloseCaseDetailsExportModel.class);
        util.importTemplateExcel(response, "项目管理-项目结案详情数据" );
    }

}
