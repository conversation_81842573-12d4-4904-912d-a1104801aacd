package com.py.web.controller.monitor;

import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.AjaxResult;
import com.py.common.core.page.TableDataInfo;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.utils.collection.ListUtil;
import com.py.system.log.domain.SysLogininfor;
import com.py.system.log.domain.query.SysLogininforQuery;
import com.py.system.log.service.ISysLogininforService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 系统访问记录
 * <AUTHOR>
 */
@Api(tags = "系统访问记录")
@RestController
@RequestMapping("/monitor/logininfor")
public class SysLogininforController extends BaseController {
    @Resource
    private ISysLogininforService logininforService;

    /**
     * 查询登录日志列表
     * @param query 系统访问日志查询对象
     * @return 登录记录集合
     */
    @ApiOperation("查询登录日志列表")
    @PreAuthorize("@ss.hasPermi('monitor:logininfor:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysLogininforQuery query) {
        startPage();
        List<SysLogininfor> list = logininforService.selectLogininforList(query);
        return getDataTable(list);
    }

    /**
     * 导出登录日志
     * @param response 请求响应
     * @param query 系统访问日志查询对象
     */
    @ApiOperation("导出登录日志")
    @Log(title = "登录日志", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('monitor:logininfor:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysLogininforQuery query) {
        List<SysLogininfor> list;
        if(ListUtil.any(query.getExportLogIdList())) {
            list = this.logininforService.selectLogininforListById(query.getExportLogIdList());
        } else {
            list = this.logininforService.selectLogininforList(query);
        }

        ExcelUtil<SysLogininfor> util = new ExcelUtil<>(SysLogininfor.class);
        util.exportExcel(response, list, "登录日志");
    }

    /**
     * 删除登录日志
     * @param infoIds 登录日志Id
     * @return 是否成功
     */
    @ApiOperation("删除登录日志")
    @PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
    @Log(title = "登录日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{infoIds}")
    public AjaxResult remove(@PathVariable List<Long> infoIds) {
        return toAjax(logininforService.deleteLogininforByIds(infoIds));
    }

    /**
     * 清空登录日志
     * @return 是否成功
     */
    @ApiOperation("清空登录日志")
    @PreAuthorize("@ss.hasPermi('monitor:logininfor:remove')")
    @Log(title = "登录日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public AjaxResult clean() {
        logininforService.cleanLogininfor();
        return AjaxResult.success();
    }
}
