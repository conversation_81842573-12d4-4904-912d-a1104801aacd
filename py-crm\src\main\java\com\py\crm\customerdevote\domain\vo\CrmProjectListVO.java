package com.py.crm.customerdevote.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 项目管理-项目表列表视图模型
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
@Data
@ApiModel("项目管理-项目表列表视图模型")
public class CrmProjectListVO {
    private static final long serialVersionUID = 1L;

    /** 自增id */
    @ApiModelProperty("自增id")
    private Long id;

    /** 项目id */
    @ApiModelProperty("项目id")
    private Long projectId;

    /** 项目名称 */
    @ApiModelProperty("项目名称")
    private String projectName;

    /** 项目状态 */
    @ApiModelProperty("项目状态")
    private Integer projectStatus;
    private String projectStatusName;

    /** 立项项目金额 */
    @ApiModelProperty("立项项目金额")
    private BigDecimal projectAmount;

    /** 立项毛利率预估 */
    @ApiModelProperty("立项毛利率预估")
    private BigDecimal grossMargin;

    /** 已完成项目收入 */
    @ApiModelProperty("已完成项目收入")
    private BigDecimal finishProjectIncome;

    /** 已完成项目成本 */
    @ApiModelProperty("已完成项目成本")
    private BigDecimal finishProjectCost;

    /** 已完成项目利润 */
    @ApiModelProperty("已完成项目利润")
    private BigDecimal finishProjectProfit;

    /** 已完成项目毛利率 */
    @ApiModelProperty("已完成项目毛利率")
    private BigDecimal finishProjectGrossMargin;

    /** 派芽合作主体id */
    @ApiModelProperty("派芽合作主体id")
    private Long paiyaMainstayId;

    /** 派芽合作主体名称 */
    @ApiModelProperty("派芽合作主体名称")
    private String mainstayName;

    /** 客户id */
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 客户名称 */
    @ApiModelProperty("客户名称")
    private String customerName;

    /** 客户合作主体id */
    @ApiModelProperty("客户合作主体id")
    private Long customerMainstayId;

    /** 客户合作主体名称 */
    @ApiModelProperty("客户合作主体名称")
    private String customerMainstayName;

    /** 品牌/业务线 */
    @ApiModelProperty("品牌/业务线")
    private String lineBusiness;

    /** 创建者Id */
    @ApiModelProperty("创建者Id")
    private Long createId;

    /** 创建者 */
    @ApiModelProperty("立项人")
    private String createBy;

    /** 创建部门 */
    @ApiModelProperty("立项人部门")
    private String createDept;

    /** 立项时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("立项时间")
    private LocalDateTime projectApprovalTime;

    /** 项目开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("项目开始时间")
    private LocalDate projectStartTime;

    /** 预计结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("预计结束时间")
    private LocalDate expectedEndTime;

    /** 结案时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("结案时间")
    private LocalDate closeCaseTime;

    /** 新增审批通过时间 */
    @ApiModelProperty("新增审批通过时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime auditTime;


}
