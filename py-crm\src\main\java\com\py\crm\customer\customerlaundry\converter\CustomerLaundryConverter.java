package com.py.crm.customer.customerlaundry.converter;

import com.py.common.core.converter.BaseDomainModelConverter;
import com.py.crm.customer.contact.domain.vo.ContactVO;
import com.py.crm.customer.customerlaundry.domain.CustomerLaundry;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryDTO;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryExportModel;
import com.py.crm.customer.customerlaundry.domain.vo.CustomerLaundryVO;
import com.py.crm.customer.customerlaundry.domain.vo.CustomerLaundryListVO;
import com.py.crm.customer.domain.SupCustomer;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 客户管理-查看清单模型转换器
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Mapper(componentModel = "spring")
public interface CustomerLaundryConverter extends BaseDomainModelConverter<CustomerLaundry, CustomerLaundryVO, CustomerLaundryDTO> {

    /**
     * 将Entity转换为ListVo
     * @param entity entity
     * @return listVo
     */
    CustomerLaundryListVO toListVoByEntity(CustomerLaundry entity);

    /**
     * 将Entity转换为ListVo
     * @param entity entity
     * @return listVo
     */
    List<CustomerLaundryListVO> toListVoByEntity(List<CustomerLaundry> entity);

    /**
     * 将Entity转换为导出模型
     * @param entityList entity列表
     * @return 导出模型列表
     */
    List<CustomerLaundryExportModel> toExportModel(List<CustomerLaundry> entityList);

    /**
     * 将导出模型转换为Entity
     * @param exportList 导出数据列表
     * @return entity列表
     */
    List<CustomerLaundry> toEntityByExportModel(List<CustomerLaundryExportModel> exportList);

    /**
     * 将导出模型转换为Entity
     * @param exportList 导出数据列表
     * @return entity列表
     */
    List<CustomerLaundryExportModel> toListVoByExportModel(List<CustomerLaundryListVO> exportList);

    /**
     * 将SupCustomer转换为导出模型
     * @param supCustomer 导出数据列表
     * @return entity列表
     */
    @Mapping(source = "customerId",target = "bizId")
    CustomerLaundryExportModel toSupCustomerByExportModel(SupCustomer supCustomer);

    /**
     * 将SupCustomer转换为导出模型
     * @param supCustomer 导出数据列表
     * @return entity列表
     */
    List<CustomerLaundryExportModel> toSupCustomerByExportModel(List<SupCustomer> supCustomer);

}
