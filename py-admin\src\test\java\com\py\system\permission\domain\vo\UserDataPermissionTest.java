package com.py.system.permission.domain.vo;

import com.py.common.core.domain.model.DataPermission;
import com.py.common.core.domain.model.UserDataPermission;
import com.py.common.datascope.DataScopePageType;
import com.py.common.datascope.DataScopeType;
import org.junit.Assert;
import org.junit.Test;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;

/**
 * 用户数据权限测试类
 * <AUTHOR>
 */
public class UserDataPermissionTest {

    /** 全部权限时, 直接返回全部权限 */
    @Test
    public void can_construct_UserDataPermission1() {

        List<DataPermission> dataPermissionList = Arrays.asList(
                this.createTestDataPermission(
                        DataScopeType.ALL,
                        Arrays.asList(DataScopePageType.CRM_ConnectionManage, DataScopePageType.Finance_IncomeManage),
                                LocalDate.of(2008,8,8)),
                this.createTestDataPermission(
                        DataScopeType.SELF,
                        Arrays.asList(DataScopePageType.CRM_CustomerManage, DataScopePageType.Finance_IncomeManage),
                        LocalDate.of(2012,10,1)),
                this.createTestDataPermission(
                        DataScopeType.DEPT,
                        Collections.singletonList(DataScopePageType.CRM_ConnectionManage),
                        null)
        );

        UserDataPermission userDataPermission = new UserDataPermission(dataPermissionList);
        // 权限验证
        Assert.assertEquals(3, userDataPermission.getPageDataScopeMap().size());
        Assert.assertEquals(EnumSet.of(DataScopeType.ALL), userDataPermission.getPageDataScope(DataScopePageType.CRM_ConnectionManage));
        Assert.assertEquals(EnumSet.of(DataScopeType.ALL), userDataPermission.getPageDataScope(DataScopePageType.Finance_IncomeManage));
        Assert.assertEquals(EnumSet.of(DataScopeType.SELF), userDataPermission.getPageDataScope(DataScopePageType.CRM_CustomerManage));

        // 时间验证
        Assert.assertEquals(LocalDate.MIN, userDataPermission.getPageDateTimeScope(DataScopePageType.CRM_ConnectionManage));
        Assert.assertEquals(LocalDate.of(2008,8,8), userDataPermission.getPageDateTimeScope(DataScopePageType.Finance_IncomeManage));
        Assert.assertEquals(LocalDate.of(2012,10,1), userDataPermission.getPageDateTimeScope(DataScopePageType.CRM_CustomerManage));
        Assert.assertNull(userDataPermission.getPageDateTimeScope(DataScopePageType.Finance_ADD_PROJECT));
    }

    /** 其他不同类型的权限则合并, 直接返回全部权限 */
    @Test
    public void can_construct_UserDataPermission2() {

        List<DataPermission> dataPermissionList = Arrays.asList(
                this.createTestDataPermission(
                        DataScopeType.DEPT_AND_CHILD,
                        Arrays.asList(DataScopePageType.CRM_ConnectionManage, DataScopePageType.Finance_IncomeManage),
                        LocalDate.of(2022, 1, 1)),
                this.createTestDataPermission(
                        DataScopeType.SELF,
                        Arrays.asList(DataScopePageType.CRM_CustomerManage, DataScopePageType.Finance_IncomeManage),
                        LocalDate.of(2022, 1, 1))
        );

        UserDataPermission userDataPermission = new UserDataPermission(dataPermissionList);

        Assert.assertEquals(3, userDataPermission.getPageDataScopeMap().size());
        Assert.assertEquals(EnumSet.of(DataScopeType.DEPT_AND_CHILD), userDataPermission.getPageDataScope(DataScopePageType.CRM_ConnectionManage));
        Assert.assertEquals(EnumSet.of(DataScopeType.DEPT_AND_CHILD, DataScopeType.SELF), userDataPermission.getPageDataScope(DataScopePageType.Finance_IncomeManage));
        Assert.assertEquals(EnumSet.of(DataScopeType.SELF), userDataPermission.getPageDataScope(DataScopePageType.CRM_CustomerManage));
    }

    /**
     * 创建测试数据权限
     * @param dataScopeType 数据权限类型
     * @param pageTypeList 页面类型列表
     * @return 数据权限
     */
    private DataPermission createTestDataPermission(DataScopeType dataScopeType, List<DataScopePageType> pageTypeList, LocalDate date) {
        DataPermission dataPermission = new DataPermission();
        dataPermission.setPermissionType(dataScopeType);
        dataPermission.setPageIdList(pageTypeList);
        dataPermission.setStartDate(date);
        return dataPermission;
    }
}
