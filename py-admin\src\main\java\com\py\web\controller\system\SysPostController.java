package com.py.web.controller.system;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.system.dept.domain.vo.DeptVO;
import com.py.system.post.domain.dto.SysPostDTO;
import com.py.system.post.domain.query.PostOptionQuery;
import com.py.system.post.domain.query.SysPostQuery;
import com.py.system.post.domain.vo.PostOptionVO;
import com.py.system.post.domain.vo.SysPostListVO;
import com.py.system.post.domain.vo.SysPostVO;
import com.py.system.post.service.ISysPostService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 组织权限管理 - 职位信息Controller
 * <AUTHOR>
 * @date 2023-07-20
 */
@Api(tags = "组织权限管理 - 职位管理")
@RestController
@RequestMapping("/system/sysPost")
public class SysPostController extends BaseController {

    /** 职位信息服务 */
    @Resource
    private ISysPostService postService;

    /**
     * 分页查询职位信息列表
     * @param query 职位信息查询参数
     * @return 职位信息分页
     */
    @ApiOperation("分页查询职位信息列表")
    @PreAuthorize("@ss.hasPermi('system:sysPost:list')")
    @GetMapping(value = "/list")
    public R<PageInfo<SysPostListVO>> pageSysPost(SysPostQuery query) {
        PageInfo<SysPostListVO> voList = this.postService.pageSysPostList(query);
        return R.success(voList);
    }

    /**
     * 获取职位下拉框选项
     * @param query 查询条件
     * @return 应用下拉框选项列表
     */
    @ApiOperation("获取职位下拉框选项")
    @GetMapping("/selectOption")
    public R<List<PostOptionVO>> selectOption(PostOptionQuery query){
        return R.success(this.postService.selectOption(query));
    }

    /**
     * 获取 职位信息详细信息
     * @param postId 职位信息主键
     * @return 职位信息视图模型
     */
    @ApiOperation("获取职位详情")
    @PreAuthorize("@ss.hasPermi('system:sysPost:query')")
    @GetMapping(value = "/{postId}")
    public R<SysPostVO> getInfo(@PathVariable("postId") Long postId) {
        return R.success(this.postService.selectSysPostByPostId(postId));
    }

    /**
     * 新增职位
     * @param dto 职位信息新增参数
     * @return 是否成功
     */
    @ApiOperation("新增职位")
    @PreAuthorize("@ss.hasPermi('system:sysPost:add')")
    @Log(title = "新增职位", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysPostDTO dto) {
        this.postService.insertPost(dto);
        return R.success();
    }

    /**
     * 修改职位
     * @param dto 职位信息修改参数
     * @return 是否成功
     */
    @ApiOperation("修改职位")
    @PreAuthorize("@ss.hasPermi('system:sysPost:edit')")
    @Log(title = "修改职位", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysPostDTO dto) {
        this.postService.updatePost(dto);
        return R.success();
    }

    /**
     * 删除职位信息
     * @param postIds 需要删除的职位ID列表列表
     * @return 是否成功
     */
    @ApiOperation("删除职位" )
    @PreAuthorize("@ss.hasPermi('system:sysPost:remove')")
    @Log(title = " 删除职位", businessType = BusinessType.DELETE)
    @DeleteMapping()
    public R<Boolean> remove(@RequestBody List<Long> postIds) {
        this.postService.deletePost(postIds);
        return R.success();
    }

    /**
     * 查询列表上的更新部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询列表上的更新部门下拉" )
    @GetMapping("/listPostDept" )
    public R<PageInfo<DeptVO>> listPostDept(SysPostQuery query){
        return R.success(postService.listPostDept(query));
    }

}
