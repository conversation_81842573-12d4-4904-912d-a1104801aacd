package com.py.common.tools.poiexcel.impl;

import com.py.common.tools.poiexcel.ExcelHandlerAdapter;
import com.py.common.utils.DateUtils;
import com.py.common.utils.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 时间转换器
 * <AUTHOR>
 */
public class LocalDateTimeExcelHandler implements ExcelHandlerAdapter {


    @Override
    public Object format(Object value, String[] args) {

        if (value == null){
            return null;
        }
        if (value instanceof LocalDateTime){
            return DateUtils.format((LocalDateTime)value,"yyyy-MM-dd HH:mm:ss");
        }

        return null;
    }
}
