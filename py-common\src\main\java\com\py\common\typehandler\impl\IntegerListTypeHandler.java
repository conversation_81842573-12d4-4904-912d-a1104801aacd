package com.py.common.typehandler.impl;

import com.py.common.typehandler.BaseListTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

/**
 * 整型列表类型处理器
 * <AUTHOR>
 */
@MappedJdbcTypes(JdbcType.LONGVARCHAR)
public class IntegerListTypeHandler extends BaseListTypeHandler<Integer> {

    /**
     * 元素类型
     * @return 元素类型
     */
    @Override
    protected Class<Integer> getItemClass() {
        return Integer.class;
    }
}
