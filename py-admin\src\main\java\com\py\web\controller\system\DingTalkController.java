package com.py.web.controller.system;

import com.py.common.constant.Constants;
import com.py.common.core.domain.AjaxResult;
import com.py.common.core.domain.model.LoginUser;
import com.py.framework.web.service.TokenService;
import com.py.system.tools.dingtalk.IDingTalkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 钉钉Controller
 * <AUTHOR>
 */
@Api(tags = "钉钉相关")
@RestController
@RequestMapping("/ding-talk")
public class DingTalkController {

    /** 令牌服务 */
    @Resource
    private TokenService tokenService;

    /** 钉钉服务 */
    @Resource
    private IDingTalkService dingTalkService;

    /**
     * 获取用户token
     * @param authCode 临时授权码
     * @return 用户token
     */
    @GetMapping("/auth")
    public AjaxResult getAccessToken(@RequestParam(value = "authCode")String authCode) throws Exception {
        LoginUser loginUser = this.dingTalkService.getUserByAuthCode(authCode);

        String sysToken = this.tokenService.createToken(loginUser);
        AjaxResult ajax = AjaxResult.success();
        ajax.put(Constants.TOKEN, sysToken);
        return ajax;
    }


    /**
     * 免密登录获取token
     * @param code 临时授权码
     * @return 用户token
     */
    @GetMapping("/login")
    @ApiOperation("免密登录获取token")
    public AjaxResult getFreeLoginAccessToken(@RequestParam(value = "code")String code) {
        LoginUser loginUser = this.dingTalkService.getFreeLoginUserInfo(code);

        String sysToken = this.tokenService.createToken(loginUser);
        AjaxResult ajax = AjaxResult.success();
        ajax.put(Constants.TOKEN, sysToken);
        return ajax;
    }


}
