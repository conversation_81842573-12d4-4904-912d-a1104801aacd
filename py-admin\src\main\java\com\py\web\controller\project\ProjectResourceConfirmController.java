package com.py.web.controller.project;


import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.exception.ServiceException;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.utils.collection.ListUtil;
import com.py.project.projectresource.comfirmation.domain.query.BatchConfirmationQuery;
import com.py.project.projectresource.comfirmation.domain.vo.BatchConfirmationVO;
import com.py.project.projectresource.comfirmation.domain.vo.ResourceConfirmCountVO;
import com.py.project.projectresource.comfirmation.excel.search.model.ImportResourceSelectModel;
import com.py.project.projectresource.comfirmation.excel.search.model.ImportResourceSelectVO;
import com.py.project.projectresource.comfirmation.excel.search.model.ResourceConfirmSearchImporter;
import com.py.project.projectresource.comfirmation.service.IProjectResourceConfirmationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

/**
 * 项目资源批量确认收入controller
 *
 * <AUTHOR>
 *
 */
@Api(tags = "项目资源批量确认收入controller")
@RestController
@RequestMapping("/project/resource/batch/confirmation/revenue")
public class ProjectResourceConfirmController {

    /**
     * 项目资源批量确认收入服务
     */
    @Resource
    private IProjectResourceConfirmationService projectResourceConfirmationService;

    /**
     * 批量确认收入导入查询
     */
    @Resource
    private ResourceConfirmSearchImporter resourceConfirmSearchImporter;


    /**
     * 批量确认收入列表查询
     * @param query 查询参数
     * @return 列表数据
     */
    @ApiOperation("批量确认收入列表")
    @PostMapping("/page")
    public R<PageInfo<BatchConfirmationVO>> pageBatchConfirmationInfo(@RequestBody BatchConfirmationQuery query){
        return R.success(projectResourceConfirmationService.pageBatchConfirmationInfo(query,true));
    }


    /**
     * 查询批量通过确认收入列表,通过人部门
     * @param query (部门名称)
     * @return 查询通过人部门结果
     */
    @ApiOperation("批量确认收入列表-通过人部门模糊搜索")
    @PostMapping("/find/passDept")
    public R<List<String>> findBatchConfirmationPassDept(@RequestBody BatchConfirmationQuery query){
        return R.success(projectResourceConfirmationService.findBatchConfirmationPassDept(query));
    }


    /**
     * 查询批量通过确认收入列表,确认收入人部门
     * @param query (确认收入人部门名称)
     * @return 查询确认收入人部门结果
     */
    @ApiOperation("批量确认收入列表-确认收入人部门模糊搜索")
    @PostMapping("/find/ConfirmUserDept")
    public R<List<String>> findBatchConfirmationConfirmUserDept(@RequestBody BatchConfirmationQuery query){
        return R.success(projectResourceConfirmationService.findBatchConfirmationConfirmUserDept(query));
    }

    /**
     * 项目管理-批量确认收入合计
     * @param query 查询参数
     * @return 确认收入列表
     */
    @ApiOperation("项目-批量确认收入合计")
    @PostMapping("/getCountConfirmIncome")
    public R<ResourceConfirmCountVO> getCountConfirmIncome(@RequestBody BatchConfirmationQuery query){
        return R.success(this.projectResourceConfirmationService.getCountConfirmIncome(query));
    }


    /**
     * 批量确认收入-获取导入查询模板
     * @param response 请求响应
     */
    @ApiOperation("批量确认收入-获取导入查询模板")
    @Log(title = "批量确认收入-获取导入查询模板" , businessType = BusinessType.IMPORT)
    @PostMapping("/importSelectTemplate" )
    public void batchConfirmationImportSelectTemplate(HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_project_resource_confirm_import_select.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("SED项目管理-批量确认收入导入查询模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = null;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量确认收入-导入查询
     * @param file 导入文件
     * @return 导入结果
     */
    @ApiOperation("批量确认收入-导入查询")
    @Log(title = "批量确认收入-导入查询", businessType = BusinessType.IMPORT)
    @PostMapping("/import/select/data")
    public R<ImportResourceSelectVO> batchConfirmationImportSelectDate(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        //校验是否有数据
        ExcelUtil<ImportResourceSelectModel> importUtils = new ExcelUtil<>(ImportResourceSelectModel.class);
        List<ImportResourceSelectModel> importModelList = importUtils.importExcel(file.getInputStream());
        if (ListUtil.isEmpty(importModelList)) {
            throw new ServiceException("导入失败,无法读取数据,请检查");
        }

        return R.success(resourceConfirmSearchImporter.importSelectData(importModelList));
    }

}
