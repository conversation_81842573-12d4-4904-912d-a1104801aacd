package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.SecurityUtils;
import com.py.project.enums.ExportType;
import com.py.project.projectinvoicing.domain.query.ProjectInvoicingCountQuery;
import com.py.project.projectinvoicing.domain.vo.InvoicingStatusCountVO;
import com.py.project.projectinvoicing.domain.vo.ProjectInvoicingApprovalSumVO;
import com.py.project.projectinvoicingresource.domain.query.ProjectInvoicingResourceApprovalQuery;
import com.py.project.projectinvoicingresource.domain.query.ProjectInvoicingResourceInfoQuery;
import com.py.project.projectinvoicingresource.domain.query.ProjectInvoicingResourceQuery;
import com.py.project.projectinvoicingresource.domain.vo.*;
import com.py.project.projectinvoicingresource.service.IProjectInvoicingResourceService;
import com.py.project.projectinvoicingresource.service.impl.ProjectInvoicingResourceDownloadServiceImpl;
import com.py.project.projectinvoicingresource.service.impl.ProjectInvoicingResourceServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 资源返点开票表Controller
 *
 * <AUTHOR>
 * @date 2023-08-01
 */
@Api(tags = "资源返点开票表")
@RestController
@RequestMapping("/projectInvoicingResource")
public class ProjectInvoicingResourceController extends BaseController {

    /** 资源返点开票表服务 */
    @Resource
    private IProjectInvoicingResourceService projectInvoicingResourceService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 查询项目资源返点各个状态开票数量
     *
     * @return 项目资源返点各个状态开票数量
     */
    @ApiOperation("查询项目资源返点各个状态开票数量")
    @GetMapping("/selectStatusTotalCount")
    public R<InvoicingStatusCountVO> selectStatusTotalCount(ProjectInvoicingCountQuery query) {
        return R.success(projectInvoicingResourceService.selectStatusTotalCount(query));
    }

    /**
     * 分页查询资源返点开票表列表
     *
     * @param query 资源返点开票表查询参数
     * @return 资源返点开票表分页
     */
    @ApiOperation("分页查询询资源返点开票表列表")
    @PreAuthorize("@ss.hasPermi('projectinvoicingresource:projectinvoicingresource:list')")
    @GetMapping("/pageProjectInvoicingResource")
    public R<PageInfo<ProjectInvoicingResourceListVO>> pageProjectInvoicingResource(@Validated ProjectInvoicingResourceQuery query) {
        PageInfo<ProjectInvoicingResourceListVO> voList = this.projectInvoicingResourceService.pageProjectInvoicingResourceList(query, true, true);
        return R.success(voList);
    }

    /**
     * 选中的分页查询资源返点开票表列表
     *
     * @param query 资源返点开票表查询参数
     * @return 资源返点开票表分页
     */
    @ApiOperation("选中的分页查询资源返点开票表列表")
    @PreAuthorize("@ss.hasPermi('projectinvoicingresource:projectinvoicingresource:list')")
    @GetMapping("/pageProjectInvoicingResourceCheck")
    public R<ProjectInvoicingResourceSelectedListVO> pageProjectInvoicingResourceCheck(@Validated ProjectInvoicingResourceQuery query) {
        return R.success(this.projectInvoicingResourceService.pageProjectInvoicingResourceCheck(query, true,true));
    }

    /**
     * 勾选合计
     *
     * @return 勾选合计金额
     */
    @ApiOperation("勾选合计")
    @GetMapping("/selectTotalStatistics")
    public R<InvoicingResourceAmountTotalVO> selectTotalStatistics(@Validated ProjectInvoicingResourceQuery query) {
        return R.success(projectInvoicingResourceService.selectTotalStatistics(query));
    }

    /**
     * 导出资源返点开票表
     * @param query 导出查询参数
     */
    @ApiOperation("导出资源返点开票表")
    @PreAuthorize("@ss.hasPermi('projectinvoicingresource:projectinvoicingresource:export')")
    @Log(title = "资源返点开票表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
   public R<String> export(@Validated @RequestBody ProjectInvoicingResourceQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("资源返点开票", TaskType.Export, query, ProjectInvoicingResourceServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 审批查询-项目资源返点开票列表
     * @param query 查询参数
     * @return 数据集合
     */
    @ApiOperation("审批查询-项目资源返点开票-分页列表")
    @PreAuthorize("@ss.hasPermi('projectinvoicingresource:projectinvoicingresource:approvalList')")
    @GetMapping("/pageInvoicingResourceApproval")
    public R<PageInfo<ProjectInvoicingResourceApprovalVO>> pageInvoicingResourceApproval(@Validated ProjectInvoicingResourceApprovalQuery query) {
        return R.success(this.projectInvoicingResourceService.pageInvoicingResourceApproval(query));
    }

    /**
     * 审批查询-项目资源返点开票-统计主体审批数量
     * @return 数据集合
     */
    @ApiOperation("审批查询-项目资源返点开票-统计主体审批数量")
    @PreAuthorize("@ss.hasPermi('projectinvoicingresource:projectinvoicingresource:sumApproval')")
    @GetMapping("/sumInvoicingResourceApproval")
    public R<List<ProjectInvoicingApprovalSumVO>> sumInvoicingResourceApproval() {
        List<ProjectInvoicingApprovalSumVO> voList = this.projectInvoicingResourceService.sumInvoicingResourceApproval();
        return R.success(voList);
    }

    /**
     * 审批查询-项目资源返点开票-根据id数组统计本次开票金额
     * @param query 是否全选/要统计的数据
     * @return 数值
     */
    @ApiOperation("审批查询-项目资源返点开票-根据id数组统计本次开票金额")
    @PreAuthorize("@ss.hasPermi('projectinvoicingresource:projectinvoicingresource:sumApprovalIds')")
    @GetMapping("/sumInvoicingApprovalByIds")
    public R<ProjectInvoicingApprovalSumVO> sumInvoicingApprovalByIds(@Validated ProjectInvoicingResourceApprovalQuery query) {
        ProjectInvoicingApprovalSumVO vo = this.projectInvoicingResourceService
                .sumInvoicingApprovalByIds(query);
        return R.success(vo);
    }

    /**
     * 审批查询-项目资源返点开票-下载
     * @param query 导出查询参数
     */
    @ApiOperation("审批查询-项目资源返点开票-下载")
    @PreAuthorize("@ss.hasPermi('projectinvoicingresource:projectinvoicingresource:exportApprocalList')")
    @Log(title = "资源返点开票表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportApprocalList")
    public R<String> exportApprocalList(@Validated @RequestBody ProjectInvoicingResourceApprovalQuery query) {
        query.setExportType(ExportType.PROJECT_INVOICING_RESOURCE_APPROVAL_LIST);
        reusableAsyncTaskService.addTask("资源返点开票审批查询",
                TaskType.Export,
                query,
                ProjectInvoicingResourceDownloadServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 获取资源返点开票申请记录
     * @param query 资源返点开票表主键
     * @return 资源返点开票表视图模型
     */
    @ApiOperation("获取资源返点开票申请记录~~~~~~~")
    @GetMapping(value = "/selectInfo")
    public R<ProjectInvoicingResourceInfoVO> getInfo(ProjectInvoicingResourceInfoQuery query) {
        return R.success(projectInvoicingResourceService.selectProjectInvoicingResourceById(query));
    }

    /**
     * 补充资源返点开票详情
     */
    @GetMapping(value = "/supplementDetail")
    public R<Boolean> supplementDetail() {
        return R.success(projectInvoicingResourceService.supplementDetail());
    }

    /**
     * 查询资源返点开票审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询资源返点开票审批列表上的创建部门下拉" )
    @GetMapping("/listInvoicingResourceApprovalDept" )
    public R<List<String>> listInvoicingResourceApprovalDept(ProjectInvoicingResourceApprovalQuery query){
        return R.success(projectInvoicingResourceService.listInvoicingResourceApprovalDept(query));
    }
}
