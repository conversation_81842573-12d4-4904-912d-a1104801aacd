package com.py.common.core.domain.vo;

/**
 * 更新查询信息接口
 * <AUTHOR>
 */
public interface IUpdateInfoVO {

    /**
     * 获取更新者ID
     * @return 更新者ID
     */
    Long getUpdateId();

    /**
     * 设置更新者名字
     * @param updateBy 更新者名字
     */
    void setUpdateBy(String updateBy);

    /**
     * 设置更新者部门
     * @param updateDept 更新者部门
     */
    void setUpdateDept(String updateDept);

    /**
     * 设置更新信息
     * @param updateBy 更新者名字
     * @param updateDept 更新者部门
     */
    default void setUpdateInfo(String updateBy, String updateDept){
        this.setUpdateBy(updateBy);
        this.setUpdateDept(updateDept);
    }
}
