package com.py.flow.flowdatauser.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.py.common.core.domain.BaseEntity;
import com.py.flow.domain.enums.ApprovalBizType;
import com.py.flow.flowdatauser.domain.enums.FlowDataUserAuditStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审批数据与人员关联对象
 * <AUTHOR>
 * @date 2023-09-04
 */
@TableName("py_flow_data_user" )
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("审批数据与人员关联" )
public class FlowDataUser extends BaseEntity {
private static final long serialVersionUID=1L;

    /** 自增主键*/
    @ApiModelProperty("自增主键")
    @TableId
    private Long id;

    /** 业务id*/
    @ApiModelProperty("业务id")
    private Long bizId;

    /** 业务类型*/
    @ApiModelProperty("业务类型")
    private ApprovalBizType bizType;

    /** 用户id*/
    @ApiModelProperty("用户id")
    private Long userId;

    /** 审批状态*/
    @ApiModelProperty("审批状态")
    private FlowDataUserAuditStatus auditStatus;

    /** Flowable流程实例ID */
    @ApiModelProperty("Flowable流程实例ID")
    private String processInstanceId;

    /** 流程节点ID */
    @ApiModelProperty("流程节点ID")
    private String activeId;

    /** 流程任务ID */
    @ApiModelProperty("流程任务ID")
    private String taskId;

    /** 是否为当前节点 */
    @ApiModelProperty("是否为当前节点")
    private Boolean currentNode;

    /** 是否为历史审批人 */
    @ApiModelProperty("是否为历史审批人")
    private Boolean historyApprover;

}
