package com.py.crm.customer.handler;

import com.py.common.enums.flow.FlowApprovalStatus;
import com.py.common.file.FileInfoVO;
import com.py.common.oss.IOssService;
import com.py.crm.customer.converter.CustomerConverter;
import com.py.crm.customer.domain.SupCustomer;
import com.py.crm.customer.domain.dto.CustomerDTO;
import com.py.crm.customer.domain.vo.CustomerVO;
import com.py.crm.customer.service.ICustomerService;
import com.py.flow.domain.enums.ApprovalBizType;
import com.py.flow.event.IApprovalEventHandler;
import com.py.flow.flowinstance.approvalsnapshot.IApprovalBizInfoHandler;
import com.py.flow.flowinstance.functional.DataDeleteCallback;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 新增客户审批结果处理
 * <AUTHOR>
 * @version InsertCustomerHandler 2023/9/1 16:35
 */
@Log4j2
@Component
public class InsertCustomerHandler implements IApprovalEventHandler, IApprovalBizInfoHandler , DataDeleteCallback {

    /** 客户管理-客户服务 */
    @Resource
    private ICustomerService customerService;


    /** 客户管理-客户模型转换器 */
    @Resource
    private CustomerConverter customerConverter;

    /** 对象存储服务 */
    @Resource
    private IOssService ossService;

    /**
     * 获取支持的审批业务类型
     * @return 审批业务类型
     */
    @Override
    public ApprovalBizType getSupport() {
        return ApprovalBizType.InsertCustomer;
    }

    /**
     * 处理审批事件
     * @param bizId 审批业务ID
     * @param flowApprovalStatus 审批状态
     */
    @Override
    public void handle(Long bizId, FlowApprovalStatus flowApprovalStatus) {
        CustomerDTO customerDTO = new CustomerDTO();
        customerDTO.setCustomerId(bizId);
        customerDTO.setAuditStatus(flowApprovalStatus.getValue());
        customerService.auditAfter(customerDTO);
    }

    /**
     * 获取审批业务详情
     * @param bizId 审批业务ID
     * @return 审批业务详情
     */
    @Override
    public Object getApprovalBizInfo(Long bizId) {
        SupCustomer customer = this.customerService.getInfo(bizId);
        CustomerVO customerVO = this.customerConverter.toVoByEntity(customer);
        List<FileInfoVO> fileInfoByKey = ossService.getFileInfoByKey(customerVO.getAnnex());
        customerVO.setAnnexList(fileInfoByKey);
        return customerService.getCustomerSnapshot(customerVO);
    }

    /***
     * 人脉删除作废记录
     * @param bizIds 删除的数据集合
     * @return 是否删除
     */
    @Override
    public boolean deleteFlow(List<Long> bizIds) {
        return customerService.deleteCustomerByBizIds(bizIds);
    }
}
