package com.py.crm.compareddraft.domain.dto;

import com.py.common.oss.model.OssObjectInfo;
import com.py.common.utils.StringUtils;
import com.py.crm.customer.domain.SupCustomer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户管理-比稿管理Req
 *
 * <AUTHOR>
 * @date 2023/07/17 09:36
 */
@Data
@ApiModel("客户管理-比稿管理")
public class ComparedDraftDTO {

    /**
     * 自增主键
     */
    @ApiModelProperty("自增主键")
    private Long id;

    /**
     * 比稿id
     */
    @ApiModelProperty("比稿id")
    private Long comparedDraftId;

    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    @NotNull(message = "客户不可为空")
    private Long customerId;

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    private String customerName;

    /**
     * 客户合作主体ID
     */
    @ApiModelProperty("客户合作主体ID")
    private Long customerCooperateId;

    /**
     * 客户合作主体
     */
    @ApiModelProperty("客户合作主体")
    private String customerCooperateName;

    /**
     * 品牌/业务线名
     */
    @ApiModelProperty("品牌/业务线名")
    private String brandName;

    /**
     * 行业类目（数据字典Id）
     */
    @ApiModelProperty("行业类目（数据字典Id）")
    private List<String> categoryId;

    /**
     * 行业类目名称
     */
    @ApiModelProperty("行业类目名称")
    private List<String> categoryName;

    /**
     * 品牌阶段
     */
    @ApiModelProperty("品牌阶段")
    private Integer brandStage;

    /**
     * 合作部门
     */
    @ApiModelProperty("合作部门")
    private String collaborateDapt;

    /**
     * 比稿项目名称
     */
    @ApiModelProperty("比稿项目名称")
    @NotBlank(message = "比稿项目名称不可为空")
    @Size(max = 100,message = "比稿项目名称不可超过100字符")
    private String comparedDraftName;

    /**
     * 商务标比稿结果
     */
    @ApiModelProperty("商务标比稿结果")
    private Integer commercialBidFruits;

    /**
     * 技术标比稿结果
     */
    @ApiModelProperty("技术标比稿结果")
    private Integer techniqueBidFruits;

    /**
     * 最终比稿结果
     */
    @ApiModelProperty("最终比稿结果")
    private Integer finalFruits;

    /**
     * 比稿时间
     */
    @ApiModelProperty("比稿时间")
    private LocalDate comparedDraftTime;

    /**
     * 录入时间
     */
    @ApiModelProperty("录入时间")
    private LocalDateTime enteringTime;

    /**
     * 比稿金额(元)
     */
    @ApiModelProperty("比稿金额(元)")
    private BigDecimal comparedDraftMoney;

    /**
     * 成交金额(元)
     */
    @ApiModelProperty("成交金额(元)")
    private BigDecimal turnoverMoney;

    /**
     * 派芽业务类型
     */
    @ApiModelProperty("派芽业务类型")
    private Integer pyType;

    /**
     * 方案类型
     */
    @ApiModelProperty("方案类型")
    private Integer planType;

    /**
     * 比稿方案
     */
    @ApiModelProperty("比稿方案")
    private List<OssObjectInfo> comparedDraftPlan;

    /**
     * 比稿策略复盘
     */
    @ApiModelProperty("比稿策略复盘")
    private String strategyReview;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /** 策划人 */
    @ApiModelProperty("策划人")
    private List<Long> userIdList;

    public void assignmentCustomer(SupCustomer serviceInfo){
        if(StringUtils.isBlank(customerName) ){
            customerName = serviceInfo.getName();
        }
        if(StringUtils.isBlank(brandName) ){
            brandName = serviceInfo.getLineBusiness();
        }
        if(StringUtils.isNull(brandStage) ){
            brandStage = serviceInfo.getBrandStage();
        }
        if(StringUtils.isBlank(collaborateDapt) ){
            collaborateDapt = serviceInfo.getCooperativeSector();
        }
    }
}
