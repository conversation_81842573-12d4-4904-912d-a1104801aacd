package com.py.common.oss;

import com.py.common.utils.NullMergeUtils;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 阿里云对象存储配置
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@ConfigurationProperties("oss")
public class OssConfig {

    /** 访问键 */
    String accessKey;

    /** 访问密钥 */
    String accessKeySecret;

    /** 存储桶名 */
    String bucketName;

    /** 访问点 */
    String endpoint;

    /** 临时凭证访问点 */
    String stsEndpoint;

    /** 临时凭证角色名称 */
    String roleArn;

    /** 角色会话名 */
    String roleSessionName;

    /** 访问区 */
    String region;

    public String getRegion() {
        return NullMergeUtils.blankMerge(this.region, "cn-hangzhou");
    }
}
