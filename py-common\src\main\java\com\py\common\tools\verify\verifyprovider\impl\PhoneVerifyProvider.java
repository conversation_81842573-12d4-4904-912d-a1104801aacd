package com.py.common.tools.verify.verifyprovider.impl;

import com.py.common.tools.verify.enums.VerifyType;
import com.py.common.tools.verify.verifyprovider.VerifyProvider;
import com.py.common.utils.DateUtils;
import com.py.common.utils.RegexUtils;
import com.py.common.utils.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 手机号格式校验
 * <AUTHOR>
 * @version PhoneVerifyProvider 2023/8/23 16:51
 */
@Component
public class PhoneVerifyProvider implements VerifyProvider {
    /**
     * 支持的验证类型
     * @return 支持的验证类型
     */
    @Override
    public VerifyType supportedVerifyType() {
        return VerifyType.Phone;
    }

    /**
     * 验证
     * @param target 验证目标
     * @return 验证结果 true:验证通过  false:验证失败
     * @throws Exception 验证异常
     */
    @Override
    public boolean verify(Object target) throws Exception {
        if(target == null) {
            return true;
        }

        if(target instanceof String == false) {
            return false;
        }
        if(StringUtils.isBlank(target.toString())){
            return true;
        }
        String phone = RegexUtils.PHONE;
        return ((String) target).matches(phone);
    }
}
