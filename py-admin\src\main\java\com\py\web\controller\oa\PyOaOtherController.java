package com.py.web.controller.oa;

import cn.hutool.core.util.BooleanUtil;
import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.oa.pyoadailycontracts.domain.vo.PyOaLabelVO;
import com.py.oa.pyoaother.domain.dto.PyOaOtherDTO;
import com.py.oa.pyoaother.domain.query.PyOaOtherCheckBoxQuery;
import com.py.oa.pyoaother.domain.query.PyOaOtherQuery;
import com.py.oa.pyoaother.domain.vo.OaOtherApproveVO;
import com.py.oa.pyoaother.domain.vo.OaOtherMatterTypeVO;
import com.py.oa.pyoaother.domain.vo.PyOaOtherCheckBoxVO;
import com.py.oa.pyoaother.domain.vo.PyOaOtherVO;
import com.py.oa.pyoaother.service.IPyOaOtherService;
import com.py.oa.pyoaother.service.impl.PyOaOtherServiceImpl;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * OA其他审批Controller
 *
 * <AUTHOR>
 * @date 2023-07-21
 */
@Api(tags = "OA其他审批")
@RestController
@RequestMapping("/oa/pyOaOther")
public class PyOaOtherController extends BaseController {

    /** OA其他审批服务 */
    @Resource
    private IPyOaOtherService pyOaOtherService;


    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 系统设置-主体参数设置服务 */
    @Resource
    private ISystemMainstayParamService systemMainstayParamService;


    /**
     * 分页查询OA其他审批列表
     *
     * @param query OA其他审批查询参数
     * @return OA其他审批分页
     */
    @ApiOperation("分页查询询OA其他审批列表")
    @PreAuthorize("@ss.hasPermi('oa:pyOaOther:list')")
    @PostMapping("/pagePyOaOther")
    public R<PageInfo<PyOaOtherVO>> pagePyOaOther(@RequestBody PyOaOtherQuery query) {
        PageInfo<PyOaOtherVO> voList = this.pyOaOtherService.pagePyOaOtherList(query);
        return R.success(voList);
    }

    /**
     * 获取OA其他审批详细信息
     * @param id OA其他审批主键
     * @return OA其他审批视图模型
     */
    @ApiOperation("获取OA其他审批详细信息")
    @PreAuthorize("@ss.hasPermi('oa:pyOaOther:query')")
    @GetMapping(value = "/getInfo/{id}")
    public R<PyOaOtherVO> getInfo(@PathVariable("id") Long id) {
        return R.success(pyOaOtherService.selectPyOaOtherById(id));
    }

    /**
     * 新增OA其他审批
     *
     * @param dto OA其他审批修改参数
     * @return 是否成功
     */
    @ApiOperation("新增OA其他审批")
    @PreAuthorize("@ss.hasPermi('oa:pyOaOther:add')")
    @Log(title = "OA其他审批", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Long> add(@RequestBody PyOaOtherDTO dto) {
        return R.success(pyOaOtherService.insertPyOaOther(dto));
    }

    /**
     * 修改OA其他审批
     *
     * @param dto OA其他审批修改参数
     * @return 是否成功
     */
    @ApiOperation("修改OA其他审批")
    @PreAuthorize("@ss.hasPermi('oa:pyOaOther:edit')")
    @Log(title = "OA其他审批", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R<Long> edit(@RequestBody PyOaOtherDTO dto) {
        return R.success(pyOaOtherService.updatePyOaOther(dto));
    }

    /**
     * 多选框查询(废弃)
     *
     * @param checkBoxQuery 多选框查询
     * @return PyOaCostCheckBoxVO
     */
    @ApiOperation("多选框多选(废弃)")
    @PreAuthorize("@ss.hasPermi('oa:pyOaCost:query')")
    @PostMapping("/checkbox")
    public R<PyOaOtherCheckBoxVO> checkBox (@RequestBody PyOaOtherCheckBoxQuery checkBoxQuery) {
        return R.success(pyOaOtherService.checkBoxQuery(checkBoxQuery));
    }

    /**
     * 导出OA其他审批
     * @param query 导出查询参数
     */
    @ApiOperation("导出OA其他审批")
    @PreAuthorize("@ss.hasPermi('oa:pyOaOther:export')")
    @Log(title = "OA其他审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@RequestBody PyOaOtherQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService
                .selectSystemMainstayParamById(ListUtil.first(query.getPyMainstayIdList()));
        query.setLoginUser(SecurityUtils.getLoginUser());
        String head = BooleanUtil.isTrue(query.getIsApproval()) ? "其它审批列表数据" : "其它管理列表数据";
        String dateStr =  DateUtils.format(LocalDateTime.now(), DateUtils.YYYY_MM_DD_COLON_HH_MM_SS);
        String fileName = String.format("%s-%s-%s.xlsx", head, systemMainstayParamVO.getMainstayName(), dateStr);
        query.setFileName(fileName);
        reusableAsyncTaskService.addTask("OA其它审批信息数据", TaskType.Export,query, PyOaOtherServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 查询其它列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询其它列表上的创建部门下拉" )
    @GetMapping("/listOtherDept" )
    public R<List<String>> listOtherDept(PyOaOtherQuery query){
        return R.success(pyOaOtherService.listOtherDept(query));
    }

    /**
     * OA其他审批查询列表
     *
     * @param query 查询参数
     * @return 其他审批查询列表
     */
    @ApiOperation("OA其他审批查询列表")
    @PreAuthorize("@ss.hasPermi('oa:pyOaOther:list')")
    @PostMapping("/pageOtherApprove")
    public R<PageInfo<OaOtherApproveVO>> pageOtherApprove(@RequestBody PyOaOtherQuery query){
        return R.success(pyOaOtherService.pageOtherApprove(query));
    }

    /**
     * OA其他审批查询顶部统计
     * @param query 主体id
     * @return 顶部统计
     */
    @ApiOperation("OA其他审批查询顶部统计")
    @PreAuthorize("@ss.hasPermi('oa:pyOaOther:query')")
    @PostMapping("/countOtherApproveTop")
    public R<List<PyOaLabelVO>> countOtherApproveTop (@RequestBody PyOaOtherQuery query) {
        return R.success(pyOaOtherService.countOtherApproveTop(query));
    }

    /**
     * OA其他审批查询下拉
     * @param query 主体id
     * @return 顶部统计
     */
    @ApiOperation("OA其他审批查询下拉")
    @PreAuthorize("@ss.hasPermi('oa:pyOaOther:query')")
    @PostMapping("/otherApprovelPull")
    public R<List<OaOtherMatterTypeVO>> otherApprovelPull (@RequestBody PyOaOtherQuery query) {
        return R.success(pyOaOtherService.otherApprovelPull(query));
    }

    /**
     * 导出OA其他审批查询
     * @param query 导出查询参数
     */
    @ApiOperation("导出OA其他审批查询")
    @PreAuthorize("@ss.hasPermi('oa:pyOaOther:export')")
    @Log(title = "OA其他审批", businessType = BusinessType.EXPORT)
    @PostMapping("/exportApprove")
    public R<String> exportApprove(@RequestBody PyOaOtherQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPyMainstayId());
        String fileName = "其它审批查询列表数据-" + systemMainstayParamVO.getMainstayName() + "-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setIsApproval(true);
        reusableAsyncTaskService.addTask("其它审批查询", TaskType.Export,query, PyOaOtherServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 查询其它审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询其它审批列表上的创建部门下拉" )
    @GetMapping("/listOtherApproveDept" )
    public R<List<String>> listOtherApproveDept(PyOaOtherQuery query){
        return R.success(pyOaOtherService.listOtherApproveDept(query));
    }
}
