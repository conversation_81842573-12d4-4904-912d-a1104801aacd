package com.py.crm.customerdevote.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.py.framework.config.jackson.serializer.MoneyJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 客户贡献管理
 * <AUTHOR>
 * @version CustomerDevoteListVO 2023/8/9 11:40
 */
@Data
public class CustomerDevoteTotal {



    /** 比稿金额(元) */
    @ApiModelProperty("比稿金额(元)")
    @JsonSerialize(using = MoneyJsonSerializer.class)
    private BigDecimal comparedDraftMoney;

    /** 成交金额 */
    @ApiModelProperty("成交金额")
    @JsonSerialize(using = MoneyJsonSerializer.class)
    private BigDecimal transactionMoney;

    /** 结案金额 */
    @ApiModelProperty("结案金额")
    @JsonSerialize(using = MoneyJsonSerializer.class)
    private BigDecimal closeCaseMoney;

    /** 收入坏账金额 */
    @ApiModelProperty("收入坏账金额")
    @JsonSerialize(using = MoneyJsonSerializer.class)
    private BigDecimal badDebtMoney;

    /** 利润率 */
    @ApiModelProperty("利润率")
    private BigDecimal profitMargin;

    /**
     *
     * @param comparedDraftMoney 比稿金额
     * @param transactionMoney 成交金额
     * @param closeCaseMoney 结案金额
     * @param badDebtMoney 收入坏账
     * @param profitMargin 利润率
     */
    public CustomerDevoteTotal(BigDecimal comparedDraftMoney, BigDecimal transactionMoney, BigDecimal closeCaseMoney, BigDecimal badDebtMoney, BigDecimal profitMargin) {
        this.comparedDraftMoney = comparedDraftMoney;
        this.transactionMoney = transactionMoney;
        this.closeCaseMoney = closeCaseMoney;
        this.badDebtMoney = badDebtMoney;
        this.profitMargin = profitMargin;
    }
}

