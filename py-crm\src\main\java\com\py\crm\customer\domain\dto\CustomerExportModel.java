package com.py.crm.customer.domain.dto;

import com.py.common.tools.poiexcel.annotation.Excel;
import com.py.common.tools.verify.config.VerifyConfig;
import com.py.common.tools.verify.domain.ISerialNumber;
import com.py.common.tools.verify.enums.VerifyType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;

/**
 * 客户管理-客户导出模型
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Data
@ApiModel("客户管理-客户导出模型" )
public class CustomerExportModel implements ISerialNumber {
    private static final long serialVersionUID = 1L;

    /** 序号 */
    @Excel(setRowIndex = true)
    @ApiModelProperty("序号")
    private Integer serialNumber;

    /** 客户ID */
    @ApiModelProperty("客户ID")
    private Long customerId;

    /** 客户名称*/
    @Excel(name = "*客户名称")
    @ApiModelProperty("客户名称" )
    private String name;

    /** 品牌/业务线*/
    @Excel(name = "品牌/业务线")
    @ApiModelProperty("品牌/业务线" )
    private String lineBusiness;

    /** 服务人员*/
    @Excel(name = "*服务人员")
    @ApiModelProperty("服务人员" )
    private String serviceUser;

    /** 服务人员id */
    @ApiModelProperty("服务人员id")
    private List<Long> serviceUserIdList;

    /**
     * 获取验证配置
     * @return 验证配置
     */
    public static VerifyConfig<CustomerExportModel> getVerifyConfig() {
        VerifyConfig<CustomerExportModel> config = new VerifyConfig<>();
        // 校验
        config.addConfig(VerifyType.NotBlank, CustomerExportModel::getName, "客户名称不能为空")
//                .addConfig(VerifyType.NotBlank, CustomerExportModel::getLineBusiness, "品牌/业务线不能为空")
                .addConfig(VerifyType.NotBlank, CustomerExportModel::getServiceUser, "服务人员不能为空");
        return config;
    }
}
