package com.py.common.core.domain.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 重置密码对象
 * <AUTHOR>
 */
@Data
@ApiModel("重置密码对象")
public class ResetPwdBody {
    /** 手机号 */
    @NotBlank(message = "手机不能为空")
    private String phone;

    /** 用户密码 */
    @ApiModelProperty("用户密码")
    @NotBlank(message = "密码不能为空")
    private String password;

}
