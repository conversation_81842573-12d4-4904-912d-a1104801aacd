package com.py.web.controller.project.baddebts;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.datascope.DataScopePageType;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.project.pyincomebaddebt.domain.dto.PyIncomeBadDebtDTO;
import com.py.project.pyincomebaddebt.domain.dto.PyIncomeBadDeptInfoDTO;
import com.py.project.pyincomebaddebt.domain.query.BusinessFinancialReportIncomeBadDebtQuery;
import com.py.project.pyincomebaddebt.domain.query.EnterpriseFinancialReportIncomeBadDebtDetailsQuery;
import com.py.project.pyincomebaddebt.domain.query.PyIncomeBadDebtCheckBoxQuery;
import com.py.project.pyincomebaddebt.domain.query.PyIncomeBadDebtQuery;
import com.py.project.pyincomebaddebt.domain.vo.*;
import com.py.project.pyincomebaddebt.service.IPyIncomeBadDebtService;
import com.py.project.pyincomebaddebt.service.impl.PyIncomeBadDebtServiceImpl;
import com.py.system.mainstayparam.domain.SystemMainstayParam;
import com.py.system.mainstayparam.domain.vo.MainstayParamListVO;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 收入坏账信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
@Api(tags = "收入坏账信息")
@RestController
@Validated
@RequestMapping("/project/pyIncomeBadDebt")
public class PyIncomeBadDebtController extends BaseController {

    /** 收入坏账信息服务 */
    @Resource
    private IPyIncomeBadDebtService pyIncomeBadDebtService;


    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 系统设置-主体参数设置服务 */
    @Resource
    private ISystemMainstayParamService systemMainstayParamService;

    /**
     * 分页查询收入坏账信息列表
     *
     * @param query 收入坏账信息查询参数
     * @return 收入坏账信息分页
     */
    @ApiOperation("分页查询询收入坏账信息列表")
    @PreAuthorize("@ss.hasPermi('project:pyIncomeBadDebt:list')")
    @PostMapping("/pagePyIncomeBadDebt")
    public R<PageInfo<PyIncomeBadDebtListVO>> pagePyIncomeBadDebt(@Validated @RequestBody PyIncomeBadDebtQuery query) {
        return R.success(this.pyIncomeBadDebtService.pagePyIncomeBadDebtList(query));
    }

    /**
     * 获取收入坏账二级详细信息
     * @param id 收入坏账信息主键
     * @return 收入坏账信息视图模型
     */
    @ApiOperation("获取收入坏账二级详细信息")
    @PreAuthorize("@ss.hasPermi('project:pyIncomeBadDebt:query')")
    @GetMapping(value = "getSecondInfo/{id}")
    public R<PyIncomeBadDebtVO> getInfo(@PathVariable("id") Long id) {
        return R.success(pyIncomeBadDebtService.selectPyIncomeBadDebtById(id));
    }

    /**
     * 获取收入坏账二级详细信息打印数据
     * @param id 收入坏账信息主键
     * @return 收入坏账信息视图模型
     */
    @ApiOperation("获取收入坏账二级详细信息打印数据")
    @GetMapping(value = "print/getSecondInfo/{id}")
    public R<PyIncomeBadDebtVO> printGetInfo(@PathVariable("id") Long id) {
        return R.success(pyIncomeBadDebtService.selectPyIncomeBadDebtById(id));
    }


    /**
     * 一级详细传参
     * @param pyIncomeBadDeptInfoDTO
     * @return
     */
    @ApiOperation("获取收入坏账一级详细信息(分页)")
    @PreAuthorize("@ss.hasPermi('project:pyIncomeBadDebt:query')")
    @PostMapping(value = "getFirstInfoPage")
    public R<IncomeBadDebtDetailsVO> getFirstInfo(@RequestBody PyIncomeBadDeptInfoDTO pyIncomeBadDeptInfoDTO) {
        return R.success(pyIncomeBadDebtService.selectFirstIncomeBadDeptInfoPage(pyIncomeBadDeptInfoDTO));
    }

    /**
     * 获取企业财务报表收入坏账明细列表
     * @param query 查询条件
     * @return 收入坏账明细列表
     */
    @ApiOperation("企业财务报表收入坏账明细列表")
    @PreAuthorize("@ss.hasPermi('project:pyIncomeBadDebt:query')")
    @PostMapping(value = "listEnterpriseFinancialReportIncomeBadDebtDetail")
    public R<IncomeBadDebtDetailsVO> listEnterpriseFinancialReportIncomeBadDebtDetail(
            @Validated @RequestBody EnterpriseFinancialReportIncomeBadDebtDetailsQuery query) {
        return R.success(pyIncomeBadDebtService.listEnterpriseFinancialReportIncomeBadDebtDetail(query));
    }

    /**
     * 获取业务财务报表收入坏账明细列表
     * 部门实时
     * @param query 查询条件
     * @return 收入坏账明细列表
     */
    @ApiOperation("业务财务报表收入坏账明细列表")
    @PostMapping(value = "ListBusinessFinancialReportIncomeBadDebt")
    public R<IncomeBadDebtDetailsVO> ListBusinessFinancialReportIncomeBadDebt(
            @Validated @RequestBody BusinessFinancialReportIncomeBadDebtQuery query) {
        return R.success(pyIncomeBadDebtService.ListBusinessFinancialReportIncomeBadDebt(query));
    }


    /**
     * 获取业务财务报表收入坏账明细列表
     * 部门快照
     * @param query 查询条件
     * @return 收入坏账明细列表
     */
    @ApiOperation("业务财务报表收入坏账明细列表")
    @PostMapping(value = "ListBusinessFinancialReportIncomeBadDebt/dept")
    public R<IncomeBadDebtDetailsVO> ListBusinessFinancialReportIncomeBadDebtSnapshot(
            @Validated @RequestBody BusinessFinancialReportIncomeBadDebtQuery query) {
        return R.success(pyIncomeBadDebtService.ListBusinessFinancialReportIncomeBadDebtSnapshot(query));
    }

    /**
     * 一级详细传参
     * @param pyIncomeBadDeptInfoDTO
     * @return
     */
    @ApiOperation("获取收入坏账一级详细信息(不分页)")
    @PreAuthorize("@ss.hasPermi('project:pyIncomeBadDebt:query')")
    @PostMapping(value = "getFirstInfo")
    public R<PageInfo<PyFirstIncomeBadDeptInfoVO>> getFirstInfoPage(@RequestBody PyIncomeBadDeptInfoDTO pyIncomeBadDeptInfoDTO) {
        return R.success(pyIncomeBadDebtService.selectFirstIncomeBadDeptInfo(pyIncomeBadDeptInfoDTO));
    }

    /**
     * 新增收入坏账信息
     *
     * @param dto 收入坏账信息修改参数
     * @return 是否成功
     */
    @ApiOperation("新增收入坏账信息")
    @PreAuthorize("@ss.hasPermi('project:pyIncomeBadDebt:add')")
    @Log(title = "收入坏账信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Long> add(@RequestBody @Validated PyIncomeBadDebtDTO dto) {
        return R.success(pyIncomeBadDebtService.insertPyIncomeBadDebt(dto));
    }

    /**
     * 修改收入坏账信息
     *
     * @param dto 收入坏账信息修改参数
     * @return 是否成功
     */
    @ApiOperation("修改收入坏账信息")
    @PreAuthorize("@ss.hasPermi('project:pyIncomeBadDebt:edit')")
    @Log(title = "收入坏账信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R<Long> edit(@RequestBody @Valid PyIncomeBadDebtDTO dto) {
        return R.success(pyIncomeBadDebtService.updatePyIncomeBadDebt(dto));
    }

    /**
     * 多选框传参
     * @param dto
     * @return
     */
    @ApiOperation("多选框统计")
    @PreAuthorize("@ss.hasPermi('project:pyIncomeBadDebt:add')")
    @Log(title = "收入坏账信息", businessType = BusinessType.INSERT)
    @PostMapping("/checkbox")
    public R<PyIncomeCheckboxVO> checkBox(@RequestBody PyIncomeBadDebtCheckBoxQuery dto) {
        return R.success(pyIncomeBadDebtService.selectCheckBox(dto));
    }


    /**
     * 财务系统资源坏账 收入坏账标签统计
     * @param pyMainstayId
     * @return
     */
    @ApiOperation("收入坏账 资源坏账标签统计")
    @PreAuthorize("@ss.hasPermi('project:pyResourceBadDebt:add')")
    @Log(title = "收入坏账信息", businessType = BusinessType.INSERT)
    @PostMapping("/badDeptCount")
    public R<PyBadDeptCountVO> checkBox(@RequestParam(value = "pyMainstayId",required = false) Long pyMainstayId) {
        return R.success(pyIncomeBadDebtService.countBadDept(pyMainstayId));
    }


    /**
     * 财务合同的标签页数量
     * @param pyMainstayIds 派芽合作主体id集合
     * @return 客户合同信息视图模型
     */
    @ApiOperation("财务合同的收入坏账标签页数量")
    @PreAuthorize("@ss.hasPermi('crm:pyCustomerContract:query')")
    @PostMapping(value = "/getLabel")
    public R<List<PyBadDeptLabelCountVO>> getLabel(@RequestBody List<Long> pyMainstayIds) {
        return R.success(pyIncomeBadDebtService.getBadDeptLabel(pyMainstayIds));
    }


    /**
     * 导出收入坏账信息
     * @param query 导出查询参数
     */
    @ApiOperation("导出收入坏账信息")
    @PreAuthorize("@ss.hasPermi('project:pyIncomeBadDebt:export')")
    @Log(title = "收入坏账信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export( @Validated@RequestBody PyIncomeBadDebtQuery query) {
        String fileName;
        if(ListUtil.any(query.getPyMainstayIdList())){
            //查询主体名称
            List<SystemMainstayParam> systemMainstayParams = this.systemMainstayParamService.listByIds(query.getPyMainstayIdList());
            fileName = "收入坏账管理列表数据-"+systemMainstayParams.get(0).getMainstayName() + "-" + DateUtils.getTimeCn() + ".xlsx";
        }else {
            fileName = "收入坏账管理列表数据-"+ DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        }

        query.setFileName(fileName);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("收入坏账管理列表数据", TaskType.Export,query, PyIncomeBadDebtServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 查询收入坏账列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询收入坏账列表上的创建部门下拉" )
    @GetMapping("/listIncomeBadDebtDept" )
    public R<List<String>> listIncomeBadDebtDept(PyIncomeBadDebtQuery query){
        return R.success(pyIncomeBadDebtService.listIncomeBadDebtDept(query));
    }

    /**
     * 财务审批收入坏账
     * @param query 查询条件
     * @return 收入坏账的审批列表
     */
    @ApiOperation("财务审批收入坏账")
    @PreAuthorize("@ss.hasPermi('crm:pyCustomerContract:query')")
    @PostMapping(value = "/listApproveIncomeBadDebt")
    public R<PageInfo<ApproveIncomeBadDebtVO>> listApproveIncomeBadDebt(@Validated @RequestBody PyIncomeBadDebtQuery query){
        return R.success(pyIncomeBadDebtService.listApproveIncomeBadDebt(query));
    }

    /**
     * 财务审批收入坏账合计
     * @param query 查询条件
     * @return 财务审批收入坏账合计
     */
    @ApiOperation("财务审批收入坏账合计")
    @PreAuthorize("@ss.hasPermi('crm:pyCustomerContract:query')")
    @PostMapping(value = "/listCountApproveIncomeBadDebt")
    public R<PyIncomeCheckboxVO> listCountApproveIncomeBadDebt(@Validated @RequestBody PyIncomeBadDebtQuery query){
        return R.success(pyIncomeBadDebtService.listCountApproveIncomeBadDebt(query));
    }

    /**
     * 财务审批收入坏账标签页统计
     * @param pyMainstayIds 派芽合作主体id集合
     * @return 签页统计
     */
    @ApiOperation("财务审批收入坏账标签页统计")
    @PreAuthorize("@ss.hasPermi('crm:pyCustomerContract:query')")
    @PostMapping(value = "/listCountApproveIncomeBadDebtLabel")
    public R<List<PyBadDeptLabelCountVO>> listCountApproveIncomeBadDebtLabel(@RequestBody List<Long> pyMainstayIds) {
        return R.success(pyIncomeBadDebtService.listCountApproveIncomeBadDebtLabel(pyMainstayIds));
    }

    /**
     * 财务审批收入坏账
     * @param query 导出查询参数
     */
    @ApiOperation("导出财务审批收入坏账")
    @PreAuthorize("@ss.hasPermi('project:pyIncomeBadDebt:exportApprove')")
    @Log(title = "收入坏账审批查询", businessType = BusinessType.EXPORT)
    @PostMapping("/exportApprove")
    public R<String> exportApprove( @Validated@RequestBody PyIncomeBadDebtQuery query) {
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(ListUtil.first(query.getPyMainstayIdList()));
        String fileName = "收入坏账审批查询列表数据-" + systemMainstayParamVO.getMainstayName() + "-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setIsApproval(true);
        reusableAsyncTaskService.addTask("收入坏账审批查询列表", TaskType.Export,query, PyIncomeBadDebtServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 查询收入坏账审批的项目主体
     *
     * @return 系统设置-主体参数设置列表
     */
    @ApiOperation("查询收入坏账审批的项目主体")
    @PreAuthorize("@ss.hasPermi('project:pyIncomeBadDebt:querySystemMainstayParam')")
    @PostMapping("/querySystemMainstayParam")
    public R<List<MainstayParamListVO>> querySystemMainstayParam(){
        List<MainstayParamListVO> mainstayParamListVOS = systemMainstayParamService.querySystemMainstayParam(DataScopePageType.Finance_Audit_BadDept);
        return R.success(mainstayParamListVOS);
    }

    /**
     * 查询收入坏账审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询收入坏账审批列表上的创建部门下拉" )
    @GetMapping("/listApprovalIncomeBadDebtDept" )
    public R<List<String>> listApprovalIncomeBadDebtDept(PyIncomeBadDebtQuery query){
        return R.success(pyIncomeBadDebtService.listApprovalIncomeBadDebtDept(query));
    }
}
