package com.py.crm.customerdevote.domain.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 项目管理-项目表对象
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
@TableName("py_project" )
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("项目管理-项目表" )
public class CrmProject extends BaseEntity {
private static final long serialVersionUID=1L;

    /** 自增id */
    @ApiModelProperty("自增id")
    private Long id;

    /** 项目id */
    @TableId
    @ApiModelProperty("项目id")
    private Long projectId;

    /** 项目名称 */
    @ApiModelProperty("项目名称")
    private String projectName;

    /** 项目编号 */
    @ApiModelProperty("项目编号")
    private String projectCode;

    /** 立项项目金额 */
    @ApiModelProperty("立项项目金额")
    private BigDecimal projectAmount;

    /** 立项毛利率预估 */
    @ApiModelProperty("立项毛利率预估")
    private BigDecimal grossMargin;

    /** 项目开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("项目开始时间")
    private LocalDate projectStartTime;

    /** 预计结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("预计结束时间")
    private LocalDate expectedEndTime;

    /** 派芽合作主体id */
    @ApiModelProperty("派芽合作主体id")
    private Long paiyaMainstayId;

    /** 派芽合作主体编码 */
    @ApiModelProperty("派芽合作主体编码")
    private String mainstayCode;

    /** 派芽合作主体名称 */
    @ApiModelProperty("派芽合作主体名称")
    private String mainstayName;

    /** 客户id */
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 客户名称 */
    @ApiModelProperty("客户名称")
    private String customerName;

    /** 客户合作主体id */
    @ApiModelProperty("客户合作主体id")
    private Long customerMainstayId;

    /** 客户合作主体名称 */
    @ApiModelProperty("客户合作主体名称")
    private String customerMainstayName;

    /** 品牌/业务线 */
    @ApiModelProperty("品牌/业务线")
    private String lineBusiness;

    /** 客户版本 */
    @ApiModelProperty("客户版本")
    private Integer customerVersion;

    /** 是否比稿 */
    @ApiModelProperty("是否比稿")
    private Integer hasComparedDraft;

    /** 比稿项目id */
    @ApiModelProperty("比稿项目id")
    private Long comparedDraftId;

    /** 比稿项目名称 */
    @ApiModelProperty("比稿项目名称")
    private String comparedDraftName;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;

    /** 审批状态 */
    @ApiModelProperty("审批状态")
    private Integer auditStatus;

    /** 立项时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("立项时间")
    private LocalDate projectApprovalTime;

    /** 项目状态 */
    @ApiModelProperty("项目状态")
    private Integer projectStatus;

    /** 确认收入 */
    @ApiModelProperty("确认收入")
    private Integer confirmIncome;

    /** 版本 */
    @ApiModelProperty("版本")
    private Long version;

    /** 结案时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("结案时间")
    private LocalDate closeCaseTime;

    /** 新增审批通过时间 */
    @ApiModelProperty("新增审批通过时间")
    private LocalDateTime auditTime;

    /** 更新部门*/
    @ApiModelProperty("更新部门")
    private String updateDept;

    /** 创建部门*/
    @ApiModelProperty("创建部门")
    private String createDept;

}
