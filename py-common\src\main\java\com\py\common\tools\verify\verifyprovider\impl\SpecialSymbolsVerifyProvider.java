package com.py.common.tools.verify.verifyprovider.impl;

import com.py.common.tools.verify.enums.VerifyType;
import com.py.common.tools.verify.verifyprovider.VerifyProvider;
import com.py.common.utils.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 特殊字符验证提供者
 * <AUTHOR>
 * @date 2022/6/28 14:20
 */
@Component
public class SpecialSymbolsVerifyProvider implements VerifyProvider {
    @Override
    public VerifyType supportedVerifyType() {
        return VerifyType.SpecialSymbols;
    }

    @Override
    public boolean verify(Object target) throws Exception {
        if(target == null) {
            return true;
        }

        if(target instanceof String == false) {
            throw new IllegalArgumentException("只有字符串才能使用 VerifyType.SpecialSymbols 验证");
        }

        return StringUtils.hasSpecialSymbol((String) target) == false;
    }
}
