package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.project.enums.ExportType;
import com.py.project.projectconfirmrecord.domain.query.ConfirmIncomeDownloadQuery;
import com.py.project.projectconfirmrecord.domain.query.ProjectConfirmRecordApprovalQuery;
import com.py.project.projectconfirmrecord.domain.query.ProjectConfirmRecordQuery;
import com.py.project.projectconfirmrecord.domain.vo.ProjectConfirmRecordApprovalSumVO;
import com.py.project.projectconfirmrecord.domain.vo.ProjectConfirmRecordApprovalVO;
import com.py.project.projectconfirmrecord.domain.vo.ProjectConfirmRecordListVO;
import com.py.project.projectconfirmrecord.domain.vo.ProjectConfirmRecordVO;
import com.py.project.projectconfirmrecord.service.IProjectConfirmRecordService;
import com.py.project.projectconfirmrecord.service.impl.ConfirmIncomeApprovalServiceImpl;
import com.py.project.projectconfirmrecord.service.impl.ProjectConfirmRecordDownloadServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 项目管理-确认收入记录Controller
 *
 * <AUTHOR>
 * @date 2023-08-01
 */
@Api(tags = "项目管理-确认收入记录")
@RestController
@RequestMapping("/project/projectConfirmRecord")
public class ProjectConfirmRecordController extends BaseController {

    /** 项目管理-确认收入记录服务 */
    @Resource
    private IProjectConfirmRecordService projectConfirmRecordService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 查询项目管理-确认收入记录列表
     *
     * @param query 项目管理-确认收入记录查询参数
     * @return 项目管理-确认收入记录列表
     */
    @ApiOperation("查询项目管理-确认收入记录列表")
    @PreAuthorize("@ss.hasPermi('project:projectConfirmRecord:list')")
    @GetMapping("/listProjectConfirmRecord")
    public R<List<ProjectConfirmRecordListVO>> listProjectConfirmRecord(ProjectConfirmRecordQuery query) {
        List<ProjectConfirmRecordListVO> voList = this.projectConfirmRecordService.listProjectConfirmRecord(query);
        return R.success(voList);
    }

    /**
     * 分页查询项目管理-确认收入记录列表
     *
     * @param query 项目管理-确认收入记录查询参数
     * @return 项目管理-确认收入记录分页
     */
    @ApiOperation("分页查询询项目管理-确认收入记录列表")
    @PreAuthorize("@ss.hasPermi('project:projectConfirmRecord:list')")
    @GetMapping("/pageProjectConfirmRecord")
    public R<PageInfo<ProjectConfirmRecordListVO>> pageProjectConfirmRecord(ProjectConfirmRecordQuery query) {
        PageInfo<ProjectConfirmRecordListVO> voList = this.projectConfirmRecordService.pageProjectConfirmRecordList(query);
        return R.success(voList);
    }

    /**
     * 获取项目管理-确认收入记录详细信息
     * @param query 项目管理-确认收入记录主键
     * @return 项目管理-确认收入记录视图模型
     */
    @ApiOperation("获取项目管理-确认收入记录详细信息")
    @PreAuthorize("@ss.hasPermi('project:projectConfirmRecord:query')")
    @GetMapping(value = "/selectRecordDetail")
    public R<ProjectConfirmRecordVO> getInfo(ProjectConfirmRecordQuery query) {
        return R.success(projectConfirmRecordService.selectProjectConfirmRecordById(query));
    }

    /**
     * 删除项目管理-确认收入记录
     * @param ids 需要删除的项目管理-确认收入记录主键集合
     * @return 是否成功
     */
    @ApiOperation("删除项目管理-确认收入记录" )
    @PreAuthorize("@ss.hasPermi('project:projectConfirmRecord:remove')")
    @Log(title = "项目管理-确认收入记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(projectConfirmRecordService.deleteProjectConfirmRecordByIds(ids));
    }

    /**
     * 审批查询-确认收入-分页列表
     *
     * @param query 项目管理-确认收入记录查询参数
     * @return 项目管理-确认收入记录分页
     */
    @ApiOperation("审批查询-确认收入-分页列表")
    @PreAuthorize("@ss.hasPermi('project:projectConfirmRecord:approcal:pagelist')")
    @GetMapping("/pageProjectConfirmRecordApproval")
    public R<PageInfo<ProjectConfirmRecordApprovalVO>> pageProjectConfirmRecordApproval(ProjectConfirmRecordApprovalQuery query) {
        PageInfo<ProjectConfirmRecordApprovalVO> voList = this.projectConfirmRecordService.pageProjectConfirmRecordApproval(query);
        return R.success(voList);
    }

    /**
     * 审批查询-确认收入-主体数量统计
     * @return 主体数量集合
     */
    @ApiOperation("审批查询-确认收入-主体数量统计")
    @PreAuthorize("@ss.hasPermi('project:projectConfirmRecord:approcal:sumlist')")
    @GetMapping("/sumConfirmApprovalByMainstayId")
    public R<List<ProjectConfirmRecordApprovalSumVO>> sumConfirmApprovalByMainstayId() {
        List<ProjectConfirmRecordApprovalSumVO> sumList = this.projectConfirmRecordService.sumConfirmApprovalByMainstayId();
        return R.success(sumList);
    }

    /**
     * 审批查询-确认收入-列表合计统计
     * @param query 是否全选/选中的id集合
     * @return 数据统计信息
     */
    @ApiOperation("审批查询-确认收入-列表合计统计")
    @PreAuthorize("@ss.hasPermi('project:projectConfirmRecord:approcal:sumIds')")
    @GetMapping("/sumConfirmApprovalByIds")
    public R<ProjectConfirmRecordApprovalSumVO> sumConfirmApprovalByIds(ProjectConfirmRecordApprovalQuery query) {
        ProjectConfirmRecordApprovalSumVO sum = this.projectConfirmRecordService.sumConfirmApprovalByIds(query);
        return R.success(sum);
    }

    /**
     * 导出下载修改申请项目审批查询列表
     * @param query 导出查询参数
     */
    @ApiOperation("审批查询-确认收入-下载审批查询列表")
    @PreAuthorize("@ss.hasPermi('project:projectConfirmRecord:approval:export')")
    @Log(title = "项目管理-确认收入记录", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadProjectConfirmApprovalList")
    public R<String> downloadProjectConfirmApprovalList(@RequestBody ProjectConfirmRecordApprovalQuery query) {
        query.setExportType(ExportType.PROJECT_CONFIRM_RECORD_APPROVAL_LIST);
        this.reusableAsyncTaskService.addTask("确认收入审批查询",
                TaskType.Export,
                query,
                ProjectConfirmRecordDownloadServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 导出下载修改申请项目审批查询列表
     * @param query 导出查询参数
     */
    @ApiOperation("确认收入-下载确认收入审批详情")
    @PreAuthorize("@ss.hasPermi('project:projectConfirmRecord:approval:export')")
    @Log(title = "项目管理-确认收入记录", businessType = BusinessType.EXPORT)
    @PostMapping("/downloadConfirmReocrdList")
    public R<String> downloadConfirmReocrdList(@RequestBody ConfirmIncomeDownloadQuery query) {
        query.setExportType(ExportType.CONFIRM_INCOME_APPROVAL_DETAILS);
        this.reusableAsyncTaskService.addTask("确认收入审批详情",
                TaskType.Export,
                query,
                ConfirmIncomeApprovalServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 查询确认收入审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询确认收入审批列表上的创建部门下拉" )
    @GetMapping("/listConfirmRecordApprovalDept" )
    public R<List<String>> listConfirmRecordApprovalDept(ProjectConfirmRecordApprovalQuery query){
        return R.success(projectConfirmRecordService.listConfirmRecordApprovalDept(query));
    }
}
