package com.py.flow.domain.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 按渠道统计金额DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("按渠道统计金额DTO")
@AllArgsConstructor
@NoArgsConstructor
public class ChannelAmountDataDTO {

    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "渠道合计金额")
    private BigDecimal channelAmount;

}
