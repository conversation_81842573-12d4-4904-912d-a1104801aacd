package com.py.common.core.domain;

import com.py.common.constant.HttpStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 通用返回结果
 * <AUTHOR>
 **/
@Data
@SuppressWarnings({"unused", "AlibabaClassNamingShouldBeCamel"})
@ApiModel("返回结果")
public class R<T> {

    @ApiModelProperty("结果码")
    private int code;

    @ApiModelProperty("操作信息")
    private String msg;

    @ApiModelProperty("结果数据")
    private T data;

    /** 反序列化用构造函数 */
    public R() {
    }

    public R(String msg, T data) {
        this(HttpStatus.SUCCESS, msg, data);
    }

    public R(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static <T> R<T> success() {
        return R.success("操作成功");
    }

    public static <T> R<T> success(String msg) {
        return R.success(msg, null);
    }

    public static <T> R<T> success(T data) {
        return R.success("操作成功", data);
    }

    public static <T> R<T> success(String msg, T data) {
        return new R<>(HttpStatus.SUCCESS, msg,data);
    }

    public static <T> R<T> failed(String msg) {
        return new R<>(HttpStatus.ERROR, msg, null);
    }

    public static <T> R<T> failed(T data) {
        return new R<>(HttpStatus.ERROR, "操作失败", data);
    }

    public static <T> R<T> failed(String msg, T data) {
        return new R<>(HttpStatus.ERROR, msg, data);
    }

    public static <T> R<T> failed(int code, String msg, T data) {
        return new R<>(code, msg, data);
    }

    public static <T> R<T> failed(int code) {
        return new R<>(code, "操作失败", null);
    }
}
