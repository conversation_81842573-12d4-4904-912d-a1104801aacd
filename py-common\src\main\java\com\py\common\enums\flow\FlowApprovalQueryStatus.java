package com.py.common.enums.flow;

import com.py.common.enums.IDict;
import com.py.common.exception.ServiceException;
import lombok.AllArgsConstructor;

import java.util.EnumSet;
import java.util.Objects;
import java.util.Set;

/**
 * 审批状态查询枚举
 * <AUTHOR>
 * @date 2023/7/27 9:54
 */
@AllArgsConstructor
public enum FlowApprovalQueryStatus implements IDict<Integer> {

    /** 审批中 */
    QUERY_IN_EXAMINATION_AND_APPROVAL(0,"审批中"),

    /** 审批完结 = 审批通过-审批撤销-审批作废 */
    QUERY_OVER_APPROVE(1,"审批完结"),

    /** 审批拒绝 */
    QUERY_APPROVAL_REJECTION(2,"审批拒绝"),

    /** 审批撤销 */
    QUERY_APPROVAL_REVOCATION(3,"审批撤回"),

    /** 审批退回 */
    QUERY_APPROVAL_RETURN(4,"审批退回");

    private final Integer value;

    private final String label;

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public Integer getValue() {
        return value;
    }


}
