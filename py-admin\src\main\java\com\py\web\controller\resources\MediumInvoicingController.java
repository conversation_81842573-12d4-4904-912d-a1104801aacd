package com.py.web.controller.resources;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.resources.mediuminvoicing.domain.dto.MediumInvoicingDTO;
import com.py.resources.mediuminvoicing.domain.dto.MediumInvoicingExportModel;
import com.py.resources.mediuminvoicing.domain.query.MediumInvoicingQuery;
import com.py.resources.mediuminvoicing.domain.vo.MediumInvoicingListVO;
import com.py.resources.mediuminvoicing.domain.vo.MediumInvoicingVO;
import com.py.resources.mediuminvoicing.service.IMediumInvoicingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 媒介资源管理-资源开票信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Api(tags = "媒介资源管理-资源开票信息")
@RestController
@RequestMapping("/resources/mediumInvoicing")
public class MediumInvoicingController extends BaseController {

    /** 媒介资源管理-资源开票信息服务 */
    @Resource
    private IMediumInvoicingService mediumInvoicingService;

    /**
     * 查询媒介资源管理-资源开票信息列表
     *
     * @param query 媒介资源管理-资源开票信息查询参数
     * @return 媒介资源管理-资源开票信息列表
     */
    @ApiOperation("查询媒介资源管理-资源开票信息列表")
    @PreAuthorize("@ss.hasPermi('resources:mediumInvoicing:list')")
    @GetMapping("/listMediumInvoicing")
    public R<List<MediumInvoicingListVO>> listMediumInvoicing(MediumInvoicingQuery query) {
        List<MediumInvoicingListVO> voList = this.mediumInvoicingService.listMediumInvoicing(query);
        return R.success(voList);
    }

    /**
     * 分页查询媒介资源管理-资源开票信息列表
     *
     * @param query 媒介资源管理-资源开票信息查询参数
     * @return 媒介资源管理-资源开票信息分页
     */
    @ApiOperation("分页查询询媒介资源管理-资源开票信息列表")
    @PreAuthorize("@ss.hasPermi('resources:mediumInvoicing:list')")
    @GetMapping("/pageMediumInvoicing")
    public R<PageInfo<MediumInvoicingListVO>> pageMediumInvoicing(MediumInvoicingQuery query) {
        PageInfo<MediumInvoicingListVO> voList = this.mediumInvoicingService.pageMediumInvoicingList(query);
        return R.success(voList);
    }

    /**
     * 获取媒介资源管理-资源开票信息详细信息
     * @param id 媒介资源管理-资源开票信息主键
     * @return 媒介资源管理-资源开票信息视图模型
     */
    @ApiOperation("获取媒介资源管理-资源开票信息详细信息")
    @PreAuthorize("@ss.hasPermi('resources:mediumInvoicing:query')")
    @GetMapping(value = "/{id}")
    public R<MediumInvoicingVO> getInfo(@PathVariable("id") Long id) {
        return R.success(mediumInvoicingService.selectMediumInvoicingById(id));
    }

    /**
     * 新增媒介资源管理-资源开票信息
     *
     * @param dto 媒介资源管理-资源开票信息修改参数
     * @return 是否成功
     */
    @ApiOperation("新增媒介资源管理-资源开票信息")
    @PreAuthorize("@ss.hasPermi('resources:mediumInvoicing:add')")
    @Log(title = "媒介资源管理-资源开票信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody List<MediumInvoicingDTO> dto) {
        return R.success(mediumInvoicingService.insertMediumInvoicing(dto));
    }


    /**
     * 删除媒介资源管理-资源开票信息
     * @param ids 需要删除的媒介资源管理-资源开票信息主键集合
     * @return 是否成功
     */
    @ApiOperation("删除媒介资源管理-资源开票信息" )
    @PreAuthorize("@ss.hasPermi('resources:mediumInvoicing:remove')")
    @Log(title = "媒介资源管理-资源开票信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(mediumInvoicingService.deleteMediumInvoicingByIds(ids));
    }

    /**
     * 导出媒介资源管理-资源开票信息
     * @param response 请求响应
     * @param resourceIdList 资源id列表
     */
    @ApiOperation("导出媒介资源管理-资源开票信息")
    @PreAuthorize("@ss.hasPermi('resources:mediumInvoicing:export')")
    @Log(title = "媒介资源管理-资源开票信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, List<Long> resourceIdList) {
        List<MediumInvoicingExportModel> exportList = this.mediumInvoicingService.exportMediumInvoicing(resourceIdList);

        ExcelUtil<MediumInvoicingExportModel> util = new ExcelUtil<>(MediumInvoicingExportModel. class);
        util.exportExcel(response, exportList, "媒介资源管理-资源开票信息数据" );
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumInvoicing:import')" )
    @Log(title = "媒介资源管理-资源开票信息" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<MediumInvoicingExportModel> util = new ExcelUtil<>(MediumInvoicingExportModel.class);
        List<MediumInvoicingExportModel> mediumInvoicingList = util.importExcel(file.getInputStream());
        String message = this.mediumInvoicingService.importMediumInvoicing(mediumInvoicingList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumInvoicing:import')" )
    @Log(title = "媒介资源管理-资源开票信息" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<MediumInvoicingExportModel> util = new ExcelUtil<>(MediumInvoicingExportModel.class);
        util.importTemplateExcel(response, "媒介资源管理-资源开票信息数据" );
    }

    /**
     * 查询媒介资源管理-资源开票信息列表
     *
     * @param resourceId 资源Id
     * @return 媒介资源管理-资源开票信息分页
     */
    @ApiOperation("查询询媒介资源管理-资源开票信息列表")
    @PreAuthorize("@ss.hasPermi('resources:mediumInvoicing:list')")
    @GetMapping("/mediumInvoicingList")
    public R<List<MediumInvoicingListVO>> mediumInvoicingList(Long resourceId) {
        List<MediumInvoicingListVO> voList = this.mediumInvoicingService.mediumInvoicingList(resourceId);
        return R.success(voList);
    }

}
