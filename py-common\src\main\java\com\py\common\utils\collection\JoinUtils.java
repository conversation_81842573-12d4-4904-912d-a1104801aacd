package com.py.common.utils.collection;

import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.tuple.Pair;
import rx.functions.Action2;
import rx.functions.Func2;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 应用层关联工具类
 * <AUTHOR>
 */
@Log4j2
@SuppressWarnings("unused")
public class JoinUtils {

    /**
     * 使用指定键将主集合和副集合进行一对一关联, 然后聚合关联的内容并返回
     * @param mainList 主集合
     * @param subList 副集合
     * @param mainRelatedKeySelector 主集合元素关联键选择器
     * @param subRelatedKeySelector 副集合元素关联键选择器
     * @param aggregationFun 聚合方法
     * @param <TMain> 主元素类型
     * @param <TSub> 副集合元素类型
     * @param <TRelatedKey> 关联键类型
     * @param <TResult> 聚合结果类型
     * @return 使用指定键关联的聚合结果
     */
    @SuppressWarnings("DuplicatedCode")
    public static <TMain, TSub, TRelatedKey extends Comparable<TRelatedKey>, TResult> List<TResult> oneBeOne(
            Collection<TMain> mainList,
            Collection<TSub> subList,
            @NonNull Function<TMain, TRelatedKey> mainRelatedKeySelector,
            @NonNull Function<TSub, TRelatedKey> subRelatedKeySelector,
            @NonNull Func2<TMain, TSub, TResult> aggregationFun) {
        if(ListUtil.isEmpty(mainList) || ListUtil.isEmpty(subList)) { return ListUtil.emptyList(); }

        Map<TRelatedKey, TSub> subMap = ListUtil.toMap(subList, subRelatedKeySelector);

        List<TResult> resultList = new ArrayList<>(mainList.size());
        for(TMain mainItem : mainList) {
            TRelatedKey relatedKey = mainRelatedKeySelector.apply(mainItem);
            if(subMap.containsKey(relatedKey) == false) {
                continue;
            }

            resultList.add(aggregationFun.call(mainItem, subMap.get(relatedKey)));
        }
        return resultList;
    }

    /**
     * 使用指定键将主集合和副集合进行一对一关联, 然后聚合关联的内容并返回
     * @param mainList 主集合
     * @param subList 副集合
     * @param relatedKeySelector 集合元素关联键选择器
     * @param aggregationFun 聚合方法
     * @param <T> 关联元素类型
     * @param <TRelatedKey> 关联键类型
     * @param <TResult> 聚合结果类型
     * @return 使用指定键关联的聚合结果
     */
    @SuppressWarnings("DuplicatedCode")
    public static <T, TRelatedKey extends Comparable<TRelatedKey>, TResult> List<TResult> oneBeOne(
            Collection<T> mainList,
            Collection<T> subList,
            @NonNull Function<T, TRelatedKey> relatedKeySelector,
            @NonNull Func2<T, T, TResult> aggregationFun) {
        return oneBeOne(mainList, subList,
                relatedKeySelector, relatedKeySelector, aggregationFun);
    }

    /**
     * 使用指定键将主集合和副集合进行一对一关联, 然后修改关联的内容
     * @param mainList 主集合
     * @param subList 副集合
     * @param mainRelatedKeySelector 主集合元素关联键选择器
     * @param subRelatedKeySelector 副集合元素关联键选择器
     * @param modifyAction 修改回调方法
     * @param <TMain> 主元素类型
     * @param <TSub> 副集合元素类型
     * @param <TRelatedKey> 关联键类型
     */
    public static <TMain, TSub, TRelatedKey extends Comparable<TRelatedKey>> void oneByOne(
            Collection<TMain> mainList,
            Collection<TSub> subList,
            @NonNull Function<TMain, TRelatedKey> mainRelatedKeySelector,
            @NonNull Function<TSub, TRelatedKey> subRelatedKeySelector,
            @NonNull Action2<TMain, TSub> modifyAction) {
        oneBeOne(
                mainList, subList,
                mainRelatedKeySelector, subRelatedKeySelector,
                (main, sub) -> {
                    modifyAction.call(main, sub);
                    // 调用时不需要返回值
                    return 0;
                });
    }

    /**
     * 使用指定键将主集合和副集合进行一对一做左连接, 然后修改通过关联内容返回聚合后的数据
     * @param mainList 主集合
     * @param subList 副集合
     * @param mainRelatedKeySelector 主集合元素关联键选择器
     * @param subRelatedKeySelector 副集合元素关联键选择器
     * @param aggregationFun 聚合方法
     * @param <TMain> 主元素类型
     * @param <TSub> 副集合元素类型
     * @param <TRelatedKey> 关联键类型
     * @param <TResult> 聚合结果类型
     */
    public static <TMain, TSub, TRelatedKey extends Comparable<TRelatedKey>, TResult> List<TResult> leftOneBeOne(
            Collection<TMain> mainList,
            Collection<TSub> subList,
            @NonNull Function<TMain, TRelatedKey> mainRelatedKeySelector,
            @NonNull Function<TSub, TRelatedKey> subRelatedKeySelector,
            @NonNull Func2<TMain, TSub, TResult> aggregationFun) {
        if(ListUtil.isEmpty(mainList) || ListUtil.isEmpty(subList)) { return ListUtil.emptyList(); }

        Map<TRelatedKey, TSub> subMap = ListUtil.toMap(subList, subRelatedKeySelector);

        List<TResult> resultList = new ArrayList<>(mainList.size());
        for(TMain mainItem : mainList) {
            TRelatedKey relatedKey = mainRelatedKeySelector.apply(mainItem);
            resultList.add(aggregationFun.call(mainItem, subMap.get(relatedKey)));
        }
        return resultList;
    }


    /**
     * 使用指定键将主集合和副集合一对多关联, 然后聚合关联的内容并返回
     * @param mainList 主集合
     * @param subList 副集合
     * @param mainRelatedKeySelector 主集合元素关联键选择器
     * @param subRelatedKeySelector 副集合元素关联键选择器
     * @param aggregationFun 聚合方法
     * @param <TMain> 主元素类型
     * @param <TSub> 副集合元素类型
     * @param <TRelatedKey> 关联键类型
     * @param <TResult> 聚合结果类型
     * @return 使用指定键关联的聚合结果
     */
    @SuppressWarnings("DuplicatedCode")
    public static <TMain, TSub, TRelatedKey extends Comparable<TRelatedKey>, TResult> List<TResult> oneBeMany(
            Collection<TMain> mainList,
            Collection<TSub> subList,
            @NonNull Function<TMain, TRelatedKey> mainRelatedKeySelector,
            @NonNull Function<TSub, TRelatedKey> subRelatedKeySelector,
            @NonNull Func2<TMain, List<TSub>, TResult> aggregationFun) {
        if(ListUtil.isEmpty(mainList) || ListUtil.isEmpty(subList)) { return ListUtil.emptyList(); }

        Map<TRelatedKey, List<TSub>> subMap = ListUtil.toGroup(subList, subRelatedKeySelector);

        List<TResult> resultList = new ArrayList<>(mainList.size());
        for(TMain mainItem : mainList) {
            TRelatedKey relatedKey = mainRelatedKeySelector.apply(mainItem);
            if(subMap.containsKey(relatedKey) == false) {
                continue;
            }

            resultList.add(aggregationFun.call(mainItem, subMap.get(relatedKey)));
        }
        return resultList;
    }

    /**
     * 使用指定键将主集合和副集合进行一对多关联, 然后修改关联的内容
     * @param mainList 主集合
     * @param subList 副集合
     * @param mainRelatedKeySelector 主集合元素关联键选择器
     * @param subRelatedKeySelector 副集合元素关联键选择器
     * @param modifyAction 修改回调方法
     * @param <TMain> 主元素类型
     * @param <TSub> 副集合元素类型
     * @param <TRelatedKey> 关联键类型
     */
    public static <TMain, TSub, TRelatedKey extends Comparable<TRelatedKey>> void oneByMany(
            Collection<TMain> mainList,
            Collection<TSub> subList,
            @NonNull Function<TMain, TRelatedKey> mainRelatedKeySelector,
            @NonNull Function<TSub, TRelatedKey> subRelatedKeySelector,
            @NonNull Action2<TMain, List<TSub>> modifyAction) {
        oneBeMany(
                mainList, subList,
                mainRelatedKeySelector, subRelatedKeySelector,
                (main, sub) -> {
                    modifyAction.call(main, sub);
                    // 调用时不需要返回值
                    return 0;
                });
    }

    /**
     * 使用指定将进行中间表关联
     * @param mainList 主集合
     * @param mediumList 中介集合
     * @param subList 副集合
     * @param mainRelatedKeySelector 主集合元素关联键选择器
     * @param mediumMainRelatedKeySelector 中介集合与主集合元素关联键选择器
     * @param subRelatedKeySelector 副集合元素关联键选择器
     * @param mediumSubRelatedKeySelector 中介集合与副集合元素关联键选择器
     * @param aggregationFun 聚合方法
     * @param <TMain> 主集合元素类型
     * @param <TMedium> 中介集合元素类型
     * @param <TSub> 副集合元素类型
     * @param <TMainKey> 主集合关联键类型
     * @param <TSubKey> 副集合关联键类型
     * @param <TResult> 返回集合元素类型
     */
    public static <TMain, TMedium, TSub, TMainKey extends Comparable<TMainKey>, TSubKey extends Comparable<TSubKey>, TResult> List<TResult> oneBeMany(
            Collection<TMain> mainList,
            Collection<TMedium> mediumList,
            Collection<TSub> subList,
            @NonNull Function<TMain, TMainKey> mainRelatedKeySelector,
            @NonNull Function<TMedium, TMainKey> mediumMainRelatedKeySelector,
            @NonNull Function<TSub, TSubKey> subRelatedKeySelector,
            @NonNull Function<TMedium, TSubKey> mediumSubRelatedKeySelector,
            @NonNull Func2<TMain, List<TSub>, TResult> aggregationFun) {

        List<Pair<TMainKey, TSub>> relatedSubList = oneBeOne(mediumList, subList,
                mediumSubRelatedKeySelector, subRelatedKeySelector,
                (medium, sub) -> Pair.of(mediumMainRelatedKeySelector.apply(medium), sub));

        Map<TMainKey, List<TSub>> subMainKeyMap = ListUtil.toGroup(relatedSubList, Pair::getLeft, Pair::getRight);

        return ListUtil.map(mainList, mainItem -> {
            List<TSub> itemRelatedSubList = subMainKeyMap.get(mainRelatedKeySelector.apply(mainItem));
            return aggregationFun.call(mainItem, itemRelatedSubList);
        });
    }

    /**
     * 使用指定将进行中间表关联
     * @param mainList 主集合
     * @param mediumList 中介集合
     * @param subList 副集合
     * @param mainRelatedKeySelector 主集合元素关联键选择器
     * @param mediumMainRelatedKeySelector 中介集合与主集合元素关联键选择器
     * @param subRelatedKeySelector 副集合元素关联键选择器
     * @param mediumSubRelatedKeySelector 中介集合与副集合元素关联键选择器
     * @param modifyAction 修改回调方法
     * @param <TMain> 主集合元素类型
     * @param <TMedium> 中介集合元素类型
     * @param <TSub> 副集合元素类型
     * @param <TMainKey> 主集合关联键类型
     * @param <TSubKey> 副集合关联键类型
     */
    public static <TMain, TMedium, TSub, TMainKey extends Comparable<TMainKey>, TSubKey extends Comparable<TSubKey>> void oneByMany(
            Collection<TMain> mainList,
            Collection<TMedium> mediumList,
            Collection<TSub> subList,
            @NonNull Function<TMain, TMainKey> mainRelatedKeySelector,
            @NonNull Function<TMedium, TMainKey> mediumMainRelatedKeySelector,
            @NonNull Function<TSub, TSubKey> subRelatedKeySelector,
            @NonNull Function<TMedium, TSubKey> mediumSubRelatedKeySelector,
            @NonNull Action2<TMain, List<TSub>> modifyAction) {

        oneBeMany(
                mainList, mediumList, subList,
                mainRelatedKeySelector, mediumMainRelatedKeySelector,
                subRelatedKeySelector, mediumSubRelatedKeySelector,
                (mainItem, itemRelatedSubList) -> {
                    modifyAction.call(mainItem, itemRelatedSubList);
                    return null;
                });
    }


    /**
     * 使用指定键将主集合和副集合进行多对多关联, 然后修改关联的内容
     * @param mainList 主集合
     * @param subList 副集合
     * @param mainRelatedKeySelector 主集合元素关联键选择器
     * @param subRelatedKeySelector 副集合元素关联键选择器
     * @param modifyAction 修改回调方法
     * @param <TMain> 主元素类型
     * @param <TSub> 副集合元素类型
     * @param <TRelatedKey> 关联键类型
     */
    public static <TMain, TSub, TRelatedKey extends Comparable<TRelatedKey>> void manyByMany(
            List<TMain> mainList,
            List<TSub> subList,
            @NonNull Function<TMain, List<TRelatedKey>> mainRelatedKeySelector,
            @NonNull Function<TSub, TRelatedKey> subRelatedKeySelector,
            @NonNull Action2<TMain, List<TSub>> modifyAction) {
        if(ListUtil.isEmpty(mainList) || ListUtil.isEmpty(subList)) {
            return;
        }

        Map<TRelatedKey, TSub> dictValueMap = ListUtil.toMap(subList, subRelatedKeySelector);
        for(TMain item : mainList) {
            List<TRelatedKey> relatedKeyList = mainRelatedKeySelector.apply(item);
            if(ListUtil.isEmpty(relatedKeyList)) {
                continue;
            }
            List<TSub> relatedDictList = new ArrayList<>(relatedKeyList.size());
            for(TRelatedKey relatedKey : relatedKeyList) {
                if(dictValueMap.containsKey(relatedKey) == false) {
                    log.warn("关联的键 {} 不存在对应值, 已跳过", relatedKey);
                    continue;
                }
                relatedDictList.add(dictValueMap.get(relatedKey));
            }
            modifyAction.call(item, relatedDictList);
        }
    }
}
