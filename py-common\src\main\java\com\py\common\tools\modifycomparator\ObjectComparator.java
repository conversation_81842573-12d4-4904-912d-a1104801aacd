package com.py.common.tools.modifycomparator;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Maps;
import com.py.common.core.domain.entity.SysDictData;
import com.py.common.core.domain.service.IDictService;
import com.py.common.tools.modifycomparator.annotation.CompareField;
import com.py.common.tools.modifycomparator.contexthandler.ICompareContextHandler;
import com.py.common.tools.modifycomparator.model.BaseModifyDiff;
import com.py.common.tools.verify.domain.ISerialNumber;
import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import lombok.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import rx.functions.Func2;
import rx.functions.Func3;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 对象比较器
 * <AUTHOR>
 */
@Component
public class ObjectComparator implements IObjectComparator {

    // TODO: 重构扩展xx部分的丑陋实现

    /** 扩展比较Map - 用于为无法定义比较接口的对象, 添加比较方法 */
    public final Map<Class<?>, Func3<Object, Object, Field, Boolean>> extensionCompareMap = this.initExtensionCompareMap();

    /** 扩展格式化Map - 用于为无法定义ToString方法的对象, 添加ToString方法 */
    public final Map<Class<?>, Func2<Object, Field, String>> extensionFormatMap = this.initExtensionFormatMap();

    /** 字典服务 */
    @Resource
    private IDictService dictService;

    /**
     * 获取通用错误头
     * @param field 字段
     * @return 通用错误头
     */
    private static String getCommonErrorHead(Field field) {
        return String.format("类型:%s 的字段:%s 配置错误! ", field.getType().getSimpleName(), field.getName());
    }

    /**
     * 获取字段值
     * @param object 对象
     * @param field 字段值
     * @return 字段值, 输入对象为 null 时返回 null
     */
    private static Object getFieldValue(Object object, Field field) {
        return NullMergeUtils.nullMerge(object, x -> ReflectUtil.getFieldValue(x, field));
    }

    /**
     * 比较对象
     * @param beforeObject 修改前的对象
     * @param afterObject 修改后的对象
     * @param clazz 需比较的类实例
     * @return 比较结果
     */
    @Override
    public List<BaseModifyDiff<?>> compareObject(
             Object beforeObject,
             Object afterObject,
            @NonNull Class<?> clazz) {
        List<BaseModifyDiff<?>> modifyDiffList = this.compare(beforeObject, afterObject, clazz);
        if(ListUtil.isEmpty(modifyDiffList)) {
            return ListUtil.emptyList();
        }

        return modifyDiffList;
    }

    /**
     * 比较对象
     * @param beforeObject 修改前的对象
     * @param afterObject 修改后的对象
     * @param clazz 需比较的类实例
     * @return 比较结果
     */
    @SuppressWarnings("unchecked")
    private List<BaseModifyDiff<?>> compare(
             Object beforeObject,
             Object afterObject,
            @NotNull Class<?> clazz) {

        List<BaseModifyDiff<?>> modifyDiffList = new ArrayList<>();
        Field[] fieldArray = ReflectUtil.getFields(clazz);
        for(Field field : fieldArray) {
            CompareField compareField = findCompareFieldAnnotation(field);
            // 未标记的字段不在比较范围内
            if(compareField == null) { continue; }

            Object beforeSubModel = getFieldValue(beforeObject, field);
            Object afterSubModel = getFieldValue(afterObject, field);

            switch(compareField.fieldType()) {
                // 简单字段处理
                case Simple:
                    this.verifyDiffConfig(field);

                    BaseModifyDiff<?> modifyDiff = this.compareField(beforeSubModel, afterSubModel, field, compareField);
                    if(modifyDiff != null) {
                        modifyDiffList.add(modifyDiff);
                    }
                    break;
                // 列表子模型处理
                case ObjectList: {
                    Assert.isTrue(List.class.isAssignableFrom(field.getType()), getCommonErrorHead(field) + "配置为列表的字段类型必须为List");
                    List<BaseModifyDiff<?>> subModModifyDiffList = this.compareList((List<Object>) beforeSubModel, (List<Object>) afterSubModel, field, compareField);
                    modifyDiffList = this.mergeSubModelDiff(modifyDiffList, subModModifyDiffList, field);
                    break;
                }
                // 单子模型处理
                case Object: {
                    List<BaseModifyDiff<?>> subModModifyDiffList = this.compare(beforeSubModel, afterSubModel, field.getType());
                    modifyDiffList = this.mergeSubModelDiff(modifyDiffList, subModModifyDiffList, field);
                    break;
                }
                default:
                    throw new IllegalArgumentException(getCommonErrorHead(field) + "配置错误, 未知的字段类型" + compareField.fieldType());
            }
        }

        return modifyDiffList;
    }

    /**
     * 比较列表项
     * @param beforeList 比较前的列表
     * @param afterList 比较后的列表
     * @param field 需比较的字段
     * @return 比较结果
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private List<BaseModifyDiff<?>> compareList(List<Object> beforeList, List<Object> afterList, Field field, CompareField compareField) {
        if(ListUtil.isEmpty(beforeList) && ListUtil.isEmpty(afterList)) {
            return null;
        }

        //校验是否实现了ISerialNumber接口
//        boolean hasAssignableFrom = ISerialNumber.class.isAssignableFrom(compareField.typeArgument());
//        if(!hasAssignableFrom) {
//            throw new IllegalArgumentException("比较的类必须实现 ISerialNumber 接口");
//        }

        List<ISerialNumber> beforeNoList = (List) beforeList;
        List<ISerialNumber> afterNoList = (List) afterList;


        List<BaseModifyDiff<?>> modifyDiffList = new ArrayList<>();

        //获取两个List的交集
        List<ISerialNumber> intersectList = ListUtil.intersect(beforeNoList, afterNoList, ISerialNumber::getSerialNumber);
        // 比较交集部分
        for(ISerialNumber serialNumber : intersectList) {
            ISerialNumber before = ListUtil.first(beforeNoList, x -> x.getSerialNumber().equals(serialNumber.getSerialNumber()));
            ISerialNumber after = ListUtil.first(afterNoList, x -> x.getSerialNumber().equals(serialNumber.getSerialNumber()));
            List<BaseModifyDiff<?>> modifyDiffs = this.compare(before, after, compareField.typeArgument());
            modifyDiffList = this.mergeSubModelDiff(modifyDiffList, modifyDiffs, field,serialNumber.getSerialNumber());
        }

        // 获取两个List的差集
        Set<Integer> integerSet = new HashSet<>(ListUtil.map(intersectList, ISerialNumber::getSerialNumber));

        if(ListUtil.any(beforeNoList)){
            List<ISerialNumber> beforeDiffList = beforeNoList.stream()
                    .filter(item -> !integerSet.contains(item.getSerialNumber()))
                    .collect(Collectors.toList());
            if(ListUtil.any(beforeDiffList)) {
                List<BaseModifyDiff<?>> beforeModifyDiffList = beforeDiffList
                        .stream()
                        .flatMap(x -> {
                            List<BaseModifyDiff<?>> modifyDiffs = this.compare(x, null, compareField.typeArgument());
                            for (BaseModifyDiff<?> modifyDiff : modifyDiffs) {
                                modifyDiff.setSerialNumber(x.getSerialNumber());
                            }
                            return modifyDiffs.stream();
                        }).collect(Collectors.toList());
                modifyDiffList.addAll(beforeModifyDiffList);
            }
        }


        List<ISerialNumber> afterDiffList = afterNoList.stream()
                .filter(item -> integerSet.contains(item.getSerialNumber()) == false)
                .collect(Collectors.toList());
        if(ListUtil.any(afterDiffList)) {
            List<BaseModifyDiff<?>> afterModifyDiffList = afterDiffList
                    .stream()
                    .flatMap(x -> {
                        List<BaseModifyDiff<?>> modifyDiffs = this.compare(null, x, compareField.typeArgument());
                        for (BaseModifyDiff<?> modifyDiff : modifyDiffs) {
                            modifyDiff.setSerialNumber(x.getSerialNumber());
                        }
                        return modifyDiffs.stream();
                    }).collect(Collectors.toList());
            modifyDiffList.addAll(afterModifyDiffList);
        }


        return modifyDiffList;
    }

    /**
     * 合并子模型比较结果
     * @param mainModifyDiffList 主比较结果
     * @param subModModifyDiffList 子模型比较结果
     * @param field 子模型字段
     * @return 合并后的结果
     */
    private List<BaseModifyDiff<?>> mergeSubModelDiff(
            List<BaseModifyDiff<?>> mainModifyDiffList,
            List<BaseModifyDiff<?>> subModModifyDiffList,
            Field field) {
        if(ListUtil.isEmpty(subModModifyDiffList)) {
            return mainModifyDiffList;
        }

        CompareField compareField = this.findCompareFieldAnnotation(field);
        Assert.notNull(compareField, "字段对象比较标记注解不能为null");

        subModModifyDiffList.forEach(modifyDiff -> modifyDiff.addFieldPathPrefix(compareField.value()));
        return ListUtil.merge(mainModifyDiffList, subModModifyDiffList);
    }

    /**
     * 合并子模型比较结果
     * @param mainModifyDiffList 主比较结果
     * @param subModModifyDiffList 子模型比较结果
     * @param field 子模型字段
     * @return 合并后的结果
     */
    private List<BaseModifyDiff<?>> mergeSubModelDiff(
            List<BaseModifyDiff<?>> mainModifyDiffList,
            List<BaseModifyDiff<?>> subModModifyDiffList,
            Field field,Integer serialNumber) {
        if(ListUtil.isEmpty(subModModifyDiffList)) {
            return mainModifyDiffList;
        }

        CompareField compareField = this.findCompareFieldAnnotation(field);
        Assert.notNull(compareField, "字段对象比较标记注解不能为null");

        subModModifyDiffList.forEach(modifyDiff -> {
            modifyDiff.addFieldPathPrefix(compareField.value());
            modifyDiff.setSerialNumber(serialNumber);
        });
        return ListUtil.merge(mainModifyDiffList, subModModifyDiffList);
    }

    /**
     * 合并列表项模型比较结果
     * @param mainModifyDiffList 主比较结果
     * @param itemModifyDiffList 列表项模型比较结果
     * @param index 下标
     * @param field 子模型字段
     * @return 合并后的结果
     */
    private List<BaseModifyDiff<?>> mergeItemModelDiff(
            List<BaseModifyDiff<?>> mainModifyDiffList,
            List<BaseModifyDiff<?>> itemModifyDiffList,
            int index, Field field) {
        if(ListUtil.isEmpty(itemModifyDiffList)) {
            return mainModifyDiffList;
        }

        CompareField compareField = this.findCompareFieldAnnotation(field);
        Assert.notNull(compareField, "字段对象比较标记注解不能为null");

        itemModifyDiffList.forEach(modifyDiff -> modifyDiff.addFieldPathPrefix(String.valueOf(index)));
        return ListUtil.merge(mainModifyDiffList, itemModifyDiffList);
    }

    /**
     * 获取字段对象比较标记注解
     * @param field 字段
     * @return 字段对象比较标记注解
     */
    private CompareField findCompareFieldAnnotation(Field field) {
        return field.getAnnotation(CompareField.class);
    }

    /**
     * 验证标记比较的字段配置是否正确
     * @param field 比较字段
     */
    private void verifyDiffConfig(Field field) {
        if(!Comparable.class.isAssignableFrom(field.getType())
                && !this.inExtensionCompareType(field)) {
            throw new IllegalArgumentException(getCommonErrorHead(field) + "标记比较的字段其类型必须实现 Comparable<T> 接口");
        }
    }

    /**
     * 字段类型是否为扩展比较类型
     * @param field 字段
     * @return true: 是扩展比较类型
     */
    private boolean inExtensionCompareType(Field field) {
        return this.extensionCompareMap.containsKey(field.getType());
    }

    /**
     * 比较字段
     * @param beforeFieldValue 修改前的对象
     * @param afterFieldValue 修改后的对象
     * @param field 需比较的字段
     * @param compareField 比较字段标记注解
     * @param <T> 需比较的类型
     * @return 比较结果
     */
    private <T> BaseModifyDiff<?> compareField(T beforeFieldValue, T afterFieldValue, Field field, CompareField compareField) {
        boolean beforeEmpty = this.isEmpty(beforeFieldValue);
        boolean afterEmpty = this.isEmpty(afterFieldValue);

        // 处理修改前后皆为空的情况
        if(beforeEmpty && afterEmpty) {
            return null;
            // 处理修改前后仅一方为空的情况
        } else if(beforeEmpty || afterEmpty) {
            return this.createModifyDiff(beforeFieldValue, afterFieldValue, field, compareField);
        }

        if(this.isModify(beforeFieldValue, afterFieldValue, field)) {
            return this.createModifyDiff(beforeFieldValue, afterFieldValue, field, compareField);
        } else {
            return null;
        }
    }

    /**
     * 值是否修改
     * @param beforeFieldValue 修改前的值
     * @param afterFieldValue 修改后的值
     * @return true: 已修改
     */
    private boolean isModify(Object beforeFieldValue, Object afterFieldValue, Field field) {
        if(Comparable.class.isAssignableFrom(field.getType())) {
            //noinspection unchecked,rawtypes
            return ((Comparable) beforeFieldValue).compareTo(afterFieldValue) != 0;
        } else {
            Func3<Object, Object, Field, Boolean> extensionCompare = this.extensionCompareMap.get(field.getType());
            return !extensionCompare.call(beforeFieldValue, afterFieldValue, field);
        }
    }

    /**
     * 创建比较结果 - 对象
     * @param beforeFieldValue 修改前的值
     * @param afterFieldValue 修改后的值
     * @param field 需比较的字段
     * @param compareField 比较字段标记注解
     * @return 比较结果
     */
    private BaseModifyDiff<?> createModifyDiff(Object beforeFieldValue, Object afterFieldValue, Field field, CompareField compareField) {
        switch(compareField.modifyContextType()) {
            case STRING:
                BaseModifyDiff.StringModifyDiff stringModifyDiff = new BaseModifyDiff.StringModifyDiff(field.getName(), compareField.value());
                stringModifyDiff.setBefore(this.toString(beforeFieldValue, field));
                stringModifyDiff.setAfter(this.toString(afterFieldValue, field));
                // 类型为字典值时
                if(StringUtils.isNotBlank(compareField.dictName())) {
                    if(compareField.isListDict()){
                        this.dictService.relatedDictList(stringModifyDiff, compareField.dictName(), item -> StringUtils.convertStringList(item.getAfter()),
                                (modifyDiff, dictList) -> modifyDiff.setAfter(StringUtils.convertStringList(dictList, SysDictData::getDictLabel, compareField.delimiter())));
                        this.dictService.relatedDictList(stringModifyDiff, compareField.dictName(), item -> StringUtils.convertStringList(item.getBefore()),
                                (modifyDiff, dictList) -> modifyDiff.setBefore(StringUtils.convertStringList(dictList, SysDictData::getDictLabel, compareField.delimiter())));
                    } else {
                        this.dictService.relatedDict(stringModifyDiff, compareField.dictName(), BaseModifyDiff::getAfter,
                                (modifyDiff, dict) -> modifyDiff.setAfter(dict.getDictLabel()));
                        this.dictService.relatedDict(stringModifyDiff, compareField.dictName(), BaseModifyDiff::getBefore,
                                (modifyDiff, dict) -> modifyDiff.setBefore(dict.getDictLabel()));
                    }
                    // 已配置自定义内容处理器时
                }
                if(compareField.customContextHandler() != ICompareContextHandler.Void.class) {
                    ICompareContextHandler contextHandler = this.getCompareContextHandler(compareField.customContextHandler());
                    stringModifyDiff.setBefore(contextHandler.handle(beforeFieldValue, field));
                    stringModifyDiff.setAfter(contextHandler.handle(afterFieldValue, field));
                }
                return stringModifyDiff;
            case OSS_File:
                BaseModifyDiff.OssFileModifyDiff ossFileModifyDiff = new BaseModifyDiff.OssFileModifyDiff(field.getName(), compareField.value());
                ossFileModifyDiff.setModifyContext(beforeFieldValue, afterFieldValue);
                return ossFileModifyDiff;
            case LIST_OSS_FILE:
                BaseModifyDiff.ListOssFileModifyDiff listOssFileModifyDiff = new BaseModifyDiff.ListOssFileModifyDiff(field.getName(), compareField.value());
                listOssFileModifyDiff.setModifyContext(beforeFieldValue, afterFieldValue);
                return listOssFileModifyDiff;
            default:
                throw new UnsupportedOperationException(String.format("枚举: %s对应操作未实现", compareField.modifyContextType()));
        }
    }

    /**
     * 格式化值
     * @param fieldValue 字段值
     * @param field 需格式化的字段信息
     * @return 格式化结果
     */
    private String toString(Object fieldValue, Field field) {
        if(this.extensionFormatMap.containsKey(field.getType())) {
            Func2<Object, Field, String> extensionFormat = this.extensionFormatMap.get(field.getType());
            return extensionFormat.call(fieldValue, field);
        }

        return NullMergeUtils.nullMerge(fieldValue, value -> value.toString());
    }

    /**
     * 获取指定类型的自定义比较内容处理器
     * @param contextHandlerClass 自定义比较内容处理器类型
     * @return 自定义比较内容处理器
     */
    private ICompareContextHandler getCompareContextHandler(Class<? extends ICompareContextHandler> contextHandlerClass) {
        ICompareContextHandler contextHandler = SpringUtil.getBean(contextHandlerClass);
        Assert.notNull(contextHandler, String.format("自定义比较内容处理器: %s 在容器中不存在, 请确认是否配置依赖注入注解", contextHandlerClass.getName()));
        return contextHandler;
    }

    /**
     * 初始化扩展比较Map
     * @return 扩展比较Map
     */
    private Map<Class<?>, Func3<Object, Object, Field, Boolean>> initExtensionCompareMap() {
        Map<Class<?>, Func3<Object, Object, Field, Boolean>> extensionCompareMap = Maps.newHashMap();
        extensionCompareMap.put(List.class, this::listCompare);

        return extensionCompareMap;
    }

    /**
     * 列表比较方法
     * @param a A
     * @param b B
     * @param field 比较字段
     * @return true: A/B 值相等
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    private Boolean listCompare(Object a, Object b, Field field) {
        List<Object> listA = (List<Object>) a;
        List<Object> listB = (List<Object>) b;

        boolean aIsEmpty = ListUtil.isEmpty(listA);
        boolean bIsEmpty = ListUtil.isEmpty(listB);
        if(aIsEmpty && bIsEmpty) {
            return true;
        } else if(aIsEmpty || bIsEmpty) {
            return false;
        } else if(listA.size() != listB.size()) {
            return false;
        }

        Class<?> typeArgument = ListUtil.firstOrThrow(listA).getClass();
        if(!Comparable.class.isAssignableFrom(typeArgument)) {
            throw new IllegalArgumentException(getCommonErrorHead(field) + "标记比较的字段其类型必须实现 Comparable<T> 接口");
        }

        for(int i = 0; i < listA.size(); i++) {
            Object itemA = listA.get(i);
            Object itemB = listB.get(i);
            if(((Comparable) itemA).compareTo(itemB) != 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 初始化扩展格式化Map
     * @return 扩展格式化Map
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    private Map<Class<?>, Func2<Object, Field, String>> initExtensionFormatMap() {
        Map<Class<?>, Func2<Object, Field, String>> extensionFormatMap = new HashMap<>(16);
        extensionFormatMap.put(List.class, (list, field) -> {
            if(list == null) {
                return StringUtils.EMPTY;
            }
            List<String> stringList = ListUtil.map((List) list, Object::toString);
            return String.join(",", stringList);
        });

        return extensionFormatMap;
    }

    /**
     * 对象内容是否为空
     * @param value 需判空的对戏
     * @return true: 对象内容为空
     */
    private boolean isEmpty(Object value) {
        if(value == null) {
            return true;
        }

        if(value instanceof Collection) {
            return ListUtil.isEmpty((Collection<?>) value);
        }

        if(value instanceof String) {
            return StringUtils.isBlank((String) value);
        }

        if(value instanceof Map) {
            return ListUtil.isEmpty((Map<?, ?>) value);
        }

        if(value.getClass().isArray()) {
            return ListUtil.isEmpty((Object[]) value);
        }

        return false;
    }
}
