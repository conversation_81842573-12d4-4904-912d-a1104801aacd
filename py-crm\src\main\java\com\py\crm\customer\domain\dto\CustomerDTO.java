package com.py.crm.customer.domain.dto;

import com.py.common.file.FileAnnex;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.customer.contact.domain.dto.ContactDTO;
import com.py.crm.customer.cooperatemainstay.domain.dto.CooperateMainstayDTO;
import com.py.crm.customer.customeraccount.domain.dto.CustomerAccountDTO;
import com.py.crm.customer.customeraddress.domain.dto.CustomerAddressDTO;
import com.py.crm.customer.customervisit.domian.dto.CustomerVisitDTO;
import com.py.crm.customer.invoicing.domain.dto.InvoicingDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 客户管理-客户数据传输模型
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Data
@ApiModel("客户管理-客户数据传输模型" )
public class CustomerDTO {
    private static final long serialVersionUID = 1L;

    /** 附件地址 {filename  ossKy}*/
    @ApiModelProperty("附件地址 {filename  ossKy}" )
    private List<FileAnnex> annex;

    /** 上次的审批ID */
    @ApiModelProperty("上次的审批ID" )
    private Long lastFlowInstanceId;

    /** 审批状态*/
    @ApiModelProperty("审批状态" )
    private Integer auditStatus;

    /** 报价策略(0.资源差价模式 1.拆分模式（策略费+服务费+资源返点）2.资源差价+拆分模式)*/
    @ApiModelProperty("报价策略(0.资源差价模式 1.拆分模式（策略费+服务费+资源返点）2.资源差价+拆分模式)" )
    private Integer biddingStrategy;

    /** 品牌阶段(0.初创品牌 1.成长期品牌 2.成熟品牌)*/
    @ApiModelProperty("品牌阶段(0.初创品牌 1.成长期品牌 2.成熟品牌)" )
    private Integer brandStage;

    /** 品牌核心生意来源(0.全渠道销售（线下强，线上辅助）1.全渠道销售（线上强，线下辅助）2. 纯线上渠道)*/
    @ApiModelProperty("品牌核心生意来源(0.全渠道销售（线下强，线上辅助）1.全渠道销售（线上强，线下辅助）2. 纯线上渠道)" )
    private Integer businessSource;

    /** 状态变更原因*/
    @ApiModelProperty("状态变更原因" )
    private String changeStatusReason;

    /** 合作状态(0.合作中 1.暂停合作 2.意向合作)*/
    @ApiModelProperty("合作状态(0.合作中 1.暂停合作 2.意向合作)" )
    @NotNull(message = "合作状态不可为空")
    private Integer cooperationStatus;

    /** 合作部门*/
    @ApiModelProperty("合作部门" )
    @Size(max = 100,message = "合作部门最大100字")
    private String cooperativeSector;


    /** 客户id*/
    @ApiModelProperty("客户id" )
    private Long customerId;

    /** 客户来源(0.老客推荐 1.熟人介绍 2.主动BD 3.客户直联)*/
    @ApiModelProperty("客户来源(0.老客推荐 1.熟人介绍 2.主动BD 3.客户直联)" )
    private Integer customerSource;

    /** 客户类型(0.品牌客户 1.平台客户 2.集团客户 3.渠道客户)*/
    @ApiModelProperty("客户类型(0.品牌客户 1.平台客户 2.集团客户 3.渠道客户)" )
    private Integer customerType;

    /** 决策链路*/
    @ApiModelProperty("决策链路" )
    @Size(max = 1000,message = "决策链路最大一千字")
    private String decisionLink;


    /** 行业类目id*/
    @ApiModelProperty("行业类目id" )
    @NotEmpty(message = "行业类目不可为空")
    private List<Long> industryCategoryIdList;

    /** 品牌/业务线*/
    @ApiModelProperty("品牌/业务线" )
    @NotBlank(message = "品牌/业务线不可为空")
    @Size(max = 100,message = "品牌/业务线最大100字")
    private String lineBusiness;

    /** 客户名称*/
    @ApiModelProperty("客户名称" )
    @NotBlank(message = "客户名称不可为空")
    @Size(max = 100,message = "客户名称最大100字")
    private String name;

    /** 备注*/
    @ApiModelProperty("备注" )
    @Size(max = 1000,message = "备注最大一千字")
    private String remark;

    /** 派芽服务产品(0.内容营销全链路服务 1.整合营销服务 2.内容营销策略及执行服务 3.媒介投放服务 4.MCN服务)*/
    @ApiModelProperty("派芽服务产品(0.内容营销全链路服务 1.整合营销服务 2.内容营销策略及执行服务 3.媒介投放服务 4.MCN服务)" )
    private List<String> serviceProduct;

    /** 目标服务人员id*/
    @ApiModelProperty("目标服务人员id" )
    private Long serviceUserId;

    /** 目标服务人员id数组 */
    @ApiModelProperty("目标服务人员id数组" )
    private List<Long> serviceUserIdList;

    /** 流转原因 */
    @ApiModelProperty("流转原因" )
    private String circulationReason;

    /** 版本*/
    @ApiModelProperty("版本" )
    private Integer version;

    /** 客户管理-客户-企业联系人数据传输模型 */
    @ApiModelProperty("客户管理-客户-企业联系人数据传输模型")
    @Valid
    private List<ContactDTO> contactDTOList;

    /** 客户管理-客户-合作主体数据传输模型 */
    @ApiModelProperty("客户管理-客户-合作主体数据传输模型")
    @Valid
    private List<CooperateMainstayDTO> cooperateMainstayDTOList;

    /** 客户管理-客户-客户地址数据传输模型 */
    @ApiModelProperty("客户管理-客户-客户地址数据传输模型")
    @Valid
    private List<CustomerAddressDTO> customerAddressDTOList;

    /** 客户管理-客户-账户信息数据传输模型 */
    @ApiModelProperty("客户管理-客户-账户信息数据传输模型")
    @Valid
    private List<CustomerAccountDTO> customerAccountDTOList;

    /** 客户管理-客户-开票信息数据传输模型 */
    @ApiModelProperty("客户管理-客户-开票信息数据传输模型")
    @Valid
    private List<InvoicingDTO> invoicingDTOList;

    @ApiModelProperty("客户管理-客户-客户拜访日志数据传输模型")
    @Valid
    private List<CustomerVisitDTO> customerVisitDTOList;

    /** 流转是客户页面 */
    @ApiModelProperty(hidden = true)
    public Boolean isCustomer;

    /** 是否是审批驳回修改 */
    @ApiModelProperty(hidden = true)
    public Boolean isApprovalEdit = false;

    public List<CooperateMainstayDTO> getCooperateMainstayDTOList() {
        if(ListUtil.isEmpty(cooperateMainstayDTOList)){
            return ListUtil.emptyList();
        }
        cooperateMainstayDTOList.removeIf(CooperateMainstayDTO::isEmpty);
        return cooperateMainstayDTOList;
    }

    public List<CustomerAddressDTO> getCustomerAddressDTOList() {
        if(ListUtil.isEmpty(customerAddressDTOList)){
            return ListUtil.emptyList();
        }
        customerAddressDTOList.removeIf(CustomerAddressDTO::isEmpty);
        return customerAddressDTOList;
    }

    public List<CustomerAccountDTO> getCustomerAccountDTOList() {
        if(ListUtil.isEmpty(customerAccountDTOList)){
            return ListUtil.emptyList();
        }
        customerAccountDTOList.removeIf(CustomerAccountDTO::isEmpty);
        return customerAccountDTOList;
    }

    public List<InvoicingDTO> getInvoicingDTOList() {
        if(ListUtil.isEmpty(invoicingDTOList)){
            return ListUtil.emptyList();
        }
        invoicingDTOList.removeIf(InvoicingDTO::isEmpty);
        return invoicingDTOList;
    }
}
