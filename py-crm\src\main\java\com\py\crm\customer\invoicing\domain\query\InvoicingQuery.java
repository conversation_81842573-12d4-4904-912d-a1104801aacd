package com.py.crm.customer.invoicing.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户管理-客户-开票信息查询对象
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Data
@ApiModel("客户管理-客户-开票信息查询对象" )
public class InvoicingQuery {
    private static final long serialVersionUID = 1L;

    /** 银行账号*/
    @ApiModelProperty("银行账号" )
    private String bankAccount;

    /** 开户银行*/
    @ApiModelProperty("开户银行" )
    private String bankDeposit;

    /** 公司地址*/
    @ApiModelProperty("公司地址" )
    private String companyAddress;

    /** 创建者*/
    @ApiModelProperty("创建者" )
    private String createBy;

    /** 创建人部门*/
    @ApiModelProperty("创建人部门" )
    private String createDept;

    /** 创建者Id*/
    @ApiModelProperty("创建者Id" )
    private Long createId;

    /** 创建时间*/
    @ApiModelProperty("创建时间" )
    private LocalDateTime createTime;

    /** 客户id*/
    @ApiModelProperty("客户id" )
    private Long customerId;

    /** 删除标志*/
    @ApiModelProperty("删除标志" )
    private String delFlag;

    /** 税号*/
    @ApiModelProperty("税号" )
    private String dutyParagraph;

    /** 自增id*/
    @ApiModelProperty("自增id" )
    private Long id;

    /** 开票id*/
    @ApiModelProperty("开票id" )
    private Long invoicingId;

    /** 开票名称*/
    @ApiModelProperty("开票名称" )
    private String invoicingName;

    /** 电话*/
    @ApiModelProperty("电话" )
    private String phone;

    /** 更新者*/
    @ApiModelProperty("更新者" )
    private String updateBy;

    /** 更新人部门*/
    @ApiModelProperty("更新人部门" )
    private String updateDept;

    /** 更新者Id*/
    @ApiModelProperty("更新者Id" )
    private Long updateId;

    /** 更新时间*/
    @ApiModelProperty("更新时间" )
    private LocalDateTime updateTime;


}
