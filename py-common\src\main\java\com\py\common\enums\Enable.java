package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 是否启用
 * <AUTHOR>
 */
@AllArgsConstructor
public enum Enable implements IDict<Integer> {

    /** 启用 */
    ENABLE(0, "启用"),
    /** 停用 */
    DISABLE(1, "停用");

    private final Integer value;

    private final String label;


    @Override
    public Integer getValue() {
        return this.value;
    }

    @Override
    public String getLabel() {
        return this.label;
    }

}
