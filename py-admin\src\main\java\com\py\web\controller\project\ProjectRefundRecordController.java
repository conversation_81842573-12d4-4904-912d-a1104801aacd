package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.exception.ServiceException;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.project.projectpaymentrecord.domain.vo.MainstayTotalCountVO;
import com.py.project.projectrefundrecord.domain.dto.ProjectRefundRecordDTO;
import com.py.project.projectrefundrecord.domain.dto.ProjectRefundRecordExportModel;
import com.py.project.projectrefundrecord.domain.dto.ProjectRefundRecordListDTO;
import com.py.project.projectrefundrecord.domain.query.ProjectRefundRecordAuditQuery;
import com.py.project.projectrefundrecord.domain.query.ProjectRefundRecordQuery;
import com.py.project.projectrefundrecord.domain.vo.ProjectRefundAuditListVO;
import com.py.project.projectrefundrecord.domain.vo.ProjectRefundRecordListVO;
import com.py.project.projectrefundrecord.service.IProjectRefundRecordService;
import com.py.project.projectrefundrecord.service.impl.ProjectRefundRecordServiceImpl;
import com.py.project.projectrefundrecord.service.impl.RefundAuditServiceImpl;
import com.py.project.projectresource.domain.vo.ProjectRefundInfoVO;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目管理-退款记录Controller
 *
 * <AUTHOR>
 * @date 2023-08-01
 */
@Api(tags = "项目管理-退款记录")
@RestController
@RequestMapping("/project/projectRefundRecord")
public class ProjectRefundRecordController extends BaseController {

    /**
     * 项目管理-退款记录服务
     */
    @Resource
    private IProjectRefundRecordService projectRefundRecordService;


    /**
     * 可视化异步任务服务
     */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 系统设置-主体参数设置服务 */
    @Resource
    private ISystemMainstayParamService systemMainstayParamService;


    /**
     * 查询项目管理-退款记录列表
     *
     * @param query 项目管理-退款记录查询参数
     * @return 项目管理-退款记录列表
     */
    @ApiOperation("查询项目管理-退款记录列表")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:list')")
    @GetMapping("/listProjectRefundRecord")
    public R<List<ProjectRefundRecordListVO>> listProjectRefundRecord(ProjectRefundRecordQuery query) {
        List<ProjectRefundRecordListVO> voList = this.projectRefundRecordService.listProjectRefundRecord(query);
        return R.success(voList);
    }

    /**
     * 分页查询项目管理-退款记录列表
     *
     * @param query 项目管理-退款记录查询参数
     * @return 项目管理-退款记录分页
     */
    @ApiOperation("分页查询询项目管理-退款记录列表")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:list')")
    @GetMapping("/pageProjectRefundRecord")
    public R<PageInfo<ProjectRefundRecordListVO>> pageProjectRefundRecord(ProjectRefundRecordQuery query) {
        PageInfo<ProjectRefundRecordListVO> voList = this.projectRefundRecordService.pageProjectRefundRecordList(query);
        return R.success(voList);
    }

    /**
     * 获取项目管理-退款记录详细信息
     *
     * @param id 项目管理-每次记录id
     * @return 项目管理-退款记录视图模型
     */
    @ApiOperation("获取项目管理-退款记录详细信息")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:query')")
    @GetMapping(value = "/{id}")
    public R<ProjectRefundInfoVO> getInfo(@PathVariable("id") Long id) {
        return R.success(projectRefundRecordService.selectProjectRefundRecordById(id));
    }

    /**
     * 新增项目管理-退款记录
     *
     * @param dto 项目管理-退款记录修改参数
     * @return 是否成功
     */
    @ApiOperation("新增项目管理-退款记录")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:add')")
    @Log(title = "项目管理-退款记录", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Long> add(@RequestBody ProjectRefundRecordListDTO dto) {
        if(ListUtil.isEmpty(dto.getDtoList())){
            throw new ServiceException("请选择退款资源");
        }
        return R.success(projectRefundRecordService.insertProjectRefundRecord(dto));
    }

    /**
     * 审批退回/撤回退款信息
     *
     * @param dto 项目管理-退款记录修改参数
     * @return 是否成功
     */
    @ApiOperation("审批退回/撤回退款信息")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:add')")
    @Log(title = "项目管理-退款记录", businessType = BusinessType.INSERT)
    @PostMapping("/approveRefundRecord")
    public R<Long> approveRefundRecord(@RequestBody ProjectRefundRecordListDTO dto) {
        return R.success(projectRefundRecordService.approveRefundRecord(dto));
    }

    /**
     * 删除项目管理-退款记录
     *
     * @param ids 需要删除的项目管理-退款记录主键集合
     * @return 是否成功
     */
    @ApiOperation("删除项目管理-退款记录")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:remove')")
    @Log(title = "项目管理-退款记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(projectRefundRecordService.deleteProjectRefundRecordByIds(ids));
    }

    /**
     * 导出项目管理-退款记录
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目管理-退款记录")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:export')")
    @Log(title = "项目管理-退款记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@RequestBody ProjectRefundRecordQuery query) {

        reusableAsyncTaskService.addTask("资源退款执行表单下载", TaskType.Export, query, ProjectRefundRecordServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 导入
     *
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:import')")
    @Log(title = "项目管理-退款记录", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<ProjectRefundRecordExportModel> util = new ExcelUtil<>(ProjectRefundRecordExportModel.class);
        List<ProjectRefundRecordExportModel> projectRefundRecordList = util.importExcel(file.getInputStream());
        String message = this.projectRefundRecordService.importProjectRefundRecord(projectRefundRecordList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     *
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:import')")
    @Log(title = "项目管理-退款记录", businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ProjectRefundRecordExportModel> util = new ExcelUtil<>(ProjectRefundRecordExportModel.class);
        util.importTemplateExcel(response, "项目管理-退款记录数据");
    }

    /**
     * 分页查询退款审批列表
     *
     * @param query 项目管理-退款记录查询参数
     * @return 项目管理-退款记录分页
     */
    @ApiOperation("分页查询退款审批列表")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:list')")
    @PostMapping("/pageProjectRefundRecordAudit")
    public R<PageInfo<ProjectRefundAuditListVO>> pageProjectRefundRecordAudit(@RequestBody ProjectRefundRecordAuditQuery query) {
        PageInfo<ProjectRefundAuditListVO> voList = this.projectRefundRecordService.pageProjectRefundRecordAudit(query);
        return R.success(voList);
    }

    /**
     * 导出退款审批列表
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出退款审批列表")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:export')")
    @Log(title = "项目管理-退款记录", businessType = BusinessType.EXPORT)
    @PostMapping("/exportRefundAudit")
    public R<String> exportRefundAudit(@Validated @RequestBody ProjectRefundRecordAuditQuery query) {
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPaiyaMainstayId());
        String fileName = "资源退款执行表单下载-"+systemMainstayParamVO.getMainstayName()+"-"+ DateUtils.format(LocalDateTime.now(),"yyyy-MM-dd-HH：mm：ss")+".xlsx";
        query.setFileName(fileName);
        reusableAsyncTaskService.addTask("退款审批查询下载", TaskType.Export, query, RefundAuditServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 本次退款金额合计
     *
     * @param query 查询条件
     * @return 合计
     */
    @ApiOperation("本次退款金额合计")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:list')")
    @PostMapping("/currentRefundAmountCount")
    public R<String> currentRefundAmountCount(@RequestBody ProjectRefundRecordAuditQuery query) {
        return R.success("操作成功",projectRefundRecordService.currentRefundAmountCount(query));
    }

    /**
     * 退款查询项目主体统计
     * @param query 查询参数
     * @return 项目主体统计
     */
    @ApiOperation("退款查询项目主体统计")
    @PreAuthorize("@ss.hasPermi('project:projectRefundRecord:list')")
    @PostMapping("/refundTotalCount")
    public R<List<MainstayTotalCountVO>> refundTotalCount(@Validated ProjectRefundRecordAuditQuery query) {
        return R.success(this.projectRefundRecordService.refundTotalCount(query));
    }

    /**
     * 查询退款审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询退款审批列表上的创建部门下拉" )
    @GetMapping("/listProjectRefundRecordApprovalDept" )
    public R<List<String>> listProjectRefundRecordApprovalDept(ProjectRefundRecordAuditQuery query){
        return R.success(projectRefundRecordService.listProjectRefundRecordApprovalDept(query));
    }
}
