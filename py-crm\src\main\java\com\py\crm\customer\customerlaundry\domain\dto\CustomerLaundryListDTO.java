package com.py.crm.customer.customerlaundry.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.datascope.IDataScopeArgs;
import com.py.crm.customer.domain.query.CustomerQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户加入清单
 * <AUTHOR>
 * @version CustomerLaundryListDTO 2023/9/6 16:29
 */
@Data
public class CustomerLaundryListDTO implements IDataScopeArgs {

    /** 是否全选，true 是 */
    @ApiModelProperty("是否全选，true 是")
    @NotNull(message = "请选择是否全选")
    private Boolean isAll;

    /** 业务id*/
    @ApiModelProperty("业务id")
    private List<Long> bizIdList;

    /** 客户查询条件 */
    @ApiModelProperty("客户查询条件")
    private CustomerQuery query;

    /** 合作状态(0.合作中 1.暂停合作 2.意向合作)*/
    @ApiModelProperty("合作状态(0.合作中 1.暂停合作 2.意向合作)" )
    private Integer cooperationStatus;

    /** 更新者Id */
    private Long updateId;


    /** 更新者Id */
    private Long createId;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 数据权限 */
    @ApiModelProperty(hidden = true)
    private String dataScopeSql;

    /** 客户id*/
    @ApiModelProperty("客户id" )
    private List<Long> customerIdList;
}
