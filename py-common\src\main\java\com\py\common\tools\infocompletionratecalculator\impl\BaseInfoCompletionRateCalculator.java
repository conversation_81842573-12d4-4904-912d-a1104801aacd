package com.py.common.tools.infocompletionratecalculator.impl;

import cn.hutool.core.util.ReflectUtil;
import com.py.common.tools.infocompletionratecalculator.IInfoCompletionRateCalculator;
import com.py.common.tools.infocompletionratecalculator.annotation.CalculatedField;
import com.py.common.tools.infocompletionratecalculator.functional.IsSetPredicateGenerator;
import com.py.common.tools.infocompletionratecalculator.metadata.InfoCompletionRateMetadata;
import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import org.springframework.util.Assert;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 基础信息完整度计算器
 * <AUTHOR>
 */
public abstract class BaseInfoCompletionRateCalculator<T> implements IInfoCompletionRateCalculator<T> {

    /** 否填写判断函数生成Map */
    protected final Map<Class<?>, IsSetPredicateGenerator<T>> setPredicateGeneratorMap;

    /** 计算信息完整度的目标类型 */
    protected final Class<T> clazz;

    public BaseInfoCompletionRateCalculator(Class<T> clazz) {
        this.clazz = clazz;
        this.setPredicateGeneratorMap = this.initGeneratorMap();
    }

    /**
     * 计算SPU信息完整度
     * @param target 需计算信息完整度的对象
     * @return SPU的信息完整度
     */
    @Override
    public abstract BigDecimal calculation(T target);

    /**
     * 实现类型支持的类型
     * @return 支持的类型
     */
    @Override
    public Class<T> supportedClass() {
        return this.clazz;
    }

    /**
     * 初始化函数生成Map
     * @return 函数生成Map
     */
    private Map<Class<?>, IsSetPredicateGenerator<T>> initGeneratorMap() {
        IsSetPredicateGenerator<T> defaultSetPredicateGenerator = fieldValueGetter -> obj -> fieldValueGetter.apply(obj) != null;

        Map<Class<?>, IsSetPredicateGenerator<T>> generatorMap = new HashMap<>();
        generatorMap.put(Integer.class, defaultSetPredicateGenerator);
        generatorMap.put(Double.class, defaultSetPredicateGenerator);
        generatorMap.put(Long.class, defaultSetPredicateGenerator);
        generatorMap.put(BigDecimal.class, defaultSetPredicateGenerator);
        generatorMap.put(Boolean.class, defaultSetPredicateGenerator);
        generatorMap.put(LocalDate.class, defaultSetPredicateGenerator);
        generatorMap.put(LocalDateTime.class, defaultSetPredicateGenerator);
        generatorMap.put(LocalTime.class, defaultSetPredicateGenerator);
        generatorMap.put(Date.class, defaultSetPredicateGenerator);
        generatorMap.put(Enum.class, defaultSetPredicateGenerator);
        generatorMap.put(String.class, fieldValueGetter -> item -> StringUtils.isNotBlank((String) fieldValueGetter.apply(item)));
        generatorMap.put(List.class, fieldValueGetter -> item -> ListUtil.any((List<?>) fieldValueGetter.apply(item)));

        return generatorMap;
    }

    /**
     * 解析SPU[通用]信息完整度计算元数据
     * @param clazz 计算的类型
     * @return SPU通用信息完整度计算元数据
     */
    protected InfoCompletionRateMetadata<T> analyzingClassMetadata(Class<T> clazz) {
        InfoCompletionRateMetadata<T> infoCompletionRateMetadata = new InfoCompletionRateMetadata<>();

        Field[] fieldArray = ReflectUtil.getFields(clazz);
        for(Field field : fieldArray) {
            // 设置字段可访问性
            field.setAccessible(true);

            CalculatedField calculatedField = field.getAnnotation(CalculatedField.class);
            // 未标记的字段不在统计范围内
            if(calculatedField == null) { continue; }

            if(calculatedField.isSubModel()) {
                InfoCompletionRateMetadata<T> subModelMetadata = this.analyzingSubMetadata(field, mainObj -> ReflectUtil.getFieldValue(mainObj, field));
                infoCompletionRateMetadata.mergeSubModelMetadata(subModelMetadata);
            } else {
                Assert.isTrue(this.isBaseType(field.getType()), String.format("逻辑错误: 字段 %s 类型为 %s, 不是基础类型又没有标识子模型无法处理", field.getName(), field.getType().getSimpleName()));
                Predicate<T> isSetPredicate = this.getBaseFieldIsSetPredicate(field, x -> ReflectUtil.getFieldValue(x, field));
                infoCompletionRateMetadata.addIsSetPredicate(calculatedField.required(), isSetPredicate);
            }
        }
        return infoCompletionRateMetadata;
    }

    /**
     * 解析[子模型]信息完整度计算元数据
     * @param subModelField 子模型字段
     * @return 关键属性信息完整度计算元数据
     */
    private InfoCompletionRateMetadata<T> analyzingSubMetadata(Field subModelField, Function<T, Object> subModelGetter) {
        InfoCompletionRateMetadata<T> subMetadata = new InfoCompletionRateMetadata<>();

        // 遍历子模型字段, 解析swagger注解, 生成子模型信息完整度计算元数据
        Field[] fieldArray = ReflectUtil.getFields(subModelField.getType());
        for(Field field : fieldArray) {
            // 设置字段可访问性
            field.setAccessible(true);

            CalculatedField calculatedField = field.getAnnotation(CalculatedField.class);
            // 未标记的字段不在统计范围内
            if(calculatedField == null) { continue; }
            // 校验标记字段是否为基础类型
            if(this.isBaseType(field.getType()) == false) {
                String errorMsg = String.format("逻辑错误: 子模型字段 %s 类型为 %s, 其字段 %s 不是基础类型! 当前不支持三重嵌套字段",
                        subModelField.getName(), subModelField.getType().getSimpleName(), field.getName());
                throw new IllegalArgumentException(errorMsg);
            }

            Predicate<T> isSetPredicate = this.getBaseFieldIsSetPredicate(field, mainObj -> {
                Object subModel = subModelGetter.apply(mainObj);
                return NullMergeUtils.nullMerge(subModel, x -> ReflectUtil.getFieldValue(x, field));
            });

            subMetadata.addIsSetPredicate(calculatedField.required(), isSetPredicate);
        }
        return subMetadata;
    }

    /**
     * 该类型是否为信息完整度计算的基础类型
     * @param type 需要判断的类型
     * @return true: 是基础类型
     */
    private boolean isBaseType(Class<?> type) {
        return type.isEnum() || this.setPredicateGeneratorMap.containsKey(type);
    }

    /**
     * 获取基础字段是否填写判断函数
     * @param field 基础字段
     * @param fieldValueGetter 字段值获取方法
     * @return 字段是否必填判断函数
     */
    protected Predicate<T> getBaseFieldIsSetPredicate(Field field, Function<T, Object> fieldValueGetter) {
        Assert.notNull(field, "字段不能为空");
        Assert.notNull(fieldValueGetter, "字段值获取方法不能为空");

        if(field.getType().isEnum()) {
            return this.setPredicateGeneratorMap.get(Enum.class).generate(fieldValueGetter);
        }

        IsSetPredicateGenerator<T> isSetPredicateGenerator = this.setPredicateGeneratorMap.get(field.getType());
        Assert.notNull(isSetPredicateGenerator, String.format("逻辑异常: 字段 %s 类型为 %s 无法获取对应的否填写判断函数生成器", field.getName(), field.getType().getSimpleName()));
        return isSetPredicateGenerator.generate(fieldValueGetter);
    }
}
