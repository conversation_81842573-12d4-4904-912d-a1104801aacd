package com.py.web.controller.project;


import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.exception.ServiceException;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.utils.collection.ListUtil;
import com.py.project.projectresource.batchupdate.domain.dto.BatchUpdateImportModel;
import com.py.project.projectresource.batchupdate.domain.dto.BatchUpdateImportQueryModel;
import com.py.project.projectresource.batchupdate.util.ResourceBatchUpdateSearchImporter;
import com.py.project.projectresource.comfirmation.domain.query.BatchConfirmationQuery;
import com.py.project.projectresource.comfirmation.domain.vo.BatchConfirmationVO;
import com.py.project.projectresource.comfirmation.domain.vo.ResourceConfirmCountVO;
import com.py.project.projectresource.comfirmation.excel.search.model.ImportResourceSelectModel;
import com.py.project.projectresource.comfirmation.excel.search.model.ImportResourceSelectVO;
import com.py.project.projectresource.comfirmation.service.IProjectResourceConfirmationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 项目资源批量修改controller
 *
 * <AUTHOR>
@Api(tags = "项目资源批量修改controller")
@RestController
@RequestMapping("/project/resource/batch/update")
public class ProjectResourceBatchUpdateController {

    /**
     * 项目资源批量修改收入服务
     */
    @Resource
    private IProjectResourceConfirmationService projectResourceConfirmationService;

    /**
     * 批量修改收入导入查询
     */
    @Resource
    private ResourceBatchUpdateSearchImporter resourceBatchUpdateSearchImporter;


    /**
     * 批量修改-列表查询
     *
     * @param query 查询参数
     * @return 列表数据
     */
    @ApiOperation("批量修改列表")
    @PostMapping("/page")
    public R<PageInfo<BatchConfirmationVO>> pageBatchConfirmationInfo(@RequestBody BatchConfirmationQuery query) {
        //查询已确认收入的 项目资源
        query.setConfirmStatusList(Collections.singletonList(2));
        return R.success(projectResourceConfirmationService.pageBatchConfirmationInfo(query, true));
    }


    /**
     * 项目管理-批量修改 收入合计
     *
     * @param query 查询参数
     * @return
     */
    @ApiOperation("项目-批量修改收入合计")
    @PostMapping("/getCountConfirmIncome")
    public R<ResourceConfirmCountVO> getCountConfirmIncome(@RequestBody BatchConfirmationQuery query) {
        return R.success(this.projectResourceConfirmationService.getCountConfirmIncome(query));
    }


    /**
     * 批量修改收入-获取导入查询模板
     *
     * @param response 请求响应
     */
    @ApiOperation("项目-批量修改-下载批量导入模板")
    @GetMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<BatchUpdateImportModel> util = new ExcelUtil<>(BatchUpdateImportModel.class);
        util.importTemplateExcel(response, "项目-批量修改-批量导入模板");
    }

    @ApiOperation("项目-批量修改-获取导入查询模板")
    @PostMapping("/importQueryTemplate")
    public void importQueryTemplate(HttpServletResponse response) {
        ExcelUtil<BatchUpdateImportQueryModel> util = new ExcelUtil<>(BatchUpdateImportQueryModel.class);
        util.importTemplateExcel(response, "项目-批量修改-导入查询模板");
    }

    @ApiOperation("项目-批量修改-导入")
    @PostMapping("/import")
    public void batchUpdateImport(HttpServletResponse response) {
        ExcelUtil<BatchUpdateImportModel> util = new ExcelUtil<>(BatchUpdateImportModel.class);
        util.importTemplateExcel(response, "项目-批量修改-获取导入查询模板");
    }

    /**
     * 批量修改-导入查询
     *
     * @param file 导入文件
     * @return 导入结果
     */
    @ApiOperation("批量修改-导入查询")
    @Log(title = "批量修改-导入查询", businessType = BusinessType.IMPORT)
    @PostMapping("/import/select/data")
    public R<ImportResourceSelectVO> batchUpdateImportSelectDate(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }
        //校验是否有数据
        ExcelUtil<ImportResourceSelectModel> importUtils = new ExcelUtil<>(ImportResourceSelectModel.class);
        List<ImportResourceSelectModel> importModelList = importUtils.importExcel(file.getInputStream());
        if (ListUtil.isEmpty(importModelList)) {
            throw new ServiceException("导入失败,无法读取数据,请检查");
        }
        return R.success(resourceBatchUpdateSearchImporter.importSelectData(importModelList));
    }


}
