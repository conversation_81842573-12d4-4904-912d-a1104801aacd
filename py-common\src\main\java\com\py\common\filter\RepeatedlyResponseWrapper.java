package com.py.common.filter;


import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.*;

/**
 * 构建可重复读取outputStream的response
 * <AUTHOR>
 */
public class RepeatedlyResponseWrapper extends HttpServletResponseWrapper {

    private ServletOutputStream outputStream;

    private PrintWriter printWriter;

    private ServletOutputStreamBackup backup;

    /**
     * Constructs a response adaptor wrapping the given response.
     * @param response The response to be wrapped
     * @throws IllegalArgumentException if the response is null
     */
    public RepeatedlyResponseWrapper(HttpServletResponse response) throws IOException {
        super(response);
    }

    /**
     * 设置是否需求备份输出流
     * @param needBackUp 是否需求备份输出流
     */
    public void isNeedBackup(boolean needBackUp) {
        if(this.outputStream != null) {
            throw new IllegalStateException("outputStream has already been called on this response.");
        }

        try {
            ((ServletOutputStreamBackup) getOutputStream()).setNeedBackUp(needBackUp);
        } catch(IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public ServletOutputStream getOutputStream() throws IOException {
        if(printWriter != null) {
            throw new IllegalStateException("getWriter() has already been called on this response.");
        }

        if(outputStream == null) {
            outputStream = getResponse().getOutputStream();
            backup = new ServletOutputStreamBackup(outputStream);
        }

        return backup;
    }

    @Override
    public PrintWriter getWriter() throws IOException {
        if(printWriter == null) {
            backup = new ServletOutputStreamBackup(getResponse().getOutputStream());
            printWriter = new PrintWriter(new OutputStreamWriter(backup, getResponse().getCharacterEncoding()), true);
        }

        return printWriter;
    }

    @Override
    public void flushBuffer() throws IOException {

        if(printWriter != null) {
            printWriter.flush();
        }
        if(outputStream != null) {
            outputStream.flush();
        }
    }

    public byte[] getBackup() {
        if(backup != null) {
            return backup.getBackup();
        } else {
            return new byte[0];
        }
    }

    private static class ServletOutputStreamBackup extends ServletOutputStream {

        private final OutputStream outputStream;
        private final ByteArrayOutputStream backup;
        private boolean needBackUp = false;

        public ServletOutputStreamBackup(OutputStream outputStream) {
            this.outputStream = outputStream;
            this.backup = new ByteArrayOutputStream(1024);
        }

        @Override
        public void write(int b) throws IOException {
            outputStream.write(b);
            if(needBackUp) {
                backup.write(b);
            }
        }

        @Override
        public boolean isReady() {
            return false;
        }

        @Override
        public void setWriteListener(WriteListener listener) {

        }

        public byte[] getBackup() {
            return backup.toByteArray();
        }

        public void setNeedBackUp(boolean needBackUp) {
            this.needBackUp = needBackUp;
        }
    }
}
