package com.py.crm.customer.customervisit.domian;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.py.common.core.domain.BaseEntity;
import com.py.common.file.FileAnnex;
import com.py.common.tools.modifycomparator.annotation.CompareField;
import com.py.common.typehandler.impl.FileAnnexTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Transient;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
@TableName(value = "py_crm_customer_visit",autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("客户管理-客户拜访日志")
public class CustomerVisit extends BaseEntity {
    private static final long serialVersionUID=1L;
    /**自增id*/
    @ApiModelProperty("自增id")
    private Long id;

    /**客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /**访问日志id*/
    @ApiModelProperty("访问日志id")
    private Long visitId;

    /**随访人员*/
    @ApiModelProperty("随访人员")
    @CompareField(value = "随访人员")
    private String visitPersonnel;
    @TableField(exist = false)
    private List<String> visitPersonnelList;

    /**访问事项*/
    @ApiModelProperty("访问事项")
    @CompareField(value = "访问事项")
    private String visitMatters;

    /**事项结果*/
    @ApiModelProperty("事项结果")
    @CompareField(value = "事项结果")
    private String visitOutcome;

    /**附件*/
    @ApiModelProperty("附件")
    @TableField(typeHandler = FileAnnexTypeHandler.class)
    private List<FileAnnex> annex;

    /**备注*/
    @ApiModelProperty("备注")
    @CompareField(value = "备注")
    private String note;
    /**展示id*/
    @ApiModelProperty("展示id")
    private Integer showId;

    /**访问日期*/
    @ApiModelProperty("访问日期")
    @CompareField(value = "访问日期")
    private LocalDate visitTime;
}
