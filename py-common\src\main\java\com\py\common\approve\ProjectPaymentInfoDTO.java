package com.py.common.approve;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.utils.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目信息视图模型
 * <AUTHOR>
 */
@Data
public class ProjectPaymentInfoDTO {

    /** 业务id */
    @ApiModelProperty("业务id")
    private Long bizId;

    /** 项目id */
    @ApiModelProperty("项目id")
    private Long projectId;

    /** 项目名称 */
    @ApiModelProperty("项目名称")
    private String projectName;

    /** 项目编号 */
    @ApiModelProperty("项目编号" )
    private String projectCode;

    /** 派芽合作主体名称 */
    @ApiModelProperty("派芽合作主体名称" )
    private String pyMainstayName;

    /**  发起人 */
    @ApiModelProperty(" 发起人")
    private String createBy;

    /**  发起部门 */
    @ApiModelProperty(" 发起部门")
    private String createDept;

    /**  发起时间 */
    @ApiModelProperty(" 发起时间")
    private LocalDateTime createTime;

    @ApiModelProperty("项目状态")
    private Integer projectStatus;

    /**  备注 */
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("执行表单信息")
    private List<PaymentInfoListDTO> paymentInfoListDTOList;

    @ApiModelProperty("立项金额")
    private BigDecimal projectAmount;

    /** 收入金额*/
    @ApiModelProperty("收入金额")
    private BigDecimal incomeMoney;

    /** 收款状态(0.未收款 1.部分收款 2.已收款)*/
    @ApiModelProperty("收款状态(0.未收款 1.部分收款 2.已收款)")
    private Integer proceedsType;
    private String proceedsTypeName;


    /** 已收金额*/
    @ApiModelProperty("已收金额")
    private BigDecimal receivedPaid;

    /** 未收金额*/
    @ApiModelProperty("未收金额")
    private BigDecimal uncollectedAmount;

    /** 坏账金额*/
    @ApiModelProperty("坏账金额")
    private BigDecimal badDebtAmount;

    /** 发起人 */
    @ApiModelProperty("发起人")
    private String promoter;

    /** 发起人部门 */
    @ApiModelProperty("发起人部门")
    private String promoterDept;

    /** 发起时间 */
    @ApiModelProperty("发起时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime promoterTime;

    /** 立项时间 */
    @ApiModelProperty("立项时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime projectApprovalTime;


    /**通过率 执行表单通过的资源数量/执行表单总资源数量 */
    @ApiModelProperty("通过率")
    private BigDecimal passingRate;
}
