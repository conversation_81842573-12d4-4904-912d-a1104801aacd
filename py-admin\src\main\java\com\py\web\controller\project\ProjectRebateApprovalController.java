package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.project.projectrebate.projectrebaterecord.domain.dto.RebateRecordApprovalDTO;
import com.py.project.projectrebate.projectrebaterecord.domain.query.ProjectRebateRecordQuery;
import com.py.project.projectrebate.projectrebaterecord.domain.query.RebateRecordApprovalQuery;
import com.py.project.projectrebate.projectrebaterecord.domain.vo.ProjectRebateRecordApprovalVO;
import com.py.project.projectrebate.projectrebaterecord.domain.vo.ProjectRebateRecordInfoVO;
import com.py.project.projectrebate.projectrebaterecord.domain.vo.RebateRecordLabelCountVO;
import com.py.project.projectrebate.projectrebaterecord.service.IProjectRebateRecordService;
import com.py.project.projectrebate.projectrebaterecord.service.impl.TaskReusableHandlerServiceImpl;
import com.py.project.projectrebate.projectrebaterecorddetails.domain.vo.ProjectRebateRecordDetailsInfoVO;
import com.py.project.projectrebate.projectrebaterecorddetails.service.IProjectRebateRecordDetailsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "项目返点登记审批表")
@RestController
@RequestMapping("/projectrebate/approval")
public class ProjectRebateApprovalController extends BaseController {


    /**返点登记审批服务*/
    @Resource
    private IProjectRebateRecordService projectRebateRecordService;

    /**返点登记服务详情服务*/
    @Resource
    private IProjectRebateRecordDetailsService projectRebateRecordDetailsService;

    /**  返点登记审批查询下载服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 发起返点登记申请
     *
     * @param dto 返点登记参数
     * @return 是否成功
     */
    @PostMapping("/add")
    @ApiOperation("项目管理-返点登记-发起")
    @Log(title = "返点登记", businessType = BusinessType.INSERT)
    public R<Long> addRebateApproval(@Validated @RequestBody RebateRecordApprovalDTO dto) {
        return R.success(projectRebateRecordService.insertOrEditRebateRecord(dto));
    }

    /**
     * 返点列表-返点明细-返点详情按钮-审批基本信息
     *
     * @param rebateRecordId 返点登记审批记录id
     * @return 审批-基本信息
     */
    @ApiOperation("/返点明细-返点详情按钮-审批基本信息")
    @GetMapping("/queryapprovalInfo/{rebateRecordId}")
    public R<ProjectRebateRecordInfoVO> queryapprovalInfo(@PathVariable("rebateRecordId") Long rebateRecordId) {
        ProjectRebateRecordInfoVO rebateRecordInfo = projectRebateRecordService.getRebateRecordInfo(rebateRecordId);
        return R.success(rebateRecordInfo);
    }

    /**
     * 返点列表-返点明细-返点详情按钮-审批收款列表
     *
     * @param query 返点登记审批记录id
     * @return 审批-收款列表
     */
    @ApiOperation("/返点明细-返点详情按钮-审批收款列表")
    @PostMapping("/queryapprovalDetail")
    public R<PageInfo<ProjectRebateRecordDetailsInfoVO>> queryapprovalDetail(@RequestBody ProjectRebateRecordQuery query) {
        PageInfo<ProjectRebateRecordDetailsInfoVO> voList = projectRebateRecordDetailsService.getRebateRecordDetails(query);
        return R.success(voList);
    }


    /**
     * 返点登记审批查询项目主体数量
     * @return 主体参数统计列表
     */
    @ApiOperation("返点登记审批查询-项目主体")
    @GetMapping("/label")
    public R<List<RebateRecordLabelCountVO>> queryRebateRecordLabelCount(){
        return R.success(projectRebateRecordService.queryRebateRecordLabelCount());
    }

    /**
     * 返点登记审批查询-查询列表
     *
     * @param query 查询参数
     * @return 查询结果
     */
    @ApiOperation("返点登记审批查询-查询列表接口")
    @PostMapping("/queryRebateInfo")
    public R<PageInfo<ProjectRebateRecordApprovalVO>> queryRebateInfo(@Validated @RequestBody RebateRecordApprovalQuery query) {
        PageInfo<ProjectRebateRecordApprovalVO> voList = projectRebateRecordService.queryRebateInfo(query,true);
        return R.success(voList);
    }


    /**
     * 返点登记审批查询-复选框合计本次返点金额
     * @param query 查询参数
     * @return 统计结果
     */
    @ApiOperation("返点登记审批查询-复选框合计本次返点金额")
    @PostMapping("/rebateCheckBox")
    public R<ProjectRebateRecordInfoVO> rebateCheckBox(@Validated @RequestBody RebateRecordApprovalQuery query){
        return R.success(projectRebateRecordService.rebateCheckBox(query));
    }


    /**
     *  返点登记审批查询-返点登记审批查询表单下载
     * @param query 查询参数
     * @return 操作结果
     */
    @ApiOperation("返点登记审批查询-返点登记审批查询表单下载")
    @Log(title = "返点登记审批查询-返点登记审批查询表单下载", businessType = BusinessType.EXPORT)
    @PostMapping("/exportRebateRecordApproval")
    public R<String> exportRebateRecordApproval(@Validated @RequestBody RebateRecordApprovalQuery query){
        reusableAsyncTaskService.addTask("返点登记审批查询表单下单", TaskType.Export,query, TaskReusableHandlerServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 查询返点登记审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询返点登记审批列表上的创建部门下拉" )
    @GetMapping("/listApprovalResourceBadDebtDept" )
    public R<List<String>> listRebateRecordApprovalDept(RebateRecordApprovalQuery query){
        return R.success(projectRebateRecordService.listRebateRecordApprovalDept(query));
    }
}
