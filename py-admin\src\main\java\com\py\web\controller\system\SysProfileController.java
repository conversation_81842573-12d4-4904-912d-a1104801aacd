package com.py.web.controller.system;

import com.py.common.annotation.Log;
import com.py.common.config.SystemConfig;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.AjaxResult;
import com.py.common.core.domain.R;
import com.py.common.core.domain.entity.ResetPasswordDTO;
import com.py.common.core.domain.model.LoginUser;
import com.py.common.core.domain.vo.user.AuthUser;
import com.py.common.enums.BusinessType;
import com.py.common.utils.RsaUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.file.FileUploadUtils;
import com.py.framework.web.service.TokenService;
import com.py.system.user.domain.dto.UserDTO;
import com.py.system.user.service.ISysUserService;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 个人信息 业务处理
 * <AUTHOR>
 */
@ApiOperation("个人信息")
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController extends BaseController {
    @Resource
    private ISysUserService userService;

    @Resource
    private TokenService tokenService;

    /**
     * 获取个人信息
     * @return 个人信息
     */
    @ApiOperation("获取个人信息")
    @GetMapping
    public R<AuthUser> profile() {
        LoginUser loginUser = getLoginUser();
        AuthUser user = loginUser.getUser();
        return R.success(user);
    }

    /**
     * 修改用户
     * @param dto 用户信息
     * @return 结果
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> updateProfile(@Validated @RequestBody UserDTO dto) {
        LoginUser loginUser = getLoginUser();

        Assert.isTrue(loginUser.getUserId().equals(dto.getUserId()), "不能修改他人信息");
        // 更新用户信息
        this.userService.updateUser(dto);

        AuthUser authUser = this.userService.selectUserById(loginUser.getUserId());
        loginUser.setUser(authUser);
        tokenService.setLoginUser(loginUser);
        return R.success();
    }

    /**
     * 重置密码
     * @param resetPassword 密码信息
     * @return 是否成功
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public R<Boolean> updatePwd(@RequestBody ResetPasswordDTO resetPassword) throws Exception {
        LoginUser loginUser = getLoginUser();
        String newPassword = RsaUtils.decryptByPrivateKey(resetPassword.getNewPassword());
        if(userService.updateUserPassword(loginUser.getUserId(), SecurityUtils.encryptPassword(newPassword)) > 0) {
            // 更新缓存用户密码
            loginUser.getUser().setPassword(SecurityUtils.encryptPassword(newPassword));
            tokenService.setLoginUser(loginUser);
            return R.success();
        }
        return R.failed("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@RequestParam("avatarfile") MultipartFile file) throws IOException {
        if(!file.isEmpty()) {
            LoginUser loginUser = getLoginUser();
            String avatar = FileUploadUtils.upload(SystemConfig.getAvatarPath(), file);
            if(userService.updateUserAvatar(loginUser.getUsername(), avatar)) {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("imgUrl", avatar);
                // 更新缓存用户头像
                loginUser.getUser().setAvatar(avatar);
                tokenService.setLoginUser(loginUser);
                return ajax;
            }
        }
        return AjaxResult.error("上传图片异常，请联系管理员");
    }
}
