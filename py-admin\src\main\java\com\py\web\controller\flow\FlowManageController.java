package com.py.web.controller.flow;

import com.github.pagehelper.PageInfo;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.flow.flowmanage.domain.dto.FlowManageDTO;
import com.py.flow.flowmanage.domain.query.FlowManageQuery;
import com.py.flow.flowmanage.domain.vo.FlowListVO;
import com.py.flow.flowmanage.domain.vo.FlowVO;
import com.py.flow.flowmanage.service.IFlowService;
import com.py.system.dept.domain.vo.DeptVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 流程管理控制器
 * <AUTHOR>
 * @date 2023/7/20 11:27
 */
@Api(tags = "流程管理 - 流程管理")
@RestController
@RequestMapping("/flow")
public class FlowManageController extends BaseController {

    /** 流程管理服务 */
    @Resource
    private IFlowService flowManageService;

    /**
     * 流程列表
     * @param flowManageQuery 流程管理列表查询参数
     * @return 流程管理列表信息
     */
    @ApiOperation("流程列表")
    @GetMapping("/pageFlow")
    public R<PageInfo<FlowListVO>> pageFlow(FlowManageQuery flowManageQuery) {
        return R.success(this.flowManageService.pageFlow(flowManageQuery));
    }

    /**
     * 获取流程详情
     * @param flowId 流程ID
     * @return 流程详情
     */
    @ApiOperation("流程详情")
    @GetMapping("/detailFlow/{flowId}")
    public R<FlowVO> getInfo(@PathVariable Long flowId){
        return R.success(this.flowManageService.getFlowInfo(flowId));
    }

    /**
     * 新增流程
     * @param flowManageDto 流程管理DTO
     * @return 是否成功
     */
    @ApiOperation("新增流程")
    @PostMapping("/addFlow")
    public R<Boolean> addFlow(@Validated @RequestBody FlowManageDTO flowManageDto) {
        this.flowManageService.addFlow(flowManageDto);
        return R.success();
    }

    /**
     * 更新流程
     * @param flowManageDTO 流程管理DTO
     * @return 是否成功
     */
    @ApiOperation("更新流程")
    @PostMapping("/updateFlow")
    public R<Boolean> updateFlow(@Validated @RequestBody FlowManageDTO flowManageDTO) {
        this.flowManageService.updateFlow(flowManageDTO);
        return R.success();
    }

    /**
     * 删除流程
     * @param flowManageId 流程管理id
     * @return 是否成功
     */
    @ApiOperation("删除流程")
    @DeleteMapping("/deleteFlow/{flowManageId}")
    public R<Boolean> deleteFlow(@PathVariable Long flowManageId) {
        Assert.notNull(flowManageId, "流程管理id不能为空");
        this.flowManageService.deleteFlow(flowManageId);
        return R.success();
    }

    /**
     * 查询列表上的更新部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询列表上的更新部门下拉" )
    @GetMapping("/listFlowDept" )
    public R<PageInfo<DeptVO>> listFlowDept(FlowManageQuery query){
        return R.success(flowManageService.listFlowDept(query));
    }

}
