package com.py.common.tools.excelhelper.functions;

import com.py.common.tools.verify.domain.RowVerifyError;

/**
 * 重复数据行错误信息构造器
 * <AUTHOR>
 */
@FunctionalInterface
public interface SameDataRowVerifyErrorGenerator<T> {

    /**
     * 创建重复行错误信息
     * @param rowIndex 出现错误的行
     * @param errorItem 错误对象
     * @param sameRowsStr 重复的行号列表, 如: 2, 5, 7
     * @return 重复行错误信息
     */
    RowVerifyError<T> create(Integer rowIndex, T errorItem, String sameRowsStr);
}
