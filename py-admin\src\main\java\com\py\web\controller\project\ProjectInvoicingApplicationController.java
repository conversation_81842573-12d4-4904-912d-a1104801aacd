package com.py.web.controller.project;

import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.SecurityUtils;
import com.py.project.projectinvoicing.domain.vo.ProjectInvoicingSelectedListVO;
import com.py.project.projectinvoicingapplication.domain.dto.ProjectInvoicingApplicationDTO;
import com.py.project.projectinvoicingapplication.domain.dto.ProjectInvoicingResourceApplicationDTO;
import com.py.project.projectinvoicingapplication.domain.query.ProjectInvoicingApplicationQuery;
import com.py.project.projectinvoicingapplication.domain.vo.ProjectInvoicingApplicationVO;
import com.py.project.projectinvoicingapplication.domain.vo.ProjectInvoicingResourceApplicationVO;
import com.py.project.projectinvoicingapplication.service.IProjectInvoicingApplicationService;
import com.py.project.projectinvoicingapplication.service.impl.ProjectInvoicingApplicationServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 项目-资源收款开票申请表Controller
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Api(tags = "项目-资源收款开票申请表")
@RestController
@RequestMapping("/invoicingApplication")
public class ProjectInvoicingApplicationController extends BaseController {

    /** 项目-资源收款开票申请表服务 */
    @Resource
    private IProjectInvoicingApplicationService projectInvoicingApplicationService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 获取项目-收款开票申请表详细信息
     * @param query 项目-资源收款开票申请表主键
     * @return 项目-资源收款开票申请表视图模型
     */
    @ApiOperation("获取项目-资源收款开票申请表详细信息")
    @PreAuthorize("@ss.hasPermi('projectinvoicing:projectinvoicing:query')")
    @GetMapping(value = "/selectInfo")
    public R<ProjectInvoicingApplicationVO> getInfo(ProjectInvoicingApplicationQuery query) {
        return R.success(projectInvoicingApplicationService.selectProjectInvoicingApplicationById(query));
    }

    /**
     * 开票申请
     *
     * @param dto 项目-资源收款开票申请表修改参数
     * @return 项目收款开票id
     */
    @ApiOperation("项目收款开票申请")
    @PreAuthorize("@ss.hasPermi('projectinvoicingapplication:projectinvoicingdetailapplication:add')")
    @Log(title = "项目收款开票申请表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<ProjectInvoicingSelectedListVO> add(@Validated @RequestBody ProjectInvoicingApplicationDTO dto) {
        return R.success(projectInvoicingApplicationService.insertProjectInvoicingApplication(dto));
    }

    /**
     * 开票申请（再次提交审批）
     *
     * @param dto 项目-资源收款开票申请表修改参数
     * @return 项目收款开票id
     */
    @ApiOperation("项目收款开票申请")
    @PreAuthorize("@ss.hasPermi('projectinvoicingapplication:projectinvoicingdetailapplication:add')")
    @Log(title = "项目收款开票申请表", businessType = BusinessType.INSERT)
    @PostMapping("/auditEdit")
    public R<ProjectInvoicingSelectedListVO> auditEdit(@Validated @RequestBody ProjectInvoicingApplicationDTO dto) {
        dto.setIsApprovalEdit(true);
        return R.success(projectInvoicingApplicationService.insertProjectInvoicingApplication(dto));
    }

    /**
     * 资源返点开票申请
     *
     * @param dto 项目-资源收款开票申请表修改参数
     * @return 资源返点开票id
     */
    @ApiOperation("资源返点开票申请~~~~~~~~~")
    @PreAuthorize("@ss.hasPermi('projectinvoicingresource:projectinvoicingdetailapplication:add')")
    @Log(title = "资源返点开票申请", businessType = BusinessType.INSERT)
    @PostMapping("/resource/add")
    public R<ProjectInvoicingSelectedListVO> addResource(@Validated @RequestBody ProjectInvoicingResourceApplicationDTO dto) {
        return R.success(projectInvoicingApplicationService.insertInvoicingResourceApplication(dto));
    }

    /**
     * 资源返点开票申请（再次提交审批）
     *
     * @param dto 项目-资源收款开票申请表修改参数
     * @return 资源返点开票id
     */
    @ApiOperation("资源返点开票申请~~~~~~~~~")
    @PreAuthorize("@ss.hasPermi('projectinvoicingresource:projectinvoicingdetailapplication:add')")
    @Log(title = "资源返点开票申请", businessType = BusinessType.INSERT)
    @PostMapping("/resource/auditEdit")
    public R<ProjectInvoicingSelectedListVO> resourceAuditEdit(@Validated @RequestBody ProjectInvoicingResourceApplicationDTO dto) {
        dto.setIsApprovalEdit(true);
        return R.success(projectInvoicingApplicationService.insertInvoicingResourceApplication(dto));
    }

    /**
     * 项目开票详情 - 导出开票申请信息
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目-项目收款开票申请表")
    @PreAuthorize("@ss.hasPermi('projectinvoicingapplication:projectinvoicingdetailapplication:export')")
    @Log(title = "导出开票申请信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@RequestBody ProjectInvoicingApplicationQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setIsProject(true);
        reusableAsyncTaskService.addTask("开票申请信息数据", TaskType.Export, query, ProjectInvoicingApplicationServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 资源开票详情 - 导出开票申请信息
     * @param query 导出查询参数
     */
    @ApiOperation("导出资源-资源返点开票申请表")
    @PreAuthorize("@ss.hasPermi('projectinvoicingapplication:projectinvoicingdetailapplication:export')")
    @Log(title = "导出开票申请信息", businessType = BusinessType.EXPORT)
    @PostMapping("/resource/export")
    public R<String> exportResource(@RequestBody ProjectInvoicingApplicationQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setIsProject(false);
        reusableAsyncTaskService.addTask("开票申请信息数据", TaskType.Export, query, ProjectInvoicingApplicationServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 获取资源返点申请表详细信息
     * @param query 资源返点开票申请表主键
     * @return 资源返点开票申请表视图模型
     */
    @ApiOperation("获取项目-资源收款开票申请表详细信息")
    @PreAuthorize("@ss.hasPermi('projectinvoicingresource:projectinvoicing:query')")
    @GetMapping(value = "/resource/selectInfo")
    public R<ProjectInvoicingResourceApplicationVO> getResourceInfo(ProjectInvoicingApplicationQuery query) {
        return R.success(projectInvoicingApplicationService.selectInvoicingResourceApplicationById(query));
    }

    /**
     * 审批查询 - 获取资源返点申请表详细信息
     * @param query 资源返点开票申请表主键
     * @return 资源返点开票申请表视图模型
     */
    @ApiOperation("审批查询 - 获取资源返点申请表详细信息")
    @PreAuthorize("@ss.hasPermi('projectinvoicingresource:projectinvoicing:query')")
    @GetMapping(value = "/resource/selectSnapInfo")
    public R<ProjectInvoicingResourceApplicationVO> selectSnapInfo(ProjectInvoicingApplicationQuery query) {
        return R.success(projectInvoicingApplicationService.selectInvoicingResourceApplicationSnapById(query));
    }
}
