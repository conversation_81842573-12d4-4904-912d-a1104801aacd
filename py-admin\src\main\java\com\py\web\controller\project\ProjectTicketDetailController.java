package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.project.projectresource.domain.vo.CheckErrorInfoVO;
import com.py.project.projectticketdetail.domain.dto.ProjectTicketDetailDTO;
import com.py.project.projectticketdetail.domain.query.ProjectTicketDetailQuery;
import com.py.project.projectticketdetail.domain.vo.ProjectTicketDetailListVO;
import com.py.project.projectticketdetail.domain.vo.ProjectTicketDetailVO;
import com.py.project.projectticketdetail.service.IProjectTicketDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 收票详情表Controller
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
@Api(tags = "收票详情表")
@RestController
@RequestMapping("/ticketDetail")
public class ProjectTicketDetailController extends BaseController {

    /** 收票详情表服务 */
    @Resource
    private IProjectTicketDetailService projectTicketDetailService;

    /**
     * 查询收票详情表列表
     *
     * @param query 收票详情表查询参数
     * @return 收票详情表列表
     */
    @ApiOperation("查询收票详情表列表")
    @PreAuthorize("@ss.hasPermi('projectticketdetail:projectticketdetail:list')")
    @GetMapping("/listProjectTicketDetail")
    public R<List<ProjectTicketDetailListVO>> listProjectTicketDetail(ProjectTicketDetailQuery query) {
        List<ProjectTicketDetailListVO> voList = this.projectTicketDetailService.listProjectTicketDetail(query);
        return R.success(voList);
    }

    /**
     * 分页查询收票详情表列表
     *
     * @param query 收票详情表查询参数
     * @return 收票详情表分页
     */
    @ApiOperation("分页查询询收票详情表列表")
    @PreAuthorize("@ss.hasPermi('projectticketdetail:projectticketdetail:list')")
    @GetMapping("/pageProjectTicketDetail")
    public R<PageInfo<ProjectTicketDetailListVO>> pageProjectTicketDetail(ProjectTicketDetailQuery query) {
        PageInfo<ProjectTicketDetailListVO> voList = this.projectTicketDetailService.pageProjectTicketDetailList(query);
        return R.success(voList);
    }

    /**
     * 获取收票详情表详细信息
     * @param id 收票详情表主键
     * @return 收票详情表视图模型
     */
    @ApiOperation("获取收票详情表详细信息")
    @PreAuthorize("@ss.hasPermi('projectticketdetail:projectticketdetail:query')")
    @GetMapping(value = "/{id}")
    public R<ProjectTicketDetailVO> getInfo(@PathVariable("id") Long id) {
        return R.success(projectTicketDetailService.selectProjectTicketDetailById(id));
    }

    /**
     * 收票登记保存
     *
     * @param dtoList 收票详情表修改参数
     * @return 是否成功
     */
    @ApiOperation("收票登记保存")
    @PreAuthorize("@ss.hasPermi('projectticketdetail:projectticketdetail:add')")
    @Log(title = "收票详情表", businessType = BusinessType.INSERT)
    @PostMapping("/addTicket")
    public R<CheckErrorInfoVO> add(@RequestBody List<ProjectTicketDetailDTO> dtoList) {
        return R.success(projectTicketDetailService.insertProjectTicketDetail(dtoList));
    }

    /**
     * 更新收票详情表
     *
     * @param dto 收票详情表修改参数
     * @return 是否成功
     */
    @ApiOperation("更新收票详情")
    @PreAuthorize("@ss.hasPermi('projectticketdetail:projectticketdetail:edit')")
    @Log(title = "收票详情表", businessType = BusinessType.UPDATE)
    @PostMapping("/updateTicket")
    public R<Boolean> edit(@RequestBody ProjectTicketDetailDTO dto) {
        return R.success(projectTicketDetailService.updateProjectTicketDetail(dto));
    }
}
