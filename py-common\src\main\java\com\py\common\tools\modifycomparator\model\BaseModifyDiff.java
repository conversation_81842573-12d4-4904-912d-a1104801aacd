package com.py.common.tools.modifycomparator.model;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.py.common.tools.modifycomparator.enums.ModifyContextType;
import com.py.common.utils.StringUtils;
import lombok.*;
import org.springframework.util.Assert;

import java.util.LinkedList;
import java.util.List;

/**
 * 修改差异
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        property = "contextType")
@JsonSubTypes({
        @JsonSubTypes.Type(value = BaseModifyDiff.StringModifyDiff.class, name = "String"),
        @JsonSubTypes.Type(value = BaseModifyDiff.OssFileModifyDiff.class, name = "OssFile"),
        @JsonSubTypes.Type(value = BaseModifyDiff.ListOssFileModifyDiff.class, name = "ListOssFile"),
})
public abstract class BaseModifyDiff<T> {

    /** 字段路径 */
    @Setter(AccessLevel.NONE)
    private final LinkedList<String> fieldPath = new LinkedList<>();
    /** 字段名称 */
    private String fieldName;
    /** 修改前 */
    private T before;
    /** 修改后 */
    private T after;

    /** 修改序列号 */
    private Integer serialNumber;



    public BaseModifyDiff(String fieldName, String fieldPath) {
        this.fieldName = fieldName;
        this.fieldPath.add(fieldPath);
    }

    /**
     * 获取修改内容类型
     * @return 修改内容类型
     */
    public abstract ModifyContextType getContextType();

    /**
     * 设置修改内容
     * @param before 修改后
     * @param after 修改前
     */
    public abstract void setModifyContext(Object before, Object after);

    /**
     * 为字段路径添加前缀
     * @param prefix 要添加的前缀
     */
    public void addFieldPathPrefix(String prefix) {
        this.fieldPath.addFirst(prefix);
    }

    /**
     * 获取字段路径
     * @return 字段路径
     */
    public List<String> getFieldPath() {
        return fieldPath;
    }

    /** 纯文本修改差异 */
    @Data
    @EqualsAndHashCode(callSuper = true)
    @NoArgsConstructor
    public static class StringModifyDiff extends BaseModifyDiff<String> {

        /** 是否为字典 */
        private boolean asDict = false;

        /** 是否为列表字典 */
        private boolean asListDict = false;

        /** 字典名称 */
        private String dictName = null;

        public StringModifyDiff(String fieldName, String fieldPath) {
            super(fieldName, fieldPath);
        }

        /** 修改内容类型 */
        @Override
        public ModifyContextType getContextType() {
            return ModifyContextType.STRING;
        }

        /**
         * 设置修改内容
         * @param before 修改后
         * @param after 修改前
         */
        @Override
        public void setModifyContext(Object before, Object after) {
            this.verifyType(after);
            if(before != null) {
                this.verifyType(before);
            }
            this.setBefore((String) before);
            this.setAfter((String) after);
        }

        /**
         * 验证入参类型, 不为String时抛异常
         * @param value 需校验的值
         */
        private void verifyType(Object value) {
            Assert.isInstanceOf(String.class, value, "设置纯文本的值类型必须为: String");
        }

        /**
         * 设置字典名称
         * @param dictName 字典名称
         */
        public void setDictName(String dictName) {
            if(StringUtils.isBlank(dictName)) {
                return;
            }
            this.dictName = dictName;
            this.asDict = true;
        }

        /**
         * 设置字典名称
         * @param dictName 字典名称
         */
        public void setListDictName(String dictName) {
            if(StringUtils.isBlank(dictName)) {
                return;
            }
            this.dictName = dictName;
            this.asListDict = true;
        }
    }

    /** Oss文件 */
    @NoArgsConstructor
    public static class OssFileModifyDiff extends BaseModifyDiff<String> {

        public OssFileModifyDiff(String fieldName, String fieldPath) {
            super(fieldName, fieldPath);
        }

        /**
         * 验证入参类型, 不为String时抛异常
         * @param value 需校验的值
         */
        private void verifyType(Object value) {
            if(value == null) {
                return;
            }
            Assert.isInstanceOf(String.class, value, "设置Oss文件的值类型必须为: String");
        }

        /** 修改内容类型 */
        @Override
        public ModifyContextType getContextType() {
            return ModifyContextType.OSS_File;
        }

        /**
         * 设置修改内容
         * @param before 修改后
         * @param after 修改前
         */
        @Override
        public void setModifyContext(Object before, Object after) {
            this.verifyType(after);
            this.verifyType(before);

            super.setAfter((String) after);
            super.setBefore((String) before);
        }
    }

    /** 列表OSS文件 */
    @NoArgsConstructor
    public static class ListOssFileModifyDiff extends BaseModifyDiff<List<String>> {

        public ListOssFileModifyDiff(String fieldName, String fieldPath) {
            super(fieldName, fieldPath);
        }

        /** 修改内容类型 */
        @Override
        public ModifyContextType getContextType() {
            return ModifyContextType.LIST_OSS_FILE;
        }

        /**
         * 设置修改内容
         * @param before 修改后
         * @param after 修改前
         */
        @SuppressWarnings("unchecked")
        @Override
        public void setModifyContext(Object before, Object after) {
            this.verifyType(after);
            this.verifyType(before);

            super.setAfter((List<String>) after);
            super.setBefore((List<String>) before);
        }

        /**
         * 验证入参类型, 不为String时抛异常
         * @param value 需校验的值
         */
        private void verifyType(Object value) {
            if(value == null) {
                return;
            }
            Assert.isInstanceOf(List.class, value, "设置Oss文件的值类型必须为: List<String>");
        }
    }
}
