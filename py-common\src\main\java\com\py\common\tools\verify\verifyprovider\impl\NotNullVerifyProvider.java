package com.py.common.tools.verify.verifyprovider.impl;

import com.py.common.tools.verify.enums.VerifyType;
import com.py.common.tools.verify.verifyprovider.VerifyProvider;
import org.springframework.stereotype.Component;

/**
 * 对象不为Null验证提供者
 * <AUTHOR>
 */
@Component
public class NotNullVerifyProvider implements VerifyProvider {
	/**
	 * 支持的验证类型
	 * @return 支持的验证类型
	 */
	@Override
	public VerifyType supportedVerifyType() {
		return VerifyType.NotNull;
	}

	/**
	 * 验证
	 * @param target 验证目标
	 * @return 验证结果 true:验证通过  false:验证失败
	 */
	@Override
	public boolean verify(Object target) {
		return target != null;
	}
}
