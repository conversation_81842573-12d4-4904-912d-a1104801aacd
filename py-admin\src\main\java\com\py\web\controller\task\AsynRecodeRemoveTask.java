package com.py.web.controller.task;

import com.py.system.tools.reusableasynctask.service.ITaskRecodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version AsynRecodeRemoveTask 2023/8/25 14:02
 */
@Slf4j
@Component
@EnableScheduling
public class AsynRecodeRemoveTask implements ApplicationRunner {


    /** 异步任务执行记录服务 */
    @Resource
    private ITaskRecodeService taskRecodeService;

    /**
     * Callback used to run the bean.
     * @param args incoming application arguments
     * @throws Exception on error
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
//        this.removeAsynRecode();
    }

    /**
     * 删除异步任务记录（30天）
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void removeAsynRecode(){
        log.info("定时删除异步任务记录：记录当前开始时间：" + LocalDateTime.now());
        taskRecodeService.taskRemoveAsynRecode();
        log.info("定时删除异步任务记录：记录当前结束时间：" + LocalDateTime.now());
    }
}
