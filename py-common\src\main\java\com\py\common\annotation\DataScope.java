package com.py.common.annotation;

import com.py.common.datascope.DataScopePageType;
import com.py.common.utils.StringUtils;
import lombok.Getter;

import java.lang.annotation.*;

/**
 * 数据权限过滤注解
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataScope {

    /** 数据权限页面类型 */
    DataScopePageType value();

    /** 用户ID字段的别名 */
    String userIdAlias() default StringUtils.EMPTY;

    /** 用户表的别名 */
    String userTableAlias() default StringUtils.EMPTY;

    /** 是否忽略用户数据权限过滤 */
    boolean isIgnoreUserPermission() default false;

    /** 主体ID字段的别名 */
    String mainstayIdAlias() default StringUtils.EMPTY;

    /** 主体表的别名 */
    String mainstayTableAlias() default StringUtils.EMPTY;

    /** 是否忽略主体数据权限过滤 */
    boolean isIgnoreMainstayPermission() default false;

    /** 时间字段的别名 */
    String dateAlias() default StringUtils.EMPTY;

    /** 时间表的别名 */
    String dateTableAlias() default StringUtils.EMPTY;

    /** 是否忽略时间数据权限过滤 */
    boolean isIgnoreDataPermission() default false;

    /** 审批查询字段的别名 */
     String approvalAlias() default StringUtils.EMPTY;

    /** 审批查询表的别名 */
     String approvalTableAlias() default StringUtils.EMPTY ;

    /** 是否忽略审批查询数据权限过滤 */
     boolean isIgnoreApprovalPermission() default true;

}
