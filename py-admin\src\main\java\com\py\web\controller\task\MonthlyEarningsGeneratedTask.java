package com.py.web.controller.task;

import com.py.project.performancedetails.service.IPerformanceDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 每月生成财报
 * <AUTHOR>
 * @version MonthlyEarningsGeneratedTask 2023/8/30 17:01
 */
@Slf4j
@Component
@EnableScheduling
public class MonthlyEarningsGeneratedTask implements ApplicationRunner {


    /** 财报明细服务 */
    @Resource
    private IPerformanceDetailsService performanceDetailsService;

    /**
     * Callback used to run the bean.
     * @param args incoming application arguments
     * @throws Exception on error
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
//        this.generateFinancial();
    }

    @Scheduled(cron = "0 5 0 1 * ?")
    public void generateFinancial(){
        try {
            log.info("每月生成财报：记录当前开始时间：" + LocalDateTime.now());
            performanceDetailsService.addMonthFinancialReport(LocalDate.now());
            log.info("每月生成财报：记录当前结束时间：" + LocalDateTime.now());
        }catch(Exception e){
            log.error("每月生成财报错误：",e);
        }
    }
}
