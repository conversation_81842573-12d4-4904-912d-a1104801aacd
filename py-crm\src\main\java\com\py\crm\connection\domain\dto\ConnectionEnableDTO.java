package com.py.crm.connection.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 人脉管理表数据传输模型
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
@ApiModel("人脉管理表禁用启用数据传输模型" )
public class ConnectionEnableDTO {
    private static final long serialVersionUID = 1L;


    /** 人脉Id */
    @ApiModelProperty("人脉Id" )
    @NotNull(message = "人脉Id不能为空")
    private Long connectionId;

    /** 状态（0无效；1有效） */
    @ApiModelProperty("状态（0无效；1有效）" )
    @NotNull(message = "状态不能为空")
    private Integer status;

    @ApiModelProperty("备注" )
    @Size(max = 1000,message = "备注不能超过1000")
    private String remark;


}
