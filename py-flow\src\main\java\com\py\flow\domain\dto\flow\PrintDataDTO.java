package com.py.flow.domain.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 打印流程审批DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("打印流程审批DTO")
public class PrintDataDTO {

    @ApiModelProperty(value = "发起人")
    private String createBy;

    @ApiModelProperty(value = "发起人部门")
    private String createDept;

    @ApiModelProperty(value = "发起时间")
    private String createTime;

    @ApiModelProperty("发起人")
    private String promoter;

    @ApiModelProperty("发起人部门")
    private String promoterDept;

    @ApiModelProperty("发起时间")
    private String promoterTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "派芽合作主体")
    private String pyMainstayName;

    @ApiModelProperty(value = "执行表单信息列表")
    private List<PrintResourceDataDTO> resourceInfoList;

    @ApiModelProperty(value = "流程节点列表")
    private List<FlowNodeDataDTO> flowNodeList;

    @ApiModelProperty(value = "本次合计金额(元)")
    private BigDecimal totalAmount;

    // 合计相关:按渠道合计数据
    @ApiModelProperty("各渠道合计数据")
    private List<ChannelAmountDataDTO> channelAmountList;

    // 项目内付款审批

    @ApiModelProperty("项目名称")
    private String projectName;


    @ApiModelProperty("立项金额")
    private BigDecimal projectAmount;

    @ApiModelProperty("收入金额")
    private BigDecimal incomeMoney;

    /** 收款状态(0.未收款 1.部分收款 2.已收款)*/
    @ApiModelProperty("收款状态(0.未收款 1.部分收款 2.已收款)")
    private String proceedsTypeName;


    @ApiModelProperty("坏账金额")
    private BigDecimal badDebtAmount;

    @ApiModelProperty("已收金额")
    private BigDecimal receivedPaid;

    @ApiModelProperty("未收金额")
    private BigDecimal uncollectedAmount;

    @ApiModelProperty("项目执行含税售价")
    private BigDecimal projectIncludingTaxPrice;

    @ApiModelProperty("项目已付金额")
    private BigDecimal projectReceivedPaid;

    @ApiModelProperty("已收金额")
    private BigDecimal receivedPaidStr;

    @ApiModelProperty("本项目是否全额回款")
    private String fullPaymentCollection;




    /** 立项时间 */
    @ApiModelProperty("立项时间")
    private String projectApprovalTime;


    @ApiModelProperty("通过率 执行表单通过的资源数量/执行表单总资源数量")
    private String passingRate;





}
