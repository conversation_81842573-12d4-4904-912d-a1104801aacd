package com.py.common.datascope;

import com.py.common.annotation.DataScope;
import com.py.common.datascope.functional.CustomDatePermissionSqlGenerator;
import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.StringUtils;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.Assert;

/**
 * 数据权限上下文
 * <AUTHOR>
 */
@Setter
public class DataScopeContext {

    /** 数据权限页面类型 */
    @Getter
    private DataScopePageType pageType;

    /** 用户ID字段的别名 */
    private String userIdAlias = "create_id";

    /** 用户表的别名 */
    private String userTableAlias;

    /** 是否忽略用户数据权限过滤 */
    @Getter
    private boolean isIgnoreUserPermission = false;

    /** 主体ID字段的别名 */
    private String mainstayIdAlias = "mainstay_id";

    /** 主体表的别名 */
    private String mainstayTableAlias;

    /** 是否忽略主体数据权限过滤 */
    @Getter
    private boolean isIgnoreMainstayPermission = false;

    /** 时间字段的别名 */
    private String dateAlias = "create_time";

    /** 时间表的别名 */
    private String dateTableAlias;

    /** 是否忽略时间数据权限过滤 */
    @Getter
    private boolean isIgnoreDataPermission = false;

    /** 自定义时间权限SQL生成器 */
    @Getter
    private CustomDatePermissionSqlGenerator customDatePermissionSqlGenerator;

    /** 拼接的sql */
    private String splicedSql;

    /** 审批查询字段的别名 */
    private String approvalAlias= "";

    /** 审批查询表的别名 */
    private String approvalTableAlias;

    /** 是否忽略审批查询数据权限过滤 */
    @Getter
    private boolean isIgnoreApprovalPermission = true;
    /**
     * 通过注解构造上下文
     * @param dataScope 数据权限注解
     * @return 数据权限上下文
     */
    public static DataScopeContext valueOf(DataScope dataScope) {
        Assert.notNull(dataScope.value(), "数据权限页面类型不能为空");

        DataScopeContext context = new DataScopeContext();
        context.setPageType(dataScope.value());
        context.setIgnoreUserPermission(dataScope.isIgnoreUserPermission());
        context.setIgnoreMainstayPermission(dataScope.isIgnoreMainstayPermission());
        context.setIgnoreDataPermission(dataScope.isIgnoreDataPermission());
        context.setIgnoreApprovalPermission(dataScope.isIgnoreApprovalPermission());

        if(StringUtils.isNotBlank(dataScope.userIdAlias())) {
            context.setUserIdAlias(dataScope.userIdAlias());
        }

        if(StringUtils.isNotBlank(dataScope.userTableAlias())) {
            context.setUserTableAlias(dataScope.userTableAlias());
        }

        if(StringUtils.isNotBlank(dataScope.mainstayIdAlias())) {
            context.setMainstayIdAlias(dataScope.mainstayIdAlias());
        }

        if(StringUtils.isNotBlank(dataScope.mainstayTableAlias())) {
            context.setMainstayTableAlias(dataScope.mainstayTableAlias());
        }

        if(StringUtils.isNotBlank(dataScope.dateAlias())) {
            context.setDateAlias(dataScope.dateAlias());
        }

        if(StringUtils.isNotBlank(dataScope.dateTableAlias())) {
            context.setDateTableAlias(dataScope.dateTableAlias());
        }

        if(StringUtils.isNotBlank(dataScope.approvalAlias())) {
            context.setApprovalAlias(dataScope.approvalAlias());
        }

        if(StringUtils.isNotBlank(dataScope.approvalTableAlias())) {
            context.setApprovalTableAlias(dataScope.approvalTableAlias());
        }
        return context;
    }

    /**
     * 获取用户表别名
     * @return 用户表别名
     */
    String getUserTableAlias() {
        return NullMergeUtils.blankMerge(this.userTableAlias, alias -> alias + ".", StringUtils.EMPTY);
    }

    /**
     * 获取主体表别名
     * @return 主体表别名
     */
    private String getMainstayTableAlias() {
        return NullMergeUtils.blankMerge(this.mainstayTableAlias, alias -> alias + ".", StringUtils.EMPTY);
    }

    /**
     * 获取时间表别名
     * @return 时间表别名
     */
    private String getDateTableAlias() {
        return NullMergeUtils.blankMerge(this.dateTableAlias, alias -> alias + ".", StringUtils.EMPTY);
    }

    /**
     * 获取用户表&字段别名
     * @return 用户表&字段别名
     */
    public String getUserAlias() {
        return String.format("%s%s", this.getUserTableAlias(), this.userIdAlias);
    }

    /**
     * 获取主体表&字段别名
     * @return 主体表&字段别名
     */
    public String getMainstayAlias() {
        return String.format("%s%s", this.getMainstayTableAlias(), this.mainstayIdAlias);
    }

    /**
     * 获取时间表&字段别名
     * @return 时间表&字段别名
     */
    public String getDateAlias() {
        return String.format("%s%s", this.getDateTableAlias(), this.dateAlias);
    }

    /**
     * 获取拼接sql
     * @return 拼接sql
     */
    String getSplicedSql() {
        return splicedSql;
    }

    /**
     * 获取时间表别名
     * @return 时间表别名
     */
    private String getApprovalTableAlias() {
        return NullMergeUtils.blankMerge(this.approvalTableAlias, alias -> alias + ".", StringUtils.EMPTY);
    }

    public String getApprovalAlias() {
        return String.format("%s%s", this.getApprovalTableAlias(), this.approvalAlias);
    }

}
