package com.py.common.core.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.enums.DelFlag;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 仅新增的Entity基类
 * <AUTHOR>
 */
@Data
public class BaseAddOnlyEntity implements IBaseCreateInfo {

    /** 创建者Id */
    @TableField(fill = FieldFill.INSERT)
    private Long createId;

    /** 创建者 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /** 创建部门 */
    @TableField(fill = FieldFill.INSERT)
    private String createDept;

    /** 删除标志（0代表存在 1代表删除） */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private DelFlag delFlag;

}
