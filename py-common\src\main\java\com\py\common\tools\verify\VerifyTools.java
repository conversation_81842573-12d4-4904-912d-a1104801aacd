package com.py.common.tools.verify;

/**
 * 验证工具
 * <AUTHOR>
 */
public class VerifyTools {

    /**
     * 长度校验
     * @param length 长度
     * @param min 最小值
     * @param max 最大值
     * @return 验证结果 true:验证通过  false:验证失败
     */
    public static boolean lengthVerify(int length, Integer min, Integer max) {
        if(min != null && max != null) {
            return length >= min && length <= max;
        } else if(min != null) {
            return length >= min;
        } else {
            return length <= max;
        }
    }
}
