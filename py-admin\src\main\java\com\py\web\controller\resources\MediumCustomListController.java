package com.py.web.controller.resources;

import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.resources.mediumcustomlist.domain.dto.MediumCustomListDTO;
import com.py.resources.mediumcustomlist.domain.query.MediumCustomListQuery;
import com.py.resources.mediumcustomlist.domain.vo.MediumCustomListVO;
import com.py.resources.mediumcustomlist.service.IMediumCustomListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 媒介资源管理-自定义列Controller
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Api(tags = "媒介资源管理-自定义列")
@RestController
@RequestMapping("/resources/mediumCustomList")
public class MediumCustomListController extends BaseController {

    /** 媒介资源管理-自定义列服务 */
    @Resource
    private IMediumCustomListService mediumCustomListService;

    /**
     * 自定义列详细信息
     * @return 自定义列视图模型
     * @param query 查询条件
     */
    @ApiOperation("获取登录人对应的自定义列表")
    @PreAuthorize("@ss.hasPermi('resources:mediumCustomList:query')")
    @GetMapping(value = "getInfoByUserId")
    public R<MediumCustomListVO> getInfoByUserId(MediumCustomListQuery query) {
        return R.success(mediumCustomListService.selectMediumCustomListById(query));
    }

    /**
     * 保存自定义列
     *
     * @param dto 自定义列修改参数
     * @return 是否成功
     */
    @ApiOperation("保存自定义列")
    @PreAuthorize("@ss.hasPermi('resources:mediumCustomList:add')")
    @Log(title = "媒介资源管理-自定义列", businessType = BusinessType.INSERT)
    @PostMapping("/saveCustomList")
    public R<Boolean> saveCustomList(@RequestBody MediumCustomListDTO dto) {
        return R.success(mediumCustomListService.saveCustomList(dto));
    }


}
