package com.py.common.core.domain.model;

import com.py.common.datascope.DataScopePageType;
import com.py.common.datascope.DataScopeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Past;
import java.time.LocalDate;
import java.util.List;

/**
 * 数据权限
 * <AUTHOR>
 */
@Data
@ApiModel("数据权限")
public class DataPermission {

    /** 权限类型 */
    @ApiModelProperty("权限类型")
    @NotNull(message = "权限类型不能为空")
    private DataScopeType permissionType;

    /** 权限页面ID列表 */
    @ApiModelProperty("权限页面ID列表")
    @NotEmpty(message = "权限页面ID列表不能为空")
    private List<DataScopePageType> pageIdList;

    /** 主体ID列表 */
    @ApiModelProperty("主体ID列表")
    private List<Long> mainstayIdList;

    /** 数据权限起始时间 */
    @ApiModelProperty("数据权限起始时间")
    @Past(message = "数据权限起始时间必须是过去的时间")
    private LocalDate startDate;
}
