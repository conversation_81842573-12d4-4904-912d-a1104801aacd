package com.py.crm.customer.customercontribute.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.customer.customercontribute.converter.ComparedDraftUserConverter;
import com.py.crm.customer.customercontribute.mapper.ComparedDraftUserMapper;
import com.py.crm.customer.customercontribute.domain.ComparedDraftUser;
import com.py.crm.customer.customercontribute.service.IComparedDraftUserService;
import com.py.system.user.service.ISysUserService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;

/**
 * 客户管理-比稿策划人关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@Service
public class ComparedDraftUserServiceImpl
        extends ServiceImpl<ComparedDraftUserMapper, ComparedDraftUser>
        implements IComparedDraftUserService{

    /** 客户管理-比稿策划人关联模型转换器 */
    @Resource
    private ComparedDraftUserConverter comparedDraftUserConverter;

    /** 客户管理-比稿策划人关联Mapper接口 */
    @Resource
    private ComparedDraftUserMapper comparedDraftUserMapper;

    /** 用户 业务层 */
    @Resource
    private ISysUserService sysUserService;


    /**
     * 新增客户管理-比稿策划人关联
     *
     * @param comparedDraftId 客户管理-比稿id
     * @param userIdList 客户管理-比稿策划人id
     * @return 是否成功
     */
    @Override
    public boolean insertComparedDraftUser(Long comparedDraftId,List<Long> userIdList) {
        List<ComparedDraftUser> comparedDraftUserList = assembleEntity(comparedDraftId, userIdList);
        return this.saveBatch(comparedDraftUserList);
    }

    /**
     * 组装新增参数
     * @param comparedDraftId 客户管理-比稿id
     * @param userIdList 客户管理-比稿策划人id
     * @return Entity
     */
    private List<ComparedDraftUser> assembleEntity(Long comparedDraftId, List<Long> userIdList) {
        List<ComparedDraftUser> comparedDraftUserList = new ArrayList<>();
        for(Long userId : userIdList) {
            ComparedDraftUser comparedDraftUser = new ComparedDraftUser();
            comparedDraftUser.setComparedDraftId(comparedDraftId);
            comparedDraftUser.setUserId(userId);
            comparedDraftUserList.add(comparedDraftUser);
        }
        sysUserService.relatedUserInfo(comparedDraftUserList, ComparedDraftUser::getUserId,
                ((comparedDraftUser, sysUser) ->
                        comparedDraftUser.setUserName(sysUser.getUserName())));
        return comparedDraftUserList;
    }

    /**
     * 批量修改客户管理-比稿策划人关联
     *
     * @param comparedDraftId 客户管理-比稿id
     * @param userIdList 客户管理-比稿策划人id
     * @return 是否成功
     */
    @Override
    public boolean updateComparedDraftUserByIds(Long comparedDraftId,List<Long> userIdList) {
        List<ComparedDraftUser> comparedDraftUserList = this.list(Wrappers.<ComparedDraftUser>lambdaQuery()
                .eq(ComparedDraftUser::getComparedDraftId, comparedDraftId));
        if(ListUtil.isNotEmpty(comparedDraftUserList)){
            List<ComparedDraftUser> removeComparedDraftUserList = new ArrayList<>();
            List<ComparedDraftUser> updateComparedDraftUserList = new ArrayList<>();
            comparedDraftUserList.forEach(comparedDraftUser -> {
                if(!userIdList.contains(comparedDraftUser.getUserId())){
                    removeComparedDraftUserList.add(comparedDraftUser);
                } else {
                    updateComparedDraftUserList.add(comparedDraftUser);
                }
                userIdList.remove(comparedDraftUser.getUserId());
            });
            if(ListUtil.isNotEmpty(removeComparedDraftUserList)){
                this.removeByIds(removeComparedDraftUserList);
            }
            if(ListUtil.isNotEmpty(updateComparedDraftUserList)){
                sysUserService.relatedUserInfo(updateComparedDraftUserList, ComparedDraftUser::getUserId,
                        ((comparedDraftUser, sysUser) ->
                                comparedDraftUser.setUserName(sysUser.getUserName())));
                this.updateBatchById(removeComparedDraftUserList);
            }
            if(ListUtil.isNotEmpty(userIdList)){
                List<ComparedDraftUser> comparedDraftUsers = assembleEntity(comparedDraftId, userIdList);
                this.saveBatch(comparedDraftUsers);
            }
            return true;
        } else {
            List<ComparedDraftUser> comparedDraftUsers = assembleEntity(comparedDraftId, userIdList);
            return this.saveBatch(comparedDraftUsers);
        }
    }

    /**
     * 根据策划人id查询比稿项目id
     * @param userIdList 策划人id
     * @return 比稿项目id
     */
    @Override
    public List<Long> listComparedDraftIdByUserIds(List<Long> userIdList,Integer pageNum,Integer pageSize) {
        return comparedDraftUserMapper.listComparedDraftIdByUserIds(userIdList,pageNum,pageSize);
    }

    /**
     * 根据比稿项目id查询策划人
     * @param comparedDraftIdList 比稿项目id
     * @return 策划人
     */
    @Override
    public List<ComparedDraftUser> listComparedDraftIds(List<Long> comparedDraftIdList) {
        if(ListUtil.isEmpty(comparedDraftIdList)){
            return ListUtil.emptyList();
        }
        return this.list(Wrappers.<ComparedDraftUser>lambdaQuery()
                .in(ComparedDraftUser::getComparedDraftId,comparedDraftIdList));
    }

    /**
     * 根据比稿项目id查询策划人
     * @param comparedDraftId 比稿项目id
     */
    @Override
    public void removeComparedDraftId(Long comparedDraftId) {
        remove(Wrappers.<ComparedDraftUser>lambdaQuery()
                .eq(ComparedDraftUser::getComparedDraftId,comparedDraftId));
    }
}
