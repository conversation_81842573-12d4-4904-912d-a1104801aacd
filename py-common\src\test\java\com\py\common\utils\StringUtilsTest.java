package com.py.common.utils;

import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;


/**
 * 字符串测试类
 * <AUTHOR>
 */
public class StringUtilsTest {

    /** 测试是否能验证输入的字符串是邮箱 */
    @Test
    public void can_verifyEmail(){
        String email = "<EMAIL>";
        assertTrue(StringUtils.isEmail(email));
    }

    /** 测试是否能验证输入的字符串不是邮箱 */
    @Test
    public void can_verifyEmail_not(){
        String email = "test@example";
        assertFalse(StringUtils.isEmail(email));
    }
}
