package com.py.common.enums.flow;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;

/**
 * 流程意见类型
 *
 * <AUTHOR>
 * @date 2023/7/14 10:56
 */
@AllArgsConstructor
public enum FlowCommentEnum implements IDict<Integer> {

    /**
     * 正常意见
     */
    NORMAL(1, "正常意见"),
    /**
     * 退回意见
     */
    REBACK(2, "退回意见"),
    /**
     * 驳回意见
     */
    REJECT(3, "驳回意见"),
    /**
     * 委派意见
     */
    DELEGATE(4, "委派意见"),
    /**
     * 转办意见
     */
    ASSIGN(5, "转办意见"),
    /**
     * 终止流程
     */
    STOP(6, "终止流程");

    /**
     * 类型
     */
    private final Integer value;

    private final String label;

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
