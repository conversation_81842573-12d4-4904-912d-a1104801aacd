<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.py.crm.compareddraft.mapper.ComparedDraftMapper">

    <resultMap type="com.py.crm.compareddraft.domain.ComparedDraft" id="ComparedDraftResult">
        <result property="id" column="id" />
        <result property="comparedDraftId" column="compared_draft_id" />
        <result property="customerId" column="customer_id" />
        <result property="customerName" column="customer_name" />
        <result property="customerCooperateId" column="customer_cooperate_id" />
        <result property="customerCooperateName" column="customer_cooperate_name" />
        <result property="brandName" column="brand_name" />
        <result property="categoryId" column="category_id"
                typeHandler="com.py.common.typehandler.impl.StringSetTypeHandler" />
        <result property="categoryName" column="category_name"
                typeHandler="com.py.common.typehandler.impl.StringSetTypeHandler" />
        <result property="brandStage" column="brand_stage" />
        <result property="collaborateDapt" column="collaborate_dapt"/>
        <result property="comparedDraftName" column="compared_draft_name" />
        <result property="commercialBidFruits" column="commercial_bid_fruits" />
        <result property="commercialBidFruits" column="commercial_bid_fruits" />
        <result property="techniqueBidFruits" column="technique_bid_fruits" />
        <result property="finalFruits" column="final_fruits" />
        <result property="comparedDraftTime" column="compared_draft_time" />
        <result property="enteringTime" column="entering_time" />
        <result property="comparedDraftMoney" column="compared_draft_money" />
        <result property="turnoverMoney" column="turnover_money" />
        <result property="pyType" column="py_type" />
        <result property="planType" column="plan_type" />
        <result property="comparedDraftPlan" column="compared_draft_plan"
                typeHandler="com.py.crm.compareddraft.typehandler.OssObjectInfoListTypeHandler" />
        <result property="strategyReview" column="strategy_review" />
        <result property="remarks" column="remarks" />
        <result property="createId" column="create_id" />
        <result property="createBy" column="create_by" />
        <result property="createDept" column="create_dept" />
        <result property="createTime" column="create_time" />
        <result property="updateId" column="update_id" />
        <result property="updateBy" column="update_by" />
        <result property="updateDept" column="update_dept" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
    </resultMap>

    <select id="amount" resultType="com.py.crm.compareddraft.domain.vo.ComparedDraftAmountVO">
        SELECT
            count(distinct draft.compared_draft_id ) as comparedDraftNum,
            count(distinct IF ( commercial_bid_fruits = 1, draft.compared_draft_id, NULL ) ) AS commercialBidSuccessNum,
            count(distinct IF ( technique_bid_fruits = 1, draft.compared_draft_id, NULL ) ) AS techniqueBidSuccessNum,
            count(distinct IF ( final_fruits = 1, draft.compared_draft_id, NULL ) ) AS finalSuccessNum
        FROM
            `py_crm_compared_draft` draft
            LEFT JOIN py_crm_wander_about wanderAbout ON draft.customer_id = wanderAbout.customer_id
            LEFT JOIN py_crm_customer customer ON draft.customer_id = customer.customer_id
        where
        draft.del_flag = 0
        AND wanderAbout.del_flag = 0
        and customer.audit_status = 1
        AND customer.del_flag = 0
        <if test="wrapper.isEmptyOfNormal() != true">
            And draft.customer_id in (select customer_id from py_crm_customer where <trim prefixOverrides="WHERE">${wrapper.customSqlSegment}</trim>)
        </if>
        <if test="wrapper.isEmptyOfNormal() != true">
            And draft.customer_id in (select customer_id from py_crm_customer where <trim prefixOverrides="WHERE">${wrapper.customSqlSegment}</trim>)
        </if>
        <if test="query.plotterList != null and query.plotterList.size() > 0">
            and draft.compared_draft_id in (Select Distinct compared_draft_id From py_crm_compared_draft_user Where del_flag = 0 and user_name in
            <foreach collection="query.plotterList" item="plotter" open="(" close=")" separator=",">
                #{plotter}
            </foreach>
            )
        </if>
        <if test="ew.isEmptyOfNormal() == true">
            ORDER BY draft.compared_draft_time DESC,draft.id DESC
        </if>
        <if test="ew.isEmptyOfNormal() != true">
            And <trim prefixOverrides="WHERE">${ew.customSqlSegment}</trim>
        </if>
    </select>
<!--  分页查询客户管理-比稿管理  -->
    <select id="listComparedDraft" resultMap="ComparedDraftResult">
        SELECT distinct
            draft.id,
        <include refid="comparedDraft"/>
        FROM
            `py_crm_compared_draft` draft
                LEFT JOIN py_crm_wander_about wanderAbout ON draft.customer_id = wanderAbout.customer_id
                LEFT JOIN py_crm_customer customer ON draft.customer_id = customer.customer_id
        WHERE
            draft.del_flag = 0
          AND wanderAbout.del_flag = 0
          AND customer.del_flag = 0
          AND customer.audit_status = 1
        <if test="wrapper.isEmptyOfNormal() != true">
            And draft.customer_id in (select customer_id from py_crm_customer where <trim prefixOverrides="WHERE">${wrapper.customSqlSegment}</trim>)
        </if>
        <if test="query.plotterList != null and query.plotterList.size() > 0">
            and draft.compared_draft_id in (Select Distinct compared_draft_id From py_crm_compared_draft_user Where del_flag = 0 and user_name in
                <foreach collection="query.plotterList" item="plotter" open="(" close=")" separator=",">
                    #{plotter}
                </foreach>
                )
        </if>
        <if test="ew.isEmptyOfNormal() == true">
            ORDER BY draft.compared_draft_time DESC,draft.id DESC
        </if>
        <if test="ew.isEmptyOfNormal() != true">
            And <trim prefixOverrides="WHERE">${ew.customSqlSegment}</trim>
        </if>
    </select>

    <!--获取所有的客户有比稿明细的客户id列表-->
    <select id="listExistedCustomerIds" resultType="java.lang.Long">
        SELECT
            distinct customer_id
        FROM
            py_crm_compared_draft
        where
            del_flag = 0
        AND customer_id IN
        <foreach collection="customerIds" item="customerId" open="(" close=")" separator=",">
            #{customerId}
        </foreach>
    </select>

    <!-- 比稿金额公共字段 -->
    <sql id="comparedDraft">
       draft.compared_draft_id, draft.customer_id, draft.customer_name,
       draft.customer_cooperate_id, draft.customer_cooperate_name,
       draft.brand_name, draft.category_id, draft.category_name,
       draft.brand_stage, draft.collaborate_dapt, draft.compared_draft_name,
       draft.commercial_bid_fruits, draft.technique_bid_fruits,
       draft.final_fruits, draft.compared_draft_time, draft.entering_time,
       draft.compared_draft_money, draft.turnover_money, draft.py_type,
       draft.plan_type, draft.compared_draft_plan, draft.strategy_review,
       draft.remarks, draft.create_id, draft.create_by, draft.create_time,
       draft.update_id, draft.update_by, draft.update_time,
       draft.del_flag, draft.create_dept, draft.update_dept
    </sql>

   <select id="listComparedDraftByContribute"  resultMap="ComparedDraftResult">
       SELECT
       draft.id,
       <include refid="comparedDraft"/>
       FROM
           `py_crm_compared_draft` draft
           JOIN py_project project ON draft.compared_draft_id = project.compared_draft_id
       WHERE
           draft.del_flag = '0'
       AND project.del_flag = '0'
       <!-- 合作时间 -->
       <if test="query.cooperateBeginDate != null and query.cooperateEndDate != null">
           AND project.project_approval_time &gt;= #{query.cooperateBeginDate}
           AND project.project_approval_time &lt;= #{query.cooperateEndDate}
       </if>
       <!-- 结案时间 -->
       <if test="query.closeCaseBeginDate != null and query.closeCaseEndDate != null">
           AND project.close_case_time &gt;= #{query.closeCaseBeginDate}
           AND project.close_case_time &lt;= #{query.closeCaseEndDate}
       </if>
       And <trim prefixOverrides="WHERE">
           ${ew.customSqlSegment}
          </trim>
   </select>

    <select id="listComparedDraftByCustomerId" resultType="com.py.crm.compareddraft.domain.ComparedDraft">
        SELECT
        draft.id,
        <include refid="comparedDraft"/>
        FROM
        `py_crm_compared_draft` draft
        WHERE   draft.del_flag = '0'
        And <trim prefixOverrides="WHERE">
        ${ew.customSqlSegment}
    </trim>
    </select>
</mapper>
