package com.py.flow.flowinstance.converter;

import com.py.common.core.domain.entity.SysUser;
import com.py.flow.domain.vo.ApprovalListVO;
import com.py.flow.flowinstance.domain.FlowInstance;
import com.py.flow.flowinstance.domain.vo.RecipientInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 流程实例转换器
 * <AUTHOR>
 * @date 2023/7/31 14:38
 */
@Mapper(componentModel = "spring")
public interface FlowInstanceConverter{



    /**
     * 将审批实例转换成审批列表VO
     * @param flowInstanceList 审批实例
     * @return 审批列表VO
     */
    List<ApprovalListVO> toApprovalListVo(List<FlowInstance> flowInstanceList);

    /**
     * 将审批实例转换成审批列表VO
     * @param flowInstance 审批实例
     * @return 审批列表VO
     */
    @Mapping(target = "launcherDept", source = "createDept")
    @Mapping(target = "launcher", source = "createBy")
    ApprovalListVO toApprovalListVo(FlowInstance flowInstance);

    /**
     * 将用户转换成抄送人信息VO
     * @param user 用户
     * @return 抄送人信息列表
     */
    @Mapping(target = "recipientId", source = "userId")
    RecipientInfoVO toRecipientInfoVo(SysUser user);
}
