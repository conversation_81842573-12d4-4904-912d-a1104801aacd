package com.py.common.tools.modifycomparator.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;

/**
 * 比较字段类型
 * <AUTHOR>
 */
@AllArgsConstructor
public enum CompareFieldType implements IDict<Integer> {

    /** 简单字段 */
    Simple(0, "简单字段"),

    /** 对象列表字段 */
    ObjectList(1, "对象列表字段"),

    /** 对象字段 */
    Object(2, "对象字段");

    private final Integer value;
    private final String label;

    @Override
    public String getLabel() {
        return this.label;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
