package com.py.common.tools.multisheetexcelexporter;

import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.common.utils.file.AutoDeleteFileOutputStream;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 提供导出多个sheet的Excel文件的类
 * <AUTHOR>
 */
public class MultiSheetExcelExporter implements IMultiSheetExcelExporter {

    private static final Logger log = LoggerFactory.getLogger(MultiSheetExcelExporter.class);

    /** 导出配置列表 */
    private final List<MultiSheetExcelExportConfigItem<?>> configList = new LinkedList<>();

    /** 需要移除的工作表配置 */
    private final List<String> needRemoveSheetConfig = new ArrayList<>();

    /** 导出的Excel的文件名 */
    private String excelName = "";

    /** 是否为导入到既有Excel中 */
    private boolean isExportToExistExcel = false;

    /**
     * 导出至指定响应
     * @param response 目标响应
     * @param workbook 导入的目标工作薄
     * @throws IOException 导入IO异常
     */
    private void exportExcel(HttpServletResponse response, Workbook workbook) throws IOException {
        this.fillWorkbook(workbook);
        try(OutputStream outputStream = response.getOutputStream()) {
            workbook.setForceFormulaRecalculation(true);
            workbook.write(outputStream);
        }
    }

    /**
     * 导出至指定响应
     * @param workbook 导入的目标工作薄
     * @return Excel字节数组
     * @throws IOException 导入IO异常
     */
    private byte[] exportExcel(Workbook workbook) throws IOException {
        this.fillWorkbook(workbook);

        try(ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.setForceFormulaRecalculation(true);
            workbook.write(outputStream);

            return outputStream.toByteArray();
        }
    }

    /**
     * 导出至指定响应
     * @param workbook 导入的目标工作薄
     * @return Excel字节数组
     * @throws IOException 导入IO异常
     */
    private InputStream exportExcelToStream(Workbook workbook) throws IOException {
        this.fillWorkbook(workbook);

        File tempFile = File.createTempFile("temp", ".xlsx");
        try(FileOutputStream outputStream = new FileOutputStream(tempFile)) {
            workbook.setForceFormulaRecalculation(true);
            workbook.write(outputStream);
        }
        return new AutoDeleteFileOutputStream(tempFile);
    }

    /**
     * 添加工作表
     * @param workbook 需填充的工作表
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    private void fillWorkbook(Workbook workbook) {
        Map<String, CellStyle> styles = createStyles(workbook);

        if(this.isExportToExistExcel && ListUtil.any(this.needRemoveSheetConfig)){
            for(String sheetName : this.needRemoveSheetConfig){
                int sheetIndex = workbook.getSheetIndex(sheetName);
                if(sheetIndex != -1){
                    workbook.removeSheetAt(sheetIndex);
                }
            }
        }

        int sheetNo = workbook.getNumberOfSheets();
        for(MultiSheetExcelExportConfigItem configItem : this.configList) {
            Sheet sheet = workbook.getSheet(configItem.getSheetName());
            if(sheet == null) {
                sheet = workbook.createSheet();
                try {
                    workbook.setSheetName(sheetNo++, configItem.getSheetName());
                } catch (Exception ex) {
                    log.error("添加工作表失败", ex);
                }
            }
            if(configItem.getConfig().hasRemoveRowConfig()){
                this.removeConfigRow(sheet, configItem.getConfig());
            }

            ExcelSheetDataFiller<?> filler = new ExcelSheetDataFiller<>();
            filler.fillSheet(sheet, workbook, configItem, styles);
        }
    }

    /**
     * 移除配置行
     * @param sheet 工作表
     * @param config 配置
     */
    private <T> void removeConfigRow(Sheet sheet, ExcelSheetConfig<T> config) {
        Assert.isTrue(this.isExportToExistExcel, "仅当写入既有Excel时才能移除配置行");

        // 配置需移除集合时, 移除配置行
        if(ListUtil.any(config.getRemoveRowSet())){
            List<Integer> removerRowSet = config.getRemoveRowSet().stream()
                    .sorted(Comparator.comparing(Integer::intValue).reversed())
                    .collect(Collectors.toList());
            for(int rowNum : removerRowSet){
                Row row = sheet.getRow(rowNum);
                if(row != null){
                    sheet.removeRow(row);
                }
            }
        }

        // 配置需保留集合时, 移除非保留行
        if(ListUtil.any(config.getNotRemoveRowSet())){
            int lastRowNum = sheet.getLastRowNum();
            for(int rowNum = lastRowNum; rowNum >= config.getExistHeaderRowNo(); rowNum--) {
                if(config.getNotRemoveRowSet().contains(rowNum)){
                    continue;
                }
                Row row = sheet.getRow(rowNum);
                if(row != null){
                    int startShiftRow = Math.min(rowNum + 1, lastRowNum + 1);
                    int endShiftRow = lastRowNum + 2;
                    sheet.shiftRows(startShiftRow, endShiftRow, -1);

                }
            }
        }
    }

    /**
     * 添加需要移除的sheet配置
     * <p>sheet将在导出时被移除, 仅当是导入到既有Excel中时生效</p>
     * @param sheetName 需移除的sheet名
     */
    public void addRemoveSheetConfig(String sheetName) {
        Assert.hasText(sheetName, "需移除的工作表名不能为空");
        sheetName = StringUtils.replaceWindowsFileSystemDisableChar(sheetName, "_");
        if(this.needRemoveSheetConfig.contains(sheetName)){
            return;
        }
        this.needRemoveSheetConfig.add(sheetName);
    }


    /**
     * 添加 sheet 配置
     * @param list 导出数据源
     * @param config 导出配置
     * @param sheetName 导出sheet名
     */
    @Override
    public <T> void addSheetConfig(List<T> list, ExcelSheetConfig<T> config, String sheetName) {
        sheetName = StringUtils.replaceWindowsFileSystemDisableChar(sheetName, "_");
        this.configList.add(new MultiSheetExcelExportConfigItem<>(list, config, sheetName));
    }

    /**
     * 导出至指定响应
     * @param response 导出Excel返回的http响应
     * @param excelName 导出Excel返回的文件名
     */
    @Override
    public void exportExcel(HttpServletResponse response, String excelName) {
        Assert.isTrue(ListUtil.any(this.configList), "必须添加Sheet配置才能导出Excel");
        this.isExportToExistExcel = false;

        this.excelName = excelName;
        try(Workbook workbook = new SXSSFWorkbook(500)) {
            this.setResponseResultFormat(response);
            this.exportExcel(response, workbook);
        } catch(IOException e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new RuntimeException("导出Excel失败，请联系网站管理员！");
        }
    }

    /**
     * 导出至指定响应并转换为byte
     * @param excelName 导出Excel返回的文件名
     * @return byte数组
     */
    @Override
    public byte[] exportExcelToByte(String excelName) {
        this.isExportToExistExcel = false;
        try(Workbook workbook = new SXSSFWorkbook()){
            return this.exportExcel(workbook);
        }catch (Exception e){
            log.error("导出Excel异常： ", e);
            throw new RuntimeException("导出Excel失败，请联系网站管理员！");
        }
    }

    /**
     * 导出至指定响应并转换为byte
     * @return byte数组
     */
    @Override
    public InputStream exportExcelToStream() {
        this.isExportToExistExcel = false;
        try(Workbook workbook = new SXSSFWorkbook()){
            return this.exportExcelToStream(workbook);
        }catch (Exception e){
            log.error("导出Excel异常： ", e);
            throw new RuntimeException("导出Excel失败，请联系网站管理员！");
        }
    }

    /**
     * 写入既有Excel然后导出至指定响应
     * @param response 导出Excel返回的http响应
     * @param excelInputStream Excel输入流
     * @param excelName 导出Excel返回的文件名
     */
    @Override
    public void exportExcel(HttpServletResponse response, InputStream excelInputStream, String excelName) {
        this.excelName = excelName;
        this.isExportToExistExcel = true;
        try(Workbook workbook = WorkbookFactory.create(excelInputStream)) {
            this.setResponseResultFormat(response);
            this.exportExcel(response, workbook);
        } catch(IOException e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new RuntimeException("导出Excel失败，请联系网站管理员！");
        }
    }

    /**
     * 写入既有Excel然后导出字节数组
     * @param excelInputStream Excel输入流
     * @return Excel字节数组
     */
    @Override
    public byte[] exportExcel(InputStream excelInputStream) {
        this.isExportToExistExcel = true;
        try(Workbook workbook = WorkbookFactory.create(excelInputStream)) {
            return this.exportExcel(workbook);
        } catch(IOException e) {
            log.error("导出Excel异常{}", e.getMessage());
            throw new RuntimeException("导出Excel失败，请联系网站管理员！");
        }
    }

    /**
     * 创建表格样式
     * @param wb 工作薄对象
     * @return 样式列表
     */
    private Map<String, CellStyle> createStyles(Workbook wb) {
        // 写入各条记录,每条记录对应excel表中的一行
        Map<String, CellStyle> styles = new HashMap<>(16);
        CellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        styles.put("data", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        dataFont = wb.createFont();
        dataFont.setFontName("Arial");
        dataFont.setFontHeightInPoints((short) 10);
        style.setFont(dataFont);
        DataFormat format = wb.createDataFormat();
        style.setDataFormat(format.getFormat("yyyy-MM-dd"));
        styles.put("Date", style);

        style = wb.createCellStyle();
        style.cloneStyleFrom(styles.get("data"));
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setFillBackgroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        Font headerFont = wb.createFont();
        headerFont.setFontName("Arial");
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setBold(true);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(headerFont);
        styles.put("header", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font totalFont = wb.createFont();
        totalFont.setFontName("Arial");
        totalFont.setFontHeightInPoints((short) 10);
        style.setFont(totalFont);
        styles.put("total", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderRight(BorderStyle.THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(BorderStyle.THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(BorderStyle.THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setFont(dataFont);
        styles.put("cellStyle", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setFontName("Arial");
        titleFont.setFontHeightInPoints((short) 24);
        style.setFont(titleFont);
        styles.put("title", style);

        style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Font subTitleFont = wb.createFont();
        subTitleFont.setFontName("Arial");
        subTitleFont.setFontHeightInPoints((short) 10);
        style.setFont(subTitleFont);
        style.setFillForegroundColor(IndexedColors.BLACK.getIndex());
        styles.put("subTitle", style);

        return styles;
    }

    /**
     * 配置返回流格式
     * @param response 需配置返回的响应实例
     */
    private void setResponseResultFormat(HttpServletResponse response) throws UnsupportedEncodingException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(excelName, "UTF-8"));
    }
}
