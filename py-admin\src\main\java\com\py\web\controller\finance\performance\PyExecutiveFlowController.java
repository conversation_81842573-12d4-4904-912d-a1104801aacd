package com.py.web.controller.finance.performance;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.PageUtils;
import com.py.common.utils.SecurityUtils;
import com.py.project.pyexecutiveflow.asynctask.ExecutivePerformanceServiceImpl;
import com.py.project.pyexecutiveflow.asynctask.ExecutiveResourceServiceImpl;
import com.py.project.pyexecutiveflow.domain.query.ExecutiveUpperSideQuery;
import com.py.project.pyexecutiveflow.domain.query.PyExecutiveCheckBoxQuery;
import com.py.project.pyexecutiveflow.domain.query.PyExecutiveFlowQuery;
import com.py.project.pyexecutiveflow.domain.query.PyExecutiveResourceInfoQuery;
import com.py.project.pyexecutiveflow.domain.vo.*;
import com.py.project.pyexecutiveflow.service.IPyExecutiveFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 执行人员业绩Controller
 *
 * <AUTHOR>
 * @date 2023-08-01
 */
@Api(tags = "执行人员业绩")
@RestController
@RequestMapping("/project/pyExecutiveFlow")
public class PyExecutiveFlowController extends BaseController {

    /** 执行人员业绩服务 */
    @Resource
    private IPyExecutiveFlowService pyExecutiveFlowService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 分页查询执行人员业绩列表
     *
     * @param pyExecutiveFlowQuery 执行人员业绩查询参数
     * @return 执行人员业绩分页
     */
    @ApiOperation("分页查询执行人员业绩列表")
    @PreAuthorize("@ss.hasPermi('project:pyExecutiveFlow:list')")
    @PostMapping("/pagePyExecutiveFlow")
    public R<PageInfo<PyExecutiveFlowListPageVO>> pagePyExecutiveFlow(@RequestBody PyExecutiveFlowQuery pyExecutiveFlowQuery) {
        PageInfo<PyExecutiveFlowListPageVO> voList = this.pyExecutiveFlowService.selectPageVO(pyExecutiveFlowQuery);
        return R.success(voList);
    }

    /**
     * 查询执行人员业绩列表,部门
     *
     * @param pyExecutiveFlowQuery 执行人员业绩查询参数
     * @return 执行人员业绩,部门
     */
    @ApiOperation("分页查询执行人员业绩列表")
    @PostMapping("/findPyExecutiveFlowDept")
    public R<List<String>> findPyExecutiveFlowDept(@RequestBody PyExecutiveFlowQuery pyExecutiveFlowQuery) {
        return R.success(this.pyExecutiveFlowService.findPyExecutiveFlowDept(pyExecutiveFlowQuery));
    }

    /**
     * 获得上侧数据
     * @param executiveUpperSideQuery
     * @return
     */
    @ApiOperation("获得明细上侧数据")
    @PostMapping("/getUpperSide")
    public R <PyExecutiveFlowListPageVO>  getUpperSide(@RequestBody ExecutiveUpperSideQuery executiveUpperSideQuery ){
        PyExecutiveFlowListPageVO pyExecutiveFlowVO = this.pyExecutiveFlowService.getUpperSide(executiveUpperSideQuery);
        return R.success(pyExecutiveFlowVO);
    }

    /**
     * 查看执行人员资源明细
     * @param query
     * @return
     */
    @ApiOperation("查看执行人员资源明细")
    @PreAuthorize("@ss.hasPermi('project:pyExecutiveFlow:list')")
    @PostMapping("/pyExecutiveFlowInfo")
    public R<PageInfo<PyExecutiveFlowVO>> executiveInfo(@RequestBody PyExecutiveResourceInfoQuery query ){
        PageUtils.startPage();
        PageInfo<PyExecutiveFlowVO> pyExecutiveFlowVoList = this.pyExecutiveFlowService.resourceInfoPage(query);
        return R.success(pyExecutiveFlowVoList);
    }

    /**
     * 导出执行人员资源明细
     * @param query 查询参数
     */
    @ApiOperation("导出执行人员资源明细")
    @PreAuthorize("@ss.hasPermi('project:pyExecutiveFlow:export')")
    @Log(title = "执行人员业绩", businessType = BusinessType.EXPORT)
    @PostMapping("/exportResourceInfo")
    public R<String> export(@RequestBody PyExecutiveResourceInfoQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("执行人员业绩资源信息数据", TaskType.Export, query, ExecutiveResourceServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 执行人员业绩未通过改为通过
     *
     * @param addPerformanceCommonDTO 执行人员业绩未通过改为通过
     * @return 是否成功
     */
    @ApiOperation("执行人员业绩未通过改为通过")
    @PreAuthorize("@ss.hasPermi('project:pyExecutiveFlow:add')")
    @Log(title = "添加管理与销售人员业绩", businessType = BusinessType.INSERT)
    @PostMapping("/failToPass")
    public R<Boolean> failToPass(@RequestBody AddPyExecutiveFlowDTO addPerformanceCommonDTO) {
        return R.success(this.pyExecutiveFlowService.addExecutiveFlowByConfirmIncomeResource(addPerformanceCommonDTO));
    }

    /**
     * 执行人员业绩通过改为未通过
     *
     * @param pyExecutiveFlowToFailDTO 执行人员业绩新增付流水参数
     * @return 是否成功
     */
    @ApiOperation("执行人员业绩通过改为未通过")
    @PreAuthorize("@ss.hasPermi('project:pyExecutiveFlow:edit')")
    @Log(title = "执行人员业绩", businessType = BusinessType.UPDATE)
    @PostMapping("/passToFail")
    public R<Boolean> passToFail(@RequestBody PyExecutiveFlowToFailDTO pyExecutiveFlowToFailDTO) {
        return R.success(pyExecutiveFlowService.pyExecutiveFlowToFail(pyExecutiveFlowToFailDTO));
    }

    /**
     * 合计全选
     *
     * @param pyExecutiveCheckBoxQuery
     * @return 是否成功
     */
    @ApiOperation("合计全选")
    @PreAuthorize("@ss.hasPermi('project:pyExecutiveFlow:edit')")
    @Log(title = "执行人员业绩", businessType = BusinessType.UPDATE)
    @PostMapping("/checkBox")
    public R<PyExecutiveCheckBoxVO> checkBox(@RequestBody PyExecutiveCheckBoxQuery pyExecutiveCheckBoxQuery) {
        return R.success(pyExecutiveFlowService.checkBoxQuery(pyExecutiveCheckBoxQuery));
    }

    /**
     * 导出执行人员业绩
     * @param query 导出查询参数
     */
    @ApiOperation("导出执行人员业绩")
    @PreAuthorize("@ss.hasPermi('project:pyExecutiveFlow:export')")
    @Log(title = "执行人员业绩", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export( @RequestBody PyExecutiveCheckBoxQuery query) {
        String fileName = "执行人员业绩列表数据-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("执行人员业绩信息数据", TaskType.Export,query, ExecutivePerformanceServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 获取人员下拉列表
     * @return
     */
    @ApiOperation("获取人员下拉列表")
    @GetMapping("/listSelectedUserName")
    public R<List<String>> listSelectedUserName() {
        return R.success(this.pyExecutiveFlowService.listSelectedUserName());
    }

    /**
     * 将老数据的修改记录ID补充
     * @return
     */
    @ApiOperation("将老数据的修改记录ID补充")
    @GetMapping("/synchronousUpdateOperationRecord")
    public R<Boolean> synchronousUpdateOperationRecord() {
        return R.success(this.pyExecutiveFlowService.synchronousUpdateOperationRecord());
    }


    /**
     * 修复空数据-部门数据
     * @return 是否成功
     */
    @ApiOperation("修复空数据-部门数据")
    @GetMapping("/repairEmptyData")
    public R<Boolean> repairEmptyData() {
        this.pyExecutiveFlowService.fixEmptyDeptData();
        return R.success();
    }

    /**
     * 修复空数据-派芽合作主体id
     * @return 是否成功
     */
    @ApiOperation("修复空数据-派芽合作主体id")
    @GetMapping("/repairEmptyData/collaborators")
    public R<Boolean> repairEmptyDataCollaborators() {
        this.pyExecutiveFlowService.repairEmptyDataCollaborators();
        return R.success();
    }
}
