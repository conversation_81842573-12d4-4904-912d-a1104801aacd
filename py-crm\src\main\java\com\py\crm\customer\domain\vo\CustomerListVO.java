package com.py.crm.customer.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.core.domain.vo.BaseUpdateInfoVO;
import com.py.common.tools.modifycomparator.annotation.CompareField;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryBaseInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;

/**
 * 客户管理-客户列表视图模型
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("客户管理-客户列表视图模型")
public class CustomerListVO extends BaseUpdateInfoVO implements CustomerLaundryBaseInfo {
    private static final long serialVersionUID = 1L;

    /** 合作状态(0.合作中 1.暂停合作 2.意向合作)*/
    @ApiModelProperty("合作状态(0.合作中 1.暂停合作 2.意向合作)")
    private Integer cooperationStatus;

    /** 创建者*/
    @ApiModelProperty("创建者")
    private String createBy;

    /** 创建人部门*/
    @ApiModelProperty("创建人部门")
    private String createDept;

    /** 创建者Id*/
    @ApiModelProperty("创建者Id")
    private Long createId;

    /** 创建时间*/
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 行业类目id*/
    @ApiModelProperty("行业类目id")
    private List<Long> industryCategoryIdList;

    /** 品牌/业务线*/
    @ApiModelProperty("品牌/业务线")
    private String lineBusiness;

    /** 客户名称*/
    @ApiModelProperty("客户名称")
    private String name;

    /** 目标服务人员id*/
    @ApiModelProperty("目标服务人员id")
    private List<Long> serviceUserIdList;

    /** 目标服务人员 */
    @ApiModelProperty("目标服务人员")
    private String serviceUserName;
    private List<String> serviceUserNameList;

    /** 更新时间*/
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 版本*/
    @ApiModelProperty("版本")
    private Integer version;

    /** 品牌阶段(0.初创品牌 1.成长期品牌 2.成熟品牌)*/
    @ApiModelProperty("品牌阶段(0.初创品牌 1.成长期品牌 2.成熟品牌)")
    private Integer brandStage;

    /** 合作部门*/
    @ApiModelProperty("合作部门")
    private String cooperativeSector;

    /** 合作主体名称*/
    @ApiModelProperty("合作主体名称")
    private String mainstayName;

    /** 合作主体id*/
    @ApiModelProperty("合作主体id")
    private Long cooperateMainstayId;

    /** 是否有所有权限 true 所有 */
    @ApiModelProperty("是否有所有权限 true 所有 ")
    private Boolean isQuery;
}
