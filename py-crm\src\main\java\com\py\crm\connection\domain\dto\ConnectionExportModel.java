package com.py.crm.connection.domain.dto;

import com.py.common.tools.poiexcel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 人脉管理表导出模型
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
@ApiModel("人脉管理表导出模型" )
public class ConnectionExportModel {
    private static final long serialVersionUID = 1L;

    /** 自增id */
    @Excel(name = "更新人部门")
    @ApiModelProperty("自增id" )
    private Long id;

    /** 人脉Id */
    @Excel(name = "人脉Id")
    @ApiModelProperty("人脉Id" )
    private Long connectionId;

    /** 客户名称 */
    @Excel(name = "客户名称")
    @ApiModelProperty("客户名称" )
    private String connectionName;

    /** 状态（0无效；1有效） */
    @Excel(name = "状态")
    @ApiModelProperty("状态（0无效；1有效）" )
    private Integer status;

    /** 电话 */
    @Excel(name = "电话")
    @ApiModelProperty("电话" )
    private String phone;

    /** 微信 */
    @Excel(name = "微信")
    @ApiModelProperty("微信" )
    private String wechatNumber;

    /** 钉钉/飞书/其它 */
    @Excel(name = "钉钉/飞书/其它")
    @ApiModelProperty("钉钉/飞书/其它" )
    private String otherNumber;

    /** 现任职企业id(客户id) */
    @Excel(name = "现任职企业id(客户id)")
    @ApiModelProperty("现任职企业id(客户id)" )
    private Long customerId;

    /** 现任职企业（手动输入） */
    @Excel(name = "现任职企业")
    @ApiModelProperty("现任职企业（手动输入）" )
    private String currentEmployer;

    /** 负责品牌/业务线 */
    @Excel(name = "负责品牌/业务线")
    @ApiModelProperty("负责品牌/业务线" )
    private List<String> responsibleBrand;

    /** 行业类目(来源于数据字典) */
    @Excel(name = "行业类目(来源于数据字典)",isDictList=true,dictType="industry_category")
    @ApiModelProperty("行业类目(来源于数据字典)" )
    private List<Long> industryCategory;

    /** 所在部门 */
    @Excel(name = "所在部门")
    @ApiModelProperty("所在部门" )
    private String departmentName;

    /** 岗位名称 */
    @Excel(name = "岗位名称")
    @ApiModelProperty("岗位名称" )
    private String postName;

    /** 对派芽信任度（数据字典） */
    @Excel(name = "对派芽信任度")
    @ApiModelProperty("对派芽信任度（数据字典）" )
    private String pyTrustLevel;

    /** 人脉地址 */
    @Excel(name = "人脉地址")
    @ApiModelProperty("人脉地址" )
    private String connectionAddress;

    /** 目标服务人员id */
    @Excel(name = "目标服务人员id")
    @ApiModelProperty("目标服务人员id" )
    private Long serviceUserId;

    /** 创建人部门 */
    @Excel(name = "创建人部门")
    @ApiModelProperty("创建人部门" )
    private String createDept;

    /** 更新人部门 */
    @Excel(name = "更新人部门")
    @ApiModelProperty("更新人部门" )
    private String updateDept;


}
