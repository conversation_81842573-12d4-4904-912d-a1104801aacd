package com.py.web.controller.finance;

import com.github.pagehelper.PageInfo;
import com.py.common.core.domain.R;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.SecurityUtils;
import com.py.project.pyexecutiveflow.asynctask.BusinessFinalcialDeptInfoServiceImpl;
import com.py.project.pyexecutiveflow.asynctask.BusinessFinalcialDeptServiceImpl;
import com.py.project.pyexecutiveflow.domain.query.PyFinancialStatementQuery;
import com.py.project.pyexecutiveflow.domain.vo.PyFinancialInfoCheckBoxVO;
import com.py.project.pyexecutiveflow.domain.vo.PyFinancialPerformanceVO;
import com.py.project.pyexecutiveflow.service.IPyBusinessExecutiveFlowService;
import com.py.system.mainstayparam.domain.vo.ISystemMainstayVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 业务财报(快照逻辑)
 * <AUTHOR>
 */
@Api(tags = "业务财报")
@RestController
@RequestMapping("/project/business/financial")
public class ProjectBusinessFinancialController {


    /**
     * 执行人员业绩(业务财报,快照)service
     */
    @Resource
    private IPyBusinessExecutiveFlowService iPyBusinessExecutiveFlowService;


    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;


    /**
     * 业务财报列表(快照)
     * @param query 查询参数
     * @return 业务财报数据
     */
    @ApiOperation("业务财报列表(快照)")
    @PreAuthorize("@ss.hasPermi('projectrebate::list')")
    @PostMapping("/page")
    public R<PyFinancialPerformanceVO> pageBusinessFinancial(@RequestBody PyFinancialStatementQuery query){
        PyFinancialPerformanceVO pyFinancialPerformanceVO= this.iPyBusinessExecutiveFlowService.pageBusinessFinancial(query);
        return R.success(pyFinancialPerformanceVO);
    }

    /**
     * 分页查询业务财报明细(快照)
     *
     * @param query 业务财报查询参数
     * @return 业务财报列表
     */
    @ApiOperation("分页查询业务财报明细")
    @PreAuthorize("@ss.hasPermi('projectrebate::list')")
    @PostMapping("/page/detail")
    public R<PageInfo<ISystemMainstayVO>> pageBusinessFinancialDetail(@RequestBody PyFinancialStatementQuery query) {
        return R.success(this.iPyBusinessExecutiveFlowService.pageBusinessFinancialDetail(query,true));
    }


    /**
     * 业务财报明细金额合计(快照部门)
     * @param query 业务财报查询参数
     * @return 业务财报明细勾选合计数据
     */
    @ApiOperation("业务财报明细金额合计")
    @PreAuthorize("@ss.hasPermi('projectrebate::list')")
    @PostMapping("/checkBox")
    public R<PyFinancialInfoCheckBoxVO> pageBusinessFinancialDetailCheckBox(@RequestBody PyFinancialStatementQuery query) {
        return R.success(this.iPyBusinessExecutiveFlowService.pageBusinessFinancialDetailCheckBox(query));
    }


    /**
     * 导出业务财报(快照部门)
     *
     * @param query 查询勾选导出
     * @return 操作结果
     */
    @ApiOperation("导出业务财报(部门快照)")
    @PostMapping("/export")
    public R<String> pageBusinessFinancialExport(@RequestBody PyFinancialStatementQuery query){
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("财务报表数据", TaskType.Export, query, BusinessFinalcialDeptServiceImpl.class);
        return R.success("提交成功");
    }


    /**
     * 导出业务财报列表(快照部门)
     *
     * @param query 查询勾选导出
     * @return 操作结果
     */
    @ApiOperation("导出业务财报统计(部门快照)")
    @PostMapping("/page/export")
    public R<String> businessFinancialExport(@RequestBody PyFinancialStatementQuery query){
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("财务报表数据", TaskType.Export, query, BusinessFinalcialDeptInfoServiceImpl.class);
        return R.success("提交成功");
    }
}
