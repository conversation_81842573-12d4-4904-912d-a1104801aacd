package com.py.common.tools.verify.verifypackage;

import com.py.common.utils.collection.ListUtil;
import lombok.Data;

import java.util.Set;

/**
 * 数据范围验证参数包
 * <AUTHOR>
 * */
@Data
public class DataRangeVerifyPackage<T> {

    /** 需校验的数据 */
    private T value;

    /** 数据范围 */
    private Set<T> rangeSet;

    public static <T> DataRangeVerifyPackage<T> valueOf(T value, Set<T> rangeSet) {
        DataRangeVerifyPackage<T> verifyPackage = new DataRangeVerifyPackage<>();
        verifyPackage.value = value;
        verifyPackage.rangeSet = ListUtil.any(rangeSet) ? rangeSet : ListUtil.emptySet();
        return verifyPackage;
    }
}
