package com.py.crm.customer.invoicing.mapper;

import com.py.common.mybatisplus.SuperMapper;
import com.py.crm.customer.invoicing.domain.Invoicing;
import org.apache.ibatis.annotations.Param;

/**
 * 客户管理-客户-开票信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
public interface InvoicingMapper extends SuperMapper<Invoicing> {

    /**
     * 获取id
     * @param customerId 客户id
     * @return id
     */
    Integer getShowId(@Param("customerId") Long customerId);
}
