package com.py.common.utils;

import com.py.common.utils.collection.ListUtil;

import java.util.List;
import java.util.function.Function;

/**
 * null合并工具类
 * <AUTHOR>
 */
public class NullMergeUtils {
    /**
     * 模仿C# null合并运算符 ?.
     * <p> 当值为null时返回null
     * <p> 不为null时返回表达式结果
     * @param value 值
     * @param selector 结果选择器
     * @param <T> 入参类型
     * @param <R> 返回值类型
     * @return 结果
     */
    public static <T, R> R nullMerge(T value, Function<T, R> selector) {
        return value == null ? null : selector.apply(value);
    }

    /**
     * 模仿C# null合并运算符 ?? throws
     * <p> 当值为null时抛异常
     * <p> 不为null时返回表达式结果
     * @param value 值
     * @param e 要抛出的异常
     * @param <T> 入参类型
     * @return 结果
     */
    public static <T> T nullMerge(T value, Exception e) throws Exception {
        if(value == null){
            throw e;
        }

        return value;
    }

    /**
     * 模仿C# null合并运算符 ?.
     * <p> 当值为null时返回null
     * <p> 不为null时返回默认结果
     * @param value 值
     * @param selector 结果选择器
     * @param defaults 默认结果
     * @param <T> 入参类型
     * @param <R> 返回值类型
     * @return 结果
     */
    public static <T, R> R nullMerge(T value, Function<T, R> selector, R defaults) {
        return value == null ? defaults : selector.apply(value);
    }


    /**
     * 模仿C# null合并运算符 ??
     * <p> 当值为null时返回默认结果
     * <p> 不为null时值
     * @param value 值
     * @param defaults 默认结果
     * @param <T> 入参类型
     * @return 结果
     */
    public static <T> T nullMerge(T value, T defaults) {
        return value == null ? defaults : value;
    }

    /**
     * 字符串为空时返回默认值, 否则返回自身
     * @param value 字符串
     * @param defaults 默认值
     * @return 结果
     */
    public static String blankMerge(String value, String defaults) {
        return StringUtils.isBlank(value) ? defaults : value;
    }

    /**
     * 模仿C# null合并运算符 ?.
     * <p> 当值为空串时返回默认值
     * <p> 不为null时返回结果选择器处理的结果
     * @param value 值
     * @param selector 结果选择器
     * @param defaults 默认结果
     * @param <R> 返回值类型
     * @return 结果
     */
    public static <R> R blankMerge(String value, Function<String, R> selector, R defaults) {
        return StringUtils.isBlank(value) ? defaults : selector.apply(value);
    }

    /**
     * 模仿C# null合并运算符 ?.
     * <p> 当值为空串时返回默认值
     * <p> 不为null时返回结果选择器处理的结果
     * @param value 值
     * @param selector 结果选择器
     * @param <R> 返回值类型
     * @return 结果
     */
    public static <R> R blankMerge(String value, Function<String, R> selector) {
        return StringUtils.isBlank(value) ? null : selector.apply(value);
    }

    /**
     * 集合为空时返回默认值, 否则返回自身
     * @param value 集合
     * @param defaults 默认值
     * @return 结果
     */
    public static <T> List<T> emptyMerge(List<T> value, List<T> defaults) {
        return ListUtil.isEmpty(value) ? defaults : value;
    }

    /**
     * 模仿C# null合并运算符 ?.
     * <p> 当值为空串时返回默认值
     * <p> 不为null时返回结果选择器处理的结果
     * @param value 值
     * @param selector 结果选择器
     * @param defaults 默认结果
     * @param <R> 返回值类型
     * @return 结果
     */
    public static <T, R> R emptyMerge(List<T> value, Function<List<T>, R> selector, R defaults) {
        return StringUtils.isEmpty(value) ? defaults : selector.apply(value);
    }
}
