package com.py.common.approve;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目退款视图模型
 */
@Data
public class ProjectRefundInfoDTO {

    /** 业务id */
    @ApiModelProperty("业务id")
    private Long bizId;

    /** 项目id */
    @ApiModelProperty("项目id")
    private Long projectId;

    /** 项目名称 */
    @ApiModelProperty("项目名称")
    private String projectName;

    /** 派芽合作主体名称 */
    @ApiModelProperty("派芽合作主体名称" )
    private String pyMainstayName;

    /**  发起人 */
    @ApiModelProperty(" 发起人")
    private String createBy;

    /**  发起部门 */
    @ApiModelProperty(" 发起部门")
    private String createDept;

    /**  发起时间 */
    @ApiModelProperty(" 发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**  备注 */
    @ApiModelProperty(" 备注")
    private String remark;

    @ApiModelProperty("执行表单信息")
    private List<RefundInfoListDTO> refundInfoListVOList;
}
