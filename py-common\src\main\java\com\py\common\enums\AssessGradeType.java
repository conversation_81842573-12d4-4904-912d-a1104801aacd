package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 评价等级
 * <AUTHOR>
 * @version AssessGradeType 2024/1/30 15:25
 */
@AllArgsConstructor
public enum AssessGradeType implements IDict<String> {

    /** 等级A */
    GRADE_A("1", "A"),
    /** 等级B */
    GRADE_B("2", "B"),
    /** 等级C */
    GRADE_C("3", "C"),
    ;

    private final String value;

    private final String label;

    /**
     * 获取字典说明
     * @return 字典说明
     */
    @Override
    public String getLabel() {
        return this.label;
    }

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.value;
    }
}
