package com.py.flow.domain.dto.flow;

import com.py.flow.domain.enums.ApprovalBizType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 流程审批提交DTO
 * <AUTHOR>
 */
@Data
@ApiModel("流程审批提交DTO")
public class ApprovalSubmitDTO {

    /** 审批业务类型 */
    @NotNull(message = "审批业务类型不能为空")
    private ApprovalBizType bizType;

    /** 业务ID */
    @NotNull(message = "业务ID不能为空")
    private Long bizId;

    /** 项目名称 */
    @NotBlank(message = "项目名称不能为空")
    private String projectName;

    /** 表单金额 */
    private BigDecimal money;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("项目id列表")
    private List<Long> projectIdList;

    /** 发起人部门 */
    @ApiModelProperty("发起人部门")
    private List<Long> launcherDeptIdList;

    private String createBy;
    private String createDept;
    private Long createId;
    private LocalDateTime createTime;

    /** 新增的字段 */
    private ExtraField extraField;

    /** 上次的审批ID */
    @NotNull(message = "上次的审批ID不能为空")
    private Long lastFlowInstanceId;

    /** 流程版本ID */
    private Long flowVersionId;

    /**
     * 构造流程审批提交DTO
     * @param bizType 审批业务类型
     * @param bizId 业务ID
     * @param projectName 项目名称
     * @return 流程审批提交DTO
     */
    public static ApprovalSubmitDTO valueOf(
            ApprovalBizType bizType,
            Long bizId,
            String projectName) {
        return valueOf(bizType, bizId, projectName, null);
    }

    /**
     * 构造流程审批提交DTO
     * @param bizType 审批业务类型
     * @param bizId 业务ID
     * @param projectName 项目名称
     * @param money 表单金额
     * @return 流程审批提交DTO
     */
    public static ApprovalSubmitDTO valueOf(
            ApprovalBizType bizType,
            Long bizId,
            String projectName,
            BigDecimal money) {
        ApprovalSubmitDTO submitDto = new ApprovalSubmitDTO();
        submitDto.setBizType(bizType);
        submitDto.setBizId(bizId);
        submitDto.setProjectName(projectName);
        submitDto.setMoney(money);
        return submitDto;
    }


    /**派芽合作主体id*/
    @ApiModelProperty("派芽合作主体id")
    private List<Long> paiyaMainstayIdList;

    /**派芽合作主体名称*/
    @ApiModelProperty("派芽合作主体名称")
    private List<String> paiyaMainstayNameList;
}
