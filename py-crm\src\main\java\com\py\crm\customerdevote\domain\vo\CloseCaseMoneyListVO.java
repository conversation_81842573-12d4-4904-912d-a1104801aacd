package com.py.crm.customerdevote.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.py.framework.config.jackson.serializer.MoneyJsonSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 客户贡献管理
 * <AUTHOR>
 * @version CustomerDevoteListVO 2023/8/9 11:40
 */
@Data
public class CloseCaseMoneyListVO {

    /** 客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 结案金额 */
    @ApiModelProperty("结案金额")
    @JsonSerialize(using = MoneyJsonSerializer.class)
    private BigDecimal closeCaseMoney;


    /** 利润率 */
    @ApiModelProperty("利润")
    private BigDecimal profitMoney;

    /** 项目id */
    @ApiModelProperty("项目id")
    private Long projectId;


    /** 利润率 */
    @ApiModelProperty("利润率")
    private BigDecimal profitMargin;


    /** 利润率-结案列表字段 */
    private BigDecimal margin;

    /** 项目数量 */
    private Integer num;

}

