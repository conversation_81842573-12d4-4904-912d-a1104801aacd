package com.py.common.tools.modifycomparator.view.converter;

import com.py.common.oss.model.OssObjectInfo;
import com.py.common.tools.modifycomparator.model.BaseModifyDiff;
import com.py.common.tools.modifycomparator.view.BaseModifyDiffVO;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import org.mapstruct.Mapper;
import org.mapstruct.SubclassExhaustiveStrategy;
import org.mapstruct.SubclassMapping;
import org.mapstruct.SubclassMappings;

/**
 * 修改差异模型转换器
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", subclassExhaustiveStrategy = SubclassExhaustiveStrategy.RUNTIME_EXCEPTION)
public interface ModifyDiffConverter {

    /**
     * 转换为修改差异视图模型
     * @param modifyDiff 修改差异
     * @return 修改差异视图模型
     */
    @SubclassMappings({
            @SubclassMapping(target = BaseModifyDiffVO.StringModifyDiff.class, source = BaseModifyDiff.StringModifyDiff.class),
            @SubclassMapping(target = BaseModifyDiffVO.OssFileModifyDiff.class, source = BaseModifyDiff.OssFileModifyDiff.class),
            @SubclassMapping(target = BaseModifyDiffVO.ListOssFileModifyDiff.class, source = BaseModifyDiff.ListOssFileModifyDiff.class),
    })
    BaseModifyDiffVO<?> toBaseModifyDiffVO(BaseModifyDiff<?> modifyDiff);

    /**
     * 将Oss键初始化为Oss对象
     * @param ossKey Oss键
     * @return Oss对象
     */
    default OssObjectInfo toOssObjectInfo(String ossKey) {
        if(StringUtils.isBlank(ossKey)) {
            return null;
        }

        OssObjectInfo ossObjectInfo = new OssObjectInfo();
        ossObjectInfo.setKey(ossKey);
        ossObjectInfo.setFile(ListUtil.last(ossKey.split("/")));
        return ossObjectInfo;
    }
}
