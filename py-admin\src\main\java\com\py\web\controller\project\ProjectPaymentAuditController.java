package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.project.projectpaymentaudit.domain.dto.ProjectPaymentAuditDTO;
import com.py.project.projectpaymentaudit.domain.dto.ProjectPaymentAuditExportModel;
import com.py.project.projectpaymentaudit.domain.query.ProjectPaymentAuditQuery;
import com.py.project.projectpaymentaudit.domain.vo.ProjectPaymentAuditListVO;
import com.py.project.projectpaymentaudit.domain.vo.ProjectPaymentAuditVO;
import com.py.project.projectpaymentaudit.service.IProjectPaymentAuditService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 项目管理-付款审核详情Controller
 *
 * <AUTHOR>
 * @date 2023-08-01
 */
@Api(tags = "项目管理-付款审核详情")
@RestController
@RequestMapping("/project/projectPaymentAudit")
public class ProjectPaymentAuditController extends BaseController {

    /** 项目管理-付款审核详情服务 */
    @Resource
    private IProjectPaymentAuditService projectPaymentAuditService;

    /**
     * 查询项目管理-付款审核详情列表
     *
     * @param query 项目管理-付款审核详情查询参数
     * @return 项目管理-付款审核详情列表
     */
    @ApiOperation("查询项目管理-付款审核详情列表")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentAudit:list')")
    @GetMapping("/listProjectPaymentAudit")
    public R<List<ProjectPaymentAuditListVO>> listProjectPaymentAudit(ProjectPaymentAuditQuery query) {
        List<ProjectPaymentAuditListVO> voList = this.projectPaymentAuditService.listProjectPaymentAudit(query);
        return R.success(voList);
    }

    /**
     * 分页查询项目管理-付款审核详情列表
     *
     * @param query 项目管理-付款审核详情查询参数
     * @return 项目管理-付款审核详情分页
     */
    @ApiOperation("分页查询询项目管理-付款审核详情列表")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentAudit:list')")
    @GetMapping("/pageProjectPaymentAudit")
    public R<PageInfo<ProjectPaymentAuditListVO>> pageProjectPaymentAudit(ProjectPaymentAuditQuery query) {
        PageInfo<ProjectPaymentAuditListVO> voList = this.projectPaymentAuditService.pageProjectPaymentAuditList(query);
        return R.success(voList);
    }

    /**
     * 获取项目管理-付款审核详情详细信息
     * @param id 项目管理-付款审核详情主键
     * @return 项目管理-付款审核详情视图模型
     */
    @ApiOperation("获取项目管理-付款审核详情详细信息")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentAudit:query')")
    @GetMapping(value = "/{id}")
    public R<ProjectPaymentAuditVO> getInfo(@PathVariable("id") Long id) {
        return R.success(projectPaymentAuditService.selectProjectPaymentAuditById(id));
    }

    /**
     * 新增项目管理-付款审核详情
     *
     * @param dto 项目管理-付款审核详情修改参数
     * @return 是否成功
     */
    @ApiOperation("新增项目管理-付款审核详情")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentAudit:add')")
    @Log(title = "项目管理-付款审核详情", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody List<ProjectPaymentAuditDTO> dto) {
        return R.success(projectPaymentAuditService.saveProjectPaymentAudit(dto));
    }

    /**
     * 修改项目管理-付款审核详情
     *
     * @param dto 项目管理-付款审核详情修改参数
     * @return 是否成功
     */
    @ApiOperation("修改项目管理-付款审核详情")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentAudit:edit')")
    @Log(title = "项目管理-付款审核详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody ProjectPaymentAuditDTO dto) {
        return R.success(projectPaymentAuditService.updateProjectPaymentAudit(dto));
    }

    /**
     * 删除项目管理-付款审核详情
     * @param ids 需要删除的项目管理-付款审核详情主键集合
     * @return 是否成功
     */
    @ApiOperation("删除项目管理-付款审核详情" )
    @PreAuthorize("@ss.hasPermi('project:projectPaymentAudit:remove')")
    @Log(title = "项目管理-付款审核详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(projectPaymentAuditService.deleteProjectPaymentAuditByIds(ids));
    }

    /**
     * 导出项目管理-付款审核详情
     * @param response 请求响应
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目管理-付款审核详情")
    @PreAuthorize("@ss.hasPermi('project:projectPaymentAudit:export')")
    @Log(title = "项目管理-付款审核详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectPaymentAuditQuery query) {
        List<ProjectPaymentAuditExportModel> exportList = this.projectPaymentAuditService.exportProjectPaymentAudit(query);

        ExcelUtil<ProjectPaymentAuditExportModel> util = new ExcelUtil<>(ProjectPaymentAuditExportModel. class);
        util.exportExcel(response, exportList, "项目管理-付款审核详情数据" );
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('project:projectPaymentAudit:import')" )
    @Log(title = "项目管理-付款审核详情" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<ProjectPaymentAuditExportModel> util = new ExcelUtil<>(ProjectPaymentAuditExportModel.class);
        List<ProjectPaymentAuditExportModel> projectPaymentAuditList = util.importExcel(file.getInputStream());
        String message = this.projectPaymentAuditService.importProjectPaymentAudit(projectPaymentAuditList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('project:projectPaymentAudit:import')" )
    @Log(title = "项目管理-付款审核详情" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ProjectPaymentAuditExportModel> util = new ExcelUtil<>(ProjectPaymentAuditExportModel.class);
        util.importTemplateExcel(response, "项目管理-付款审核详情数据" );
    }

}
