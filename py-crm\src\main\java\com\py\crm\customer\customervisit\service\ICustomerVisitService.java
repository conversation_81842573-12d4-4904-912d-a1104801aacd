package com.py.crm.customer.customervisit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryExportModel;
import com.py.crm.customer.customervisit.domian.CustomerVisit;
import com.py.crm.customer.customervisit.domian.dto.CustomerVisitDTO;
import com.py.crm.customer.customervisit.domian.dto.CustomerVisitExportModel;
import com.py.crm.customer.customervisit.domian.vo.CustomerVisitVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public interface ICustomerVisitService extends IService<CustomerVisit> {
    /**
     * 批量新增客户拜访日志数据
     * @param dtoList 客户管理-客户拜访日志数据传输模型
     * @param customerId 客户id
     * @param isApprovalEdit 是否是审批驳回修改
     */
    void insertBatchCustomerVisit(List<CustomerVisitDTO> dtoList, Long customerId,Boolean isApprovalEdit );

    /**
     * 修改客户拜访访日志
     * @param dtoList 客户管理-客户拜访日志数据传输模型
     * @param customerId 客户id
     * @param name 客户 名称
     */
    void updateBatchCustomerVisit(List<CustomerVisitDTO> dtoList,Long customerId,String name);

    /**
     * 根据客户id查询客户拜访日志
     * @param customerId 客户id
     * @return  分页视图数据
     */
    PageInfo<CustomerVisitVO> pageCustomerVisitList(Long customerId);

    /**
     * 根据客户id删除拜访日志
     * @param customerId 客户id
     * @param name 客户名称
     */
    void deleteBatchCustomerVisit(Long customerId, String name);

    /**
     * 根据客户id查询客户拜访日志列表
     * @param customerId 客户id
     * @return CustomerVisitVO 客户拜访视图模型
     */
    List<CustomerVisitVO> selectCustomerVisitByCustomerId(Long customerId);

    /**
     * 根据客户ID查询客户拜访日志列表
     * @param customerIdList 客户ID
     * @param customerMap 客户Map
     * @return 客户拜访日志列表
     */
    List<CustomerVisitExportModel> listVisitByCustomerIds(List<Long> customerIdList, Map<Long, CustomerLaundryExportModel> customerMap);
}
