package com.py.crm.customer.customerlaundry.domain.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.constant.DictConstant;
import com.py.common.tools.modifycomparator.annotation.CompareField;
import com.py.common.tools.poiexcel.annotation.Excel;
import com.py.common.utils.DateUtils;
import com.py.crm.compareddraft.domain.vo.ComparedDraftVO;
import com.py.crm.customer.contact.domain.vo.ContactVO;
import com.py.crm.customer.cooperatemainstay.domain.vo.CooperateMainstayListVO;
import com.py.crm.customer.customeraccount.domain.vo.CustomerAccountListVO;
import com.py.crm.customer.customeraddress.domain.vo.CustomerAddressVO;
import com.py.crm.customer.invoicing.domain.vo.InvoicingVO;
import com.py.crm.customerdevote.domain.dto.CrmProjectListExportVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;

/**
 * 客户管理-查看清单导出模型
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Data
@ApiModel("客户管理-查看清单导出模型" )
public class CustomerLaundryExportModel implements CustomerLaundryBaseInfo {
    private static final long serialVersionUID = 1L;

    /** 自增id*/
    @ApiModelProperty("自增id" )
    private Long id;

    /** 清单id*/
    @ApiModelProperty("清单id" )
    private Long laundryId;

    /** 业务id*/
    @ApiModelProperty("业务id" )
    private Long bizId;

    /** 客户名称*/
    @ApiModelProperty("客户名称")
    private String name;

    /** 品牌/业务线*/
    @ApiModelProperty("品牌/业务线")
    private String lineBusiness;

    /** 行业类目id*/
    @ApiModelProperty("行业类目id" )
    private List<Long> industryCategoryIdList;
    private String industryCategoryStr;

    /** 合作状态(0.合作中 1.暂停合作 2.意向合作)*/
    @ApiModelProperty("合作状态(0.合作中 1.暂停合作 2.意向合作)")
    private Integer cooperationStatus;
    private String cooperationStatusStr;

    /** 目标服务人员 */
    @ApiModelProperty("目标服务人员")
    private List<Long> serviceUserIdList;

    /** 目标服务人员*/
    @ApiModelProperty("目标服务人员")
    private String serviceUserName;
    private List<String> serviceUserNameList;

    /** 客户来源(0.老客推荐 1.熟人介绍 2.主动BD 3.客户直联)*/
    @ApiModelProperty("客户来源(0.老客推荐 1.熟人介绍 2.主动BD 3.客户直联)")
    private Integer customerSource;
    private String customerSourceStr;

    /** 客户类型(0.品牌客户 1.平台客户 2.集团客户 3.渠道客户)*/
    @ApiModelProperty("客户类型(0.品牌客户 1.平台客户 2.集团客户 3.渠道客户)")
    private Integer customerType;
    private String customerTypeStr;

    /** 品牌阶段(0.初创品牌 1.成长期品牌 2.成熟品牌)*/
    @ApiModelProperty("品牌阶段(0.初创品牌 1.成长期品牌 2.成熟品牌)")
    private Integer brandStage;
    private String brandStageStr;

    /** 合作部门*/
    @ApiModelProperty("合作部门")
    private String cooperativeSector;

    /** 品牌核心生意来源(0.全渠道销售（线下强，线上辅助）1.全渠道销售（线上强，线下辅助）2. 纯线上渠道)*/
    @ApiModelProperty("品牌核心生意来源(0.全渠道销售（线下强，线上辅助）1.全渠道销售（线上强，线下辅助）2. 纯线上渠道)")
    private Integer businessSource;
    private String businessSourceStr;

    /** 派芽服务产品(0.内容营销全链路服务 1.整合营销服务 2.内容营销策略及执行服务 3.媒介投放服务 4.MCN服务)*/
    @ApiModelProperty("派芽服务产品(0.内容营销全链路服务 1.整合营销服务 2.内容营销策略及执行服务 3.媒介投放服务 4.MCN服务)")
    private List<String> serviceProduct;
    private String serviceProductStr;

    /** 报价策略(0.资源差价模式 1.拆分模式（策略费+服务费+资源返点）2.资源差价+拆分模式)*/
    @ApiModelProperty("报价策略(0.资源差价模式 1.拆分模式（策略费+服务费+资源返点）2.资源差价+拆分模式)")
    private Integer biddingStrategy;
    private String biddingStrategyStr;

    /** 决策链路*/
    @ApiModelProperty("决策链路")
    private String decisionLink;

    /** 备注*/
    @ApiModelProperty("备注")
    private String remark;

    /** 创建者Id */
    private Long createId;

    /** 创建者 */
    private String createBy;

    /** 创建部门 */
    private String createDept;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    private String createTimeStr;

    /** 企业联系人视图模型  */
    private ContactVO contactVO;

    /** 获取合作主体  */
    private CooperateMainstayListVO cooperateMainstayListVo;

    /** 客户地址视图模型 */
    private CustomerAddressVO customerAddressVO;

    /** 账户信息列表视图模型 */
    private CustomerAccountListVO customerAccountListVo;

    /** 开票信息视图模型 */
    private InvoicingVO invoicingVO;

    /** 比稿管理 */
    private ComparedDraftVO comparedDraftVO;

    /** 项目表列表视图模型 */
    private CrmProjectListExportVO projectListExportVO;

    public String getCreateTimeStr() {
        return DateUtils.format(createTime,DateUtils.YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 客户ID
     * @return 客户ID
     */
    @Override
    public Long getCustomerId() {
        return this.bizId;
    }
}
