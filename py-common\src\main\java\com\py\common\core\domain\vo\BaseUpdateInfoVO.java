package com.py.common.core.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

//import jdk.nashorn.internal.ir.annotations.Ignore;

/**
 * 基础更新查询信息
 * <AUTHOR>
 */
@Data
public class BaseUpdateInfoVO implements IUpdateInfoVO {

    /** 更新人ID */
    @ApiModelProperty(value = "更新人ID")
    private Long updateId;

    /** 更新人 */
    @ApiModelProperty("更新人")
    private String updateBy;

    /** 更新人部门 */
    @ApiModelProperty("更新人部门")
    private String updateDept;

    /** 更新时间 */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
