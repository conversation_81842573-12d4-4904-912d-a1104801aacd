package com.py.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 类描述：
 * <AUTHOR>
 */
@Component
@ConfigurationProperties("sms")
@Data
public class SmsConfig {

    private String domain;
    private String regionId;
    private String smsSignName;
    private String accessKeyId;
    private String accessKeySecret;
}
