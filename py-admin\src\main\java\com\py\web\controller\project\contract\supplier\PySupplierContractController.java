package com.py.web.controller.project.contract.supplier;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.approve.ApproveDTO;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.file.FileInfoVO;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.project.pycustomercontract.domain.vo.InsertCustomerContractVO;
import com.py.project.pycustomercontract.domain.vo.PyContractMultipleChoiceVO;
import com.py.project.pysuppliercontract.domain.dto.PyDeleteSupplierSealedContractVO;
import com.py.project.pysuppliercontract.domain.dto.PySupplierContractDTO;
import com.py.project.pysuppliercontract.domain.query.PySupplierContractCheckBoxQuery;
import com.py.project.pysuppliercontract.domain.query.PySupplierContractQuery;
import com.py.project.pysuppliercontract.domain.vo.PySupplierContractListVO;
import com.py.project.pysuppliercontract.domain.vo.PySupplierContractVO;
import com.py.project.pysuppliercontract.domain.vo.SupplierContractAuditVO;
import com.py.project.pysuppliercontract.service.IPySupplierContractService;
import com.py.project.pysuppliercontract.service.impl.PySupplierContractServiceImpl;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商合同信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-23
 */
@Api(tags = "供应商合同信息")
@RestController
@Validated
@RequestMapping("/crm/pySupplierContract")
public class PySupplierContractController extends BaseController {

    /** 供应商合同信息服务 */
    @Resource
    private IPySupplierContractService pySupplierContractService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 系统设置-主体参数设置服务 */
    @Resource
    private ISystemMainstayParamService systemMainstayParamService;

    /**
     * 分页查询供应商合同信息列表
     *
     * @param query 供应商合同信息查询参数
     * @return 供应商合同信息分页
     */
    @ApiOperation("分页查询询供应商合同信息列表")
    @PreAuthorize("@ss.hasPermi('crm:pySupplierContract:list')")
    @PostMapping("/pagePySupplierContract")
    public R<PageInfo<PySupplierContractListVO>> pagePySupplierContract(@Validated @RequestBody PySupplierContractQuery query) {
        PageInfo<PySupplierContractListVO> voList = this.pySupplierContractService.pagePySupplierContractList(query);
        return R.success(voList);
    }

    /**
     * 获取供应商合同信息详细信息
     * @param id 供应商合同信息主键
     * @return 供应商合同信息视图模型
     */
    @ApiOperation("获取供应商合同信息详细信息")
    @PreAuthorize("@ss.hasPermi('crm:pySupplierContract:query')")
    @GetMapping(value = "/get/{id}")
    public R<PySupplierContractVO> getInfo(@PathVariable("id") Long id) {
        return R.success(pySupplierContractService.selectPySupplierContractById(id));
    }

    /**
     * 新增供应商合同信息
     *
     * @param dto 供应商合同信息修改参数
     * @return 是否成功
     */
    @ApiOperation("新增供应商合同信息")
    @PreAuthorize("@ss.hasPermi('crm:pySupplierContract:add')")
    @Log(title = "供应商合同信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<InsertCustomerContractVO> add(@Validated @RequestBody  PySupplierContractDTO dto) {
        return R.success(pySupplierContractService.insertPySupplierContract(dto));
    }

    /**
     * 修改供应商合同信息
     *
     * @param dto 供应商合同信息修改参数
     * @return 是否成功
     */
    @ApiOperation("修改供应商合同信息")
    @PreAuthorize("@ss.hasPermi('crm:pySupplierContract:edit')")
    @Log(title = "供应商合同信息", businessType = BusinessType.INSERT)
    @PostMapping("/edit")
    public R<InsertCustomerContractVO> edit(@Validated @RequestBody  PySupplierContractDTO dto) {
        return R.success(pySupplierContractService.updatePySupplierContract(dto));
    }

    /**
     * 供应商合同多选合计
     * @param dto
     * @return
     */
    @ApiOperation("供应商合同多选框合计")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:query')")
    @PostMapping(value = "/checkBox")
    public R<PyContractMultipleChoiceVO> checkBox(@RequestBody PySupplierContractCheckBoxQuery dto) {
        return R.success(pySupplierContractService.checkBoxAmount(dto));
    }

    /**
     * 查看盖章合同
     * @param id 客户合同信息主键
     * @return 客户合同信息视图模型
     */
    @ApiOperation("查看盖章合同(财务)")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pySupplierContract:query')")
    @GetMapping(value = "/selectSealedContract/{id}")
    public R<List<FileInfoVO>> selectSealedContract(@PathVariable("id") Long id) {
        return R.success(pySupplierContractService.selectSealedContractById(id));
    }

    /**
     * 上传盖章合同
     * @param
     * @return
     */
    @ApiOperation("上传盖章合同(财务)")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pySupplierContract:delete')")
    @PostMapping(value = "/addSealedContract")
    public R<Boolean> addSealedContract(@RequestBody PyDeleteSupplierSealedContractVO pySupplierSealedContractVO) {
        return R.success(pySupplierContractService.addSealedContractById(pySupplierSealedContractVO));
    }


    /**
     * 删除盖章合同
     * @param
     * @return
     */
    @ApiOperation("删除盖章合同(财务)")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pySupplierContract:delete')")
    @PostMapping(value = "/deleteSealedContract")
    public R<Boolean> deleteSealedContract(@RequestBody PyDeleteSupplierSealedContractVO pySupplierSealedContractVO) {
        return R.success(pySupplierContractService.deleteSealedContractById(pySupplierSealedContractVO));
    }

    /**
     * 导出供应商合同信息
     * @param query 导出查询参数
     */
    @ApiOperation("导出供应商合同信息")
    @PreAuthorize("@ss.hasPermi('crm:pySupplierContract:export')")
    @Log(title = "供应商合同信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@Validated @RequestBody PySupplierContractQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        if(query.getPyMainstayId() == null){
            String fileName = "供应商合同管理列表数据-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
            query.setFileName(fileName);
            reusableAsyncTaskService.addTask("合同管理-供应商合同(项目)", TaskType.Export,query, PySupplierContractServiceImpl.class);
        } else {
            SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPyMainstayId());
            String fileName = "供应商合同管理列表数据-" + systemMainstayParamVO.getMainstayName() + "-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
            query.setFileName(fileName);
            reusableAsyncTaskService.addTask("合同管理-供应商合同(财务)", TaskType.Export,query, PySupplierContractServiceImpl.class);
        }
        return R.success("提交成功");
    }

    /**
     * 查询供应商列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询供应商列表上的创建部门下拉" )
    @GetMapping("/listSupplierDept" )
    public R<List<String>> listSupplierDept(PySupplierContractQuery query){
        return R.success(pySupplierContractService.listSupplierDept(query));
    }

    /**
     * 审批流测试
     * @param
     * @return
     */
    @ApiOperation("审批流测试")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pySupplierContract:delete')")
    @PostMapping(value = "/flowTest")
    public R<Boolean> flowTest(@RequestBody ApproveDTO approveDTO) {
       return R.success(pySupplierContractService.auditAfter(approveDTO));
    }

    /**
     * 分页查询询供应商合同审批列表查询
     *
     * @param query 供应商合同信息查询参数
     * @return 供应商合同信息分页
     */
    @ApiOperation("分页查询询供应商合同审批列表查询")
    @PreAuthorize("@ss.hasPermi('crm:pySupplierContract:list')")
    @PostMapping("/listAuditSupplierContract")
    public R<PageInfo<SupplierContractAuditVO>> listAuditSupplierContract(@Validated @RequestBody PySupplierContractQuery query) {
        PageInfo<SupplierContractAuditVO> voList = this.pySupplierContractService.listAuditSupplierContract(query);
        return R.success(voList);
    }

    /**
     * 供应商合同审批列表合计
     * @param query 查询条件
     * @return 结果
     */
    @ApiOperation("供应商合同审批列表合计")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:query')")
    @PostMapping(value = "/checkBoxAudit")
    public R<PyContractMultipleChoiceVO> checkBoxAudit(@Validated @RequestBody PySupplierContractCheckBoxQuery query) {
        return R.success(pySupplierContractService.checkBoxAudit(query));
    }

    /**
     * 导出供应商合同信息审批列表
     * @param query 导出查询参数
     */
    @ApiOperation("导出供应商合同信息审批列表")
    @PreAuthorize("@ss.hasPermi('crm:pySupplierContract:export')")
    @Log(title = "供应商合同信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAudit")
    public R<String> exportAudit(@Validated @RequestBody PySupplierContractCheckBoxQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPyMainstayId());
        String fileName = "供应商合同审批查询列表数据-" + systemMainstayParamVO.getMainstayName() + "-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setIsApproval(true);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("合同管理-供应商合同审批列表", TaskType.Export,query, PySupplierContractServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 查询供应商审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询供应商审批列表上的创建部门下拉" )
    @GetMapping("/listAuditSupplierDept" )
    public R<List<String>> listAuditSupplierDept(PySupplierContractQuery query){
        return R.success(pySupplierContractService.listAuditSupplierDept(query));
    }
}
