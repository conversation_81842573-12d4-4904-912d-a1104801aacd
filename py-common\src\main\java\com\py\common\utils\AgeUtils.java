package com.py.common.utils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 年龄计算
 * @date Created in 2022/6/29
 * @version: $
 */
public class AgeUtils {
    /**
     * 根据出生日期获取年龄
     * @param birthday
     * @return
     */
    public static String getAgeByBirthday(Date birthday){
        if(birthday==null){
            return null;
        }
        LocalDate lBirthday = birthday.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return String.valueOf(lBirthday.until(LocalDate.now()).getYears());
    }

}
