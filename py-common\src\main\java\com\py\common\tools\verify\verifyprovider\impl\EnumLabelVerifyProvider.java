package com.py.common.tools.verify.verifyprovider.impl;

import com.py.common.enums.IDict;
import com.py.common.tools.verify.enums.VerifyType;
import com.py.common.tools.verify.verifyprovider.VerifyProvider;
import com.py.common.utils.EnumUtils;
import com.py.common.utils.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 枚举标签值合法性验证提供者
 * <AUTHOR>
 */
@Component
public class EnumLabelVerifyProvider implements VerifyProvider {
    @Override
    public VerifyType supportedVerifyType() {
        return VerifyType.EnumLabelLegal;
    }

    /**
     * 验证枚举值是否合法
     * @param target 验证目标
     * @return true: 枚举字合法, false: 非法
     */
    @Override
    public boolean verify(Object target) throws Exception {
        if(target == null) {
            return true;
        }

        if(target instanceof LabelVerifyPackage == false) {
            throw new IllegalArgumentException("枚举标签值合法性验证应使用 LabelVerifyPackage.valueOf 作为入参");
        }

        LabelVerifyPackage verifyPackage = (LabelVerifyPackage) target;
        if(StringUtils.isBlank(verifyPackage.label)) {
            return true;
        }
        IDict<?> dict = EnumUtils.toDictNullable(verifyPackage.label, verifyPackage.dictClass);
        return dict != null;
    }

    /** 枚举标签值合法性验证参数包 */
    @SuppressWarnings("rawtypes")
    public static class LabelVerifyPackage {

        /** 需校验的枚举标签值 */
        private String label;

        /** 目标字典类型 */
        private Class<? extends IDict> dictClass;

        public static LabelVerifyPackage valueOf(String label, Class<? extends IDict> dictClass) {
            LabelVerifyPackage verifyPackage = new LabelVerifyPackage();
            verifyPackage.label = label;
            verifyPackage.dictClass = dictClass;
            return verifyPackage;
        }
    }

}
