package com.py.crm.customer.customerlaundry.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.py.common.constant.DictConstant;
import com.py.common.core.domain.entity.SysDictData;
import com.py.common.core.domain.model.LoginUser;
import com.py.common.enums.CooperationStatus;
import com.py.common.enums.flow.FlowApprovalStatus;
import com.py.common.exception.ServiceException;
import com.py.common.mybatisplus.SuperServiceImpl;
import com.py.common.oss.IOssService;
import com.py.common.tools.multisheetexcelexporter.MultiSheetExcelExporter;
import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTask;
import com.py.common.utils.PageUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.compareddraft.domain.vo.ComparedDraftVO;
import com.py.crm.compareddraft.service.IComparedDraftService;
import com.py.crm.crmdownload.domain.dto.CrmDownloadDTO;
import com.py.crm.crmdownload.service.ICrmDownloadService;
import com.py.crm.customer.contact.domain.vo.ContactVO;
import com.py.crm.customer.contact.service.IContactService;
import com.py.crm.customer.cooperatemainstay.domain.vo.CooperateMainstayListVO;
import com.py.crm.customer.cooperatemainstay.service.ICooperateMainstayService;
import com.py.crm.customer.customeraccount.domain.vo.CustomerAccountListVO;
import com.py.crm.customer.customeraccount.service.ICustomerAccountService;
import com.py.crm.customer.customeraddress.domain.vo.CustomerAddressVO;
import com.py.crm.customer.customeraddress.service.ICustomerAddressService;
import com.py.crm.customer.customerlaundry.converter.CustomerLaundryConverter;
import com.py.crm.customer.customerlaundry.domain.CustomerLaundry;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryExportModel;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryListDTO;
import com.py.crm.customer.customerlaundry.domain.query.CustomerLaundryQuery;
import com.py.crm.customer.customerlaundry.domain.vo.CustomerLaundryListVO;
import com.py.crm.customer.customerlaundry.enums.LaundryType;
import com.py.crm.customer.customerlaundry.mapper.CustomerLaundryMapper;
import com.py.crm.customer.customerlaundry.service.ICustomerLaundryService;
import com.py.crm.customer.customervisit.domian.dto.CustomerVisitExportModel;
import com.py.crm.customer.customervisit.service.ICustomerVisitService;
import com.py.crm.customer.domain.SupCustomer;
import com.py.crm.customer.domain.vo.CustomerCountVO;
import com.py.crm.customer.invoicing.domain.vo.InvoicingVO;
import com.py.crm.customer.invoicing.service.IInvoicingService;
import com.py.crm.customer.service.ICustomerService;
import com.py.crm.customerdevote.domain.dto.CrmProjectListExportVO;
import com.py.crm.customerdevote.service.ICrmProjectService;
import com.py.system.dict.service.ISysDictDataService;
import com.py.system.tools.reusableasynctask.domain.ExportTaskResult;
import com.py.system.tools.reusableasynctask.service.ITaskRecodeService;
import com.py.system.user.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户管理-查看清单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Slf4j
@Service
public class CustomerLaundryServiceImpl
        extends SuperServiceImpl<CustomerLaundryMapper, CustomerLaundry>
        implements ICustomerLaundryService , ReusableAsyncTask<CustomerLaundryQuery> {

    /** 客户管理-查看清单模型转换器 */
    @Resource
    private CustomerLaundryConverter customerLaundryConverter;

    /** 客户管理-查看清单Mapper接口 */
    @Resource
    private CustomerLaundryMapper customerLaundryMapper;

    /** 客户管理-客户服务 */
    @Resource
    private ICustomerService customerService;

    /** 异步任务执行记录服务 */
    @Resource
    private ITaskRecodeService taskRecodeService;


    /** 客户管理-客户-账户信息服务 */
    @Resource
    private ICustomerAccountService customerAccountService;

    /** 客户管理-客户-客户地址服务 */
    @Resource
    private ICustomerAddressService customerAddressService;

    /** 客户管理-客户-开票信息服务 */
    @Resource
    private IInvoicingService invoicingService;

    /** 客户管理-客户-企业联系人服务 */
    @Resource
    private IContactService contactService;

    /** 客户管理-客户-合作主体服务 */
    @Resource
    private ICooperateMainstayService cooperateMainstayService;

    /** 项目管理-项目表Service接口 */
    @Resource
    private ICrmProjectService projectService;

    /** 客户管理-比稿管理服务 */
    @Resource
    private IComparedDraftService comparedDraftService;

    /** 字典 业务层 */
    @Resource
    private ISysDictDataService dictDataService;

    /** 用户 业务层 */
    @Resource
    private ISysUserService userService;

    /** 对象存储服务 */
    @Resource
    private IOssService ossService;

    /** 客户/人脉清单下载记录服务 */
    @Resource
    private ICrmDownloadService crmDownloadService;

    /**客户管理-拜访日志*/
    @Resource
    private ICustomerVisitService customerVisitService;

    /**
     * 分页客户管理-查看清单列表
     * @param query 客户管理-查看清单
     * @return 客户管理-查看清单分页
     */
    @Override
    public PageInfo<CustomerLaundryListVO> pageCustomerLaundryList(CustomerLaundryQuery query) {
        query.setAuditStatus(FlowApprovalStatus.APPROVE.getValue());
        query.setUserId(SecurityUtils.getUserId());
        query.setBizType(LaundryType.CUSTOMER.getValue());
        if(query.getTaskId() == null){
            PageUtils.startPage();
        }
        List<CustomerLaundryListVO> customerLaundryListVoS = customerLaundryMapper.pageCustomerLaundryList(query);
//        List<CustomerLaundryListVO> customerLaundryListVoS = customerConverter.toCustomerLaundryListVOByEntity(supCustomers);
        if(ListUtil.isEmpty(customerLaundryListVoS)){
            return ListUtil.emptyPage();
        }
        // 设置目标服务人员的名和部门
        customerService.setServiceUserCategory(customerLaundryListVoS);
        return ListUtil.pageConvert(customerLaundryListVoS,customerLaundryListVoS);

    }

    /**
     * 批量新增客户管理-查看清单
     * @param dto 客户管理-查看清单修改参数
     * @return 是否成功
     */
    @Override
    public boolean insertBatchCustomerLaundry(CustomerLaundryListDTO dto) {
        if(!dto.getIsAll() && ListUtil.isEmpty(dto.getBizIdList())){
            throw new ServiceException("请选择加入清单的客户");
        }
        List<Long> customerIdList;
        if(dto.getIsAll()){
            customerIdList = customerService.listAllCustomerIdByStatus(dto.getBizIdList(), dto.getQuery());
        } else {
            customerIdList = dto.getBizIdList();
        }
        if(!ListUtil.isEmpty(customerIdList)){
            Set<Long> customerIdSet = getCustomerIdListByUserId();
            List<CustomerLaundry> customerLaundryList = new ArrayList<>(customerIdList.size());
            for(Long customerId : customerIdList) {
                if(customerIdSet.contains(customerId)){
                    continue;
                }
                CustomerLaundry customerLaundry = new CustomerLaundry();
                customerLaundry.setBizId(customerId);
                customerLaundry.setBizType(LaundryType.CUSTOMER.getValue());
                customerLaundryList.add(customerLaundry);
            }
            if(ListUtil.isNotEmpty(customerLaundryList)){
                return this.saveBatch(customerLaundryList);
            }
        }
        return true;
    }

    /**
     * 根据用户id获取以加进清单的客户id
     * @return 客户id
     */
    private Set<Long> getCustomerIdListByUserId(){
        LambdaQueryWrapper<CustomerLaundry> wrapper = Wrappers.lambdaQuery(CustomerLaundry.class);
        wrapper.eq(CustomerLaundry::getCreateId, SecurityUtils.getUserId());
        wrapper.eq(CustomerLaundry::getBizType,LaundryType.CUSTOMER.getValue());
        List<CustomerLaundry> customerLaundryList = this.list(wrapper);
        if(ListUtil.isEmpty(customerLaundryList)){
            return ListUtil.emptySet();
        }
        return customerLaundryList.stream().map(CustomerLaundry::getBizId).collect(Collectors.toSet());
    }

    /**
     * 批量删除客户管理-查看清单
     * @param idList 需要删除的客户管理-查看清单主键
     * @return 是否成功
     */
    @Override
    public boolean deleteCustomerLaundryByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

    /**
     * 导出客户管理-查看清单
     * @param query 导出查询参数
     * @return 导出数据
     */
    @Override
    public void exportCustomerLaundry(CustomerLaundryQuery query) {
        taskRecodeService.editTaskBegin(query.getTaskId());
        List<SupCustomer> supCustomers = customerService.listCustomerIdByCustomerIds(query.getLaundryIdList());
        List<CustomerLaundryExportModel> exportModels = customerLaundryConverter.toSupCustomerByExportModel(supCustomers);
        ExportTaskResult exportTaskResult = new ExportTaskResult();
        exportTaskResult.setFileName(query.getFileName());
        try {
            // 组装客户字典值
            this.assembleCustomerDict(exportModels);
            List<Long> customerIdList = query.getCustomerIdList();
            Map<Long, CustomerLaundryExportModel> bizIdMap = ListUtil.toMap(exportModels, CustomerLaundryExportModel::getBizId);
            // 获取企业联系人
            List<CustomerLaundryExportModel> contactExportModelList = this.getContactExport(bizIdMap, customerIdList);
            // 获取合作主体
            List<CustomerLaundryExportModel> cooperateMainstayExportModelList = this.getCooperateMainstayExport(customerIdList, bizIdMap);
            // 获取企业地址信息
            List<CustomerLaundryExportModel> addressExportModelList = this.getAddressExport(customerIdList, bizIdMap);
            // 获取企业账号信息
            List<CustomerLaundryExportModel> accountExportModelList = this.getAccountExport(customerIdList, bizIdMap);
            // 获取开票信息服务
            List<CustomerLaundryExportModel> invoicingExportModelList = this.getInvoicingExport(customerIdList, bizIdMap);
            // 客户拜访日志
            List<CustomerVisitExportModel> customerVisitExportModelList = customerVisitService.listVisitByCustomerIds(customerIdList,bizIdMap);
            // 获取客户比稿信息
            List<CustomerLaundryExportModel> comparedDraftExportModelList = this.getComparedDraftExportModels(customerIdList, bizIdMap);
            // 获取客户项目信息
            List<CustomerLaundryExportModel> projectExportModelList = this.getProjectExportModels(customerIdList, bizIdMap);

            MultiSheetExcelExporter excelExporter = new MultiSheetExcelExporter();
            ExcelSheetConfig<CustomerLaundryExportModel> customerExportSheetConfig = this.getCustomerExportConfig();
            excelExporter.addSheetConfig(exportModels,customerExportSheetConfig,"客户基本信息");
            ExcelSheetConfig<CustomerLaundryExportModel> contactExcelSheetConfig = this.getContactExportConfig();
            excelExporter.addSheetConfig(contactExportModelList,contactExcelSheetConfig,"企业联系人");
            ExcelSheetConfig<CustomerLaundryExportModel> cooperateMainstayExcelSheetConfig = this.getCooperateMainstayExportConfig();
            excelExporter.addSheetConfig(cooperateMainstayExportModelList,cooperateMainstayExcelSheetConfig,"合作主体");
            ExcelSheetConfig<CustomerLaundryExportModel> addressExcelSheetConfig = this.getAddressExportConfig();
            excelExporter.addSheetConfig(addressExportModelList,addressExcelSheetConfig,"企业地址信息");
            ExcelSheetConfig<CustomerLaundryExportModel> accountExportConfig = this.getAccountExportConfig();
            excelExporter.addSheetConfig(accountExportModelList,accountExportConfig,"企业账户信息");
            ExcelSheetConfig<CustomerLaundryExportModel> invoicingExportConfig = this.getInvoicingExportConfig();
            excelExporter.addSheetConfig(invoicingExportModelList,invoicingExportConfig,"企业开票信息");
            // 客户拜访日志
            ExcelSheetConfig<CustomerVisitExportModel> visitExportConfig = this.getVisitExportConfig(customerVisitExportModelList);
            excelExporter.addSheetConfig(customerVisitExportModelList,visitExportConfig,"客户拜访日志");

            ExcelSheetConfig<CustomerLaundryExportModel> comparedDraftExportConfig = this.getComparedDraftExportConfig();
            excelExporter.addSheetConfig(comparedDraftExportModelList,comparedDraftExportConfig,"比稿记录");
            ExcelSheetConfig<CustomerLaundryExportModel> projectExportConfig = this.getProjectExportConfig();
            excelExporter.addSheetConfig(projectExportModelList,projectExportConfig,"项目信息");

            byte[] bytes = excelExporter.exportExcelToByte(query.getFileName());
            String fileKey = this.ossService.uploadFileToOss(bytes, query.getFileName());
            exportTaskResult.setKey(fileKey);
            exportTaskResult.setIsSuccess(true);
            taskRecodeService.taskExportCompleted(query.getTaskId(), exportTaskResult);
            CrmDownloadDTO downloadDTO = new CrmDownloadDTO();
            downloadDTO.setFileName(query.getFileName());
            downloadDTO.setFileSize(String.valueOf(bytes.length));
            downloadDTO.setCrmNum(exportModels.size());
            downloadDTO.setBizType(1);
            downloadDTO.setToken(fileKey);
            crmDownloadService.insertCrmDownload(downloadDTO);
            return;
        }catch(Exception e){
            log.error("客户管理-清单下载错误：" + e);
            exportTaskResult.setIsSuccess(false);
        }
        taskRecodeService.taskExportFailed(query.getTaskId(),exportTaskResult);
    }

    /**
     * 删除下载的客户
     * @param query 查询条件
     * @return 返回结果
     */
    @Override
    public List<CustomerLaundryListVO> removeCustomerLaundry(CustomerLaundryQuery query) {
        query.setAuditStatus(FlowApprovalStatus.APPROVE.getValue());
        query.setUserId(SecurityUtils.getUserId());
        query.setBizType(LaundryType.CUSTOMER.getValue());
        List<CustomerLaundryListVO> supCustomers = customerLaundryMapper.pageCustomerLaundryList(query);
        if (ListUtil.isEmpty(supCustomers)){
            return ListUtil.emptyList();
        }
        List<Long> laundryIdList = ListUtil.distinctMap(supCustomers, CustomerLaundryListVO::getLaundryId);
        // 删除已下载的客户
        this.remove(Wrappers.<CustomerLaundry>lambdaQuery()
                .in(CustomerLaundry::getLaundryId,laundryIdList)
                .eq(CustomerLaundry::getCreateId,SecurityUtils.getUserId()));
        return supCustomers;
    }

    /**
     * 获取客户项目信息
     * @param customerIdList 客户id
     * @param bizIdMap <客户id,客户基本信息>
     * @return 导出数据
     */
    private List<CustomerLaundryExportModel> getProjectExportModels(List<Long> customerIdList, Map<Long, CustomerLaundryExportModel> bizIdMap) {
        List<CrmProjectListExportVO> projectListExportVOList = projectService.listProjectByCustomerId(customerIdList);
        List<CustomerLaundryExportModel> projectExportModelList = new ArrayList<>();
        projectListExportVOList.forEach(crmProjectListExportVO -> {
            CustomerLaundryExportModel projectExportModel = this.getCustomerBasicInformation(bizIdMap, crmProjectListExportVO.getCustomerId());
            projectExportModel.setProjectListExportVO(crmProjectListExportVO);
            projectExportModelList.add(projectExportModel);
        });
        return projectExportModelList;
    }

    /**
     * 获取客户比稿信息
     * @param customerIdList 客户id
     * @param bizIdMap <客户id,客户基本信息>
     * @return 导出数据
     */
    private List<CustomerLaundryExportModel> getComparedDraftExportModels(List<Long> customerIdList, Map<Long, CustomerLaundryExportModel> bizIdMap) {
        // 获取比稿信息服务
        List<ComparedDraftVO> comparedDraftVOList = comparedDraftService.queryComparedDraftDetailByIds(customerIdList, null);

        // 商务标比稿结果
        List<SysDictData> businessDraftResult = dictDataService.selectAllByType(DictConstant.BUSINESS_DRAFT_RESULT);
        Map<String, String> businessDraftResultMap = ListUtil.toMap(businessDraftResult, SysDictData::getDictValue, SysDictData::getDictLabel);
        // 技术标比稿结果
        List<SysDictData> technicalDraftResult = dictDataService.selectAllByType(DictConstant.TECHNICAL_DRAFT_RESULT);
        Map<String, String> technicalDraftResultMap = ListUtil.toMap(technicalDraftResult, SysDictData::getDictValue, SysDictData::getDictLabel);
        // 最终比稿结果
        List<SysDictData> finalDraftResult = dictDataService.selectAllByType(DictConstant.FINAL_DRAFT_RESULT);
        Map<String, String> finalDraftResultMap = ListUtil.toMap(finalDraftResult, SysDictData::getDictValue, SysDictData::getDictLabel);
        // 方案类型
        List<SysDictData> planType = dictDataService.selectAllByType(DictConstant.PLAN_TYPE);
        Map<String, String> planTypeMap = ListUtil.toMap(planType, SysDictData::getDictValue, SysDictData::getDictLabel);
        // 派芽业务类型
        List<SysDictData> pyType = dictDataService.selectAllByType(DictConstant.PY_TYPE);
        Map<String, String> pyTypeMap = ListUtil.toMap(pyType, SysDictData::getDictValue, SysDictData::getDictLabel);
        List<CustomerLaundryExportModel> comparedDraftExportModelList = new ArrayList<>(comparedDraftVOList.size());
        comparedDraftVOList.forEach(comparedDraftVO -> {
            if(comparedDraftVO.getCommercialBidFruits() != null){
                comparedDraftVO.setCommercialBidFruitsStr(businessDraftResultMap.get(comparedDraftVO.getCommercialBidFruits().toString()));
            }
            if(comparedDraftVO.getTechniqueBidFruits() != null){
                comparedDraftVO.setTechniqueBidFruitsStr(technicalDraftResultMap.get(comparedDraftVO.getTechniqueBidFruits().toString()));
            }
            if(comparedDraftVO.getFinalFruits() != null){
                comparedDraftVO.setFinalFruitsStr(finalDraftResultMap.get(comparedDraftVO.getFinalFruits().toString()));
            }
            if(comparedDraftVO.getPlanType() != null){
                comparedDraftVO.setPlanTypeStr(planTypeMap.get(comparedDraftVO.getPlanType().toString()));
            }
            if(comparedDraftVO.getPyType() != null){
                comparedDraftVO.setPyTypeStr(pyTypeMap.get(comparedDraftVO.getPyType().toString()));
            }
            CustomerLaundryExportModel comparedDraftExportModel = this.getCustomerBasicInformation(bizIdMap, comparedDraftVO.getCustomerId());
            comparedDraftExportModel.setComparedDraftVO(comparedDraftVO);
            comparedDraftExportModelList.add(comparedDraftExportModel);
        });
        return comparedDraftExportModelList;
    }

    /**
     * 获取客户开票信息
     * @param customerIdList 客户id
     * @param bizIdMap <客户id,客户基本信息>
     * @return 导出数据
     */
    private List<CustomerLaundryExportModel> getInvoicingExport(List<Long> customerIdList, Map<Long, CustomerLaundryExportModel> bizIdMap) {
        List<InvoicingVO> invoicingVOList = invoicingService.listInvoicingByCustomerId(customerIdList);
        userService.relatedUpdateInfo(invoicingVOList);
        List<CustomerLaundryExportModel> invoicingExportModelList = new ArrayList<>(invoicingVOList.size());
        invoicingVOList.forEach(invoicingVO -> {
            CustomerLaundryExportModel invoicingExportModel = this.getCustomerBasicInformation(bizIdMap, invoicingVO.getCustomerId());
            invoicingExportModel.setInvoicingVO(invoicingVO);
            invoicingExportModelList.add(invoicingExportModel);
        });
        return invoicingExportModelList;
    }

    /**
     * 获取企业账号信息
     * @param customerIdList 客户id
     * @param bizIdMap <客户id,客户基本信息>
     * @return 导出数据
     */
    private List<CustomerLaundryExportModel> getAccountExport(List<Long> customerIdList, Map<Long, CustomerLaundryExportModel> bizIdMap) {
        List<CustomerAccountListVO> customerAccountListVoS = customerAccountService.listCustomerAccountByCustomerId(customerIdList);
        userService.relatedUpdateInfo(customerAccountListVoS);
        List<CustomerLaundryExportModel> accountExportModelList = new ArrayList<>(customerAccountListVoS.size());
        customerAccountListVoS.forEach(customerAccountListVO -> {
            CustomerLaundryExportModel customerAccountExportModel = this.getCustomerBasicInformation(bizIdMap, customerAccountListVO.getCustomerId());
            customerAccountExportModel.setCustomerAccountListVo(customerAccountListVO);
            accountExportModelList.add(customerAccountExportModel);
        });
        return accountExportModelList;
    }

    /**
     * 获取企业地址信息
     * @param customerIdList 客户id
     * @param bizIdMap <客户id,客户基本信息>
     * @return 导出数据
     */
    private List<CustomerLaundryExportModel> getAddressExport(List<Long> customerIdList, Map<Long, CustomerLaundryExportModel> bizIdMap) {
        List<CustomerAddressVO> customerAddressVOList = customerAddressService.listCustomerAddressByCustomerId(customerIdList);
        if(ListUtil.isEmpty(customerAddressVOList)){
            return ListUtil.emptyList();
        }
        userService.relatedUpdateInfo(customerAddressVOList);
        List<CustomerLaundryExportModel> addressExportModelList = new ArrayList<>();
        customerAddressVOList.forEach(customerAddressVO -> {
            CustomerLaundryExportModel customerAddressExportModel = this.getCustomerBasicInformation(bizIdMap, customerAddressVO.getCustomerId());
            customerAddressExportModel.setCustomerAddressVO(customerAddressVO);
            addressExportModelList.add(customerAddressExportModel);
        });
        return addressExportModelList;
    }

    /**
     * 获取合作主体
     * @param customerIdList 客户id
     * @param bizIdMap <客户id,客户基本信息>
     * @return 导出数据
     */
    private List<CustomerLaundryExportModel> getCooperateMainstayExport(List<Long> customerIdList, Map<Long, CustomerLaundryExportModel> bizIdMap) {
        List<CooperateMainstayListVO> cooperateMainstayListVoS = cooperateMainstayService.listCooperateMainByCustomerIds(customerIdList);
        userService.relatedUpdateInfo(cooperateMainstayListVoS);
        List<CustomerLaundryExportModel> cooperateMainstayExportModelList = new ArrayList<>(cooperateMainstayListVoS.size());
        cooperateMainstayListVoS.forEach(cooperateMainstayListVO -> {
            CustomerLaundryExportModel cooperateMainstayExportModel = this.getCustomerBasicInformation(bizIdMap, cooperateMainstayListVO.getCustomerId());
            cooperateMainstayExportModel.setCooperateMainstayListVo(cooperateMainstayListVO);
            cooperateMainstayExportModelList.add(cooperateMainstayExportModel);
        });
        return cooperateMainstayExportModelList;
    }

    /**
     * 赋值客户基本信息
     * @param bizIdMap <客户id,客户基本信息>
     * @param customerId 客户id
     * @return 导出数据
     */
    private CustomerLaundryExportModel getCustomerBasicInformation(Map<Long, CustomerLaundryExportModel> bizIdMap, Long customerId) {
        CustomerLaundryExportModel customerLaundryExportModel = bizIdMap.get(customerId);
        CustomerLaundryExportModel cooperateMainstayExportModel = new CustomerLaundryExportModel();
        if(customerLaundryExportModel != null){
            cooperateMainstayExportModel.setName(customerLaundryExportModel.getName());
            cooperateMainstayExportModel.setLineBusiness(customerLaundryExportModel.getLineBusiness());
            cooperateMainstayExportModel.setIndustryCategoryStr(customerLaundryExportModel.getIndustryCategoryStr());
            cooperateMainstayExportModel.setCooperationStatusStr(customerLaundryExportModel.getCooperationStatusStr());
        }

        return cooperateMainstayExportModel;
    }

    /**
     * 获取企业联系人
     * @param bizIdMap <客户id,客户基本信息>
     * @param customerIdList 客户id
     * @return 企业联系人导出数据
     */
    private List<CustomerLaundryExportModel> getContactExport( Map<Long, CustomerLaundryExportModel> bizIdMap, List<Long> customerIdList) {
        List<ContactVO> contactVOList = contactService.listContactByCustomerId(customerIdList);
        if(ListUtil.isEmpty(contactVOList)){
            return ListUtil.emptyList();
        }
        // 查询更新人部门
        userService.relatedUpdateInfo(contactVOList);

        // 在职状态
        List<SysDictData> employmentStatus = dictDataService.selectAllByType(DictConstant.EMPLOYMENT_STATUS);
        Map<String, String> employmentStatusMap = ListUtil.toMap(employmentStatus, SysDictData::getDictValue, SysDictData::getDictLabel);
        // 决策权限
        List<SysDictData> decisionAuthority = dictDataService.selectAllByType(DictConstant.DECISION_AUTHORITY);
        Map<String, String> decisionAuthorityMap = ListUtil.toMap(decisionAuthority, SysDictData::getDictValue, SysDictData::getDictLabel);

        List<CustomerLaundryExportModel> contactExportModelList = new ArrayList<>(contactVOList.size());
        contactVOList.forEach(contactVO -> {
            if(contactVO.getEmploymentStatus() != null){
                String s = employmentStatusMap.get(contactVO.getEmploymentStatus().toString());
                contactVO.setEmploymentStatusStr(s);
            }
            if(contactVO.getDecisionAuthority() != null){
                String s = decisionAuthorityMap.get(contactVO.getDecisionAuthority().toString());
                contactVO.setDecisionAuthorityStr(s);
            }
            CustomerLaundryExportModel contactExportModel = this.getCustomerBasicInformation(bizIdMap, contactVO.getCustomerId());
            contactExportModel.setContactVO(contactVO);
            contactExportModelList.add(contactExportModel);
        });
        return contactExportModelList;
    }

    /**
     * 组装客户字典值
     * @param exportModels 导出信息
     */
    private void assembleCustomerDict(List<CustomerLaundryExportModel> exportModels) {
        if(ListUtil.isEmpty(exportModels)){
            return;
        }
        customerService.setServiceUserCategory(exportModels);
        // 客户合作状态
        List<SysDictData> cooperationStatus = dictDataService.selectAllByType(DictConstant.COOPERATION_STATUS);
        Map<String, String> cooperationStatusMap = ListUtil.toMap(cooperationStatus, SysDictData::getDictValue, SysDictData::getDictLabel);
        // 客户来源
        List<SysDictData> customerSource = dictDataService.selectAllByType(DictConstant.CUSTOMER_SOURCE);
        Map<String, String> customerSourceMap = ListUtil.toMap(customerSource, SysDictData::getDictValue, SysDictData::getDictLabel);
        // 客户类型
        List<SysDictData> customerType = dictDataService.selectAllByType(DictConstant.CUSTOMER_TYPE);
        Map<String, String> customerTypeMap = ListUtil.toMap(customerType, SysDictData::getDictValue, SysDictData::getDictLabel);
        // 品牌阶段
        List<SysDictData> brandStage = dictDataService.selectAllByType(DictConstant.BRAND_STAGE);
        Map<String, String> brandStageMap = ListUtil.toMap(brandStage, SysDictData::getDictValue, SysDictData::getDictLabel);
        // 品牌核心生意来源
        List<SysDictData> businessSource = dictDataService.selectAllByType(DictConstant.BUSINESS_SOURCE);
        Map<String, String> businessSourceMap = ListUtil.toMap(businessSource, SysDictData::getDictValue, SysDictData::getDictLabel);
        // 派芽服务产品
        List<SysDictData> serviceProduct = dictDataService.selectAllByType(DictConstant.SERVICE_PRODUCT);
        Map<String, String> serviceProductMap = ListUtil.toMap(serviceProduct, SysDictData::getDictValue, SysDictData::getDictLabel);
        // 报价策略
        List<SysDictData> biddingStrategy = dictDataService.selectAllByType(DictConstant.BIDDING_STRATEGY);
        Map<String, String> biddingStrategyMap = ListUtil.toMap(biddingStrategy, SysDictData::getDictValue, SysDictData::getDictLabel);
        // 行业类目
        List<SysDictData> industryCategory = dictDataService.selectAllByType(DictConstant.INDUSTRY_CATEGORY);
        Map<String, String> industryCategoryMap = ListUtil.toMap(industryCategory, SysDictData::getDictValue, SysDictData::getDictLabel);

        exportModels.forEach(exportModel -> {
            if(exportModel.getCooperationStatus() != null){
                exportModel.setCooperationStatusStr(cooperationStatusMap.get(exportModel.getCooperationStatus().toString()));
            }
            if(exportModel.getCustomerSource() != null){
                exportModel.setCustomerSourceStr(customerSourceMap.get(exportModel.getCustomerSource().toString()));
            }
            if(exportModel.getCustomerType() != null){
                exportModel.setCustomerTypeStr(customerTypeMap.get(exportModel.getCustomerType().toString()));
            }

            if(exportModel.getBrandStage() != null){
                exportModel.setBrandStageStr(brandStageMap.get(exportModel.getBrandStage().toString()));
            }
            if(exportModel.getBusinessSource() != null){
                exportModel.setBusinessSourceStr(businessSourceMap.get(exportModel.getBusinessSource().toString()));
            }
            if(ListUtil.isNotEmpty(exportModel.getServiceProduct())){
                List<String> serviceProductList = new ArrayList<>();
                exportModel.getServiceProduct().forEach(item -> {
                    String s = serviceProductMap.get(item);
                    if(StringUtils.isNotBlank(s)){
                        serviceProductList.add(s);
                    }
                });
                exportModel.setServiceProductStr(StringUtils.convertStringList(serviceProductList));
            }
            if(exportModel.getBiddingStrategy() != null){
                exportModel.setBiddingStrategyStr(biddingStrategyMap.get(exportModel.getBiddingStrategy().toString()));
            }
            if(ListUtil.isNotEmpty(exportModel.getIndustryCategoryIdList())){
                List<String> industryCategoryList = new ArrayList<>();
                exportModel.getIndustryCategoryIdList().forEach(item -> {
                    String s = industryCategoryMap.get(item.toString());
                    if(StringUtils.isNotBlank(s)){
                        industryCategoryList.add(s);
                    }
                });
                exportModel.setIndustryCategoryStr(StringUtils.convertStringList(industryCategoryList));
            }
        });
    }

    /**
     * 客户项目模板
     *
     * @return 模板
     */
    private ExcelSheetConfig<CustomerLaundryExportModel> getProjectExportConfig() {
        ExcelSheetConfig<CustomerLaundryExportModel> config = new ExcelSheetConfig<>();
        //项目
        config.setNeedSerialNumber(false)
                .addConfig(CustomerLaundryExportModel::getName ,"客户名称")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getCustomerMainstayName(), "客户合作主体")
                .addConfig(CustomerLaundryExportModel::getLineBusiness, "品牌/业务线")
                .addConfig(CustomerLaundryExportModel::getIndustryCategoryStr, "行业类目")
                .addConfig(CustomerLaundryExportModel::getCooperationStatusStr, "合作状态")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getProjectName(), "项目名称")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getProjectStatusStr(), "项目状态")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getProjectAmountStr(), "立项项目金额(元)")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getGrossMarginStr(), "立项毛利率预估(%)")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getFinishProjectIncomeStr(), "已完成项目收入(元)")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getFinishProjectCostStr(), "已完成项目成本(元)")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getFinishProjectProfitStr(), "已完成项目利润(元)")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getFinishProjectGrossMarginStr(), "已完成项目毛利率(%)")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getMainstayName(), "派芽合作主体")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getCreateBy(), "立项人")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getCreateDept(), "立项部门")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getProjectApprovalTimeStr(), "立项时间")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getProjectStartTimeStr(), "项目开始时间")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getExpectedEndTimeStr(), "预计结束时间")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getProjectListExportVO().getCloseCaseTimeStr(), "结案时间");
        return config;
    }

    /**
     * 客户拜访记录模板
     *
     * @return 模板
     */
    private ExcelSheetConfig<CustomerVisitExportModel> getVisitExportConfig(List<CustomerVisitExportModel> customerVisitExportModelList) {
        ExcelSheetConfig<CustomerVisitExportModel> config = new ExcelSheetConfig<>();
        //项目
        config.setNeedSerialNumber(false)
                .addConfig(CustomerVisitExportModel::getName ,"客户名称")
                .addConfig(CustomerVisitExportModel::getLineBusiness, "品牌/业务线")
                .addConfig(CustomerVisitExportModel::getIndustryCategoryStr, "行业类目")
                .addConfig(CustomerVisitExportModel::getCooperationStatusStr, "合作状态")
                .addConfig(CustomerVisitExportModel::getVisitTimeStr,"访问日期")
                .addConfig(CustomerVisitExportModel::getVisitPersonnel, "随访人员")
                .addConfig(CustomerVisitExportModel::getVisitMatters, "访问事项")
                .addConfig(CustomerVisitExportModel::getVisitOutcome, "事项结果")
                .addConfig(CustomerVisitExportModel::getNote, "备注")
                .addConfig(CustomerVisitExportModel::getUpdateBy, "更新人")
                .addConfig(CustomerVisitExportModel::getUpdateDept, "更新人部门")
                .addConfig(CustomerVisitExportModel::getUpdateTimeStr, "更新时间");
        if(ListUtil.isNotEmpty(customerVisitExportModelList)){
            List<CustomerVisitExportModel> modelList = customerVisitExportModelList.stream()
                    .filter(item -> ListUtil.isNotEmpty(item.getAnnexModel()))
                    .collect(Collectors.toList());
            int max = 1;
            if(ListUtil.isNotEmpty(modelList)){
                max = modelList.stream().mapToInt(item -> item.getAnnexModel().size()).max().getAsInt();
            }
            for(int i = 0; i < max; i++) {
                int finalI = i;
                config.addFileCellConfig(item -> item.getCustomerVisitIndex(finalI), "随访文件");
            }
        } else {
            config.addFileCellConfig(item -> item.getCustomerVisitIndex(0), "随访文件");
        }
        return config;
    }

    /**
     * 客户比稿模板
     *
     * @return 模板
     */
    private ExcelSheetConfig<CustomerLaundryExportModel> getComparedDraftExportConfig() {
        ExcelSheetConfig<CustomerLaundryExportModel> config = new ExcelSheetConfig<>();
        //项目
        config.setNeedSerialNumber(false)
                .addConfig(CustomerLaundryExportModel::getName ,"客户名称")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getCustomerCooperateName(), "客户合作主体")
                .addConfig(CustomerLaundryExportModel::getLineBusiness, "品牌/业务线")
                .addConfig(CustomerLaundryExportModel::getIndustryCategoryStr, "行业类目")
                .addConfig(CustomerLaundryExportModel::getCooperationStatusStr, "合作状态")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getComparedDraftName(), "比稿项目名称")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getCommercialBidFruitsStr(), "商务标比稿结果")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getTechniqueBidFruitsStr(), "技术标比稿结果")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getFinalFruitsStr(), "最终比稿结果")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getComparedDraftMoneyStr(), "比稿金额(元)")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getTurnoverMoneyStr(), "成交金额(元)")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getPyTypeStr(), "派芽业务类型")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getPlanTypeStr(), "方案类型")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getUpdateBy(), "比稿人")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getPlotterName(), "策划人")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getComparedDraftTimeStr(), "比稿时间")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getComparedDraftVO().getEnteringTimeStr(), "录入时间");
        return config;
    }

    /**
     * 客户开票模板
     *
     * @return 模板
     */
    private ExcelSheetConfig<CustomerLaundryExportModel> getInvoicingExportConfig() {
        ExcelSheetConfig<CustomerLaundryExportModel> config = new ExcelSheetConfig<>();
        //项目
        config.setNeedSerialNumber(false)
                .addConfig(CustomerLaundryExportModel::getName ,"客户名称")
                .addConfig(CustomerLaundryExportModel::getLineBusiness, "品牌/业务线")
                .addConfig(CustomerLaundryExportModel::getIndustryCategoryStr, "行业类目")
                .addConfig(CustomerLaundryExportModel::getCooperationStatusStr, "合作状态")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getInvoicingVO().getInvoicingName(), "开票名称")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getInvoicingVO().getDutyParagraph(), "税号")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getInvoicingVO().getBankDeposit(), "开户银行")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getInvoicingVO().getBankAccount(), "银行账号")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getInvoicingVO().getCompanyAddress(), "公司地址")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getInvoicingVO().getPhone(), "电话")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getInvoicingVO().getUpdateBy(), "更新人")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getInvoicingVO().getUpdateDept(), "更新人部门")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getInvoicingVO().getUpdateTimeStr(), "更新时间");
        return config;
    }

    /**
     * 客户账号模板
     *
     * @return 模板
     */
    private ExcelSheetConfig<CustomerLaundryExportModel> getAccountExportConfig() {
        ExcelSheetConfig<CustomerLaundryExportModel> config = new ExcelSheetConfig<>();
        //项目
        config.setNeedSerialNumber(false)
                .addConfig(CustomerLaundryExportModel::getName ,"客户名称")
                .addConfig(CustomerLaundryExportModel::getLineBusiness, "品牌/业务线")
                .addConfig(CustomerLaundryExportModel::getIndustryCategoryStr, "行业类目")
                .addConfig(CustomerLaundryExportModel::getCooperationStatusStr, "合作状态")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCustomerAccountListVo().getAccountName(), "账户名称")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCustomerAccountListVo().getBankAccount(), "银行账号")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCustomerAccountListVo().getBankDeposit(), "开户银行")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCustomerAccountListVo().getUpdateBy(), "更新人")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCustomerAccountListVo().getUpdateDept(), "更新人部门")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCustomerAccountListVo().getUpdateTimeStr(), "更新时间");
        return config;
    }

    /**
     * 客户地址模板
     *
     * @return 模板
     */
    private ExcelSheetConfig<CustomerLaundryExportModel> getAddressExportConfig() {
        ExcelSheetConfig<CustomerLaundryExportModel> config = new ExcelSheetConfig<>();
        //项目
        config.setNeedSerialNumber(false)
                .addConfig(CustomerLaundryExportModel::getName ,"客户名称")
                .addConfig(CustomerLaundryExportModel::getLineBusiness, "品牌/业务线")
                .addConfig(CustomerLaundryExportModel::getIndustryCategoryStr, "行业类目")
                .addConfig(CustomerLaundryExportModel::getCooperationStatusStr, "合作状态")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCustomerAddressVO().getCustomerAddress(), "客户地址")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCustomerAddressVO().getUpdateBy(), "更新人")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCustomerAddressVO().getUpdateDept(), "更新人部门")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCustomerAddressVO().getUpdateTimeStr(), "更新时间");
        return config;
    }

    /**
     * 客户合作主体模板
     *
     * @return 模板
     */
    private ExcelSheetConfig<CustomerLaundryExportModel> getCooperateMainstayExportConfig() {
        ExcelSheetConfig<CustomerLaundryExportModel> config = new ExcelSheetConfig<>();
        //项目
        config.setNeedSerialNumber(false)
                .addConfig(CustomerLaundryExportModel::getName ,"客户名称")
                .addConfig(CustomerLaundryExportModel::getLineBusiness, "品牌/业务线")
                .addConfig(CustomerLaundryExportModel::getIndustryCategoryStr, "行业类目")
                .addConfig(CustomerLaundryExportModel::getCooperationStatusStr, "合作状态")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCooperateMainstayListVo().getMainstayName(), "合作主体名称")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCooperateMainstayListVo().getUpdateBy(), "更新人")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCooperateMainstayListVo().getUpdateDept(), "更新人部门")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getCooperateMainstayListVo().getUpdateTimeStr(), "更新时间");
        return config;
    }

    /**
     * 联系人模板
     *
     * @return 模板
     */
    private ExcelSheetConfig<CustomerLaundryExportModel> getContactExportConfig() {
        ExcelSheetConfig<CustomerLaundryExportModel> config = new ExcelSheetConfig<>();
        //项目
        config.setNeedSerialNumber(false)
                .addConfig(CustomerLaundryExportModel::getName ,"客户名称")
                .addConfig(CustomerLaundryExportModel::getLineBusiness, "品牌/业务线")
                .addConfig(CustomerLaundryExportModel::getIndustryCategoryStr, "行业类目")
                .addConfig(CustomerLaundryExportModel::getCooperationStatusStr, "合作状态")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getContactVO().getConnectionName(), "客户姓名")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getContactVO().getPhone(), "电话")

                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getContactVO().getWechatNumber(), "微信")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getContactVO().getOtherNumber(), "钉钉/飞书/其它")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getContactVO().getEmail(), "邮箱")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getContactVO().getDepartmentName(), "所在部门")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getContactVO().getPostName(), "岗位")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getContactVO().getDecisionAuthorityStr(), "决策权限")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getContactVO().getEmploymentStatusStr(), "就职状态")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getContactVO().getUpdateBy(), "更新人")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getContactVO().getUpdateDept(), "更新人部门")
                .addConfig(customerLaundryExportModel -> customerLaundryExportModel.getContactVO().getUpdateTimeStr(), "更新时间");
        return config;
    }

    /**
     * 客户基本信息模板
     *
     * @return 模板
     */
    private ExcelSheetConfig<CustomerLaundryExportModel> getCustomerExportConfig() {
        ExcelSheetConfig<CustomerLaundryExportModel> config = new ExcelSheetConfig<>();
        //项目
        config.setNeedSerialNumber(false)
                .addConfig(CustomerLaundryExportModel::getName, "客户名称")
                .addConfig(CustomerLaundryExportModel::getLineBusiness, "品牌/业务线")
                .addConfig(CustomerLaundryExportModel::getIndustryCategoryStr, "行业类目")
                .addConfig(CustomerLaundryExportModel::getCooperationStatusStr, "合作状态")
                .addConfig(CustomerLaundryExportModel::getServiceUserName, "服务人员")
                .addConfig(CustomerLaundryExportModel::getCustomerSourceStr, "客户来源")
                .addConfig(CustomerLaundryExportModel::getCustomerTypeStr, "客户类型")
                .addConfig(CustomerLaundryExportModel::getBrandStageStr, "品牌阶段")
                .addConfig(CustomerLaundryExportModel::getCooperativeSector, "合作部门")
                .addConfig(CustomerLaundryExportModel::getBusinessSourceStr, "品牌核心生意来源")
                .addConfig(CustomerLaundryExportModel::getServiceProductStr, "派芽服务产品")
                .addConfig(CustomerLaundryExportModel::getBiddingStrategyStr, "报价策略")
                .addConfig(CustomerLaundryExportModel::getDecisionLink, "决策链路")
                .addConfig(CustomerLaundryExportModel::getRemark, "备注")
                .addConfig(CustomerLaundryExportModel::getCreateBy, "录入人")
                .addConfig(CustomerLaundryExportModel::getCreateDept, "录入人部门")
                .addConfig(CustomerLaundryExportModel::getCreateTimeStr, "录入时间");
        return config;
    }


    /**
     * 导入客户管理-查看清单
     * @param exportList 导入数据
     * @return 导入结果
     */
    @Override
    public String importCustomerLaundry(List<CustomerLaundryExportModel> exportList) {
        List<CustomerLaundry> customerLaundryList = this.customerLaundryConverter.toEntityByExportModel(exportList);
        this.saveBatch(customerLaundryList);
        return "导入成功!";
    }

    /**
     * 客户管理-查看清单头部统计
     * @return 是否成功
     */
    @Override
    public CustomerCountVO customerLaundryCount() {
        CustomerLaundryQuery query = new CustomerLaundryQuery();
        query.setAuditStatus(FlowApprovalStatus.APPROVE.getValue());
        query.setUserId(SecurityUtils.getUserId());
        query.setBizType(LaundryType.CUSTOMER.getValue());
        return customerLaundryMapper.customerLaundryCount(query);
    }

    /**
     * 客户管理-移除清单
     * @param dto 客户ID
     * @return 是否成功
     */
    @Override
    public Boolean  removeLaundry(CustomerLaundryListDTO dto) {
        if(!dto.getIsAll() && ListUtil.isEmpty(dto.getCustomerIdList())){
            throw new ServiceException("请选择要移除的客户");
        }
        dto.setUpdateTime(LocalDateTime.now());

        LoginUser loginUser = SecurityUtils.getLoginUser();
        Assert.notNull(loginUser,"登录用户为空，请重新登陆");
        dto.setUpdateId(loginUser.getUserId());
        dto.setUpdateBy(loginUser.getUsername());
        dto.setCreateId(loginUser.getUserId());

        //如果合作状态的值为3，则置为null
        if(Objects.nonNull(dto.getCooperationStatus()) && Objects.equals(CooperationStatus.ALL.getValue(), dto.getCooperationStatus())) {
            dto.setCooperationStatus(null);
        }
        return customerLaundryMapper.removeLaundry(dto);
    }

    /**
     * 执行任务
     * @param args 任务参数
     */
    @Override
    public void execute(CustomerLaundryQuery args) {
        this.exportCustomerLaundry(args);
    }

    /**
     * 获取任务参数类型
     * @return 任务参数类型
     */
    @Override
    public Class<CustomerLaundryQuery> argsClass() {
        return CustomerLaundryQuery.class;
    }
}
