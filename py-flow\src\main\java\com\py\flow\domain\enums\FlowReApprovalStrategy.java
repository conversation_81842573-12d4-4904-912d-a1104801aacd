package com.py.flow.domain.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程重新审批策略
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FlowReApprovalStrategy implements IDict<Integer> {

    /** 重新逐级审批 */
    Restart(0, "退回后, 重新逐级审批"),
    /** 跳转至上次审批失败的节点 */
    JumpToFailNode(1,"退回后再次审批, 从上一失败节点开始审批"),
    ;

    private final Integer value;
    private final String label;
}
