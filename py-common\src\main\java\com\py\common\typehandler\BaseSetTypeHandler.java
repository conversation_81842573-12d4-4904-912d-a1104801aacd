package com.py.common.typehandler;

import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 基础集合类型处理器
 * <p>对集合使用 ',' 拼接</p>
 * @param <T> 处理元素类型
 * <AUTHOR>
 */
public abstract class BaseSetTypeHandler<T> extends BaseTypeHandler<Collection<T>> {

    /** 分隔符 */
    public static final String DELIMITER = ",";

    /** 内容序列化器 */
    protected final Function<T, String> serializer;

    /** 内容反序列化器 */
    protected final Function<String, T> deserializer;

    public BaseSetTypeHandler() {
        this.serializer = this.initSerializer();
        this.deserializer = this.initDeserializer();
    }

    /**
     * 初始化内容序列化器, 将序列元素转化为字符串
     * <p>输入不会为null</p>
     * @return 内容序列化器
     */
    protected abstract Function<T, String> initSerializer();

    /**
     * 初始化内容反序列化器, 从字符串还原为序列元素
     * @return 内容反序列化器
     */
    protected abstract Function<String, T> initDeserializer();

    @Override
    public void setNonNullParameter(
            PreparedStatement preparedStatement,
            int i,
            Collection<T> list,
            JdbcType jdbcType) throws SQLException {
        if(ListUtil.isEmpty(list)) {
            preparedStatement.setString(i, StringUtils.EMPTY);
        } else {
            List<String> content = list.stream()
                    .filter(Objects::nonNull)
                    .map(this.serializer)
                    .collect(Collectors.toList());
            preparedStatement.setString(i, String.join(DELIMITER, content));
        }
    }

    @Override
    public List<T> getNullableResult(ResultSet resultSet, String columnName) throws SQLException {
        return this.deserializeFieldList(resultSet.getString(columnName));
    }

    @Override
    public List<T> getNullableResult(ResultSet resultSet, int columnIndex) throws SQLException {
        return this.deserializeFieldList(resultSet.getString(columnIndex));
    }

    @Override
    public List<T> getNullableResult(CallableStatement callableStatement, int columnIndex) throws SQLException {
        return this.deserializeFieldList(callableStatement.getString(columnIndex));
    }

    /**
     * 反序列化接口字段列表
     * @param content 内容
     * @return 接口字段列表对象
     */
    private List<T> deserializeFieldList(String content) {
        if(StringUtils.isBlank(content)) {
            return ListUtil.emptyList();
        }

        String[] split = content.split(DELIMITER);
        return ListUtil.map(split, this.deserializer);
    }
}
