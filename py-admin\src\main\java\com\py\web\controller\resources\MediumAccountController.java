package com.py.web.controller.resources;


import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.resources.mediumaccount.domain.dto.MediumAccountDTO;
import com.py.resources.mediumaccount.domain.dto.MediumAccountExportModel;
import com.py.resources.mediumaccount.domain.query.MediumAccountQuery;
import com.py.resources.mediumaccount.domain.vo.MediumAccountListVO;
import com.py.resources.mediumaccount.domain.vo.MediumAccountVO;
import com.py.resources.mediumaccount.service.IMediumAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 媒介资源管理-资源账号信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Api(tags = "媒介资源管理-资源账号信息")
@RestController
@RequestMapping("/resources/mediumAccount")
public class MediumAccountController extends BaseController {

    /** 媒介资源管理-资源账号信息服务 */
    @Resource
    private IMediumAccountService mediumAccountService;

    /**
     * 查询媒介资源管理-资源账号信息列表
     *
     * @param query 媒介资源管理-资源账号信息查询参数
     * @return 媒介资源管理-资源账号信息列表
     */
    @ApiOperation("查询媒介资源管理-资源账号信息列表")
    @PreAuthorize("@ss.hasPermi('resources:mediumAccount:list')")
    @GetMapping("/listMediumAccount")
    public R<List<MediumAccountListVO>> listMediumAccount(MediumAccountQuery query) {
        List<MediumAccountListVO> voList = this.mediumAccountService.listMediumAccount(query);
        return R.success(voList);
    }

    /**
     * 分页查询媒介资源管理-资源账号信息列表
     *
     * @param query 媒介资源管理-资源账号信息查询参数
     * @return 媒介资源管理-资源账号信息分页
     */
    @ApiOperation("分页查询询媒介资源管理-资源账号信息列表")
    @PreAuthorize("@ss.hasPermi('resources:mediumAccount:list')")
    @GetMapping("/pageMediumAccount")
    public R<PageInfo<MediumAccountListVO>> pageMediumAccount(MediumAccountQuery query) {
        PageInfo<MediumAccountListVO> voList = this.mediumAccountService.pageMediumAccountList(query);
        return R.success(voList);
    }

    /**
     * 获取媒介资源管理-资源账号信息详细信息
     * @param id 媒介资源管理-资源账号信息主键
     * @return 媒介资源管理-资源账号信息视图模型
     */
    @ApiOperation("获取媒介资源管理-资源账号信息详细信息")
    @PreAuthorize("@ss.hasPermi('resources:mediumAccount:query')")
    @GetMapping(value = "/{id}")
    public R<MediumAccountVO> getInfo(@PathVariable("id") Long id) {
        return R.success(mediumAccountService.selectMediumAccountById(id));
    }

    /**
     * 新增媒介资源管理-资源账号信息(废弃)
     *
     * @param dto 媒介资源管理-资源账号信息修改参数
     * @return 是否成功
     */
    @ApiOperation("新增媒介资源管理-资源账号信息")
    @PreAuthorize("@ss.hasPermi('resources:mediumAccount:add')")
    @Log(title = "媒介资源管理-资源账号信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody List<MediumAccountDTO> dto) {
        return R.success(mediumAccountService.insertMediumAccount(dto));
    }


    /**
     * 删除媒介资源管理-资源账号信息
     * @param ids 需要删除的媒介资源管理-资源账号信息主键集合
     * @return 是否成功
     */
    @ApiOperation("删除媒介资源管理-资源账号信息" )
    @PreAuthorize("@ss.hasPermi('resources:mediumAccount:remove')")
    @Log(title = "媒介资源管理-资源账号信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(mediumAccountService.deleteMediumAccountByIds(ids));
    }

    /**
     * 导出媒介资源管理-资源账号信息
     * @param response 请求响应
     * @param resourceIdList 资源id列表
     */
    @ApiOperation("导出媒介资源管理-资源账号信息")
    @PreAuthorize("@ss.hasPermi('resources:mediumAccount:export')")
    @Log(title = "媒介资源管理-资源账号信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, List<Long> resourceIdList) {
        List<MediumAccountExportModel> exportList = this.mediumAccountService.exportMediumAccount(resourceIdList);

        ExcelUtil<MediumAccountExportModel> util = new ExcelUtil<>(MediumAccountExportModel. class);
        util.exportExcel(response, exportList, "媒介资源管理-资源账号信息数据" );
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumAccount:import')" )
    @Log(title = "媒介资源管理-资源账号信息" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<MediumAccountExportModel> util = new ExcelUtil<>(MediumAccountExportModel.class);
        List<MediumAccountExportModel> mediumAccountList = util.importExcel(file.getInputStream());
        String message = this.mediumAccountService.importMediumAccount(mediumAccountList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumAccount:import')" )
    @Log(title = "媒介资源管理-资源账号信息" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<MediumAccountExportModel> util = new ExcelUtil<>(MediumAccountExportModel.class);
        util.importTemplateExcel(response, "媒介资源管理-资源账号信息数据" );
    }

    /**
     * 查询媒介资源管理-资源账号信息列表
     *
     * @param query 媒介资源管理-资源账号信息查询参数
     * @return 媒介资源管理-资源账号信息列表
     */
    @ApiOperation("查询媒介资源管理-资源账号信息列表")
    @PreAuthorize("@ss.hasPermi('resources:mediumAccount:list')")
    @GetMapping("/listMediumPaymentAccount")
    public R<List<MediumAccountListVO>> listMediumPaymentAccount(MediumAccountQuery query) {
        List<MediumAccountListVO> voList = this.mediumAccountService.listMediumPaymentAccount(query);
        return R.success(voList);
    }


    /**
     * 查询媒介资源管理-资源账号信息列表(退款页面使用)
     *
     * @param query 媒介资源管理-资源账号信息查询参数
     * @return 媒介资源管理-资源账号信息列表
     */
    @ApiOperation("查询媒介资源管理-资源账号信息列表(退款页面使用)")
    @PreAuthorize("@ss.hasPermi('resources:mediumAccount:list')")
    @GetMapping("/listMediumPaymentAccount/refund")
    public R<List<MediumAccountListVO>> listMediumPaymentAccountRefund(MediumAccountQuery query) {
        List<MediumAccountListVO> voList = this.mediumAccountService.findRefundMediumPaymentAccount(query);
        return R.success(voList);
    }
}
