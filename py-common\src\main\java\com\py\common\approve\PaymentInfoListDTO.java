package com.py.common.approve;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.enums.PaymentType;
import com.py.common.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 付款列表视图模型
 *
 */
@Data
@ApiModel("付款列表视图模型")
public class PaymentInfoListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 自增id */
    @ApiModelProperty("自增id")
    private Long id;

    /** 项目资源id */
    @ApiModelProperty("项目资源id")
    private Long projectResourceId;

    /** 项目id */
    @ApiModelProperty("项目id")
    private Long projectId;

    /** 项目名称 */
    @ApiModelProperty("项目名称" )
    private String projectName;

    /** 项目编号 */
    @ApiModelProperty("项目编号" )
    private String projectCode;

    /** 派芽合作主体名称 */
    @ApiModelProperty("派芽合作主体名称")
    private String mainstayName;

    /** 资源id */
    @ApiModelProperty("资源id")
    private Long resourceId;

    /** 资源code */
    @ApiModelProperty("资源code")
    private String resourceCode;

    /** 选资源类型 */
    @ApiModelProperty("选资源类型")
    private Integer type;

    /** 资源名称 */
    @ApiModelProperty("资源名称")
    private String resourceName;

    /** 合作平台 */
    @ApiModelProperty("合作平台")
    private String cooperationPlatform;

    /** 资源类型 */
    @ApiModelProperty("资源类型")
    private Integer resourceType;
    private String resourceTypeName;

    /** 合作方式 */
    @ApiModelProperty("合作方式")
    private Integer cooperationMethod;
    private String cooperationMethodName;

    /** 付款渠道id */
    @ApiModelProperty("付款渠道id")
    private Long channelsId;

    /** 渠道名称 */
    @ApiModelProperty("渠道名称")
    private String channelsName;

    /** 付款账户 */
    @ApiModelProperty("付款账户")
    private String paymentAccount;

    /** 付款状态 */
    @ApiModelProperty("付款状态")
    private Integer primePaymentType;
    private Integer paymentType;
    private String paymentTypeName;

    /** 已付金额 */
    @ApiModelProperty("已付金额")
    private BigDecimal amountPaid;

    /** 已退金额 */
    @ApiModelProperty("已退金额")
    private BigDecimal refundedAmount;

    /** 成本金额 */
    @ApiModelProperty("成本金额")
    private BigDecimal costAmount;

    /** 资源含税售价 */
    @ApiModelProperty("资源含税售价")
    private BigDecimal includingTaxPrice;


    /** 资源未税成本价 */
    @ApiModelProperty("资源未税成本价" )
    private BigDecimal resourceUntaxedCostPrice;

    /** 资源含税采购价 */
    @ApiModelProperty("资源含税采购价" )
    private BigDecimal purchasePrice;

    /** 立项金额 */
    @ApiModelProperty("立项金额")
    private BigDecimal projectAmount;

    /** 收入金额*/
    @ApiModelProperty("收入金额")
    private BigDecimal incomeMoney;

    /** 收款状态(0.未收款 1.部分收款 2.已收款)*/
    @ApiModelProperty("收款状态(0.未收款 1.部分收款 2.已收款)")
    private Integer proceedsType;


    /** 已收金额(回款金额)*/
    @ApiModelProperty("已收金额")
    private BigDecimal receivedPaid;

    /** 未收金额*/
    @ApiModelProperty("未收金额")
    private BigDecimal uncollectedAmount;

    /** 坏账金额*/
    @ApiModelProperty("坏账金额")
    private BigDecimal badDebtAmount;

    /** 未付金额 */
    @ApiModelProperty("未付金额")
    private BigDecimal unpaidAmount;

    /** 本次付款金额*/
    @ApiModelProperty("本次付款金额" )
    private BigDecimal currentPaymentAmount;

    /** 合作状态(0.请选择 1.未通过 2.已通过 3.未通过-已确认 4.已通过-已确认) */
    @ApiModelProperty("合作状态(0.请选择 1.未通过 2.已通过 3.未通过-已确认 4.已通过-已确认)")
    private Integer cooperationStatus;

    /** 是否通过过(成本管理) */
    @ApiModelProperty("是否通过过(成本管理)")
    private Integer hasPassed;


    /** 资源的执行未付金额 */
    @ApiModelProperty("资源的执行未付金额" )
    private BigDecimal carryUncollectedAmount;

    /**项目执行含税售价  */
    @ApiModelProperty("项目执行含税售价")
    private BigDecimal projectIncludingTaxPrice;

    /** 项目已付金额字段 */
    @ApiModelProperty("项目已付金额字段")
    private BigDecimal projectReceivedPaid;

    /**本项目是否全额回款*/
    @ApiModelProperty("本项目是否全额回款")
    private String fullPaymentCollection;




    public String getFullPaymentCollection() {
        if (Objects.isNull(this.getProceedsType())){
            return "--";
        }
        if (this.getProceedsType().equals(PaymentType.PAID.getValue())){
            return "是";
        }
        if (this.getProceedsType().equals(PaymentType.NON_PAYMENT.getValue())||
                this.getProceedsType().equals(PaymentType.PARTIAL_PAYMENT.getValue())){
            return "否";
        }
        return "--";
    }

    //------数据来源py_project表,依据项目id查
    /** 立项时间 */
    @ApiModelProperty("立项时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime projectApprovalTime;

    /**通过率 执行表单通过的资源数量/执行表单总资源数量 */
    @ApiModelProperty("通过率")
    private BigDecimal passingRate;

}
