package com.py.common.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务类型
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TaskType implements IDict<Integer> {

    /** 导入 */
    Import(0, "导入"),
    /** 导出 */
    Export(1, "导出"),

    /** 交接 */
    HANDOVER(2, "交接"),
    ;

    private final Integer value;
    private final String label;
}
