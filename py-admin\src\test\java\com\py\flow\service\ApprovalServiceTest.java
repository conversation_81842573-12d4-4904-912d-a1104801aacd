package com.py.flow.service;

import com.py.common.core.domain.model.LoginUser;
import com.py.common.utils.JsonUtil;
import com.py.flow.api.IApprovalService;
import com.py.flow.domain.dto.flow.ApprovalSubmitDTO;
import com.py.flow.domain.enums.ApprovalBizType;
import com.py.flow.flowinstance.domain.query.ApprovalInfoQuery;
import com.py.flow.flowinstance.domain.vo.ApprovalFlowChartVO;
import lombok.extern.log4j.Log4j2;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * 审批服务测试类型
 * <AUTHOR>
 */
@Log4j2
@SpringBootTest
@RunWith(SpringRunner.class)
public class ApprovalServiceTest {

    /** 审批服务 */
    @Resource
    private IApprovalService approvalService;

    /** 系统用户服务 */
    @Resource
    private UserDetailsService userDetailsService;

    private Long bizId = 5L;

    @Ignore
    @Before
    public void setUp() {
        UserDetails userDetails = this.userDetailsService.loadUserByUsername("admin");
        LoginUser loginUser = (LoginUser) userDetails;

        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authenticationToken);
    }

    /** 是否能提交审批 */
    @Ignore
    @Test
    public void can_submitFlow() {
        ApprovalSubmitDTO submitDto = new ApprovalSubmitDTO();
        submitDto.setBizType(ApprovalBizType.AddProject);
        submitDto.setBizId(bizId);
        submitDto.setProjectName("测试项目");
        this.approvalService.submitFlow(submitDto);

        log.info("提交审批完成");
    }

    /** 是否能获取审批详情 */
    @Ignore
    @Test
    public void getApprovalFlowChart(){
        ApprovalInfoQuery query = new ApprovalInfoQuery();
        query.setBizType(ApprovalBizType.AddProject.getValue());
        query.setBizId(bizId);
        Object approvalFlowChart = this.approvalService.getApprovalFlowChart(query);
        log.info("审批详情: {}", JsonUtil.obj2StringPretty(approvalFlowChart));
    }
}
