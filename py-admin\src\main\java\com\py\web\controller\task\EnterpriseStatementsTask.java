package com.py.web.controller.task;

import com.py.common.enums.MessageType;
import com.py.common.utils.collection.ListUtil;
import com.py.system.message.domain.dto.HpMessageDTO;
import com.py.system.message.service.IHpMessageService;
import com.py.system.user.userdept.service.ISysUserDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version EnterpriseStatementsTask 2023/11/28 17:45
 */
@Slf4j
@Component
@EnableScheduling
public class EnterpriseStatementsTask {

    /** 首页-消息中心服务 */
    @Resource
    private IHpMessageService hpMessageService;

    /** 组织权限管理-用户部门Service接口 */
    @Resource
    private ISysUserDeptService sysUserDeptService;

    @Scheduled(cron = "0 5 0 1 * ?")
    public void generateFinancial() {
        try {
            log.info("每月企业财报发送消息：记录当前开始时间：" + LocalDateTime.now());
            List<Long> userIdList = sysUserDeptService.listSpecifyPermissionsUser();
            // 添加Admin用户
            if(ListUtil.isEmpty(userIdList)) {
                userIdList = ListUtil.singletonList(1L);
            } else {
                userIdList.add(1L);
                userIdList = ListUtil.distinct(userIdList,x -> x);
            }

            HpMessageDTO hpMessageDTO = new HpMessageDTO();
            hpMessageDTO.setBizId(-1L);
            hpMessageDTO.setMessageType(MessageType.ENTERPRISE_STATEMENTS);
            hpMessageDTO.setUserIds(userIdList);
            hpMessageDTO.setMessageBody("【企业财报】上月财报已生成，请查看");
            hpMessageService.insertHpMessage(hpMessageDTO);

            log.info("每月企业财报发送消息：记录当前结束时间：" + LocalDateTime.now());
        } catch(Exception e) {
            log.error("每月企业财报发送消息：", e);
        }
    }
}
