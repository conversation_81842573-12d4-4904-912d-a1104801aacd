package com.py.common.core.domain.service;

import com.py.common.core.domain.entity.SysDictData;
import rx.functions.Action2;

import java.util.List;
import java.util.function.Function;

/**
 * 字典服务
 * <AUTHOR>
 */
public interface IDictService {

    /**
     * 根据字典类型查询字典数据
     * @param dictType 字典类型
     * @return 字典数据集合信息
     */
    List<SysDictData> selectDictDataByType(String dictType);

    /**
     * 提供回调函数为指定项目关联字典
     * @param <T> 关联项目类型
     * @param item 项目
     * @param dictType 字典类型
     * @param dictValueSelector 字典键值选择器
     * @param relatedDictCallback 关联字典设置回调
     */
    <T> void relatedDict(T item, String dictType, Function<T, String> dictValueSelector, Action2<T, SysDictData> relatedDictCallback);

    /**
     * 提供回调函数为指定关联字典
     * @param <T> 关联项目类型
     * @param item 项目
     * @param dictType 字典类型
     * @param dictValueSelector 字典键值选择器
     * @param relatedDictCallback 关联字典设置回调
     */
    <T> void relatedDictList(T item, String dictType, Function<T, List<String>> dictValueSelector, Action2<T, List<SysDictData>> relatedDictCallback);

    /**
     * 提供回调函数为指定列表关联字典
     * @param <T> 查询列表元素类型
     * @param list 列表
     * @param dictType 字典类型
     * @param dictValueSelector 字典键值选择器
     * @param relatedDictCallback 关联字典设置回调
     */
    <T> void relatedDict(List<T> list, String dictType, Function<T, String> dictValueSelector, Action2<T, SysDictData> relatedDictCallback);

    /**
     * 提供回调函数为指定列表关联字典
     * @param <T> 查询列表元素类型
     * @param list 列表
     * @param dictType 字典类型
     * @param dictValueSelector 字典键值选择器
     * @param relatedDictCallback 关联字典设置回调
     */
    <T> void relatedDictList(List<T> list, String dictType, Function<T, List<String>> dictValueSelector, Action2<T, List<SysDictData>> relatedDictCallback);
}
