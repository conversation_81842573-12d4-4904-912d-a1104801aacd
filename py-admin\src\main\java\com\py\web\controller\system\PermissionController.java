package com.py.web.controller.system;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.framework.web.service.UserInfoCacheHelper;
import com.py.system.dept.domain.vo.DeptVO;
import com.py.system.permission.domain.dto.PermissionDTO;
import com.py.system.permission.domain.query.PermissionListQuery;
import com.py.system.permission.domain.vo.PermissionListVO;
import com.py.system.permission.domain.vo.PermissionVO;
import com.py.system.permission.postpermission.service.IPostPermissionService;
import com.py.system.permission.service.IPermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 权限Controller
 * <AUTHOR>
 */
@Api(tags = "组织权限管理 - 权限管理")
@RestController
@RequestMapping("/permission/")
public class PermissionController {

    /** 权限服务 */
    @Resource
    private IPermissionService permissionService;

    /** 职位数据权限服务 */
    @Resource
    private IPostPermissionService postPermissionService;

    /** 用户信息缓存帮助类 */
    @Resource
    private UserInfoCacheHelper userInfoCacheHelper;

    /**
     * 查询权限列表
     * @param query 权限列表查询请求
     * @return 权限列表VO
     */
    @ApiOperation("查询权限列表")
    @PreAuthorize("@ss.hasPermi('system:permission:list')")
    @GetMapping(value = "/list")
    public R<PageInfo<PermissionListVO>> pagePermission(PermissionListQuery query){
        PageInfo<PermissionListVO> voList = this.postPermissionService.pagePermission(query);
        return R.success(voList);
    }

    /**
     * 获取权限信息
     * @param postId 职位ID
     * @return 权限信息
     */
    @ApiOperation("获取权限信息")
    @PreAuthorize("@ss.hasPermi('system:permission:query')")
    @GetMapping(value = "/{postId}")
    public R<PermissionVO> getInfo(@PathVariable Long postId){
        Assert.notNull(postId, "职位ID不能为空");
        return R.success(this.permissionService.getInfo(postId));
    }

    /**
     * 配置权限
     * @param dto 权限配置请求
     * @return 是否成功
     */
    @ApiOperation("配置权限")
    @PreAuthorize("@ss.hasPermi('system:permission:edit')")
    @Log(title = "权限管理 - 配置权限", businessType = BusinessType.UPDATE)
    @PostMapping
    public R<Boolean> configPermission(@Validated @RequestBody PermissionDTO dto){
        this.permissionService.configPermission(dto);
        this.userInfoCacheHelper.refreshLoginUserCache(user -> user.getUser().getPostIdList().contains(dto.getPostId()));
        return R.success(true);
    }

    /**
     * 查询列表上的更新部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询列表上的更新部门下拉" )
    @GetMapping("/listPermissionDept" )
    public R<PageInfo<DeptVO>> listPermissionDept(PermissionListQuery query){
        return R.success(postPermissionService.listPermissionDept(query));
    }
}
