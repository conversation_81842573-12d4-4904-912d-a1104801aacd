package com.py.common.tools.infocompletionratecalculator;

import com.py.common.tools.infocompletionratecalculator.annotation.CalculatedField;
import lombok.Getter;
import lombok.Setter;
import org.junit.Ignore;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * 信息完整度计算器测试
 * <AUTHOR>
 */
//@SpringBootTest
//@RunWith(SpringRunner.class)
class InfoCompletionRateCalculatorTest {

    /** 信息完整度计算器 */
    @Resource
    private InfoCompletionRateCalculator calculator;

    /**
     * 是否能执行完整度计算测试
     */
    @Test
    @Ignore
    void canCalculationTest() {
        TestClass test = new TestClass();
        test.setA(2);

        BigDecimal result = this.calculator.calculation(test, TestClass.class);
        assertEquals(new BigDecimal("0.25"), result);
    }

    /** 测试类 */
    @Getter
    @Setter
    private static class TestClass {

        @CalculatedField(required = true)
        private Integer a;

        @CalculatedField
        private Integer b;

        @CalculatedField
        private List<Long> c;

        @CalculatedField
        private String d;
    }

}
