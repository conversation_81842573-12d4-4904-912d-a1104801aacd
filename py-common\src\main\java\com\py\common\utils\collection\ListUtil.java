package com.py.common.utils.collection;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import com.py.common.constant.Constants;
import com.py.common.utils.MathUtils;
import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.queryable.PageQueryable;
import lombok.NonNull;
import lombok.var;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.Assert;
import rx.functions.Action1;
import rx.functions.Action2;
import rx.functions.Func2;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;

/**
 * List工具类
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class ListUtil {

    /**
     * 分割List
     * @param list 被分割的list
     * @param len 需要的list长度
     * @return 按分割长度, 分割的嵌套列表
     */
    public static <T> List<List<T>> splitList(List<T> list, int len) {
        if(len < 1) {
            throw new IllegalArgumentException("分割长度必需大于1, 当前输入分割长度:" + len);
        }
        if(isEmpty(list) == true) {
            return emptyList();
        }

        int size = list.size();
        int resultSize = size / len;
        if(size % len > 0) {
            resultSize++;
        }
        List<List<T>> result = new ArrayList<>(resultSize);

        if(size <= len) {
            result.add(list);
            return result;
        }

        int count = (size + len - 1) / len;

        for(int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * len, (Math.min((i + 1) * len, size)));
            result.add(subList);
        }
        return result;
    }

    /**
     * 分割List
     * @param list 被分割的list
     * @param len 需要的list长度
     * @return 按分割长度, 分割的嵌套列表
     */
    public static <T> List<List<T>> splitList(Collection<T> list, int len) {
        if(len < 1) {
            throw new IllegalArgumentException("分割长度必需大于1, 当前输入分割长度:" + len);
        }

        List<T> newList = new ArrayList<>(list);
        return splitList(newList, len);
    }

    /**
     * 取得列表中的最后一项
     * @param list 列表
     * @param <T> 列表类型
     * @return 列表中的最后一项, 列表为空时返回 null
     */
    public static <T> T getLast(List<T> list) {
        if(isEmpty(list)) {
            return null;
        }

        return list.get(list.size() - 1);
    }

    /**
     * 取得列表中的最大值和最小值
     * @param list 数据源
     * @return 最大值 + 最小值
     */
    public static <T extends Comparable<T>> Pair<T, T> getMaxAndMin(List<T> list) {
        if(isEmpty(list)) {
            return null;
        }

        int listSize = list.size();
        if(listSize == 1) {
            return Pair.of(list.get(0), list.get(0));
        }

        T min = list.get(0), max = list.get(0);

        for(int i = 1; i < listSize; i++) {

            if(list.get(i).compareTo(min) < 0) {
                min = list.get(i);
            }
            if(list.get(i).compareTo(max) > 0) {
                max = list.get(i);
            }
        }

        return Pair.of(max, min);
    }

    /**
     * 取得列表中指定键的最大值和最小值
     * @param list 数据源
     * @param ksySelector 键选择器
     * @param <T> 列表元素类型
     * @param <K> 需要获取最大值和最小值的键类型
     * @return 最大值 + 最小值
     */
    public static <T,K extends Comparable<K>> Pair<K, K> getMaxAndMin(List<T> list, Function<T, K> ksySelector) {
        if(isEmpty(list)) {
            return null;
        }

        List<K> keyList = map(list, ksySelector);
        return getMaxAndMin(keyList);
    }

    /**
     * 列表是否仅有一个元素
     * @param list 数据源
     * @return true: 列表仅有一个元素
     */
    public static boolean isSingleton(Collection<?> list) {
        return isEmpty(list) == false && list.size() == 1;
    }

    /**
     * 判断一个Collection是否为空， 包含List，Set，Queue
     * @param coll 要判断的Collection
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(Collection<?> coll) {
        return coll == null || coll.isEmpty();
    }

    /**
     * 判断多值Map是否为空
     * @param multiMap 多值Map
     * @return  true：为空 false：非空
     */
    public static boolean isEmpty(Multimap<?,?> multiMap){
        return multiMap == null || multiMap.isEmpty();
    }

    /**
     * 判断Table是否为空
     * @param table table
     * @return  true：为空 false：非空
     */
    public static boolean isEmpty(Table<?,?,?> table){
        return table == null || table.isEmpty();
    }

    /**
     * 判断一个pageQueryable是否为空
     * @param pageQueryable 要判断的Collection
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(PageQueryable<?> pageQueryable) {
        return pageQueryable == null || pageQueryable.isEmpty();
    }

    /**
     * 判断一个Collection是否为空， 包含List，Set，Queue
     * @param collection 要判断的Collection
     * @return true：非空 false：为空
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }

    /**
     * 判断一个Map是否为空
     * @param map 要判断的Collection
     * @return true：为空 false：非空
     */
    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 判断一个array是否为空
     * @param array 要判断的array
     * @return true：为空 false：非空
     */
    public static <T> boolean isEmpty(T[] array) {
        return array == null || array.length == 0;
    }

    /**
     * 基于匹配键对两个序列的元素进行关联。 使用指定的 comparer 对键进行比较。
     * @param firstList 集合A
     * @param secondList 集合B
     * @param comparer 键结果比较器
     * @param resultSelector 返回值选择器
     * @param <TFirst> 集合A的元素类型
     * @param <TSecond> 集合B的元素类型
     * @param <TResult> 返回列表元素类型
     * @return 两个集合的关联结果
     */
    public static <TFirst, TSecond, TResult> List<TResult> join(
            List<TFirst> firstList,
            List<TSecond> secondList,
            @NonNull Func2<TFirst, TSecond, Boolean> comparer,
            @NonNull Func2<TFirst, TSecond, TResult> resultSelector) {
        if(isEmpty(firstList) || isEmpty(secondList)) { return emptyList(); }

        List<TResult> resultList = new ArrayList<>(Math.max(firstList.size(), secondList.size()));
        for(TFirst firstItem : firstList) {
            for(TSecond secondItem : secondList) {
                if(comparer.call(firstItem, secondItem)) {
                    resultList.add(resultSelector.call(firstItem, secondItem));
                }
            }
        }
        return resultList;
    }

    /**
     * 使用指定的键选择器重现映射集合, 简化java流语法
     * @param list 数据源
     * @param keySelector 键选择器
     * @param <T> 输入元素类型
     * @param <K> 输出元素类型
     * @return 映射后的集合, 输入集合为null时返回null
     */
    public static <T, K> List<K> map(T[] list, Function<T, K> keySelector) {
        if(isEmpty(list)) { return emptyList();}
        return Arrays.stream(list).map(keySelector).collect(Collectors.toList());
    }

    /**
     * 使用指定的键选择器重现映射集合, 简化java流语法
     * @param list 数据源
     * @param keySelector 键选择器
     * @param <T> 输入元素类型
     * @param <K> 输出元素类型
     * @return 映射后的集合, 输入集合为null时返回null
     */
    public static <T, K> List<K> map(Collection<T> list, Function<T, K> keySelector) {
        if(isEmpty(list)) { return emptyList();}
        return list.stream().map(keySelector).collect(Collectors.toList());
    }


    /**
     * 使用指定的键选择器重现映射集合-忽略null元素, 简化java流语法
     * @param list 数据源
     * @param keySelector 键选择器
     * @param <T> 输入元素类型
     * @param <K> 输出元素类型
     * @return 映射后的集合, 输入集合为null时返回null
     */
    public static <T, K> List<K> mapIgnoreNull(List<T> list, Function<T, K> keySelector) {
        if(isEmpty(list)) { return emptyList();}
        return list.stream()
                .map(keySelector)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

    }

    /**
     * 使用指定的键选择器重现映射集合, 简化java流语法
     * @param list 数据源
     * @param keySelector 键选择器
     * @param <T> 输入元素类型
     * @param <K> 输出元素类型
     * @return 映射后的集合, 输入集合为null时返回null
     */
    public static <T, K> List<K> flatMap(Collection<T> list, Function<T, List<K>> keySelector) {
        if(isEmpty(list)) { return emptyList();}
        return list.stream()
                .map(keySelector)
                .filter(ListUtil::any)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 使用平铺二维集合, 简化java流语法
     * @param list 数据源
     * @param <T> 输入元素类型
     * @return 映射后的集合, 输入集合为null时返回null
     */
    public static <TList extends Collection<T>,T> List<T> flatMap(Collection<TList> list) {
        if(isEmpty(list)) { return emptyList();}
        return list.stream()
                .filter(ListUtil::any)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 使用指定的键选择器映射集合, 去除重复的键, 简化java流语法
     * @param list 数据源
     * @param keySelector 键选择器
     * @param <T> 输入元素类型
     * @param <K> 输出元素类型
     * @return 映射后的集合, 输入集合为null时返回null
     */
    public static <T, K> List<K> distinctMap(Collection<T> list, Function<T, K> keySelector) {
        if(isEmpty(list)) { return emptyList();}
        return list.stream()
                .map(keySelector)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 使用指定的键选择器映射集合, 去除重复的键, 去除为null的项, 简化java流语法
     * @param list 数据源
     * @param keySelector 键选择器
     * @param <T> 输入元素类型
     * @param <K> 输出元素类型
     * @return 映射后的集合, 输入集合为null时返回null
     */
    public static <T, K> List<K> distinctMapNotNull(Collection<T> list, Function<T, K> keySelector) {
        if(isEmpty(list)) { return emptyList();}
        return list.stream()
                .map(keySelector)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 使用指定的键选择器映射字符串集合, 去除重复的键, 去除为空字符串, 简化java流语法
     * @param list 数据源
     * @param keySelector 键选择器
     * @param <T> 输入元素类型
     * @return 映射后的集合, 输入集合为null时返回null
     */
    public static <T> List<String> distinctMapNotBlank(Collection<T> list, Function<T, String> keySelector) {
        if(isEmpty(list)) { return emptyList();}
        return list.stream()
                .map(keySelector)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 将集合元素转换为字符串
     * @param list 数据源
     * @param <T> 输入元素类型
     * @return 映射后的集合, 输入集合为null时返回null
     */
    public static <T> List<String> mapToString(Collection<T> list) {
        if(isEmpty(list)) { return emptyList();}
        return list.stream().map(Objects::toString).collect(Collectors.toList());
    }

    /**
     * 返回序列的第一个元素。
     * @param source 要返回第一个元素的集合
     * @param <T> source 的元素类型。
     * @return 序列的第一个元素。
     */
    public static <T> T first(Collection<T> source) {
        if(isEmpty(source)) { return null;}
        return source.iterator().next();
    }

    /**
     * 返回序列的第一个元素, 集合为空时跑异常
     * @param source 要返回第一个元素的集合
     * @param <T> source 的元素类型。
     * @return 序列的第一个元素。
     */
    public static <T> T firstOrThrow(Collection<T> source) {
        T result = first((source));
        if(result == null) {
            throw new IllegalArgumentException();
        }
        return result;
    }

    /**
     * 返回序列的匹配的第一个元素。
     * @param source 要返回第一个元素的集合
     * @param predicate 匹配表达式
     * @param <T> source 的元素类型。
     * @return 序列的匹配的第一个元素。
     */
    public static <T> T first(Collection<T> source, Function<T, Boolean> predicate) {
        if(isEmpty(source)) { return null;}

        for(T item : source) {
            if(predicate.apply(item) == true) {
                return item;
            }
        }

        return null;
    }

    /**
     * 返回序列的匹配的第一个元素, 结果为空时跑异常
     * @param source 要返回第一个元素的集合
     * @param predicate 匹配表达式
     * @param <T> source 的元素类型。
     * @return 序列的第一个元素。
     */
    public static <T> T firstOrThrow(Collection<T> source, Function<T, Boolean> predicate) {
        T result = first(source, predicate);
        if(result == null) {
            throw new IllegalArgumentException();
        }
        return result;
    }

    /**
     * 返回序列中的唯一元素；如果该序列为空，则返回默认值；如果该序列包含多个元素，此方法将引发异常。
     * @param source 要返回其单个元素的集合
     * @param <T> source 的元素类型。
     * @return 集合的唯一元素
     */
    public static <T> T single(Collection<T> source) {
        if(ListUtil.isEmpty(source)) {
            return null;
        } else if(source.size() == 1) {
            return source.iterator().next();
        } else {
            throw new IllegalArgumentException();
        }
    }

    /**
     * 返回序列的唯一元素；如果该序列并非恰好包含一个元素，则会引发异常
     * @param source 要返回其单个元素的集合
     * @param <T> source 的元素类型。
     * @return 集合的唯一元素
     */
    public static <T> T singleOrThrow(Collection<T> source) {
        if(ListUtil.isSingleton(source) == false) {
            throw new IllegalArgumentException();
        }
        return source.iterator().next();
    }

    /**
     * 返回序列的匹配的第一个元素。
     * @param source 要返回第一个元素的集合
     * @param predicate 匹配表达式
     * @param <T> source 的元素类型。
     * @return 序列的匹配的第一个元素。
     */
    public static <T> T first(T[] source, Function<T, Boolean> predicate) {
        if(isEmpty(source)) { return null;}

        for(T item : source) {
            if(predicate.apply(item) == true) {
                return item;
            }
        }

        return null;
    }

    /**
     * 返回序列的最后一个元素, 集合为空时跑异常
     * @param source 要返回最后一个元素的集合
     * @param <T> source 的元素类型。
     * @return 序列的最后一个元素。
     */
    public static <T> T lastOrThrow(List<T> source) {
        T result = last((source));
        if(result == null) {
            throw new IllegalArgumentException();
        }
        return result;
    }

    /**
     * 返回序列的最后一个元素。
     * @param source 要返回最后一个元素的集合
     * @param <T> source 的元素类型。
     * @return 序列的最后一个元素。
     */
    public static <T> T last(List<T> source) {
        if(isEmpty(source)) { return null;}

        if(source instanceof LinkedList) {
            LinkedList<T> list = (LinkedList<T>) source;
            return list.getLast();
        }

        return source.get(source.size() - 1);
    }

    /**
     * 返回序列的最后一个元素。
     * @param source 要返回最后一个元素的集合
     * @param <T> source 的元素类型。
     * @return 序列的最后一个元素。
     */
    public static <T> T last(T[] source) {
        if(isEmpty(source)) { return null;}


        return source[source.length - 1];
    }

    /**
     * 返回序列的匹配的最后一个元素。
     * @param source 要返回最后一个元素的集合
     * @param predicate 匹配表达式
     * @param <T> source 的元素类型。
     * @return 序列的最后一个元素。
     */
    public static <T> T last(List<T> source, Function<T, Boolean> predicate) {
        if(isEmpty(source)) { return null;}

        if(source instanceof ArrayList) {
            ArrayList<T> list = (ArrayList<T>) source;
            for(int i = list.size() - 1; i >= 0; --i) {
                T result = list.get(i);
                if(predicate.apply(result)) {
                    return result;
                }
            }
        } else {
            ListIterator<T> e = source.listIterator();
            while(e.hasNext()) {
                T result = e.next();
                if(predicate.apply(result)) {
                    while(e.hasNext()) {
                        T element = e.next();
                        if(predicate.apply(element)) {
                            result = element;
                        }
                    }
                    return result;
                }
            }
        }

        return null;
    }

    /**
     * 返回序列的匹配的最后一个元素的索引
     * @param source 要返回最后一个元素的集合
     * @param predicate 匹配表达式
     * @return 序列的最后一个元素的索引, 元素不存在返回 -1
     * @param <T> source 的元素类型。
     */
    public static <T> int lastIndex(List<T> source, Function<T, Boolean> predicate){
        if(isEmpty(source)) { return -1;}

        for(int i = source.size() - 1; i >= 0; --i) {
            T result = source.get(i);
            if(predicate.apply(result)) {
                return i;
            }
        }

        return -1;
    }


    /**
     * 替换列表中对一个匹配的对象
     * @param source 需替换的集合
     * @param replaceItem 要替换的对象
     * @param predicate 匹配表达式
     * @param <T> source 的元素类型。
     */
    public static <T> void replaceItem(List<T> source, T replaceItem, Function<T, Boolean> predicate) {
        if(isEmpty(source)) {
            return;
        }

        for(int i = 0; i < source.size(); i++) {
            if(predicate.apply(source.get(i)) == true) {
                source.remove(i);
                source.add(i, replaceItem);
                return;
            }
        }
    }

    /**
     * 通过使用默认的 comparer 对值进行比较, 生成两个序列的交集
     * @param listA 序列A
     * @param listB 序列B
     * @param <T> 输入集合的元素类型
     * @return 生成两个序列的交集
     */
    public static <T extends Comparable<T>> List<T> intersect(List<T> listA, List<T> listB) {
        return intersect(listA, listB, item -> item);
    }

    /**
     * 通过使用指定的键的对值进行比较, 生成两个序列的交集
     * @param listA 序列A
     * @param listB 序列B
     * @param keySelector 取得用于交集比较的值的方法
     * @param <T> 输入集合的元素类型
     * @param <K> 交集比较类型
     * @return 生成两个序列的交集
     */
    public static <T, K extends Comparable<K>> List<T> intersect(List<T> listA, List<T> listB, Function<T, K> keySelector) {
        if(isEmpty(listA) || isEmpty(listB)) { return emptyList(); }

        List<T> result = new ArrayList<>(Math.min(listA.size(), listB.size()));
        Map<K, T> map = listA.stream().collect(Collectors.toMap(keySelector, x -> x));
        for(T second : listB) {
            K key = keySelector.apply(second);
            if(map.containsKey(key)) {
                result.add(second);
            }
        }
        return result;
    }

    /**
     * 当两个集合都不为空时, 通过使用默认的 comparer 对值进行比较, 生成两个序列的交集
     * <p>当一个集合为空时返回另外一个集合</p>
     * <p>为了比较避免影响传入的集合对象, 不管如何都会返回一个新的集合对象</p>
     * @param listA 序列A
     * @param listB 序列B
     * @param <T> 输入集合的元素类型
     * @return 两个序列的交集
     */
    public static <T extends Comparable<T>> List<T> intersectWhenNotEmpty(List<T> listA, List<T> listB) {
        return intersectWhenNotEmpty(listA, listB, item -> item);
    }

    /**
     * 当两个集合都不为空时, 通过使用指定的键的对值进行比较, 生成两个序列的交集
     * <p>当一个集合为空时返回另外一个集合</p>
     * <p>为了比较避免影响传入的集合对象, 不管如何都会返回一个新的集合对象</p>
     * @param listA 序列A
     * @param listB 序列B
     * @param keySelector 取得用于交集比较的值的方法
     * @param <T> 输入集合的元素类型
     * @param <K> 交集比较类型
     * @return 两个序列的交集
     */
    public static <T, K extends Comparable<K>> List<T> intersectWhenNotEmpty(List<T> listA, List<T> listB, Function<T, K> keySelector) {
        boolean aHasAny = any(listA);
        boolean bHasAny = any(listB);

        if(aHasAny && bHasAny){
            return intersect(listA, listB, keySelector);
        }

        if(aHasAny){
            return new ArrayList<>(listA);
        }else{
            return new ArrayList<>(listB);
        }
    }

    /**
     * 通过使用指定的 comparer 对值进行比较, 移除两个序列中的交集
     * @param listA 序列A
     * @param listB 序列B
     * @param keySelector 取得用于交集比较的值的方法
     * @param <T> 输入集合的元素类型
     * @param <K> 交集比较类型
     * @return 去除集合后的序列
     */
    public static <T, K extends Comparable<K>> Pair<List<T>, List<T>> removeIntersection(List<T> listA, List<T> listB, Function<T, K> keySelector) {
        if(isEmpty(listA) || isEmpty(listB)) {
            return Pair.of(listA, listB);
        }

        Set<K> intersectKeySet = intersect(listA, listB, keySelector)
                .stream()
                .map(keySelector)
                .collect(Collectors.toSet());

        if(ListUtil.isEmpty(intersectKeySet)) {
            return Pair.of(listA, listB);
        }

        List<T> resultA = listA.stream()
                .filter(item -> intersectKeySet.contains(keySelector.apply(item)) == false)
                .collect(Collectors.toList());

        List<T> resultB = listB.stream()
                .filter(item -> intersectKeySet.contains(keySelector.apply(item)) == false)
                .collect(Collectors.toList());

        return Pair.of(resultA, resultB);
    }

    /**
     * 通过使用指定的 comparer 对值进行比较, 获取两个序列的差集
     * @param listA 序列A
     * @param listB 序列B
     * @param keySelector 取得用于差集比较的值的方法
     * @param <T> 输入集合的元素类型
     * @param <K> 差集比较类型
     * @return 两个序列的差集
     */
    public static <T, K extends Comparable<K>> List<T> diffSet(List<T> listA, List<T> listB, Function<T, K> keySelector) {
        if(isEmpty(listA)) {
            listA = emptyList();
        }
        if(isEmpty(listB)) {
            listB = emptyList();
        }
        var result = removeIntersection(listA, listB, keySelector);
        return result.getKey();
    }

    /**
     * 去除列表中的指定键的重复项
     * @param list 数据源
     * @param keySelector 键选择器
     * @param <T> 输入集合的元素类型
     * @param <K> 重复项判断键的类型
     * @return 去除重复项后的列表
     */
    public static <T, K> List<T> distinct(List<T> list, Function<T, K> keySelector) {
        if(isEmpty(list) == true) {
            return emptyList();
        }

        Set<K> distinctSet = new HashSet<>(list.size());
        List<T> result = new ArrayList<>(list.size());

        for(T item : list) {
            K key = keySelector.apply(item);
            if(distinctSet.contains(key) == false) {
                result.add(item);
                distinctSet.add(key);
            }
        }

        return result;
    }

    /**
     * 获取列表中重复的键
     * @param list 列表
     * @param keySelector 键选择器
     * @param <T> 元素类型
     * @param <K> 键类型
     * @return 重复键列表
     */
    public static <T, K> List<K> sameKeyList(Collection<T> list, Function<T, K> keySelector) {
        if(isEmpty(list) == true) {
            return emptyList();
        }

        return list.stream().collect(Collectors.groupingBy(keySelector))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 获取列表中重复的项目
     * @param list 列表
     * @param keySelector 键选择器
     * @param <T> 元素类型
     * @param <K> 键类型
     * @return 重复键列表
     */
    public static <T, K> List<Map.Entry<K, List<T>>> sameItemList(Collection<T> list, Function<T, K> keySelector) {
        if(isEmpty(list) == true) {
            return emptyList();
        }

        return list.stream().collect(Collectors.groupingBy(keySelector))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toList());
    }

    /**
     * 判断列表中是否有重复的键
     * <p>将过滤null的项</p>
     * @param list 列表
     * @param keySelector 键选择器
     * @param <T> 元素类型
     * @param <K> 键类型
     * @return true: 有重复的键, false: 没有重复的键
     */
    public static <T, K> boolean hasSameItem(Collection<T> list, Function<T, K> keySelector) {
        if(isEmpty(list) == true) {
            return false;
        }

        Set<K> distinctSet = new HashSet<>(list.size());
        for(T item : list) {
            K key = keySelector.apply(item);
            if(key == null) {
                continue;
            }
            if(distinctSet.contains(key) == true) {
                return true;
            }
            distinctSet.add(key);
        }
        return false;
    }

    /**
     * 去除列表中的指定键的重复项, 并通过保留项比较器保留最大的项
     * @param list 数据源
     * @param distinctKeySelector 重复项判断的键选择器
     * @param reservedItemComparator 保留项比较器
     * @param <T> 输入集合的元素类型
     * @param <K> 重复项判断键的类型
     * @return 去除重复项后的列表
     */
    public static <T, K> List<T> distinct(List<T> list, Function<T, K> distinctKeySelector, Comparator<T> reservedItemComparator) {
        return list.stream()
                .collect(Collectors.groupingBy(distinctKeySelector))
                .values().stream()
                .map(itemList -> {
                    if(ListUtil.isSingleton(itemList)) {
                        return ListUtil.first(itemList);
                    }

                    itemList = itemList.stream()
                            .sorted(reservedItemComparator)
                            .collect(Collectors.toList());
                    return ListUtil.last(itemList);
                }).collect(Collectors.toList());
    }

    /**
     * 合计列表中Map键相同的项目的和
     * @param list 目标列表
     * @param keySelector 键选择器
     * @param <T> 序列元素
     * @param <TKey> Map键类型
     * @return 计算的和
     */
    public static <T, TKey> Map<TKey, Integer> sumIntMap(Collection<T> list, Function<T, Map<TKey, Integer>> keySelector) {
        return sumMap(list, keySelector, MathUtils::add);
    }

    /**
     * 合计列表中Map键相同的项目的和
     * @param list 目标序列
     * @param keySelector 键选择器
     * @param sumFunction 计算和的方法
     * @param <T> 序列元素
     * @param <TKey> Map键类型
     * @param <TValue> Map值类型
     * @return 计算的和
     */
    public static <T, TKey, TValue> Map<TKey, TValue> sumMap(
            Collection<T> list,
            Function<T, Map<TKey, TValue>> keySelector,
            Func2<TValue, TValue, TValue> sumFunction) {
        if(isEmpty(list) == true) {
            return emptyMap();
        }

        if(isSingleton(list) == true) {
            Map<TKey, TValue> first = keySelector.apply(first(list));
            return isEmpty(first) ? emptyMap() : first;
        }

        Map<TKey, TValue> result = new HashMap<>(16);

        int index = 0;
        for(T item : list) {
            Map<TKey, TValue> valueMap = keySelector.apply(item);
            MathUtils.addTo(result, valueMap, sumFunction);
        }

        return result;
    }

    /**
     * 获取序列中使用键选择器选择的项的和
     * @param list 目标序列
     * @param keySelector 键选择器
     * @param sumFunction 计算和的方法
     * @param <T> 序列元素
     * @param <K> 计算和的属性类型
     * @return 计算的和
     */
    public static <T, K> K sum(Collection<T> list, Function<T, K> keySelector, Func2<K, K, K> sumFunction) {
        if(isEmpty(list) == true) {
            return null;
        }

        if(isSingleton(list) == true) {
            return keySelector.apply(first(list));
        }

        K sum = keySelector.apply(first(list));
        int index = 0;
        for(T item : list) {
            if(index++ == 0) {
                continue;
            }
            K value = keySelector.apply(item);
            sum = sumFunction.call(sum, value);
        }

        return sum;
    }

    /**
     * 获取序列中使用键选择器选择的项的和
     * @param list 目标序列
     * @param keySelector 键选择器
     * @param <T> 序列元素
     * @return 计算的和
     */
    public static <T> BigDecimal sum(Collection<T> list, Function<T, BigDecimal> keySelector) {
        BigDecimal result = sum(list, keySelector, MathUtils::add);
        return NullMergeUtils.nullMerge(result, BigDecimal.ZERO);
    }

    /**
     * 获取序列中使用键选择器选择的项的和(返回空)
     * @param list 目标序列
     * @param keySelector 键选择器
     * @param <T> 序列元素
     * @return 计算的和
     */
    public static <T> BigDecimal sumNull(Collection<T> list, Function<T, BigDecimal> keySelector) {
        return sum(list, keySelector, MathUtils::add);
    }

    /**
     * 获取序列中使用键选择器选择的项的和
     * @param list 目标序列
     * @param keySelector 键选择器
     * @param <T> 序列元素
     * @return 计算的和
     */
    public static <T> Integer sumInt(Collection<T> list, Function<T, Integer> keySelector) {
        Integer result = sum(list, keySelector, MathUtils::add);
        return NullMergeUtils.nullMerge(result, 0);
    }

    /**
     * 获取序列中使用键选择器选择的项的和
     * @param list 目标序列
     * @param keySelector 键选择器
     * @param <T> 序列元素
     * @return 计算的和
     */
    public static <T> Long sumLong(Collection<T> list, Function<T, Long> keySelector) {
        Long result = sum(list, keySelector, MathUtils::add);
        return NullMergeUtils.nullMerge(result, 0L);
    }

    /**
     * 获取序列中使用键选择器选择的项的平均值
     * @param list 目标序列
     * @param keySelector 键选择器
     * @param sumFunction 计算和的方法
     * @param divideFunction 计算除的方法
     * @param <T> 序列元素
     * @param <K> 计算和的属性类型
     * @return 计算的平均值
     */
    public static <T, K> K average(
            List<T> list,
            Function<T, K> keySelector,
            Func2<K, K, K> sumFunction,
            Func2<K, Integer, K> divideFunction) {
        if(isEmpty(list) == true) {
            return null;
        }

        K sum = sum(list, keySelector, sumFunction);
        return divideFunction.call(sum, list.size());
    }

    /**
     * 获取序列中使用键选择器选择的项的平均值
     * @param list 目标序列
     * @param keySelector 键选择器
     * @param <T> 序列元素
     * @return 计算的平均值
     */
    public static <T> BigDecimal averageBigDecimal(
            List<T> list,
            Function<T, BigDecimal> keySelector) {
        if(isEmpty(list) == true) {
            return null;
        }

        BigDecimal sum = sum(list, keySelector);
        if(MathUtils.equal(sum, BigDecimal.ZERO)) {
            return BigDecimal.ZERO;
        }
        return sum.divide(BigDecimal.valueOf(list.size()), 4, RoundingMode.HALF_UP);
    }

    /**
     * 通过指定的键选择器对集合进行分组
     * @param list 需要分组的集合
     * @param keySelector1 键选择器1
     * @param keySelector2 键选择器2
     * @param <T> 分组集合元素类型
     * @param <TKey1> 键1类型
     * @param <TKey2> 键2类型
     * @return 完成分组的集合
     */
    public static <T, TKey1, TKey2> Table<TKey1, TKey2, List<T>> listGroupingBy(
            Collection<T> list,
            @NonNull Function<T, TKey1> keySelector1,
            @NonNull Function<T, TKey2> keySelector2) {
        if(isEmpty(list) == true) {
            return HashBasedTable.create();
        }

        Table<TKey1, TKey2, List<T>> result = HashBasedTable.create();

        for(T item : list) {
            TKey1 row = keySelector1.apply(item);
            TKey2 col = keySelector2.apply(item);
            if(result.contains(row, col) == false) {
                result.put(row, col, new ArrayList<>(32));
            }
            //noinspection ConstantConditions
            result.get(row, col).add(item);
        }
        return result;
    }

    /**
     * 通过指定的键选择器对集合进行分组
     * @param list 需要分组的集合
     * @param keySelector1 键选择器1
     * @param keySelector2 键选择器2
     * @param <T> 分组集合元素类型
     * @param <TKey1> 键1类型
     * @param <TKey2> 键2类型
     * @return 完成分组的集合
     */
    public static <T, TKey1, TKey2> Map<Pair<TKey1, TKey2>, T> itemGroupingBy(
            Collection<T> list,
            @NonNull Function<T, TKey1> keySelector1,
            @NonNull Function<T, TKey2> keySelector2) {
        if(isEmpty(list) == true) {
            return Collections.emptyMap();
        }

        Map<Pair<TKey1, TKey2>, T> result = new HashMap<>(16);
        for(T item : list) {
            Pair<TKey1, TKey2> key = Pair.of(keySelector1.apply(item), keySelector2.apply(item));
            if(result.containsKey(key) == false) {
                result.put(key, item);
            }
            result.get(key);
        }
        return result;
    }

    /**
     * 使用指定的键将列表转换为以元素本身为值的Table
     * @param list 需要转换Table的集合
     * @param keySelector1 键选择器1
     * @param keySelector2 键选择器2
     * @param <T> 分组集合元素类型
     * @param <TKey1> 键1类型
     * @param <TKey2> 键2类型
     * @return 完成分组的集合
     */
    public static <T, TKey1, TKey2> Table<TKey1, TKey2, T> toTable(
            Collection<T> list,
            @NonNull Function<T, TKey1> keySelector1,
            @NonNull Function<T, TKey2> keySelector2){
        return toTable(list, keySelector1, keySelector2, Function.identity());
    }


    /**
     * 使用指定的键将列表转换为以指定值的Table
     * @param list 需要转换Table的集合
     * @param keySelector1 键选择器1
     * @param keySelector2 键选择器2
     * @param valueSelector 值选择器
     * @param <T> 分组集合元素类型
     * @param <TKey1> 键1类型
     * @param <TKey2> 键2类型
     * @param <TValue> 值类型
     * @return 完成分组的集合
     */
    public static <T, TKey1, TKey2, TValue> Table<TKey1, TKey2, TValue> toTable(
            Collection<T> list,
            @NonNull Function<T, TKey1> keySelector1,
            @NonNull Function<T, TKey2> keySelector2,
            @NonNull Function<T, TValue> valueSelector) {
        if(isEmpty(list) == true) {
            return HashBasedTable.create();
        }

        Table<TKey1, TKey2, TValue> result = HashBasedTable.create();
        for(T item : list) {
            TKey1 row = keySelector1.apply(item);
            TKey2 col = keySelector2.apply(item);
            if(result.contains(row, col)) {
                throw new IllegalArgumentException("重复的键值: " + row + ", " + col);
            }
            result.put(row, col, valueSelector.apply(item));
        }
        return result;
    }

    /**
     * 使用指定的键将列表转换为以元素本身为值的Map
     * @param list 数据源
     * @param keySelector 键选择器
     * @param <T> Map值类型
     * @param <K> Map键类型
     * @return (K, T)
     */
    public static <T, K> Map<K, T> toMap(Collection<T> list, Function<T, K> keySelector) {
        if(isEmpty(list) == true) {
            return emptyMap();
        }
        return list.stream().collect(Collectors.toMap(keySelector, item -> item));
    }

    /**
     * 使用指定的键将列表转换为以元素本身为值的Map
     * @param list 数据源
     * @param keySelector 键选择器
     * @param <T> Map值类型
     * @param <K> Map键类型
     * @return (K, T)
     */
    public static <T, K> LinkedHashMap<K, T> toLinkedHashMap(Collection<T> list, Function<T, K> keySelector) {
        if(isEmpty(list) == true) {
            return new LinkedHashMap<>(0);
        }
        return list.stream().collect(Collectors.toMap(
                keySelector, item -> item,
                (u, v) -> { throw new IllegalStateException(String.format("Duplicate key %s", u)); },
                LinkedHashMap::new));
    }

    /**
     * 使用指定的键将列表转换为以元素本身为值的Map
     * @param list 数据源
     * @param keySelector 键选择器
     * @param <T> Map值类型
     * @param <K> Map键类型
     * @return (K, T)
     */
    public static <T, K> Map<K, T> toMap(T[] list, Function<T, K> keySelector) {
        if(isEmpty(list) == true) {
            return emptyMap();
        }
        return Arrays.stream(list).collect(Collectors.toMap(keySelector, item -> item));
    }


    /**
     * 使用指定的键将列表转换为以元素本身为值的Map
     * @param list 数据源
     * @param keySelector 键选择器
     * @param valueSelector 值选择器
     * @param <T> 数据库元素类型
     * @param <V> Map值类型
     * @param <K> Map键类型
     * @return (K, V)
     */
    public static <T, K, V> Map<K, V> toMap(Collection<T> list, Function<T, K> keySelector, Function<T, V> valueSelector) {
        if(isEmpty(list) == true) {
            return emptyMap();
        }
        return list.stream().collect(Collectors.toMap(keySelector, valueSelector));
    }

    /**
     * 按指定键对列表进行分组并统计数量
     *
     * @param list 数据源
     * @param keySelector 分组键选择器
     * @param <T> 列表元素类型
     * @param <K> 分组键类型
     * @return Map<K, Long> 分组后的计数结果
     */
    public static <T, K> Map<K, Long> countMap(Collection<T> list, Function<T, K> keySelector) {
        if (isEmpty(list)) {
            return emptyMap();
        }
        return list.stream()
            .collect(Collectors.groupingBy(keySelector, Collectors.counting()));
    }

    /**
     * 使用指定的键将列表转换为以元素本身为值的Map
     * @param list 数据源
     * @param keySelector 键选择器
     * @param valueSelector 值选择器
     * @param <T> 数据库元素类型
     * @param <V> Map值类型
     * @param <K> Map键类型
     * @return (K, V)
     */
    public static <T, K, V> Map<K, V> toMapIgnoreNull(Collection<T> list, Function<T, K> keySelector, Function<T, V> valueSelector) {
        if(isEmpty(list) == true) {
            return emptyMap();
        }

        return list.stream()
                .filter(Objects::nonNull)
                .filter(x -> valueSelector.apply(x) != null)
                .filter(x -> keySelector.apply(x) != null )
                .collect(Collectors.toMap(keySelector, valueSelector));
    }

    /**
     * 使用指定的键将列表转换为以元素本身为值的组
     * @param list 数据源
     * @param keySelector 键选择器
     * @param <T> 组值类型
     * @param <K> 组键类型
     * @return (K, T)
     */
    public static <T, K> Map<K, List<T>> toGroup(Collection<T> list, Function<T, K> keySelector) {
        if(isEmpty(list) == true) {
            return emptyMap();
        }
        return list.stream().collect(Collectors.groupingBy(keySelector));
    }

    /**
     * 使用指定的键将列表转换为以元素本身为值的组
     * @param list 数据源
     * @param keySelector 键选择器
     * @param valueSelector 值选择器
     * @param <T> 数据源元素类型
     * @param <V> Map值类型
     * @param <K> Map键类型
     * @return (K, V)
     */
    public static <T, K, V> Map<K, List<V>> toGroup(Collection<T> list, Function<T, K> keySelector, Function<T, V> valueSelector) {
        if(isEmpty(list) == true) {
            return emptyMap();
        }

        Map<K, List<V>> result = new HashMap<>(16);
        for(T item : list) {
            K key = keySelector.apply(item);
            if(result.containsKey(key) == false) {
                result.put(key, new ArrayList<>());
            }
            result.get(key).add(valueSelector.apply(item));
        }
        return result;
    }

    /**
     * 将值尽可能平均的分配给列表中的项
     * @param list 被分配的列表
     * @param totalValue 需要分配的值
     * @param distributionAction 分配回调方法
     * @param <T> 输入元素类型
     */
    public static <T> void distribution(Collection<T> list, Integer totalValue, Action2<T, Integer> distributionAction) {
        int listSize = list.size();
        int singleItemValue = totalValue / listSize;
        int remainder = totalValue % listSize;

        int index = 0;
        for(T item : list) {
            int value = index < remainder ? singleItemValue + 1 : singleItemValue;
            index++;
            distributionAction.call(item, value);
        }
    }

    /**
     * 获取空的Map
     * @param <K> 键类型
     * @param <V> 值类型
     * @return 空的Map
     */
    public static <K, V> Map<K, V> emptyMap() {
        return new HashMap<>(0);
    }

    /**
     * 获取空列表
     * @param <T> 元素类
     * @return 空列表
     */
    public static <T> List<T> emptyList() {
        return new ArrayList<>(0);
    }

    /**
     * 获取空集合
     * @param <T> 原始类型
     * @return 空集合
     */
    public static <T> Set<T> emptySet() {
        return new HashSet<>(0);
    }

    /**
     * 是否存在任意元素
     * @param list 列表
     * @param <T> 列表元素类型
     * @return true: 列表存在任意元素
     */
    public static <T> Boolean any(Collection<T> list) {
        return ListUtil.isEmpty(list) == false;
    }

    /**
     * 是否存在任意元素
     * @param list 列表
     * @param <T> 列表元素类型
     * @return true: 列表存在任意元素
     */
    public static <T> Boolean any(T[] list) {
        return ListUtil.isEmpty(list) == false;
    }

    /**
     * 是否存在任意元素
     * @param map Map
     * @return true: Map存在任意元素
     */
    public static Boolean any(Map<?, ?> map) {
        return ListUtil.isEmpty(map) == false;
    }

    /**
     * 是否存在任意符合条件的元素
     * @param list 列表
     * @param expression 条件表达式
     * @param <T> 列表元素类型
     * @return true: 列表存在符合条件的任意元素
     */
    public static <T> Boolean any(Collection<T> list, Function<T, Boolean> expression) {
        if(ListUtil.isEmpty(list)) {
            return false;
        }
        return list.stream().anyMatch(expression::apply);
    }

    /**
     * 分页转换
     * @param pageOriginal 原始分页结果
     * @param result 分页返回数据
     * @param <T> 分页元素类型
     * @return 分页信息
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static <T> PageInfo<T> pageConvert(List<?> pageOriginal, List<T> result) {
        PageInfo pageInfo = new PageInfo(pageOriginal);
        pageInfo.setList(result);
        return (PageInfo<T>) pageInfo;
    }

    /**
     * 分页转换
     * @param pageOriginal 分页结果
     * @param <T> 分页元素类型
     * @return 分页信息
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static <T> PageInfo<T> pageConvert(List<T> pageOriginal) {
        PageInfo pageInfo = new PageInfo(pageOriginal);
        return (PageInfo<T>) pageInfo;
    }

    /**
     * 空分页
     * @param <T> 分页元素类型
     * @return 空分页
     */
    public static <T> PageInfo<T> emptyPage() {
        return new PageInfo<>(emptyList());
    }

    /**
     * 列表合并
     * @param listA 列表A
     * @param listB 列表B
     * @param <T> 合并的列表元素类型
     * @return 合并后的列表
     */
    public static <T> List<T> merge(List<? extends T> listA, List<? extends T> listB) {
        if(isEmpty(listA) && isEmpty(listB)) {
            return ListUtil.emptyList();
        } else if(isEmpty(listA)) {
            return new ArrayList<>(listB);
        } else if(isEmpty(listB)) {
            return new ArrayList<>(listA);
        }

        List<T> result = new ArrayList<>(listA.size() + listB.size());
        result.addAll(listA);
        result.addAll(listB);

        return result;
    }

    /**
     * 集合合并
     * @param setA 集合A
     * @param setB 集合B
     * @param <T> 合并的集合元素类型
     * @return 合并后的集合
     */
    public static <T> Set<T> merge(Set<? extends T> setA, Set<? extends T> setB) {
        if(isEmpty(setA) && isEmpty(setB)) {
            return ListUtil.emptySet();
        } else if(isEmpty(setA)) {
            return new HashSet<>(setB);
        } else if(isEmpty(setB)) {
            return new HashSet<>(setA);
        }

        Set<T> result = new HashSet<>(setA);
        result.addAll(setB);

        return result;
    }

    /**
     * 列表合并, 并去除重复项
     * @param listA 列表A
     * @param listB 列表B
     * @param <T> 合并的列表元素类型
     * @return 合并后的列表
     */
    public static <T> List<T> mergeAndDistinct(List<T> listA, List<T> listB) {
        List<T> mergeResult = merge(listA, listB);
        return distinct(mergeResult, item -> item);
    }

    /**
     * 列表合并, 并提供指定键去除重复项
     * @param listA 列表A
     * @param listB 列表B
     * @param <T> 合并的列表元素类型
     * @return 合并后的列表
     */
    public static <T, K> List<T> mergeAndDistinct(List<T> listA, List<T> listB, Function<T, K> keySelector) {
        List<T> mergeResult = merge(listA, listB);
        return distinct(mergeResult, keySelector);
    }

    /**
     * 平铺嵌套列表
     * @param nestedList 嵌套列表
     * @param <T> 平铺的列表元素类型
     * @return 平铺后的的列表
     */
    public static <T> List<T> flat(List<List<T>> nestedList) {
        if(isEmpty(nestedList)) {
            return ListUtil.emptyList();
        } else if(isSingleton(nestedList)) {
            List<T> first = first(nestedList);
            if(ListUtil.isEmpty(first)) {
                return ListUtil.emptyList();
            } else {
                return new ArrayList<>(first);
            }
        }

        return nestedList.stream()
                .filter(ListUtil::any)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 平铺嵌套列表
     * @param nestedList 嵌套列表
     * @param <T> 平铺的列表元素类型
     * @return 平铺后的的列表
     */
    public static <T> List<T> flatDistinct(List<List<T>> nestedList) {
        if(isEmpty(nestedList)) {
            return ListUtil.emptyList();
        } else if(isSingleton(nestedList)) {
            List<T> first = first(nestedList);
            if(ListUtil.isEmpty(first)) {
                return ListUtil.emptyList();
            } else {
                return new ArrayList<>(first);
            }
        }

        return nestedList.stream()
                .filter(ListUtil::any)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 对列表内容按步长分批处理, 然后合并处理结果
     * @param list 需要分批处理的列表
     * @param executionMethod 执行方法
     * @param stepSize 分批大小
     * @param <T> 列表元素类型
     * @param <TResult> 结果类型
     * @return 结果集合
     */
    public static <T, TResult> List<TResult> batchExecute(List<T> list, @NonNull Function<List<T>, List<TResult>> executionMethod, int stepSize) {
        if(ListUtil.isEmpty(list)) {
            return ListUtil.emptyList();
        }
        return splitList(list, stepSize).stream()
                .map(executionMethod)
                .filter(ListUtil::any)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 对列表内容按默认大小分批处理, 然后合并处理结果
     * @param list 需要分批处理的列表
     * @param executionMethod 执行方法
     * @param <T> 列表元素类型
     * @param <TResult> 结果类型
     * @return 结果集合
     */
    public static <T, TResult> List<TResult> batchExecute(List<T> list, @NonNull Function<List<T>, List<TResult>> executionMethod) {
        return batchExecute(list, executionMethod, Constants.BATCH_SIZE);
    }

    /**
     * 反序给定List，会在原List基础上直接修改
     * @param <T> 元素类型
     * @param list 被反转的List
     * @return 反转后的List
     */
    public static <T> List<T> reverse(List<T> list) {
        Collections.reverse(list);
        return list;
    }


    /**
     * 反序给定List，会创建一个新的List，原List数据不变
     * @param <T> 元素类型
     * @param list 被反转的List
     * @return 反转后的List
     */
    public static <T> List<T> reverseNew(List<T> list) {
        List<T> list2 = ObjectUtil.clone(list);
        if(null == list2) {
            // 不支持clone
            list2 = new ArrayList<>(list);
        }
        return reverse(list2);
    }

    /**
     * 使用指定分隔符拼接列表中选定的字符串
     * @param list 列表
     * @param delimiter 分隔符
     * @param joinSelector 评价字符串选择器
     * @param <T> 类型元素类型
     * @return 拼接结果
     */
    public static <T> String joining(List<T> list, String delimiter, @NonNull Function<T, String> joinSelector) {
        Assert.hasText(delimiter, "分隔符不能为空");
        if(isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream()
                .map(joinSelector)
                .collect(Collectors.joining(delimiter));
    }

    /**
     * 获取集合组中的最大长度
     * @param collections 集合组
     * @return 集合最大长度
     */
    public static int getMaxSize(List<List<?>> collections) {
        if(isEmpty(collections)) {
            return 0;
        }

        int max = 0, size;
        for(List<?> subList : collections) {
            size = NullMergeUtils.emptyMerge(subList, Collection::size, 0);
            max = Math.max(max, size);
        }
        return max;
    }

    /**
     * 参数获取列表字段下标的元素, 下标越界时返回null
     * @param list 列表
     * @param index 请求下标
     * @param <T> 请求列表元素类型
     * @return 列表字段下标的元素
     */
    public static <T> T tryGetItem(List<T> list, int index) {
        if(ListUtil.isEmpty(list)
                || index >= list.size()) {
            return null;
        }
        return list.get(index);
    }

    /**
     * 将Collection转换为Set
     * <p>collection为空时返回空Set</p>
     * @param collection 需转换的数据集
     * @param <T> 集合原始
     * @return 转换后的Set
     */
    public static <T> Set<T> toSet(Collection<T> collection) {
        if(ListUtil.isEmpty(collection)) {
            return ListUtil.emptySet();
        }
        return Sets.newHashSet(collection);
    }

    /**
     * 将Collection转换为Set
     * <p>collection为空时返回空Set</p>
     * @param collection 需转换的数据集
     * @param keySelector 转换后的Set元素选择器
     * @param <T> 集合原始
     * @param <K> 转换后的Set元素类型
     * @return 转换后的Set
     */
    public static <T, K> Set<K> toSet(Collection<T> collection, Function<T, K> keySelector) {
        if(ListUtil.isEmpty(collection)) {
            return ListUtil.emptySet();
        }
        return collection.stream().map(keySelector).collect(Collectors.toSet());
    }

    /**
     * 查找指定Int最大值, 值不存在或列表为空时抛异常
     * @param list 需查找最大值的列表
     * @param keySelector 最大值字段选择器
     * @param <T> 输入元素类型
     * @return 最大值
     */
    public static <T> int maxInt(Collection<T> list, ToIntFunction<T> keySelector) {
        Assert.notEmpty(list, "输入列表不能为空");

        return list.stream().mapToInt(keySelector)
                .max()
                .orElseThrow((() -> new IllegalArgumentException("最大值不存在")));
    }

    /**
     * 将值转换为列表
     * @param value 要转换的值
     * @param <T> 值的类型
     * @return 转换后的集合
     */
    public static <T> List<T> singleToList(T value) {
        return Collections.singletonList(value);
    }

    /**
     * 返回仅包含指定对象的不可变列表。返回的列表是可序列化的
     * @param value 要转换的值
     * @param <T> 值的类型
     * @return 单元素集合
     */
    public static <T> List<T> singletonList(T value) {
        return Collections.singletonList(value);
    }
    /**
     * 返回仅包含指定对象的不可变列表。返回的列表是可序列化的
     * @param value 要转换的值
     * @param <T> 值的类型
     * @return 单元素集合
     */
    public static <T> Set<T> singletonSet(T value) {
        return Collections.singleton(value);
    }

    /**
     * 使得主列表按照子列表的顺序排序
     * <p>使用指定的键选择器定义关联的键</p>
     * @param mainList 主列表
     * @param subList 子列表
     * @param mainKeySelector 主列表键选择器
     * @param subKeySelector 子列表键选择器
     * @param <TMain> 主列表元素类型
     * @param <TSub> 子列表元素类型
     * @param <TKey> 键类型
     * @return 排序后的主列表
     */
    public static <TMain, TSub, TKey> List<TMain> orderByOtherList(
            List<TMain> mainList,
            List<TSub> subList,
            Function<TMain, TKey> mainKeySelector,
            Function<TSub, TKey> subKeySelector) {
        List<TMain> resultList = new ArrayList<>(mainList.size());
        Map<TKey, TMain> mainMap = ListUtil.toMap(mainList, mainKeySelector);
        for(TSub sub : subList) {
            TKey key = subKeySelector.apply(sub);
            TMain main = mainMap.get(key);
            if(null != main) {
                resultList.add(main);
            }
        }
        return resultList;
    }

    /**
     * 对列表内容按步长分批处理, 然后合并处理结果
     * @param list 需要分批处理的列表
     * @param executionMethod 执行方法
     * @param stepSize 分批大小
     * @param <T> 列表元素类型
     * @param <TResult> 结果类型
     * @return 结果集合
     */
    public static <T, TResult> List<TResult> batchExecuteAsync(List<T> list, @NonNull Function<List<T>, List<TResult>> executionMethod, int stepSize) {
        if(ListUtil.isEmpty(list)) {
            return ListUtil.emptyList();
        }
        return splitList(list, stepSize).stream()
                .parallel()
                .map(executionMethod)
                .filter(ListUtil::any)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 对列表内容按步长分批处理, 然后合并处理结果
     * @param list 需要分批处理的列表
     * @param executionMethod 执行方法
     * @param stepSize 分批大小
     * @param <T> 列表元素类型
     * @param <TResult> 结果类型
     * @return 结果集合
     */
    public static <T, TValue, TResult> TResult batchExecuteAsync(
            List<T> list,
            @NonNull Function<List<T>, TValue> executionMethod,
            @NonNull Function<List<TValue>, TResult> aggregationFun,
            int stepSize) {
        if(ListUtil.isEmpty(list)) {
            return null;
        }
        List<TValue> resultList = splitList(list, stepSize).stream()
                .parallel()
                .map(executionMethod)
                .filter(ObjectUtil::isNotNull)
                .collect(Collectors.toList());
        return aggregationFun.apply(resultList);
    }

    /**
     * 对列表内容按步长分批处理, 然后合并处理结果
     * @param list 需要分批处理的列表
     * @param executionMethod 执行方法
     * @param stepSize 分批大小
     * @param <T> 列表元素类型
     */
    public static <T> void batchExecuteAsync(List<T> list, @NonNull Action1<List<T>> executionMethod, int stepSize) {
        if(ListUtil.isEmpty(list)) {
            return;
        }
        splitList(list, stepSize).stream()
                .parallel()
                .forEach(executionMethod::call);
    }

    /**
     * 对列表内容按默认大小分批处理, 然后合并处理结果
     * @param list 需要分批处理的列表
     * @param executionMethod 执行方法
     * @param <T> 列表元素类型
     * @param <TResult> 结果类型
     * @return 结果集合
     */
    public static <T, TResult> List<TResult> batchExecuteAsync(List<T> list, @NonNull Function<List<T>, List<TResult>> executionMethod) {
        return batchExecuteAsync(list, executionMethod, Constants.BATCH_SIZE);
    }

    /**
     * 根据字段值分隔字符串，获取需要的值
     * @param fieldValue 字段值
     * @param separator 分隔符
     * @param fieldName 字段名
     * @param subscript 下标
     * @return 值
     */
    public static String splitField(String fieldValue,String separator,String fieldName,Integer subscript){
        String[] split = fieldValue.split(separator);
        StringBuilder name = new StringBuilder();
        for(String s : split) {
            if(s.contains(fieldName)){
                name.append(s);
                continue;
            }
            if(StringUtils.isNotBlank(name) && s.contains("：")){
                break;
            } else if(StringUtils.isNotBlank(name)){
                name.append(separator);
                name.append(s);
            }
        }
        if(StringUtils.isNotEmpty(name)){
            return name.substring(subscript);
        }
        return "--";
    }
}
