package com.py.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.py.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Json工具类
 * <P>使用系统内配置的jackson进行Json对象映射</P>
 * <AUTHOR>
 */
@Slf4j
@Component
public class JsonUtil implements ApplicationContextAware {

    /** jackson对象映射 */
    private static ObjectMapper objectMapper;

    /**
     * 对象转字符串
     * @param obj 对象
     * @param <T> 对象类型
     * @return json字符串
     */
    public static <T> String obj2String(T obj) {
        if(obj == null) {
            return null;
        }
        try {
            return obj instanceof String ? (String) obj : objectMapper.writeValueAsString(obj);
        } catch(Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 对象转的字符串（格式化后）
     * @param obj 对象
     * @param <T> 对象类型
     * @return json字符串
     */
    public static <T> String obj2StringPretty(T obj) {
        if(obj == null) {
            return null;
        }
        try {
            return obj instanceof String ? (String) obj : objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch(Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * json字符串转对象
     * @param json json字符串
     * @param clazz 对象类型
     * @param <T> 对象类型
     * @return 转换结果
     */
    public static <T> T string2Obj(String json, Class<T> clazz) {
        if(StringUtils.isEmpty(json) || clazz == null) {
            return null;
        }

        try {
            //noinspection unchecked
            return clazz.equals(String.class) ? (T) json : objectMapper.readValue(json, clazz);
        } catch(Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 字符串转复杂对象（List，Map，Set等）
     * @param json json字符串
     * @param typeReference 类型参考
     * @param <T> 目标类型
     * @return 转换结果
     */
    public static <T> T string2Obj(String json, TypeReference<T> typeReference) {
        if(StringUtils.isBlank(json) || typeReference == null) {
            return null;
        }
        try {
            //noinspection unchecked
            return (T) (typeReference.getType().equals(String.class) ? json : objectMapper.readValue(json, typeReference));
        } catch(Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 字符串转复杂对象（List，Map，Set等）
     * @param json json字符串
     * @param collectionClass 要参数化的类型擦除类型
     * @param elementClasses 键入要应用的参数
     * @param <T> 目标类型
     * @return 转换结果
     */
    public static <T> T string2Obj(String json, Class<?> collectionClass, Class<?>... elementClasses) {
        if(StringUtils.isBlank(json)){
            return null;
        }

        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);
        try {
            return objectMapper.readValue(json, javaType);
        } catch(Exception e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        objectMapper = applicationContext.getBean(ObjectMapper.class);
    }
}
