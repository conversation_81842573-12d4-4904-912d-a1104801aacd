package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.exception.ServiceException;
import com.py.common.oss.IOssService;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.project.projectrebate.domain.dto.ProjectRebateAddDTO;
import com.py.project.projectrebate.domain.dto.ProjectRebateImportModel;
import com.py.project.projectrebate.domain.dto.ProjectRebateUpdateDTO;
import com.py.project.projectrebate.domain.query.ProjectRebateQuery;
import com.py.project.projectrebate.domain.vo.*;
import com.py.project.projectrebate.projectrebatedetail.domain.dto.ProjectRebateAddDetailDTO;
import com.py.project.projectrebate.projectrebatedetail.domain.dto.ProjectRebateDetailDTO;
import com.py.project.projectrebate.projectrebatedetail.domain.vo.ProjectRebateDetailVO;
import com.py.project.projectrebate.projectrebatedetail.service.IProjectRebateDetailService;
import com.py.project.projectrebate.projectrebaterecord.domain.query.ProjectRebateRecordInfoQuery;
import com.py.project.projectrebate.projectrebaterecord.domain.query.ProjectRebateRecordQuery;
import com.py.project.projectrebate.projectrebaterecord.tools.RebateLock;
import com.py.project.projectrebate.service.IProjectRebateService;
import com.py.project.projectrebate.service.impl.ProjectRebateServiceImpl;
import com.py.resources.mediumresourceservice.domain.query.ImportType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 项目返点管理表Controller
 *
 * <AUTHOR>
 * @date 2023-07-24
 */
@Api(tags = "项目返点管理表")
@RestController
@RequestMapping("/projectrebate")
public class ProjectRebateController extends BaseController {

    /** 项目返点管理表服务 */
    @Resource
    private IProjectRebateService projectRebateService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    @Resource
    private IOssService ossService;

    /** 返点明细服务 */
    @Resource
    private IProjectRebateDetailService iProjectRebateDetailService;

    @Resource
    private RebateLock rebateLock;

    /**
     * 分页查询项目返点管理表列表
     *
     * @param query 项目返点管理表查询参数
     * @return 项目返点管理表分页
     */
    @ApiOperation("分页查询询项目返点管理表列表")
    @PreAuthorize("@ss.hasPermi('projectrebate::list')")
    @GetMapping("/pageProjectRebate")
    public R<PageInfo<ProjectRebateListVO>> pageProjectRebate(@Validated ProjectRebateQuery query) {
        PageInfo<ProjectRebateListVO> voList = this.projectRebateService.pageRebateList(query, true);
        return R.success(voList);
    }

    /**
     * 获取项目主体下返点数量
     * @return 项目主体下返点数量
     */
    @ApiOperation("获取项目主体下返点数量")
    @PreAuthorize("@ss.hasPermi('projectrebate::query')")
    @GetMapping(value = "/selectProjectItemCount")
    public R<List<ProjectRebateMainstayCountVO>> selectProjectItemCount() {
        return R.success(projectRebateService.selectProjectItemCount());
    }

    /**
     * 获取项目主体各个状态返点数量
     * @return 项目主体下返点数量
     */
    @ApiOperation("获取项目主体各个状态返点数量")
    @PreAuthorize("@ss.hasPermi('projectrebate::query')")
    @GetMapping(value = "/selectStatusTotalCount")
    public R<MainstayStatusCountVO> selectStatusTotalCount(@Validated ProjectRebateQuery query) {
        return R.success(projectRebateService.selectProjectItemCountByStatus(query));
    }

    /**
     * 返点登记选中合计已返金额
     *
     * @param query 项目返点管理表查询参数
     * @return 项目返点管理表分页
     */
    @ApiOperation("返点登记选中合计已返金额")
    @PreAuthorize("@ss.hasPermi('projectrebate::list')")
    @PostMapping("/selectTotalStatistics")
    public R<SelectedAmountVO> projectRebateCheckAmount(@RequestBody ProjectRebateQuery query) {
        SelectedAmountVO result = this.projectRebateService.projectRebateListAmount(query);
        return R.success(result);
    }

    /**
     * 导出项目返点管理表
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目返点管理表")
    @PreAuthorize("@ss.hasPermi('projectrebate::export')")
    @Log(title = "项目返点管理表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@Validated @RequestBody ProjectRebateQuery query) throws IOException {
        String fileName = "返点管理列表数据-"+ DateUtils.format(LocalDateTime.now(),"yyyy-MM-dd-HH:mm:ss") + ".xlsx";
        query.setFileName(fileName);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("项目返点管理", TaskType.Export, query, ProjectRebateServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 调用前提条件校验接口
     * 返点登记选中明细（校验通过之后传可填资源id集合展示）
     *
     * @param query 项目返点管理表查询参数（校验通过之后的项目资源id集合）
     * @return 项目返点管理表分页
     */
    @ApiOperation("返点登记选中明细")
    @PreAuthorize("@ss.hasPermi('projectrebate::list')")
    @PostMapping("/projectRebateCheckAmount")
    public R<PageInfo<ProjectRebateListVO>> pageProjectRebateCheck(@Validated @RequestBody ProjectRebateQuery query) {
        PageInfo<ProjectRebateListVO> voList = this.projectRebateService.pageProjectRebateCheck(query);
        return R.success(voList);
    }

    /**
     * 获取项目返点管理表详细信息 (废弃)
     * @param id 项目返点管理表主键
     * @return 项目返点管理表视图模型
     */
    @ApiOperation("获取项目返点管理表详细信息")
    @PreAuthorize("@ss.hasPermi('projectrebate::query')")
    @GetMapping(value = "/{id}")
    public R<ProjectRebateVO> getInfo(@PathVariable("id") Long id) {
        return R.success(projectRebateService.selectProjectRebateById(id));
    }

    /**
     * 返点登记保存(废弃)
     *
     * @param dtoList 项目返点管理表修改参数
     * @return 是否成功
     */
    @ApiOperation("返点登记保存")
    @PreAuthorize("@ss.hasPermi('projectrebate::edit')")
    @Log(title = "返点登记保存", businessType = BusinessType.UPDATE)
    @PostMapping("/addRebate")
    public R<Boolean> addRebate(@Validated @RequestBody List<ProjectRebateAddDTO> dtoList) {
        return R.success(projectRebateService.addRebate(dtoList));
    }

    /**
     * 返点登记明细(返点明细)
     *
     * @param dtoList 项目返点管理表修改参数
     * @return 是否成功
     */
    @ApiOperation("返点登记明细")
    @PreAuthorize("@ss.hasPermi('projectrebate::edit')")
    @Log(title = "返点登记明细", businessType = BusinessType.UPDATE)
    @PostMapping("/addRebateDetail")
    public R<Boolean> addRebateDetail(@Validated @RequestBody List<ProjectRebateAddDetailDTO> dtoList) {
        return R.success(projectRebateService.addRebateDetail(dtoList,Boolean.TRUE));
    }


    /**
     * 财务返点明细更新校验资源是否被占用
     * @param projectResourceId 更新明细所属的项目资源
     * @return 校验结果
     */
    @ApiOperation("财务修改返点明细校验")
    @GetMapping("/checkRebateDetails/{projectResourceId}")
    public R<Boolean> checkRebateDetailStatus(@PathVariable(value = "projectResourceId") Long projectResourceId){
        projectRebateService.checkRebateDetail(projectResourceId);
        return R.success(Boolean.TRUE);
    }


    /**
     * 财务更新返点明细
     * @param dto 明细更新信息
     * @return 更新结果
     */
    @ApiOperation("更新返点明细")
    @PostMapping("/updateRebateDetail")
    public R<Boolean> updateRebateDetail(@RequestBody ProjectRebateDetailDTO dto ){
        projectRebateService.updateRebateDetail(dto);
        return R.success(Boolean.TRUE);
    }

    /**
     * 返点更新 (废弃)
     *
     * @param dto 项目返点管理表修改参数
     * @return 是否成功
     */
    @ApiOperation("返点更新")
    @PreAuthorize("@ss.hasPermi('projectrebate::edit')")
    @Log(title = "返点更新", businessType = BusinessType.UPDATE)
    @PostMapping("/updateRebate")
    public R<Boolean> updateRebate(@Validated @RequestBody ProjectRebateUpdateDTO dto) {
        return R.success(projectRebateService.updateRebate(dto));
    }

    /**
     * 导入
     * @param file 批量导入返点登记导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('projectrebate::import')" )
    @Log(title = "项目返点管理表" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }
        ExcelUtil<ProjectRebateImportModel> util = new ExcelUtil<>(ProjectRebateImportModel.class);
        List<ProjectRebateImportModel> projectRebateList = util.importExcel(file.getInputStream());

        if(ListUtil.isEmpty(projectRebateList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }

        ProjectRebateQuery query = new ProjectRebateQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(), false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        query.setImportType(ImportType.INSERT);
        reusableAsyncTaskService.addTask("项目返点管理-批量登记", TaskType.Import, query, ProjectRebateServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('projectrebate::import')" )
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_rebate.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("返点管理批量登记模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 项目管理-返点管理-返点登记申请校验接口
     *
     * @param query 查询参数（包含是否全选）
     * @return 校验结果
     */
    @ApiOperation("项目管理-返点管理-返点登记申请校验接口")
    @PostMapping("/checkProjectResourceRebates")
    public R<CheckRebateErrorInfo> checkProjectResourceRebates(@Validated @RequestBody ProjectRebateRecordInfoQuery query) {
        if (Objects.nonNull(query.getPyMainstayId())){
            query.setIsFinanceSedMenu(Boolean.TRUE);
        }else {
            query.setIsFinanceSedMenu(Boolean.FALSE);
        }
        return R.success(projectRebateService.checkProjectResourceRebates(query));
    }

    /**
     * 项目管理-返点管理-进入返点登记列表回显
     *
     * @param query 项目资源id集合
     * @return 项目资源详情
     */
    @ApiOperation("项目管理-返点管理-返点登记列表回显")
    @PostMapping("/getProjectResourceRebateInfo")
    public R<List<ProjectRebateListVO>> getProjectResourceRebateInfo(@RequestBody ProjectRebateRecordInfoQuery query) {
        //标记项目查询
        query.setIsFinanceSedMenu(Boolean.FALSE);
        List<ProjectRebateListVO> voList = projectRebateService.getProjectResourceRebateInfo(query);
        return R.success(voList);
    }


    /**
     * 项目返点登记设置缓存-不允许同一时间多人修改
     *
     * @param query 资源id查询参数
     * @return 设置结果
     */
    @ApiOperation("反点登记设置缓存-不允许同一时间多人修改")
    @PostMapping("/checkRebateApplication")
    public R<Boolean> checkRebateApplication(@RequestBody ProjectRebateRecordInfoQuery query) {
        return projectRebateService.checkRebateApplication(query);
    }

    /**
     * 项目管理-退出资源返点登记删除缓存
     *
     * @param query 项目资源id集合
     * @return 删除结果
     */
    @ApiOperation("返点登记-离开资源返点登页删除缓存")
    @PostMapping("/exitsProjectResourceRebateId")
    public R<Boolean> exitsProjectResourceRebateId(@RequestBody ProjectRebateRecordInfoQuery query) {
        rebateLock.exitsProjectResourceRebateId(query);
        return R.success(true);
    }


    /**
     * 返点明细按钮-回显基本信息
     *
     * @param projectResourceId 项目资源id
     * @return 项目资源基本信息
     */
    @ApiOperation("返点明细按钮-回显基本信息")
    @GetMapping("/queryRebateInfo/{projectResourceId}")
    public R<ProjectRebateListVO> queryRebateInfo(@PathVariable("projectResourceId") Long projectResourceId) {
        ProjectRebateListVO projectRebateInfoVO = projectRebateService.queryRebateInfo(projectResourceId);
        return R.success(projectRebateInfoVO);
    }

    /**
     * 返点明细按钮-回显列表信息
     * @param query 项目资源id
     * @return 返点明细信息列表
     */
    @ApiOperation("返点明细按钮-回显附件信息列表")
    @PostMapping("/queryRebateDetails")
    public R<PageInfo<ProjectRebateDetailVO>> queryRebateDetails(@RequestBody ProjectRebateRecordQuery query){
        PageInfo<ProjectRebateDetailVO> voList = this.iProjectRebateDetailService.queryRebateDetails(query);
        return R.success(voList);
    }


    /**
     * 用户关闭浏览器清除该用户的所有redis锁
     * @return 操作结果
     */
    @ApiOperation("用户关闭页面时候清除占用的redis")
    @GetMapping("/exitsUserRebateLock")
    public R<Void> exitsUserRebateLock(){
        rebateLock.exitsProjectResourceRebateIdByUserId();
        return R.success();
    }
}
