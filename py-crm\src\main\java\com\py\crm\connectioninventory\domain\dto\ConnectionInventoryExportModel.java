package com.py.crm.connectioninventory.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.py.common.tools.poiexcel.annotation.Excel;
import com.py.common.typehandler.impl.LongSetTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 人脉管理-清单列表视图模型
 *
 * <AUTHOR>
 * @date 2023-09-26
 */
@Data
@ApiModel("人脉管理-清单列表视图模型")
public class ConnectionInventoryExportModel {
    private static final long serialVersionUID = 1L;

    /** 客户名称 */
    @ApiModelProperty("客户姓名")
    @Excel(name = "人脉姓名")
    private String connectionName;

    /** 状态（0-无效；1-有效） */
    @Excel(name = "人脉状态")
    private String statusStr;

    /** 现任职企业（手动输入） */
    @ApiModelProperty("现任企业")
    @Excel(name = "现任企业")
    private String currentEmployer;

    /** 现任负责品牌/业务线 */
    @ApiModelProperty("现任负责品牌/业务线")
    @Excel(name = "现任负责品牌/业务线")
    private String responsibleBrandStr;

    /** 行业类目(来源于数据字典) */
    @TableField(typeHandler = LongSetTypeHandler.class)
    private List<Long> industryCategory;
    @ApiModelProperty("现任行业类目")
    @Excel(name = "现任行业类目")
    private String industryCategoryStr;

    /** 所在部门 */
    @ApiModelProperty("所在部门")
    @Excel(name = "现任部门")
    private String departmentName;

    /** 现任岗位 */
    @ApiModelProperty("现任岗位")
    @Excel(name = "现任岗位")
    private String postName;

    /** 对派芽信任度（数据字典） */
    private String pyTrustLevel;
    @ApiModelProperty("对派芽信任度")
    @Excel(name = "对派芽信任度")
    private String pyTrustLevelStr;

    /** 人脉地址 */
    @ApiModelProperty("人脉地址")
    @Excel(name = "人脉地址")
    private String connectionAddress;

    /** 电话 */
    @ApiModelProperty("电话")
    @Excel(name = "电话")
    private String phone;

    /** 微信 */
    @ApiModelProperty("微信")
    @Excel(name = "微信")
    private String wechatNumber;

    /** 钉钉/飞书/其它 */
    @ApiModelProperty("钉钉/飞书/其它")
    @Excel(name = "钉钉/飞书/其它")
    private String otherNumber;

    /** 目标服务人员id */
    private Long serviceUserId;
    @ApiModelProperty("服务人员")
    @Excel(name = "服务人员")
    private String serviceUserName;

    /** 录入人 */
    @ApiModelProperty("录入人")
    @Excel(name = "录入人")
    private String createBy;

    /** 录入部门 */
    @ApiModelProperty("录入部门")
    @Excel(name = "录入部门")
    private String createDept;

    /** 录入时间 */
    @ApiModelProperty("录入时间")
    @Excel(name = "录入时间")
    private String createTimeStr;

    /** 备注 */
    @ApiModelProperty("备注")
    private String remark;
}
