package com.py.common.utils.tree;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.py.common.utils.collection.ListUtil;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 * <AUTHOR>
 */
@Getter
@Setter
public class TreeSelect<T extends ITreeNode<T>> implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;

    /** 上一个节点 */
    private Long previous;

    /** 上一个节点名字 */
    private String previousName;

    /** 下一个节点 */
    private Long next;

    /** 下一个节点名称 */
    private String nextName;

    /** 节点序号 */
    private Integer nodeIndex = 0;

    /** 可以上移 */
    private Boolean canUp = false;

    /** 可以下移 */
    private Boolean canDown = false;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect<T>> children;

    public TreeSelect(ITreeNode<T> treeNode) {
        this.id = treeNode.getTreeId();
        this.label = treeNode.getNodeName();
        this.children = ListUtil.map(treeNode.getChildren(), children -> new TreeSelect<>(children));
    }

    /**
     * 获取所有节点
     * @return 节点列表
     */
    @JsonIgnore
    public List<TreeSelect<T>> getAllNode() {
        if(ListUtil.isEmpty(children)) {
            return Collections.singletonList(this);
        }

        List<TreeSelect<T>> result = new ArrayList<>(children.size() + 1);
        result.add(this);
        List<TreeSelect<T>> subNode = children.stream()
                .flatMap(node -> node.getAllNode().stream())
                .collect(Collectors.toList());
        result.addAll(subNode);
        return result;
    }

}
