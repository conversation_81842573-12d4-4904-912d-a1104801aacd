package com.py.common.typehandler.impl;

import com.py.common.typehandler.BaseSetTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.util.function.Function;

/**
 * 整型集合类型处理器
 * <p>将整型集合转换为 1,2,3 存储至数据库</p>
 * <AUTHOR>
 */
@MappedJdbcTypes(JdbcType.LONGVARCHAR)
public class IntegerSetTypeHandler extends BaseSetTypeHandler<Integer> {

    /**
     * 初始化内容序列化器
     * @return 内容序列化器
     */
    @Override
    protected Function<Integer, String> initSerializer() {
        return value -> value.toString();
    }

    /**
     * 初始化内容反序列化器
     * @return 内容反序列化器
     */
    @Override
    protected Function<String, Integer> initDeserializer() {
        return Integer::valueOf;
    }
}
