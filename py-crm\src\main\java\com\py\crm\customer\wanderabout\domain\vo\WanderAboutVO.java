package com.py.crm.customer.wanderabout.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户管理-客户流转视图模型
 *
 * <AUTHOR>
 * @date 2023-08-16
 */
@Data
@ApiModel("客户管理-客户流转视图模型" )
public class WanderAboutVO {
    private static final long serialVersionUID = 1L;

    /** 自增主键*/
    @ApiModelProperty("自增主键")
    private Long id;

    /** 客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 服务人员id*/
    @ApiModelProperty("服务人员id")
    private Long userId;

    /** 创建者Id*/
    @ApiModelProperty("创建者Id")
    private Long createId;

    /** 创建者*/
    @ApiModelProperty("创建者")
    private String createBy;

    /** 创建人部门*/
    @ApiModelProperty("创建人部门")
    private String createDept;

    /** 创建时间*/
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /** 更新者Id*/
    @ApiModelProperty("更新者Id")
    private Long updateId;

    /** 更新者*/
    @ApiModelProperty("更新者")
    private String updateBy;

    /** 更新人部门*/
    @ApiModelProperty("更新人部门")
    private String updateDept;

    /** 更新时间*/
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /** 删除标志*/
    @ApiModelProperty("删除标志")
    private String delFlag;


}
