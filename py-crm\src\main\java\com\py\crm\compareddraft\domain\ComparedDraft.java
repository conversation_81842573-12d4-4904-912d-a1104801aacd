package com.py.crm.compareddraft.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.py.common.core.domain.BaseEntity;
import com.py.common.oss.model.OssObjectInfo;
import com.py.common.typehandler.impl.LongSetTypeHandler;
import com.py.common.typehandler.impl.StringSetTypeHandler;
import com.py.crm.compareddraft.typehandler.OssObjectInfoListTypeHandler;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户管理-比稿管理实体类
 *
 * <AUTHOR>
 * @date 2023/07/17 09:36
 */
@Data
@TableName(value = "py_crm_compared_draft",autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class ComparedDraft extends BaseEntity {
    private static final long serialVersionUID=1L;
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 比稿id
     */
    @TableId
    private Long comparedDraftId;

    /**
     * 客户id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long customerId;

    /**
     * 客户名
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String customerName;

    /**
     * 客户合作主体ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long customerCooperateId;

    /**
     * 客户合作主体
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String customerCooperateName;

    /**
     * 品牌/业务线名
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String brandName;

    /**
     * 行业类目（数据字典Id）
     */
    @ApiModelProperty("行业类目（数据字典Id）")
    @TableField(updateStrategy = FieldStrategy.IGNORED,typeHandler = StringSetTypeHandler.class)
    private List<String> categoryId;

    /**
     * 行业类目名称
     */
    @ApiModelProperty("行业类目名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED,typeHandler = StringSetTypeHandler.class)
    private List<String> categoryName;

    /**
     * 品牌阶段
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer brandStage;

    /**
     * 合作部门
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String collaborateDapt;

    /**
     * 比稿项目名称
     */
    private String comparedDraftName;

    /**
     * 商务标比稿结果
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer commercialBidFruits;

    /**
     * 技术标比稿结果
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer techniqueBidFruits;

    /**
     * 最终比稿结果
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer finalFruits;

    /**
     * 比稿时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDate comparedDraftTime;

    /**
     * 录入时间
     */
    private LocalDateTime enteringTime;

    /**
     * 比稿金额(元)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal comparedDraftMoney;

    /**
     * 成交金额(元)
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal turnoverMoney;

    /**
     * 派芽业务类型
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer pyType;

    /**
     * 方案类型
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer planType;

    /**
     * 比稿方案
     */
    @TableField(typeHandler = OssObjectInfoListTypeHandler.class,updateStrategy = FieldStrategy.IGNORED)
    private List<OssObjectInfo> comparedDraftPlan;

    /**
     * 比稿策略复盘
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String strategyReview;

    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remarks;

}
