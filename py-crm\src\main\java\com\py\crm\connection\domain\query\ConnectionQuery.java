package com.py.crm.connection.domain.query;

import com.py.common.core.domain.query.BaseCreateListInfo;
import com.py.common.core.domain.query.ICreateQuery;
import com.py.common.core.domain.query.IUpdateQuery;
import com.py.common.datascope.IDataScopeArgs;
import com.py.common.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * 人脉管理表查询对象
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
@ApiModel("人脉管理表查询对象" )
public  class ConnectionQuery extends BaseCreateListInfo implements IUpdateQuery,ICreateQuery, IDataScopeArgs {
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @ApiModelProperty("自增id")
    private Long id;

    /**
     * 人脉Id
     */
    @ApiModelProperty("人脉Id")
    private Long connectionId;

    /**
     * 人脉Id集合
     */
    @ApiModelProperty("人脉Id集合")
    private List<Long> connectionIdList;
    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String connectionName;

    /**
     * 状态（0无效；1有效）
     */
    @ApiModelProperty("状态（0无效；1有效）")
    private Integer status;

    /**
     * 电话
     */
    @ApiModelProperty("电话")
    private String phone;

    /**
     * 微信
     */
    @ApiModelProperty("微信")
    private String wechatNumber;

    /**
     * 钉钉/飞书/其它
     */
    @ApiModelProperty("钉钉/飞书/其它")
    private String otherNumber;

    /**
     * 现任职企业id(客户id)
     */
    @ApiModelProperty("现任职企业id(客户id)")
    private Long customerId;

    /**
     * 现任职企业（手动输入）
     */
    @ApiModelProperty("现任职企业（手动输入）")
    private String currentEmployer;

    /**
     * 历任职企业（手动输入）
     */
    @ApiModelProperty("历任职企业")
    private String historyCurrentEmployer;
    /**
     * 负责品牌/业务线
     */
    @ApiModelProperty("负责品牌/业务线")
    private String responsibleBrand;

    /**
     * 历任负责品牌/业务线
     */
    @ApiModelProperty("历任负责品牌/业务线")
    private String historyResponsibleBrand;

    /**
     * 现任行业类目(来源于数据字典)
     */
    @ApiModelProperty("现任行业类目(来源于数据字典)")
    private List<Long> industryCategoryList;
    /**
     * 历任行业类目(来源于数据字典)
     */
    @ApiModelProperty("历任行业类目(来源于数据字典)")
    private List<Long> historyIndustryCategoryList;

    /**
     * 所在部门
     */
    @ApiModelProperty("所在部门")
    private String departmentName;
    /**
     * 历任所在部门
     */
    @ApiModelProperty("历任所在部门")
    private String historyDepartmentName;
    /**
     * 岗位名称
     */
    @ApiModelProperty("岗位名称")
    private String postName;
    /**
     * 历任岗位名称
     */
    @ApiModelProperty("历任岗位名称")
    private String historyPostName;

    /**
     * 对派芽信任度（数据字典）
     */
    @ApiModelProperty("对派芽信任度（数据字典）")
    private String pyTrustLevel;
    /**
     * 对派芽信任度（数据字典）
     */
    @ApiModelProperty("对派芽信任度（数据字典）")
    private List<Long> pyTrustLevelList;
    /**
     * 人脉地址
     */
    @ApiModelProperty("人脉地址")
    private String connectionAddress;

    /**
     * 目标服务人员id
     */
    @ApiModelProperty("目标服务人员id")
    private Long serviceUserId;
    /**
     * 现任服务人员id
     */
    @ApiModelProperty("现任服务人员id")
    private List<Long> serviceUserIds;

    /**
     * 现任服务人员部门列表
     */
    @ApiModelProperty("现任服务人员部门列表")
    private List<String> serviceUserDeptList;

    private String createUser;
    /** 更新人 */
    @ApiModelProperty("创建人" )
    private List<Long> createIdList;

    /** 更新部门*/
    @ApiModelProperty("创建人部门" )
    private List<String> createDeptList;

    private String createDept;
    /**
     * 创建时间-开始
     */
    @ApiModelProperty("创建时间-开始")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate startCreateDate;

    /**
     * 创建时间-结束
     */
    @ApiModelProperty("创建时间-结束")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate endCreateDate;

    /** 恢复时间-开始 */
    @ApiModelProperty("更新时间-开始")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate startUpdateDate;

    /** 恢复时间-结束 */
    @ApiModelProperty("更新时间-结束")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate endUpdateDate;
    /** 更新人 */
    @ApiModelProperty("更新人" )
    private List<Long> updateIdList;

    /** 更新部门*/
    @ApiModelProperty("更新部门" )
    private List<Long> updateDept;

    /** 数据权限*/
    private String dataScopeSql;

    @Override
    public List<Long> getUpdateUser() {
        return this.updateIdList;
    }

    @Override
    public LocalDate getStartUpdateDate() {
        return this.startUpdateDate;
    }

    @Override
    public LocalDate getEndUpdateDate() {
        return this.endUpdateDate;
    }

    @Override
    public String getCreateUser() {
        return this.createUser;
    }

    /** 是否是移除 */
    private Boolean isDeleted = false;

    /** 部门名称*/
    @ApiModelProperty("部门名称" )
    private String deptName;
}
