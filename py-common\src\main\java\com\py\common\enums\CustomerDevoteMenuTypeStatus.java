package com.py.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户贡献-按钮菜单类型：1-比稿；2-项目；3-结案；4-坏账
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CustomerDevoteMenuTypeStatus implements IDict<Integer> {

    /** 比稿 */
    COMPARED_DRAFT(1, "比稿"),
    /** 项目 */
    PROJECT(2, "项目"),
    /** 结案 */
    CLOSE_CASE_PROJECT(3, "结案"),
    /** 坏账 */
    BAD_DEBT(4, "坏账"),
    ;

    private final Integer value;

    private final String label;
}
