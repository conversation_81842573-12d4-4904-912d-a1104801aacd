package com.py.common.tools.verify;

import com.py.common.tools.verify.config.VerifyConfig;
import com.py.common.tools.verify.config.VerifyItem;
import com.py.common.tools.verify.domain.ISerialNumber;
import com.py.common.tools.verify.domain.RowVerifyError;
import com.py.common.tools.verify.domain.VerifyItemResult;
import com.py.common.tools.verify.enums.VerifyType;
import com.py.common.tools.verify.verifyprovider.VerifyProvider;
import com.py.common.utils.collection.ListUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 对象验证器
 * <AUTHOR>
 */
@Component
@SuppressWarnings("unused")
public class ObjectValidator {

	/** 验证提供器Map */
	private final Map<VerifyType, VerifyProvider> verifyProviderMap;

	/** 日志 */
	private final Logger logger = LoggerFactory.getLogger(ObjectValidator.class);

	public ObjectValidator(Collection<VerifyProvider> verifyProviderList) {
		this.verifyProviderMap = ListUtil.toMap(verifyProviderList, VerifyProvider::supportedVerifyType);
	}

	/**
	 * 校验对象合法性
	 * @param verifyData 被校验数据
	 * @param <T> 校验数据类型
	 * @return 正确的数据
	 */
	public <T extends ISerialNumber & Verifiable<T>> List<RowVerifyError<T>> verifyImportList(List<T> verifyData) {
		if(ListUtil.isEmpty(verifyData)) {
			return ListUtil.emptyList();
		}

		return this.verifyImportList(verifyData, ListUtil.firstOrThrow(verifyData).getVerifyConfig());
	}

	/**
	 * 校验对象合法性
	 * @param verifyData 被校验数据
	 * @param verifyConfig 验证配置
	 * @param <T> 校验数据类型
	 * @return 正确的数据
	 */
	public <T extends ISerialNumber> List<RowVerifyError<T>> verifyImportList(List<T> verifyData, VerifyConfig<T> verifyConfig) {
		if(ListUtil.isEmpty(verifyData)) {
			return ListUtil.emptyList();
		}

		List<RowVerifyError<T>> verifyErrorList = new ArrayList<>();
		for(T item : verifyData) {
			if(item == null) {
				continue;
			}

			VerifyItemResult verifyResult = this.verify(item, verifyConfig);
			if(verifyResult.isError() == true) {
				RowVerifyError<T> rowVerifyError = new RowVerifyError<>(item.getSerialNumber(), item, verifyResult.getErrorMsgList());
				verifyErrorList.add(rowVerifyError);
			}
		}
		return verifyErrorList;
	}

	/**
	 * 验证对象
	 * @param target 对象
	 * @param verifyConfig 验证配置
	 * @param <T> 对象类型
	 * @return 验证结果
	 */
	public <T> VerifyItemResult verify(T target, VerifyConfig<T> verifyConfig) {
		VerifyItemResult verifyItemResult = new VerifyItemResult();

		for(VerifyItem<T> verifyItem : verifyConfig.getVerifyItemList()) {
			VerifyProvider verifyProvider = this.getVerifyProvider(verifyItem.getVerifyType());
			Object verifyObject = verifyItem.getContextSelector().apply(target);
			try {
				if(verifyProvider.verify(verifyObject) == false) {
					String errorMessage = verifyItem.getErrorMessageGenerator().apply(target);
					verifyItemResult.addErrorMsg(errorMessage);

				}
			} catch(Exception e) {
				logger.error("校验异常", e);
				String expectedErrorMessage = "";
				try {
					expectedErrorMessage = verifyItem.getErrorMessageGenerator().apply(target);
				} catch(Exception exception) {
					logger.error("预期警告获取失败", exception);
				}

				throw new RuntimeException(String.format("类型: %s 校验异常: %s, 预期警告为: %s", target.getClass(), e.getMessage(), expectedErrorMessage), e);
			}
		}
		return verifyItemResult;
	}

	/**
	 * 获取验证提供者
	 * @param verifyType 需要验证的类型
	 * @return 对应类型的验证提供者
	 */
	private VerifyProvider getVerifyProvider(VerifyType verifyType) {
		VerifyProvider verifyProvider = this.verifyProviderMap.get(verifyType);
		if(verifyProvider == null) {
			throw new IllegalArgumentException(String.format("%s 的验证器未实现", verifyType));
		}
		return verifyProvider;
	}
}
