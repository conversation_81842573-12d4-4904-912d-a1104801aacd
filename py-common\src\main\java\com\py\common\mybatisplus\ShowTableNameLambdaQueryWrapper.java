package com.py.common.mybatisplus;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.Query;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.support.ColumnCache;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import lombok.Setter;

/**
 * 显示表名的LambdaQueryWrapper
 * <AUTHOR>
 */
public class ShowTableNameLambdaQueryWrapper<T> extends LambdaQueryWrapper<T>
        implements Query<LambdaQueryWrapper<T>, T, SFunction<T, ?>> {

    public ShowTableNameLambdaQueryWrapper(T entity) {
        super(entity);
    }

    public ShowTableNameLambdaQueryWrapper(Class<T> entityClass) {
        super(entityClass);
    }

    /** 表别名 */
    @Setter
    private String tableAlias = null;

    @Override
    protected String columnToString(SFunction<T, ?> column) {
        return columnToString(column, true);
    }

    @Override
    protected String columnToString(SFunction<T, ?> column, boolean onlyColumn) {
        ColumnCache cache = getColumnCache(column);
        String columnName = onlyColumn ? cache.getColumn() : cache.getColumnSelect();
        return this.getTableAlias() + "." + columnName;
    }

    /**
     * 获取表别名
     * @return 表别名
     */
    public String getTableAlias() {
        if(tableAlias != null){
            return tableAlias;
        }
        TableInfo tableInfo = TableInfoHelper.getTableInfo(getEntityClass());
        this.tableAlias = tableInfo.getTableName();
        return tableAlias;
    }
}
