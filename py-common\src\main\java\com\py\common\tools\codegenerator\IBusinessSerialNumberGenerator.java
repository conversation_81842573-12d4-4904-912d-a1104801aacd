package com.py.common.tools.codegenerator;

import com.py.common.tools.codegenerator.enums.SerialNumberBizType;
import com.py.common.tools.codegenerator.functional.SerialNumberInitCallback;

import java.util.concurrent.TimeUnit;

/**
 * 业务序列号生成器
 * <AUTHOR>
 */
public interface IBusinessSerialNumberGenerator {

    /**
     * 生成序列号
     * @param bizType 序列号业务类型
     * @param initCallback 初始化回调, 缓存中没有序列号时调用该方法, 用户缓存失效时使用数据库里的序列号初始化缓存
     * @return 序列号
     */
    long generateSerialNumber(
            SerialNumberBizType bizType,
            SerialNumberInitCallback initCallback);

    /**
     * 生成序列号
     * @param bizType 序列号业务类型
     * @param otherId 其他识别码
     * @param initCallback 初始化回调, 缓存中没有序列号时调用该方法, 用户缓存失效时使用数据库里的序列号初始化缓存
     * @return 序列号
     */
    long generateSerialNumber(
            SerialNumberBizType bizType,
            String otherId,
            SerialNumberInitCallback initCallback);

    /**
     * 生成序列号
     * @param bizType 序列号业务类型
     * @param otherId 其他识别码
     * @param initCallback 初始化回调, 缓存中没有序列号时调用该方法, 用户缓存失效时使用数据库里的序列号初始化缓存
     * @param timeout 缓存失效时间
     * @param timeUnit 缓存失效时间单位
     * @return 序列号
     */
    long generateSerialNumber(
            SerialNumberBizType bizType,
            String otherId,
            SerialNumberInitCallback initCallback,
            Integer timeout,
            TimeUnit timeUnit);

    /**
     * 清除缓存
     * @param bizType 序列号业务类型
     * @return 是否成功
     */
    Boolean clearSerialNumberCache(SerialNumberBizType bizType);
}
