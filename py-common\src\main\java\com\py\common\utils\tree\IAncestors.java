package com.py.common.utils.tree;

import java.util.ArrayList;
import java.util.List;

/**
 * 祖籍列表接口
 * <AUTHOR>
 */
public interface IAncestors<T> extends ITreeNode<T> {

    /**
     * 获取祖籍列表
     * @return 祖籍列表
     */
    List<Long> getAncestors();

    /**
     * 设置祖籍列表
     * @param ancestors 祖籍列表
     */
    void setAncestors(List<Long> ancestors);

    /**
     * 初始化祖级列表
     * @param parentNode 父节点
     */
    default void initAncestors(IAncestors<T> parentNode) {
        if(parentNode == null || TreeUtil.TREE_ROOT.equals(this.getParentId())){
            List<Long> ancestors = new ArrayList<>(1);
            ancestors.add(TreeUtil.TREE_ROOT);
            this.setAncestors(ancestors);
            return;
        }

        List<Long> ancestors = new ArrayList<>(parentNode.getAncestors());
        ancestors.add(parentNode.getTreeId());
        this.setAncestors(ancestors);
    }
}
