package com.py.web.controller.crm;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryListDTO;
import com.py.crm.customer.customerlaundry.domain.query.CustomerLaundryQuery;
import com.py.crm.customer.customerlaundry.domain.vo.CustomerLaundryListVO;
import com.py.crm.customer.customerlaundry.enums.LaundryType;
import com.py.crm.customer.customerlaundry.service.ICustomerLaundryService;
import com.py.crm.customer.customerlaundry.service.impl.CustomerLaundryServiceImpl;
import com.py.crm.customer.domain.vo.CustomerCountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户管理-查看清单Controller
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Api(tags = "客户管理-查看清单")
@RestController
@RequestMapping("/crm/customerLaundry")
public class CustomerLaundryController extends BaseController {

    /** 客户管理-查看清单服务 */
    @Resource
    private ICustomerLaundryService customerLaundryService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 分页查询客户管理-查看清单列表
     *
     * @param query 客户管理-查看清单查询参数
     * @return 客户管理-查看清单分页
     */
    @ApiOperation("分页查询询客户管理-查看清单列表")
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerLaundry:list')")
    @GetMapping("/pageCustomerLaundry")
    public R<PageInfo<CustomerLaundryListVO>> pageCustomerLaundry(CustomerLaundryQuery query) {
        PageInfo<CustomerLaundryListVO> voList = this.customerLaundryService.pageCustomerLaundryList(query);
        return R.success(voList);
    }

    /**
     * 批量新增客户管理-查看清单
     *
     * @param dto 客户管理-查看清单修改参数
     * @return 是否成功
     */
    @ApiOperation("新增客户管理-查看清单")
    @PreAuthorize("@ss.hasPermi('crm:customerLaundry:add')")
    @Log(title = "客户管理-查看清单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Boolean> add(@Validated @RequestBody CustomerLaundryListDTO dto) {
        return R.success(customerLaundryService.insertBatchCustomerLaundry(dto));
    }

    /**
     * 删除客户管理-查看清单
     * @param ids 需要删除的客户管理-查看清单主键集合
     * @return 是否成功
     */
    @ApiOperation("删除客户管理-查看清单" )
    @PreAuthorize("@ss.hasPermi('crm:customerLaundry:remove')")
    @Log(title = "客户管理-查看清单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(customerLaundryService.deleteCustomerLaundryByIds(ids));
    }

    /**
     * 客户管理-查看清单头部统计
     * @return 是否成功
     */
    @ApiOperation("客户管理-查看清单头部统计" )
    @PreAuthorize("@ss.hasPermi('crm:customerLaundry:customerLaundryCount')")
    @GetMapping("/customerLaundryCount" )
    public R<CustomerCountVO> customerLaundryCount(){
        return R.success(customerLaundryService.customerLaundryCount());
    }

    /**
     * 客户管理-移除清单
     * @param dto 客户ID
     * @return 是否成功
     */
    @ApiOperation("客户管理-移除清单" )
    @PreAuthorize("@ss.hasPermi('crm:customerLaundry:removeLaundry')")
    @PostMapping("/removeLaundry" )
    public R<Boolean> removeLaundry(@RequestBody CustomerLaundryListDTO dto){
        return R.success(customerLaundryService.removeLaundry(dto));
    }

    /**
     * 客户管理-导出
     * @return 是否成功
     */
    @ApiOperation("客户管理-导出" )
    @PreAuthorize("@ss.hasPermi('crm:customerLaundry:export')")
    @Log(title = "客户管理",businessType = BusinessType.EXPORT)
    @PostMapping("/export" )
    public R<String> export(){
        CustomerLaundryQuery query = new CustomerLaundryQuery();
        query.setBizType(LaundryType.CUSTOMER.getValue());
        query.setLoginUser(SecurityUtils.getLoginUser());
        String format = DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss");
        query.setFileName("客户管理-" + format + ".xlsx");
        List<CustomerLaundryListVO> list = customerLaundryService.removeCustomerLaundry(query);
        query.setCustomerIdList(ListUtil.distinctMap(list,CustomerLaundryListVO::getCustomerId));
        query.setLaundryIdList(ListUtil.distinctMap(list,CustomerLaundryListVO::getLaundryId));
        reusableAsyncTaskService.addTask("客户管理", TaskType.Export,query, CustomerLaundryServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }
}
