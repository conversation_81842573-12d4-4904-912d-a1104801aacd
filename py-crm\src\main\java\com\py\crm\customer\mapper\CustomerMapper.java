package com.py.crm.customer.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.py.common.annotation.DataScope;
import com.py.common.datascope.DataScopePageType;
import com.py.common.mybatisplus.ShowTableNameLambdaQueryWrapper;
import com.py.common.mybatisplus.SuperMapper;
import com.py.crm.customer.domain.SupCustomer;
import com.py.crm.customer.domain.query.CustomerQuery;
import com.py.crm.customer.domain.vo.ComparedDraftMoneyVO;
import com.py.crm.customer.domain.vo.CrmCustomerProjectVO;
import com.py.crm.customer.domain.vo.CustomerCountVO;
import com.py.crm.customerdevote.domain.query.CustomerDevoteQuery;
import com.py.crm.customerdevote.domain.vo.CloseCaseMoneyListVO;
import com.py.crm.customerdevote.domain.vo.CustomerDevoteListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户管理-客户Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
public interface CustomerMapper extends SuperMapper<SupCustomer> {

    /**
     * 客户统计
     * @param query 数据权限
     * @return 客户统计
     */
    @DataScope(value = DataScopePageType.CRM_CustomerManage,userIdAlias = "user_id"
            ,dateAlias = "audit_time")
    CustomerCountVO customerCount(CustomerQuery query);

    /**
     * 品牌/业务线唯一校验
     * @param lineBusiness 参数
     * @return 客户信息
     */
    SupCustomer unique(@Param("lineBusiness") String lineBusiness);

    /**
     * 客户贡献列表按成交金额倒序
     * @param query 客户贡献管理查询模型
     * @return 客户对象
     */
    @DataScope(value = DataScopePageType.CRM_CustomerContribute,
            isIgnoreUserPermission = true,
            dateAlias = "project_approval_time",
            dateTableAlias = "project")
    List<SupCustomer> getCustomerDevoteList(
            @Param("query") CustomerDevoteQuery query,
            @Param("customerDataScopeWrapper") LambdaQueryWrapper<SupCustomer> customerDataScopeWrapper);

    /**
     * 比稿金额
     * @param query 客户id列表-结案时间-合作时间
     * @return 客户比稿金额
     */
    List<ComparedDraftMoneyVO> getCustomerDevoteProjectList(@Param("query") CustomerDevoteQuery query);

    /**
     * 比稿金额
     * @param query 客户id列表
     * @return 客户比稿金额
     */
    List<ComparedDraftMoneyVO> getCustomerComparedList(@Param("query") CustomerDevoteQuery query);


    /**
     * 根据客户的被比稿项目查询每个比稿金额
     * @param projectIdList 项目id
     * @return 查询每个项目的比稿金额
     */
    List<CrmCustomerProjectVO> getCustomerByProjectId(@Param("projectIdList") List<Long> projectIdList);

    /**
     * 查询项目id
     * @param query 客户贡献管理查询模型
     * @return 项目id
     */
    List<Long> getCustomerDevoteProjectIdList(CustomerDevoteQuery query);

    /**
     * 毛利率
     * @param projectIds 项目id
     * @return 客户贡献管理
     */
    List<CloseCaseMoneyListVO> getCloseProfitMarginList(@Param("projectIds") List<Long> projectIds);


    /**
     *  结案金额
     * @param projectIds 项目id
     * @return  客户贡献管理
     */
    List<CloseCaseMoneyListVO> getCloseCaseMoneyList(@Param("projectIds") List<Long> projectIds);

    /**
     * 坏账金额
     * @param query 客户贡献管理查询模型
     * @return 客户贡献管理
     */
    List<CustomerDevoteListVO> getBadeMoneyMap(@Param("query") CustomerDevoteQuery query);

    /**
     * 分页客户管理-客户列表
     * @param query 客户管理-客户
     * @param queryWrapper 参数
     * @return 客户管理-客户分页
     */
    @DataScope(value = DataScopePageType.CRM_CustomerManage,
            userIdAlias = "user_id",
            userTableAlias = "wanderAbout",
            dateAlias = "audit_time",
            dateTableAlias = "customer")
    List<SupCustomer> pageCustomerList(@Param("query") CustomerQuery query, @Param("ew") LambdaQueryWrapper<SupCustomer> queryWrapper);


    /**
     * 客户管理-客户列表,模糊查询录入人部门
     * @param query 客户管理-客户(录入人部门)
     * @param queryWrapper 参数
     * @return 列表中部门数据
     */
    @DataScope(value = DataScopePageType.CRM_CustomerManage,
            userIdAlias = "user_id",
            userTableAlias = "wanderAbout",
            dateAlias = "audit_time",
            dateTableAlias = "customer")
    List<SupCustomer> findCustomerCreateDeptList(@Param("query") CustomerQuery query, @Param("ew") LambdaQueryWrapper<SupCustomer> queryWrapper);

    /**
     * 客户管理-客户列表,模糊查询服务人员部门
     * @param query 客户管理-客户(服务人员部门)
     * @param queryWrapper 参数
     * @return 列表中部门数据
     */
    @DataScope(value = DataScopePageType.CRM_CustomerManage,
            userIdAlias = "user_id",
            userTableAlias = "wanderAbout",
            dateAlias = "audit_time",
            dateTableAlias = "customer")
    List<SupCustomer> findCustomerServiceDeptList(@Param("query") CustomerQuery query, @Param("ew") LambdaQueryWrapper<SupCustomer> queryWrapper);

    /**
     * 客户下拉
     * @param queryWrapper 参数
     * @param query 客户管理-客户
     * @return 下拉
     */
    @DataScope(value = DataScopePageType.CRM_CustomerManage,userIdAlias = "user_id")
    List<SupCustomer> pollDown(@Param("query") CustomerQuery query,@Param("ew") LambdaQueryWrapper<SupCustomer> queryWrapper);

    /**
     * 根据权限查询当前角色权限范围内的客户ID
     * @param query 查询条件
     * @return 客户ID
     */
    @DataScope(value = DataScopePageType.CRM_CustomerManage,userIdAlias = "user_id"
            ,userTableAlias = "wanderAbout",dateAlias = "audit_time",dateTableAlias = "customer")
    List<Long> getJudgmentCustomerId(CustomerQuery query);

    /**
     * 根据客户id查询客户信息
     * @param customerId
     * @return
     */
    SupCustomer selectByCustomerId(Long customerId);

    /**
     * 查询每个客户的比稿详情
     * @param query 客户
     * @return 客户比稿结果
     */

    List<ComparedDraftMoneyVO> getCustomerComparedInfoList(@Param("query") CustomerDevoteQuery query);

    /**
     * 根据客户ID查询客户信息
     * @param customerIdList 客户ID
     * @return 客户信息
     */
    List<SupCustomer> listCustomerIdByCustomerIds(@Param("customerIdList") List<Long> customerIdList);

    @DataScope(value = DataScopePageType.CRM_CustomerManage,
            userIdAlias = "user_id",
            userTableAlias = "wanderAbout",
            dateAlias = "audit_time",
            dateTableAlias = "customer")
    List<SupCustomer> userCustomerList(@Param("query") CustomerQuery query,@Param("ew") ShowTableNameLambdaQueryWrapper<SupCustomer> queryWrapper);
}
