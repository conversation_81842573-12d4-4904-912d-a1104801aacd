package com.py.crm.connection.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.py.common.constant.Constants;
import com.py.common.constant.DictConstant;
import com.py.common.constant.ResultCode;
import com.py.common.core.domain.entity.SysDictData;
import com.py.common.core.domain.entity.SysUser;
import com.py.common.core.domain.vo.user.AuthUser;
import com.py.common.datascope.DataScopePageType;
import com.py.common.datascope.DataScopeUtils;
import com.py.common.enums.DelFlag;
import com.py.common.enums.SnapshotBizType;
import com.py.common.enums.flow.FlowApprovalStatus;
import com.py.common.exception.ServiceException;
import com.py.common.mybatisplus.ShowTableNameLambdaQueryWrapper;
import com.py.common.mybatisplus.SuperServiceImpl;
import com.py.common.oss.IOssService;
import com.py.common.tools.modifycomparator.IObjectComparator;
import com.py.common.tools.modifycomparator.model.BaseModifyDiff;
import com.py.common.tools.multisheetexcelexporter.MultiSheetExcelExporter;
import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTask;
import com.py.common.tools.verify.ObjectValidator;
import com.py.common.tools.verify.domain.RowVerifyError;
import com.py.common.utils.*;
import com.py.common.utils.collection.JoinUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.connection.converter.ConnectionConverter;
import com.py.crm.connection.domain.Connection;
import com.py.crm.connection.domain.dto.*;
import com.py.crm.connection.domain.enums.ConnectionStatus;
import com.py.common.enums.EmploymentStatus;
import com.py.crm.connection.domain.query.ConnectionBrandQuery;
import com.py.crm.connection.domain.query.ConnectionImportQuery;
import com.py.crm.connection.domain.query.ConnectionQuery;
import com.py.crm.connection.domain.vo.ConnectionCount;
import com.py.crm.connection.domain.vo.ConnectionListVO;
import com.py.crm.connection.domain.vo.ConnectionVO;
import com.py.crm.connection.mapper.ConnectionMapper;
import com.py.crm.connection.service.IConnectionService;
import com.py.crm.connectionemployment.converter.ConnectionEmploymentConverter;
import com.py.crm.connectionemployment.domain.ConnectionEmployment;
import com.py.crm.connectionemployment.domain.dto.ConnectionEmploymentDTO;
import com.py.crm.connectionemployment.service.IConnectionEmploymentService;
import com.py.crm.connectionwander.domain.ConnectionWander;
import com.py.crm.connectionwander.domain.dto.ConnectionWanderDTO;
import com.py.crm.connectionwander.service.IConnectionWanderService;
import com.py.crm.crmupdaterecord.converter.CrmUpdateRecordConverter;
import com.py.crm.crmupdaterecord.domain.CrmUpdateRecord;
import com.py.crm.crmupdaterecord.domain.dto.CrmUpdateRecordDTO;
import com.py.crm.crmupdaterecord.domain.enums.RecordBizType;
import com.py.crm.crmupdaterecord.domain.enums.RecordMenuType;
import com.py.crm.crmupdaterecord.service.ICrmUpdateRecordService;
import com.py.crm.customer.contact.domain.SupContact;
import com.py.crm.customer.contact.domain.dto.ContactDTO;
import com.py.crm.customer.customercategory.service.ICustomerCategoryService;
import com.py.crm.customer.domain.SupCustomer;
import com.py.crm.customer.service.ICustomerService;
import com.py.flow.api.IApprovalService;
import com.py.flow.domain.dto.flow.ApprovalSubmitDTO;
import com.py.flow.domain.dto.flow.ReApprovalSubmitDTO;
import com.py.flow.domain.enums.ApprovalBizType;
import com.py.framework.config.UidConfig;
import com.py.system.dict.service.ISysDictDataService;
import com.py.system.recyclebin.domain.dto.DeleteAddDTO;
import com.py.system.recyclebin.domain.enums.RecycleBizType;
import com.py.system.recyclebin.service.IRecycleBinService;
import com.py.system.snapshot.domain.Snapshot;
import com.py.system.snapshot.service.ISnapshotService;
import com.py.system.tools.reusableasynctask.domain.ImportTaskResult;
import com.py.system.tools.reusableasynctask.service.ITaskRecodeService;
import com.py.system.user.service.ISysUserService;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 人脉管理表Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Service
public class ConnectionServiceImpl extends SuperServiceImpl<ConnectionMapper, Connection> implements IConnectionService, ReusableAsyncTask<ConnectionImportQuery> {

    /** 人脉管理表模型转换器 */
    @Resource
    private ConnectionConverter connectionConverter;

    /** 人脉管理表Mapper接口 */
    @Resource
    private ConnectionMapper connectionMapper;

    @Resource
    private ObjectValidator objectValidator;

    /** 从业经历表Service接口 */
    @Resource
    private IConnectionEmploymentService connectionEmploymentService;

    /** 从业经历表模型转换器 */
    @Resource
    private ConnectionEmploymentConverter connectionEmploymentConverter;

    @Resource
    private IObjectComparator objectComparator;

    /** 客户/人脉(状态)更新记录表Service接口*/
    @Resource
    private ICrmUpdateRecordService crmUpdateRecordService;

    /** 客户/人脉(状态)更新记录表模型转换器 */
    @Resource
    private CrmUpdateRecordConverter crmUpdateRecordConverter;

    /** 生成序号 */
    @Resource
    private UidConfig uidConfig;

    /** 客户管理-客户与行业类目关联Service接口 */
    @Resource
    private ICustomerCategoryService customerCategoryService;

    /** 客户管理-客户服务 */
    @Resource
    private ICustomerService customerService;

    @Resource
    private ISysUserService userService;

    /** 字典 业务层 */
    @Resource
    private ISysDictDataService sysDictDataService;

    @Resource
    private IRecycleBinService recycleBinService;

    /** 快照服务 */
    @Resource
    private ISnapshotService snapshotService;

    /** 人脉管理-人脉流转Service接口 */
    @Resource
    private IConnectionWanderService connectionWanderService;

    /** 审批服务 */
    @Resource
    private IApprovalService approvalService;

    @Resource
    private IOssService ossService;

    /** 异步任务执行记录服务 */
    @Resource
    private ITaskRecodeService taskRecodeService;

    /** 最大导入条数 */
    public static final Integer MAX_DOWNLOAD_NUM = 1500;

    public ConnectionServiceImpl(
            IRecycleBinService recycleBinService) {
        recycleBinService.registeredRestoreCallback(RecycleBizType.CONNECTION, this::restoreConnection);
        this.recycleBinService = recycleBinService;
    }

    /**
     * 恢复人脉指定主键逻辑删除的记录
     * @param connectionId 主键ID
     * @return 是否成功
     */
    private boolean restoreConnection(Long connectionId) {
        Connection connection = baseMapper.getByConnectionId(connectionId);
        if(Objects.isNull(connection)) {
            throw new ServiceException("恢复失败");
        }
        long count = this.connectionMapper.getCount(connection.getPhone(), connection.getConnectionId());
        if (count > 0) {
            throw new ServiceException("该手机号已存在");
        }

        boolean restore = restore(connectionId);
        if(!restore){
            throw new ServiceException("恢复失败");
        }
        return true;
    }

    /**
     * 查询人脉管理表列表
     *
     * @param query 人脉管理表
     * @return 人脉管理表
     */
    @Override
    public List<ConnectionListVO> listConnection(ConnectionQuery query) {
        List<Connection> connectionList = this.list(Wrappers.<Connection>lambdaQuery()
                .eq(Connection::getAuditStatus,FlowApprovalStatus.APPROVE.getValue())
                .like(StringUtils.isNotEmpty(query.getConnectionName()),Connection::getConnectionName,query.getConnectionName()));
        return this.connectionConverter.toListVoByEntity(connectionList);
    }

    /**
     * 获取有效、无效的人脉数量
     * @return 人脉管理表列表数量
     */
    @Override
    public ConnectionCount connectionCount() {

        ConnectionQuery query = new ConnectionQuery();
        //有效、无效查询条件拼接
        LambdaQueryWrapper<Connection> queryInvalidWrapper = Wrappers.lambdaQuery(Connection.class)
                .eq(Connection::getAuditStatus, FlowApprovalStatus.APPROVE.getValue())
                .eq(Connection::getStatus, ConnectionStatus.INVALID.getValue());
        LambdaQueryWrapper<Connection> queryValidWrapper = Wrappers.lambdaQuery(Connection.class)
                .eq(Connection::getAuditStatus, FlowApprovalStatus.APPROVE.getValue())
                .eq(Connection::getStatus, ConnectionStatus.VALID.getValue());

        ConnectionCount connectionCount = new ConnectionCount();
        //获取有效数量
        long invalidCount = connectionMapper.countConnection(query, queryInvalidWrapper);
        connectionCount.setInvalidCount(invalidCount);
        //获取无效数量
        long validCount = connectionMapper.countConnection(query, queryValidWrapper);
        connectionCount.setValidCount(validCount);
        return connectionCount;
    }

    /**
     * 分页人脉管理表列表
     *
     * @param query 人脉管理表
     * @return 人脉管理表分页
     */
    @Override
    public PageInfo<ConnectionListVO> pageConnectionList(ConnectionQuery query) {
        //封装人脉管理查询条件
        ShowTableNameLambdaQueryWrapper<Connection> wrapper = setConnectionWrapper(query);
        SqlHelper.filterUpdateInfoTableAlias(wrapper, query,"py_crm_connection");
        SqlHelper.filterCreateListInfo(wrapper, query,"py_crm_connection");
        SqlHelper.filterCreateDeptListInfo(wrapper, query,"py_crm_connection");

        //现任行业类目
        SqlHelper.mathInSet(wrapper,Connection::getIndustryCategory, query.getIndustryCategoryList());

        //历任条件查询从业经历
        LambdaQueryWrapper<ConnectionEmployment> employmentWrapper = setConnectionEmploymentWrapper(query);
        SqlHelper.mathInSet(employmentWrapper, ConnectionEmployment::getIndustryCategory, query.getHistoryIndustryCategoryList());
        employmentWrapper.setParamAlias(ConnectionMapper.CONNECTION_EMPLOYMENT_WRAPPER_ALIAS);


        //流转的数据权限拼接
        PageUtils.startPage();
        List<Connection> connectionList = this.baseMapper.selectConnection(query, wrapper, employmentWrapper);
        if(ListUtil.isEmpty(connectionList)) {
            return ListUtil.emptyPage();
        }
        List<ConnectionListVO> connectionVoList = this.connectionConverter.toListVoByEntity(connectionList);
        this.userService.relatedUpdateInfo(connectionVoList);
        List<SysDictData> sysDictDataList = this.sysDictDataService.selectAllByType(DictConstant.PY_TRUST);
        Map<String, String> pyTrustMap = ListUtil.toMap(sysDictDataList, SysDictData::getDictValue, SysDictData::getDictLabel);
        connectionVoList.forEach(connection-> {
            if (Objects.nonNull(connection.getPyTrustLevel())){
                connection.setPyTrustLevelStr(pyTrustMap.get(connection.getPyTrustLevel()));
            }
            if (CollectionUtils.isNotEmpty(connection.getIndustryCategory())){
                List<String> industryCategory = sysDictDataService.selectByDictLabelList(DictConstant.INDUSTRY_CATEGORY, connection.getIndustryCategory());
                connection.setIndustryCategoryStr(String.join(",", industryCategory));
            }
            connection.setIsQuery(true);
            //负责品牌/业务线转换
            if(ListUtil.isNotEmpty(connection.getResponsibleBrand())) {
                connection.setResponsibleBrandStr(String.join(",", connection.getResponsibleBrand()));
            }
        });

        //分配人员的id和名和部门(代码待优化)
        List<Long> connectionIds = ListUtil.map(connectionVoList, ConnectionListVO::getConnectionId);
        List<ConnectionWander> connectionWanderList = connectionWanderService.listWanderByConnectionIds(connectionIds);
        if(ListUtil.isNotEmpty(connectionWanderList)) {
            JoinUtils.oneByMany(connectionVoList, connectionWanderList
                    , ConnectionListVO::getConnectionId,ConnectionWander::getConnectionId
                    , (vo, connectionWander) -> {
                        List<Long> serviceUserIdList = ListUtil.distinctMap(connectionWander, ConnectionWander::getServiceUserId);
                        vo.setServiceUserIdList(serviceUserIdList);
                    });
            List<Long> userIdList = ListUtil.distinctMap(connectionWanderList, ConnectionWander::getServiceUserId);
            List<AuthUser> authUsers = userService.selectUserById(userIdList);
            JoinUtils.manyByMany(connectionVoList, authUsers
                    ,ConnectionListVO::getServiceUserIdList, AuthUser::getUserId
                    , (vo, userList) -> {
                        String content = customerService.getServiceUserName(userList, "、");
                        vo.setServiceUserName(content);
                    });
        }

        return ListUtil.pageConvert(connectionList, connectionVoList);
    }

    /**
     * 人脉管理表列表,模糊查询录入人部门
     *
     * @param query 人脉管理表查询参数(录入人部门)
     * @return 人脉管理表列表-录入人部门
     */
    @Override
    public List<String> findConnectionCreateDeptList(ConnectionQuery query) {
        //封装人脉管理查询条件
        ShowTableNameLambdaQueryWrapper<Connection> wrapper = setConnectionWrapper(query);
        wrapper.isNotNull(Connection::getCreateDept);

        //流转的数据权限拼接
        List<Connection> connectionList = this.baseMapper.findConnectionCreateDeptList(query, wrapper);
        if(ListUtil.isEmpty(connectionList)) {
            return new ArrayList<>();
        }
        return connectionList.stream()
                .map(connection -> Arrays.asList(connection.getCreateDept().split(",")))
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 人脉管理表列表,模糊查询录服务人员部门
     *
     * @param query 人脉管理表查询参数(服务人员部门)
     * @return 人脉管理表列表-服务人员部门
     */
    @Override
    public List<String> findConnectionServiceUserDeptList(ConnectionQuery query) {
        //封装人脉管理查询条件
        ShowTableNameLambdaQueryWrapper<Connection> wrapper = setConnectionWrapper(query);

        //流转的数据权限拼接
        List<Connection> connectionList = this.baseMapper.findConnectionServiceUserDeptList(query, wrapper);
        if(ListUtil.isEmpty(connectionList)) {
            return new ArrayList<>();
        }
        return connectionList.stream()
                .map(connection -> Arrays.asList(connection.getCreateDept().split(",")))
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 判断权限
     * @param connectionVoList 人脉列表
     */
    private void judgmentPerm(List<ConnectionListVO> connectionVoList) {
        if(SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            connectionVoList.forEach(customerListVO -> customerListVO.setIsQuery(true));
            return;
        }
        List<Long> connectionIdList = getJudgmentConnectionId(null);
        connectionVoList.forEach(customerListVO -> customerListVO.setIsQuery(connectionIdList.contains(customerListVO.getConnectionId())));
    }

    /***
     * 获取有操作权限的客户id
     * @return 客户id
     */
    private List<Long> getJudgmentConnectionId(Long connectionId) {
        LambdaQueryWrapper<Connection> wrapper = Wrappers.lambdaQuery(Connection.class)
                .select(Connection::getConnectionId)
                .eq(connectionId != null, Connection::getConnectionId, connectionId);
        DataScopeUtils.setDateScope(wrapper, DataScopePageType.CRM_ConnectionManage, context -> context.setUserIdAlias("ifNull(service_user_id, create_id)"));
        return SqlHelper.selectIdList(this, wrapper);
    }

    /**
     * 判断当前操作是否有权限
     * @param connectionId 客户id
     */
    private void operationPerm(Long connectionId) {
        if(SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            return;
        }
        List<Long> connectionIdList = getJudgmentConnectionId(connectionId);
        if(!connectionIdList.contains(connectionId)) {
            throw new ServiceException("当前操作无权限");
        }
    }

    /**
     * 封装历任条件查询从业经历
     * @param query 查询条件
     * @return Wrapper
     */
    private LambdaQueryWrapper<ConnectionEmployment> setConnectionEmploymentWrapper(ConnectionQuery query) {
        return Wrappers.lambdaQuery(ConnectionEmployment.class)
                .like(StringUtils.isNotEmpty(query.getHistoryDepartmentName()), ConnectionEmployment::getDepartmentName, query.getHistoryDepartmentName())
                .like(StringUtils.isNotEmpty(query.getHistoryPostName()), ConnectionEmployment::getPostName, query.getHistoryPostName())
                .like(StringUtils.isNotEmpty(query.getHistoryResponsibleBrand()), ConnectionEmployment::getResponsibleBrand, query.getHistoryResponsibleBrand())
                .like(StringUtils.isNotEmpty(query.getHistoryCurrentEmployer()), ConnectionEmployment::getCurrentEmployer, query.getHistoryCurrentEmployer())
                ;
    }

    /**
     * 封装人脉管理查询条件
     * @param query  人脉管理表查询对象
     * @return wrapper
     */
    private ShowTableNameLambdaQueryWrapper<Connection> setConnectionWrapper(ConnectionQuery query) {
        ShowTableNameLambdaQueryWrapper<Connection> wrapper = new ShowTableNameLambdaQueryWrapper<>(Connection.class);
        wrapper.like(StringUtils.isNotEmpty(query.getConnectionName()), Connection::getConnectionName, query.getConnectionName())
                .like(StringUtils.isNotEmpty(query.getPhone()), Connection::getPhone, query.getPhone())
                .eq(Connection::getStatus, query.getStatus())
                .eq(Connection::getAuditStatus, FlowApprovalStatus.APPROVE.getValue())
                .in(CollectionUtils.isNotEmpty(query.getCreateIdList()),Connection::getCreateId,query.getCreateIdList())
                // 创建人查询
                .in(CollectionUtils.isNotEmpty(query.getCreateUserList()),Connection::getCreateBy,query.getCreateUserList())
                .in(CollectionUtils.isNotEmpty(query.getUpdateUser()),Connection::getUpdateId,query.getUpdateIdList())
                .like(StringUtils.isNotEmpty(query.getCurrentEmployer()), Connection::getCurrentEmployer, query.getCurrentEmployer())
                .like(StringUtils.isNotEmpty(query.getResponsibleBrand()), Connection::getResponsibleBrand, query.getResponsibleBrand())
                .like(StringUtils.isNotEmpty(query.getDepartmentName()), Connection::getDepartmentName, query.getDepartmentName())
                .like(StringUtils.isNotEmpty(query.getPostName()), Connection::getPostName, query.getPostName())
                .in(CollectionUtils.isNotEmpty(query.getPyTrustLevelList()), Connection::getPyTrustLevel, query.getPyTrustLevelList())
                .in(CollectionUtils.isNotEmpty(query.getConnectionIdList()), Connection::getConnectionId, query.getConnectionIdList());
        return wrapper;
    }

    /**
     * 查询人脉管理表
     *
     * @param id 人脉管理表主键
     * @return 人脉管理表视图模型
     */
    @Override
    public ConnectionVO selectConnectionById(Long id) {
        Connection connection = baseMapper.getByConnectionId(id);
        ConnectionVO connectionVO = this.connectionConverter.toVoByEntity(connection);
        if (Objects.nonNull(connectionVO.getPyTrustLevel())){
            String pyTrustStr = this.sysDictDataService.selectDictLabel(DictConstant.PY_TRUST,connectionVO.getPyTrustLevel());
            connectionVO.setPyTrustLevelStr(pyTrustStr);
        }
        if (CollectionUtils.isNotEmpty(connectionVO.getIndustryCategory())){
            List<String> industryCategory = sysDictDataService.selectByDictLabelList(DictConstant.INDUSTRY_CATEGORY, connectionVO.getIndustryCategory());
            connectionVO.setIndustryCategoryStr(String.join(",", industryCategory));
        }
        //负责品牌/业务线转换
        if(ListUtil.isNotEmpty(connectionVO.getResponsibleBrand())) {
            connectionVO.setResponsibleBrandStr(String.join(",", connectionVO.getResponsibleBrand()));
        }
        return connectionVO;
    }

    /**
     * 新增人脉管理表
     *
     * @param dto 人脉管理表修改参数
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = {ServiceException.class, Exception.class})
    public Long insertConnection(ConnectionDTO dto) {
        long count = this.connectionMapper.getCount(dto.getPhone(), dto.getConnectionId());
        if (count > 0) {
            throw new ServiceException("该电话已存在！");
        }

        dto.setStatus(ConnectionStatus.VALID.getValue());
        Connection connection = this.connectionConverter.toEntityByDto(dto);
        connection.setAuditStatus(FlowApprovalStatus.WAIT_APPROVAL.getValue());
        boolean flag;
        LocalDateTime nowTime = LocalDateTime.now();
        connection.setInitiationTime(nowTime);

        //根据企业名称和负责品牌业务线获取企业id
        Long customerId = customerService.getCustomerId(dto.getCurrentEmployer(), dto.getResponsibleBrand());
        connection.setCustomerId(customerId);
        if(Objects.isNull(dto.getConnectionId())) {
            //新增人脉
            flag = this.save(connection);
            dto.setConnectionId(connection.getConnectionId());
        } else {
            // 审批驳回后的修改
            connection.setCreateTime(nowTime);
            flag = this.updateById(connection);
            //如果是重新发起审批，需要删除旧的从业经历和更新记录
            crmUpdateRecordService.removeUpdateRecordByBizId(connection.getConnectionId(), RecordBizType.CONNECTIONS.getValue());
            connectionEmploymentService.removeByConnectionId(connection.getConnectionId());
        }
        ConnectionVO connectionVO = this.connectionConverter.toVoByEntity(connection);
        //拼接更新记录
        List<CrmUpdateRecordDTO> crmUpdateRecordDTOList = montageCrmUpdateRecord(connectionVO, dto.getIndustryCategory(), connection.getConnectionId());

        if (CollectionUtils.isNotEmpty(dto.getConnectionEmploymentDTOS())) {
            //从业经历
            List<ConnectionEmployment> list = this.connectionEmploymentConverter.toEntityByDto(dto.getConnectionEmploymentDTOS());
            int sort = 0;
            for (ConnectionEmployment connectionEmployment : list) {
                connectionEmployment.setConnectionId(connection.getConnectionId());
                connectionEmployment.setSort(++sort);
                connectionEmployment.setId(null);
            }
            flag =flag && connectionEmploymentService.saveBatch(list);
            setConnectionEmploymentRecord(list,dto.getConnectionId(),crmUpdateRecordDTOList);
        }
        if (CollectionUtils.isNotEmpty(crmUpdateRecordDTOList)){
            List<CrmUpdateRecord> crmUpdateRecords = crmUpdateRecordConverter.toEntityByDto(crmUpdateRecordDTOList);
            flag =flag && crmUpdateRecordService.saveBatch(crmUpdateRecords);
        }

        if (!flag) {
            throw new ServiceException("新增人脉失败");
        }
        // 审核接口
        if(dto.getLastFlowInstanceId() == null) {
            ApprovalSubmitDTO approvalSubmitDTO = new ApprovalSubmitDTO();
            approvalSubmitDTO.setBizId(connection.getConnectionId());
            approvalSubmitDTO.setBizType(ApprovalBizType.InsertConnection);
            approvalService.submitFlow(approvalSubmitDTO);
        } else {
            ReApprovalSubmitDTO reApprovalSubmitDTO = new ReApprovalSubmitDTO();
            reApprovalSubmitDTO.setBizId(connection.getConnectionId());
            reApprovalSubmitDTO.setBizType(ApprovalBizType.InsertConnection);
            reApprovalSubmitDTO.setLastFlowInstanceId(dto.getLastFlowInstanceId());
            //再次提交流程
            approvalService.reSubmitFlow(reApprovalSubmitDTO);
        }
        return connection.getConnectionId();
    }

    /**
     * 拼接更新记录
     * @param connectionVO 人脉管理表视图模型
     * @param industryCategory 行业ID
     * @param connectionId 人脉id
     * @return 客户/人脉(状态)更新记录表数据传输模型
     */
    private List<CrmUpdateRecordDTO> montageCrmUpdateRecord(ConnectionVO connectionVO, List<Long> industryCategory, Long connectionId) {
        List<CrmUpdateRecordDTO> resultList = new ArrayList<>();

        List<BaseModifyDiff<?>> baseModifyDiffs = objectComparator.compareObject(null, connectionVO, ConnectionVO.class);
        if(ListUtil.isEmpty(baseModifyDiffs)) {
            return resultList;
        }
        baseModifyDiffs.forEach(s -> {
            CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO().valueOf(s);
            crmUpdateRecordDTO.setBizId(connectionId);
            if (DictConstant.INDUSTRY_CATEGORY_FIELD.equals(ListUtil.firstOrThrow(s.getFieldPath()))){
                List<String> afterList = sysDictDataService.selectByDictLabelList(DictConstant.INDUSTRY_CATEGORY, industryCategory);
                String after = ListUtil.isNotEmpty(afterList) ? StringUtils.convertStringList(afterList) : Constants.DEFAULT_CONNECTOR;
                String content = "基本信息-【" + ListUtil.firstOrThrow(s.getFieldPath())+ "】新增“" + after + "”";
                crmUpdateRecordDTO.setContent(content);
            } else  if (DictConstant.PY_TRUST_LEVEL.equals(ListUtil.firstOrThrow(s.getFieldPath()))) {
                //对派芽信任度
                String after = Objects.nonNull(s.getAfter()) ? sysDictDataService.selectDictLabel(DictConstant.PY_TRUST, s.getAfter().toString()) : "";
                after = StringUtils.isNotEmpty(after) ?after: Constants.DEFAULT_CONNECTOR;
                String content = "基本信息-【" + ListUtil.firstOrThrow(s.getFieldPath())+ "】新增“" + after + "”";
                crmUpdateRecordDTO.setContent(content);
            } else {
                String after = Objects.nonNull(s.getAfter()) ? s.getAfter().toString() : Constants.DEFAULT_CONNECTOR;
                crmUpdateRecordDTO.setContent("基本信息-【" + ListUtil.firstOrThrow(s.getFieldPath()) + "】新增“" + after + "”");
            }
            resultList.add(crmUpdateRecordDTO);
        });

        return resultList;
    }

    /**
     * 修改人脉管理表
     *
     * @param dto 人脉管理表修改参数
     * @param needRights 是否需要权限校验
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = {ServiceException.class, Exception.class})
    public boolean updateConnection(ConnectionDTO dto, Boolean needRights) {
        long count = this.connectionMapper.getCount(dto.getPhone(), dto.getConnectionId());
        if (count > 0) {
            throw new ServiceException("该手机号已存在");
        }
        //根据企业名称和负责品牌业务线获取企业id
        Long customerId = customerService.getCustomerId(dto.getCurrentEmployer(), dto.getResponsibleBrand());
        dto.setCustomerId(customerId);

        //获取旧的人脉数据
        Connection oldConnection = this.baseMapper.getByConnectionId(dto.getConnectionId());
        Connection connection = this.connectionConverter.toEntityByDto(dto);
        List<ConnectionEmploymentDTO> connectionEmploymentDTOList = dto.getConnectionEmploymentDTOS();
        //查询最新的从业经历sort
        ConnectionEmployment connectionEmploymentLast = connectionEmploymentService.getLastOne(dto.getConnectionId());
        int sort = 0;
        if (Objects.nonNull(connectionEmploymentLast)) {
            sort = connectionEmploymentLast.getSort();
        }
        List<CrmUpdateRecordDTO> crmUpdateRecordDTOList = new ArrayList<>();
        List<ConnectionEmployment> addConnectionEmployments = new ArrayList<>();
        //基本信息修改添加从业经历
        addConnectionEmploymentsByConnection(oldConnection, connection, crmUpdateRecordDTOList, sort, addConnectionEmployments);
        //如果新增了一条从业经历，则需sort+1
        if(ListUtil.isNotEmpty(addConnectionEmployments)) {
            sort += 1;
        }
        //所有从业经历
        List<ConnectionEmployment> connectionEmployments = connectionEmploymentService.list(Wrappers.<ConnectionEmployment>lambdaQuery()
                .eq(ConnectionEmployment::getConnectionId, dto.getConnectionId()));
        // 从业经历更新内容记录更新记录
        if (CollectionUtils.isNotEmpty(connectionEmploymentDTOList)) {
            for (ConnectionEmploymentDTO connectionEmploymentDTO : connectionEmploymentDTOList) {
                if (Objects.isNull(connectionEmploymentDTO.getSort())){
                    connectionEmploymentDTO.setSort(++sort);
                }
            }
            List<ConnectionEmployment> connectionEmploymentList = this.connectionEmploymentConverter.toEntityByDto(connectionEmploymentDTOList);
            connectionEmploymentList.forEach(s->s.setConnectionId(connection.getConnectionId()));
            //新增的
            addConnectionEmployments.addAll(connectionEmploymentList.stream()
                    .filter(s->Objects.isNull(s.getEmploymentId())).collect(Collectors.toList()));
             //修改的
            List<ConnectionEmployment> updateConnectionList = connectionEmploymentList.stream()
                    .filter(s -> Objects.nonNull(s.getEmploymentId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(updateConnectionList)){
                recordUpdateConnectionEmployment(updateConnectionList,connectionEmployments,dto,crmUpdateRecordDTOList);

            }else if (CollectionUtils.isNotEmpty(connectionEmployments)){
                //从业经历删除记录
                recordConnEmploymentRemove(connectionEmployments,crmUpdateRecordDTOList,dto);
                connectionEmploymentService.remove(
                        Wrappers.<ConnectionEmployment>lambdaQuery()
                                .eq(ConnectionEmployment::getConnectionId,connection.getConnectionId()));
            }
        } else {
            recordConnEmploymentRemove(connectionEmployments,crmUpdateRecordDTOList,dto);
            connectionEmploymentService.remove(
                    Wrappers.<ConnectionEmployment>lambdaQuery()
                            .eq(ConnectionEmployment::getConnectionId,connection.getConnectionId()));
        }
        recordAddConnectionEmployment(addConnectionEmployments,dto,crmUpdateRecordDTOList);
        List<CrmUpdateRecord> crmUpdateRecords = crmUpdateRecordConverter.toEntityByDto(crmUpdateRecordDTOList);
        //基本信息-从业经历更新记录
        crmUpdateRecordService.saveBatch(crmUpdateRecords);
        Connection updateConnection = this.connectionConverter.toEntityByDto(dto);
        return this.updateById(updateConnection);
    }

    /**
     * 删除从业经历
     * @param connectionEmployments 从业经历表对象
     * @param crmUpdateRecordDTOList 客户/人脉(状态)更新记录表数据传输模型
     * @param dto 人脉管理表数据传输模型
     */
    private void recordConnEmploymentRemove(List<ConnectionEmployment> connectionEmployments, List<CrmUpdateRecordDTO> crmUpdateRecordDTOList, ConnectionDTO dto) {
        for (ConnectionEmployment connectionEmployment : connectionEmployments) {
            List<BaseModifyDiff<?>> baseModifyDiffsAdd = objectComparator.compareObject(connectionEmployment, null, ConnectionEmployment.class);
            baseModifyDiffsAdd.forEach(s -> {
                CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                if (Objects.nonNull(s.getBefore())) {
                    //行业类目
                    if (DictConstant.INDUSTRY_CATEGORY_FIELD.equals(ListUtil.firstOrThrow(s.getFieldPath()))){
                        List<String> beforeList = StringUtils.isNotEmpty(s.getBefore().toString()) ? Arrays.asList(s.getBefore().toString().split(",")) : null;
                        List<String> afterStrList = sysDictDataService.selectByDictLabels(DictConstant.INDUSTRY_CATEGORY, beforeList);
                        String before = ListUtil.isNotEmpty(beforeList) ? StringUtils.convertStringList(afterStrList) : Constants.DEFAULT_CONNECTOR;
                        crmUpdateRecordDTO.setContent("从业经历-删除ID:" + connectionEmployment.getSort() + "【" + s.getFieldPath().get(0) + "】“" + before + "”");
                    }else{
                        crmUpdateRecordDTO.setContent("从业经历-删除ID：" + connectionEmployment.getSort() + "【" + s.getFieldPath().get(0) + "】“" + s.getBefore() + "”");

                    }

                     crmUpdateRecordDTO.setBizType(RecordBizType.CONNECTIONS.getValue());
                    crmUpdateRecordDTO.setMenuType(RecordMenuType.UPDATE_RECORD.getValue());
                    crmUpdateRecordDTO.setBizId(dto.getConnectionId());
                    crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
                }
            });
        }
    }

    /**
     * 人脉管理修改添加从业经历
     * @param oldConnection 老人脉管理表对象
     * @param connection 人脉管理表对象
     * @param crmUpdateRecordDTOList 客户/人脉(状态)更新记录表数据传输模型
     * @param sort 排序
     * @param addConnectionEmployments 从业经历表对象
     */
    private void addConnectionEmploymentsByConnection(Connection oldConnection, Connection connection,
                                                      List<CrmUpdateRecordDTO> crmUpdateRecordDTOList, int sort,
                                                      List<ConnectionEmployment> addConnectionEmployments) {
        //所有的更新记录
        List<String> updateRecords = new ArrayList<>();
        List<BaseModifyDiff<?>> baseModifyDiffs = objectComparator.compareObject(oldConnection, connection, Connection.class);
        if (CollectionUtils.isNotEmpty(baseModifyDiffs)) {
            baseModifyDiffs.forEach(s -> {
                if (DictConstant.INDUSTRY_CATEGORY_FIELD.equals(ListUtil.firstOrThrow(s.getFieldPath()))){
                    CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO().valueOf(s);
                    crmUpdateRecordDTO.setBizId(oldConnection.getConnectionId());
                    List<String> beforeList = StringUtils.isNotEmpty(s.getBefore().toString())?Arrays.asList(s.getBefore().toString().split(",")):null;
                    List<String> afterList = StringUtils.isNotEmpty(s.getAfter().toString())?Arrays.asList(s.getAfter().toString().split(",")):null;
                    List<String> beforeStrList = sysDictDataService.selectByDictLabels(DictConstant.INDUSTRY_CATEGORY,beforeList);
                    List<String> afterStrList = sysDictDataService.selectByDictLabels(DictConstant.INDUSTRY_CATEGORY, afterList);
                    String before = ListUtil.isNotEmpty(beforeList) ? StringUtils.convertStringList(beforeStrList) : Constants.DEFAULT_CONNECTOR;
                    String after = ListUtil.isNotEmpty(afterList) ? StringUtils.convertStringList(afterStrList) : Constants.DEFAULT_CONNECTOR;
                    if(!StringUtils.equals(Constants.DEFAULT_CONNECTOR, before) || !StringUtils.equals(Constants.DEFAULT_CONNECTOR, after)) {
                        String content = "基本信息-【" + ListUtil.firstOrThrow(s.getFieldPath()) + "】从“" + before + "”更新为“" + after + "”";
                        crmUpdateRecordDTO.setContent(content);
                    }
                    updateRecords.add(crmUpdateRecordDTO.getContent());
                    crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
                }
            });
            //从业经历
            ConnectionEmployment connectionEmployment = this.connectionEmploymentConverter.toEntityByConnection(oldConnection);
            connectionEmployment.setId(null);
            connectionEmployment.setSort(++sort);
            connectionEmployment.setUpdateTime(LocalDateTime.now());
            if(!(Objects.isNull(connectionEmployment.getCustomerId()) && StringUtils.isBlank(connectionEmployment.getCurrentEmployer())
                    && ListUtil.isEmpty(connectionEmployment.getResponsibleBrand()) && ListUtil.isEmpty(connectionEmployment.getIndustryCategory())
                    && StringUtils.isBlank(connectionEmployment.getDepartmentName()) && StringUtils.isBlank(connectionEmployment.getPostName()))) {
                addConnectionEmployments.add(connectionEmployment);
            }
        }

        ConnectionVO oldConnectionVo = this.connectionConverter.toVoByEntity(oldConnection);
        ConnectionVO newConnectionVo = this.connectionConverter.toVoByEntity(connection);
        List<BaseModifyDiff<?>> baseModifyDiffList = objectComparator.compareObject(oldConnectionVo, newConnectionVo, ConnectionVO.class);
        if (CollectionUtils.isNotEmpty(baseModifyDiffList)) {
            for(BaseModifyDiff s : baseModifyDiffList) {
                if (DictConstant.INDUSTRY_CATEGORY_FIELD.equals(ListUtil.firstOrThrow(s.getFieldPath()))){
                    continue;
                }
                CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO().valueOf(s);
                crmUpdateRecordDTO.setBizId(oldConnectionVo.getConnectionId());
                //对派芽信任度
                if (DictConstant.PY_TRUST_LEVEL.equals(ListUtil.firstOrThrow(s.getFieldPath()))){
                    String before = Objects.nonNull(s.getBefore()) ? sysDictDataService.selectDictLabel(DictConstant.PY_TRUST, s.getBefore().toString()) : "";
                    String after = Objects.nonNull(s.getAfter()) ?  sysDictDataService.selectDictLabel(DictConstant.PY_TRUST, s.getAfter().toString()) : "";
                     before =  StringUtils.isNotEmpty(before) ?before: Constants.DEFAULT_CONNECTOR;
                     after =  StringUtils.isNotEmpty(after) ?after: Constants.DEFAULT_CONNECTOR;
                    String content = "基本信息-【" + ListUtil.firstOrThrow(s.getFieldPath())+ "】从“" + before + "”更新为“" + after + "”";
                    crmUpdateRecordDTO.setContent(content);
                }
                //防止重复添加
                if(!updateRecords.contains(crmUpdateRecordDTO.getContent())) {
                    crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
                    updateRecords.add(crmUpdateRecordDTO.getContent());
                }
            }
        }
    }

    /**
     * 记录新增的从业经历记录
     * @param addConnectionEmployments 从业经历表对象
     * @param dto 人脉管理表数据传输模型
     * @param crmUpdateRecordDTOList 客户/人脉(状态)更新记录表数据传输模型
     */
    private void recordAddConnectionEmployment(List<ConnectionEmployment> addConnectionEmployments, ConnectionDTO dto, List<CrmUpdateRecordDTO> crmUpdateRecordDTOList) {
        if (CollectionUtils.isEmpty(addConnectionEmployments)) {
            return;
        }
        //批量新增从业经历
        connectionEmploymentService.saveBatch(addConnectionEmployments);
        setConnectionEmploymentRecord(addConnectionEmployments, dto.getConnectionId(), crmUpdateRecordDTOList);

    }

    /**
     * 增加从业经历的新增字段变更
     * @param addConnectionEmployments
     * @param connectionId
     * @param crmUpdateRecordDTOList
     */
    private void setConnectionEmploymentRecord(List<ConnectionEmployment> addConnectionEmployments, Long connectionId, List<CrmUpdateRecordDTO> crmUpdateRecordDTOList) {
        for (ConnectionEmployment addConnectionEmployment : addConnectionEmployments) {
            //比较对象
            List<BaseModifyDiff<?>> baseModifyDiffsAdd = objectComparator.compareObject(null, addConnectionEmployment, ConnectionEmployment.class);
            baseModifyDiffsAdd.forEach(s -> {
                CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                String after = Objects.nonNull(s.getAfter()) ? s.getAfter().toString() : Constants.DEFAULT_CONNECTOR;
                if (Objects.nonNull(s.getAfter())) {
                    if (DictConstant.INDUSTRY_CATEGORY_FIELD.equals(ListUtil.firstOrThrow(s.getFieldPath()))){
                        List<String> afterList = StringUtils.isNotEmpty(s.getAfter().toString())?Arrays.asList(s.getAfter().toString().split(",")):null;
                        List<String> afterStrList = sysDictDataService.selectByDictLabels(DictConstant.INDUSTRY_CATEGORY, afterList);
                        after = ListUtil.isNotEmpty(afterList) ? StringUtils.convertStringList(afterStrList) : Constants.DEFAULT_CONNECTOR;
                        crmUpdateRecordDTO.setContent("从业经历-新增ID:" + addConnectionEmployment.getSort() + "【" + s.getFieldPath().get(0) + "】“" + after + "”");
                    }else{
                        crmUpdateRecordDTO.setContent("从业经历-新增ID:" + addConnectionEmployment.getSort() + "【" + s.getFieldPath().get(0) + "】“" + after + "”");
                    }
                    crmUpdateRecordDTO.setBizType(RecordBizType.CONNECTIONS.getValue());
                    crmUpdateRecordDTO.setMenuType(RecordMenuType.UPDATE_RECORD.getValue());
                    crmUpdateRecordDTO.setBizId(connectionId);
                    crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
                }
            });
        }
    }

    /**
     * 记录修改从业经历记录
     * @param updateConnectionList 从业经历表对象
     * @param connectionEmployments 从业经历表对象
     * @param dto 人脉管理表数据传输模型
     * @param crmUpdateRecordDTOList 客户/人脉(状态)更新记录表数据传输模型
     */
    private void recordUpdateConnectionEmployment(List<ConnectionEmployment> updateConnectionList,
                                                  List<ConnectionEmployment> connectionEmployments,
                                                  ConnectionDTO dto,
                                                  List<CrmUpdateRecordDTO> crmUpdateRecordDTOList) {
        List<ConnectionEmployment> updateNeedList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(connectionEmployments)) {
            List<Long> connectionEmpIds = updateConnectionList.stream().map(ConnectionEmployment::getEmploymentId).collect(Collectors.toList());
            connectionEmploymentService.remove(Wrappers.<ConnectionEmployment>lambdaQuery()
                    .eq(ConnectionEmployment::getConnectionId,dto.getConnectionId())
                    .notIn(ConnectionEmployment::getEmploymentId, connectionEmpIds));
            List<ConnectionEmployment> collect = connectionEmployments.stream().filter(s -> !connectionEmpIds.contains(s.getEmploymentId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                for (ConnectionEmployment connectionEmployment : collect) {
                    List<BaseModifyDiff<?>> baseModifyDiffsAdd = objectComparator.compareObject(connectionEmployment, null, ConnectionEmployment.class);
                    baseModifyDiffsAdd.forEach(s -> {
                        CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                        if (Objects.nonNull(s.getBefore())){
                            crmUpdateRecordDTO.setContent("从业经历-删除ID：" + connectionEmployment.getSort()+"【" + s.getFieldPath().get(0) + "】“" + s.getBefore() + "”");
                            crmUpdateRecordDTO.setBizType(RecordBizType.CONNECTIONS.getValue());
                            crmUpdateRecordDTO.setMenuType(RecordMenuType.UPDATE_RECORD.getValue());
                            crmUpdateRecordDTO.setBizId(dto.getConnectionId());
                            if (DictConstant.INDUSTRY_CATEGORY_FIELD.equals(ListUtil.firstOrThrow(s.getFieldPath()))){
                                List<String> afterStrList = sysDictDataService.selectByDictLabelList(DictConstant.INDUSTRY_CATEGORY, connectionEmployment.getIndustryCategory());
                                 String after = ListUtil.isNotEmpty(afterStrList) ? StringUtils.convertStringList(afterStrList) : Constants.DEFAULT_CONNECTOR;
                                crmUpdateRecordDTO.setContent("从业经历-删除ID：" + connectionEmployment.getSort()+"【" + s.getFieldPath().get(0) + "】“" + after + "”");
                            }
                            crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
                        }
                    });
                }
            }
        }
        Map<Integer,ConnectionEmployment> connectionEmploymentMap = ListUtil.toMap(connectionEmployments,ConnectionEmployment::getSort);
        for (ConnectionEmployment updateEmployment : updateConnectionList) {
            ConnectionEmployment old = connectionEmploymentMap.get(updateEmployment.getSort());
            List<BaseModifyDiff<?>> baseModifyDiffsAdd = objectComparator.compareObject(Objects.nonNull(old)?old:null, updateEmployment, ConnectionEmployment.class);
            baseModifyDiffsAdd.forEach(s -> {
                CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                String before = Constants.DEFAULT_CONNECTOR;
                if(Objects.nonNull(s.getBefore())) {
                    if(StringUtils.isEmpty(s.getBefore().toString())) {
                        before = Constants.DEFAULT_CONNECTOR;
                    } else {
                        before = s.getBefore().toString();
                    }
                }
                String after = Constants.DEFAULT_CONNECTOR;
                if(Objects.nonNull(s.getAfter())) {
                    if(StringUtils.isEmpty(s.getAfter().toString())) {
                        after = Constants.DEFAULT_CONNECTOR;
                    } else {
                        after = s.getAfter().toString();
                    }
                }
                if (DictConstant.INDUSTRY_CATEGORY_FIELD.equals(ListUtil.firstOrThrow(s.getFieldPath()))) {
                    List<String> beforeList = StringUtils.isNotEmpty(s.getBefore().toString()) ? Arrays.asList(s.getBefore().toString().split(",")) : null;
                    List<String> afterList = StringUtils.isNotEmpty(s.getAfter().toString()) ? Arrays.asList(s.getAfter().toString().split(",")) : null;
                    List<String> beforeStrList = sysDictDataService.selectByDictLabels(DictConstant.INDUSTRY_CATEGORY, beforeList);
                    List<String> afterStrList = sysDictDataService.selectByDictLabels(DictConstant.INDUSTRY_CATEGORY, afterList);
                    before = ListUtil.isNotEmpty(beforeList) ? StringUtils.convertStringList(beforeStrList) : Constants.DEFAULT_CONNECTOR;
                    after = ListUtil.isNotEmpty(afterList) ? StringUtils.convertStringList(afterStrList) : Constants.DEFAULT_CONNECTOR;
                    String content = "从业经历-修改ID：" + updateEmployment.getSort() + "【" + ListUtil.firstOrThrow(s.getFieldPath()) + "】从“" + before + "”更新为“" + after + "”";
                    crmUpdateRecordDTO.setContent(content);
                } else {
                    crmUpdateRecordDTO.setContent("从业经历-修改ID" + updateEmployment.getSort() +"【" + ListUtil.firstOrThrow(s.getFieldPath())+ "】从“" + before + "”更新为“" + after + "”");
                }
                crmUpdateRecordDTO.setBizType(RecordBizType.CONNECTIONS.getValue());
                crmUpdateRecordDTO.setMenuType(RecordMenuType.UPDATE_RECORD.getValue());
                crmUpdateRecordDTO.setBizId(dto.getConnectionId());
                crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
                updateNeedList.add(updateEmployment);
            });
        }

        //批量更新从业经历信息
        if(ListUtil.isNotEmpty(updateNeedList)) {
            connectionEmploymentService.updateBatchById(updateNeedList);
        }
    }

    /**
     * 批量删除人脉管理表
     *
     * @param idList 需要删除的人脉管理表主键
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = {ServiceException.class, Exception.class})
    public boolean deleteConnectionByIds(List<Long> idList) {

        List<Connection> connectionList = this.list(Wrappers.<Connection>lambdaQuery().in(Connection::getConnectionId, idList));
        boolean isSucceed = true;
        if(SecurityUtils.getUserId() != null){
            List<DeleteAddDTO> deleteAddDtoList = ListUtil.map(connectionList,
                    connection -> DeleteAddDTO.valueOf(connection.getConnectionId(), String.format("人脉名: %s", connection.getConnectionName())));
            isSucceed = this.recycleBinService.addDeleteRecord(RecycleBizType.CONNECTION, deleteAddDtoList);
        }
         isSucceed = isSucceed&&this.removeByIds(connectionList);
        // 删除失败时, 抛异常回滚删除操作
        if (!isSucceed) {
            throw new ServiceException("删除人脉失败");
        }
        return true;
    }

    /**
     * 禁用启用
     *
     * @param dto 人脉管理表数据传输模型
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = {ServiceException.class, Exception.class})
    public boolean enableConnection(ConnectionEnableDTO dto) {
        Connection connection = this.getById(dto.getConnectionId());
        if (Objects.isNull(connection)) {
            throw new ServiceException("数据出现异常或丢失");
        }
        CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
        crmUpdateRecordDTO.setBizId(connection.getConnectionId());
        if (StringUtils.isNotEmpty(dto.getRemark())) {
            crmUpdateRecordDTO.setReason(dto.getRemark());
        }
        CrmUpdateRecord crmUpdateRecord = this.crmUpdateRecordConverter.toEntityByDto(crmUpdateRecordDTO);
        crmUpdateRecord.setBizType(RecordBizType.CONNECTIONS.getValue());
        crmUpdateRecord.setMenuType(RecordMenuType.STATUS_UPDATE.getValue());
        crmUpdateRecord.setEnableStatus(dto.getStatus());
        //解禁
        if (ConnectionStatus.VALID.getValue().equals(dto.getStatus())) {
            crmUpdateRecord.setContent(Objects.requireNonNull(SecurityUtils.getLoginUser()).getDeptName()+ SecurityUtils.getUsername() + "把【客户状态】从”" + ConnectionStatus.INVALID.getLabel() + "”更新为“" + ConnectionStatus.VALID.getLabel() + "“");
        } else {
            //禁用
            crmUpdateRecord.setContent(Objects.requireNonNull(SecurityUtils.getLoginUser()).getDeptName()+ SecurityUtils.getUsername() + "把【客户状态】从“" + ConnectionStatus.VALID.getLabel() + "”更新为”" + ConnectionStatus.INVALID.getLabel() + "“");
        }
        crmUpdateRecordService.save(crmUpdateRecord);

        connection.setStatus(dto.getStatus());
        return this.updateById(connection);
    }

    /**
     * 修改批量人脉管理表
     * @param supContactList 人脉管理表修改参数
     * @param isCustomer 是否是客户数据同步来的
     */
    @Override
    public void updateBatchConnection(List<SupContact> supContactList, Boolean isCustomer) {
        if(ListUtil.isEmpty(supContactList)) {
            return;
        }
        SupContact supContact = ListUtil.firstOrThrow(supContactList);
        SupCustomer supCustomer = customerService.getInfo(supContact.getCustomerId());
        List<Long> categoryIdList = customerCategoryService.selectCustomerCategoryById(supContact.getCustomerId());
        if(supCustomer == null) {
            return;
        }
        List<String> phoneList = ListUtil.distinctMap(supContactList, SupContact::getPhone);
        List<Connection> connectionList = this.list(Wrappers.<Connection>lambdaQuery()
                .eq(Connection::getAuditStatus,FlowApprovalStatus.APPROVE.getValue())
                .in(Connection::getPhone, phoneList));

        // 根据客户ID和手机号查询其他关联的在职的客户并将在职改为离职
        List<String> incumbencyPhoneList = supContactList.stream().filter(supContact1 -> EmploymentStatus.INCUMBENCY.getValue().equals(supContact1.getEmploymentStatus()))
                .map(SupContact::getPhone).distinct().collect(Collectors.toList());
        Map<String, SupCustomer> phoneSupCustomerMap = customerService.listCustomerByPhoneId(supCustomer.getCustomerId(), incumbencyPhoneList);

        List<ConnectionUpdateDTO> connectionUpdateDTOList = connectionConverter.toUpdateDtoByEntity(connectionList);
        Map<String, ConnectionUpdateDTO> phoneConnectionMap = ListUtil.toMap(connectionUpdateDTOList, ConnectionUpdateDTO::getPhone);
        // 从业经历
        List<ConnectionEmployment> connectionEmploymentList = new ArrayList<>();
        // 更新记录
        List<CrmUpdateRecordDTO> crmUpdateRecordDTOList = new ArrayList<>();
        // 修改人脉管理
        List<ConnectionUpdateDTO> editConnectionList = new ArrayList<>();
        // 新增人脉管理
        List<ConnectionUpdateDTO> addConnectionList = new ArrayList<>();
        // 新增人脉管理从业经历
        List<ConnectionEmployment> addConnectionEmploymentList = new ArrayList<>();

        // 手机号
        Map<String,ConnectionUpdateDTO> phoneSoleSet = new LinkedHashMap<>();
        // 从业经历排序
        Map<Long,Integer> connectionIdSortMap = new LinkedHashMap<>();
        supContactList.forEach(contact -> {
            ConnectionUpdateDTO connectionUpdateDTO = phoneConnectionMap.get(contact.getPhone());
            if(connectionUpdateDTO != null){
                //修改人脉信息
                this.getEditConnection(supCustomer, categoryIdList,
                            crmUpdateRecordDTOList, editConnectionList,
                            contact, connectionUpdateDTO,phoneSupCustomerMap
                        ,connectionEmploymentList);
            } else {
                if(phoneSoleSet.containsKey(contact.getPhone())){
                    this.getNotAddConnectionEmployment(supCustomer, categoryIdList,
                            connectionEmploymentList, crmUpdateRecordDTOList,
                            phoneSoleSet, connectionIdSortMap, contact);
                } else {
                    this.getAddConnection(supCustomer, categoryIdList,
                            addConnectionList, addConnectionEmploymentList, phoneSoleSet,
                            crmUpdateRecordDTOList, contact);
                }
            }
        });
        if(ListUtil.isNotEmpty(editConnectionList)){
            List<Connection> connections = connectionConverter.toEntityByUpdateDto(editConnectionList);
            this.updateBatchById(connections);
        }
        if(ListUtil.isNotEmpty(addConnectionList)){
            List<Connection> connections = connectionConverter.toEntityByUpdateDto(addConnectionList);
            SqlHelper.appendAddUpdateInfo(connections);

            LocalDateTime nowTime = LocalDateTime.now();

            connections.forEach(connection -> {
                connection.setStatus(ConnectionStatus.VALID.getValue());
                connection.setAuditStatus(FlowApprovalStatus.APPROVE.getValue());
                connection.setAuditTime(nowTime);
                //如果是从客户同步过来的数据，则创建数据为客户的创建数据
                if(isCustomer) {
                    connection.setCreateAndUpdateInfo(supCustomer.getCreateBy(), supCustomer.getCreateId(), supCustomer.getCreateDept());
                }
            });
            this.saveBatch(connections);
            //批量新增人脉的从业经历数据
            if(ListUtil.isNotEmpty(addConnectionEmploymentList)) {
                connectionEmploymentService.saveBatch(addConnectionEmploymentList);
            }

            //批量新增人脉流转数据
            connectionWanderService.addBatchByConnection(connections);
        }
        if(ListUtil.isNotEmpty(connectionEmploymentList)){
            SqlHelper.appendAddUpdateInfo(connectionEmploymentList);
            //如果是从客户同步过来的数据，则创建数据为客户的创建数据
            if(isCustomer) {
                connectionEmploymentList.forEach(connectionEmployment ->
                        connectionEmployment.setCreateAndUpdateInfo(supCustomer.getCreateBy(), supCustomer.getCreateId(), supCustomer.getCreateDept()));

            }
            connectionEmploymentService.insertBatchConnectionEmployment(connectionEmploymentList);
        }
        //更新记录
        if(ListUtil.isNotEmpty(crmUpdateRecordDTOList)) {
            //是从客户同步的数据
            if(isCustomer) {
                crmUpdateRecordService.insertBatchCustomerUpdateRecord(crmUpdateRecordDTOList, supCustomer);
            } else {
                crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordDTOList);
            }
        }
    }

    /**
     * 离职后更新人脉管理数据，清空人脉的基本信息现任职企业，负责品牌，行业类目，所在部门，岗位数据，更新到从业经历里
     * @param oldPhoneContactList
     * @param supContactList
     * @param addContactList
     */
    @Override
    public void clearConnectionCustomerData(List<SupContact> oldPhoneContactList, List<ContactDTO> supContactList, List<ContactDTO> addContactList) {
        List<String> newPhoneList = new ArrayList<>();
        if(ListUtil.isNotEmpty(oldPhoneContactList) && ListUtil.isNotEmpty(supContactList)) {
            //获取现在离职的企业联系人的手机号
            List<ContactDTO> newPhoneContactList = supContactList.stream()
                    .filter(contact -> Objects.equals(EmploymentStatus.LEAVE_OFFICE.getValue(), contact.getEmploymentStatus())).collect(Collectors.toList());
            if(ListUtil.isEmpty(newPhoneContactList)) {
                return;
            }
            newPhoneList = ListUtil.map(newPhoneContactList, ContactDTO::getPhone);

            //获取旧的在职的企业联系人的手机号列表
            List<String> oldPhoneList = ListUtil.map(oldPhoneContactList, SupContact::getPhone);

            //获取由在职到离职的企业联系人手机号列表(取交集)
            newPhoneList.retainAll(oldPhoneList);
        }
        //获取新增的离职的人的手机号列表
        if(ListUtil.isNotEmpty(addContactList)) {
            List<ContactDTO> nowLeaveList = addContactList.stream()
                    .filter(contact -> Objects.equals(EmploymentStatus.LEAVE_OFFICE.getValue(), contact.getEmploymentStatus())).collect(Collectors.toList());
            if(ListUtil.isNotEmpty(nowLeaveList)) {
                List<String> nowLeavePhoneList = ListUtil.map(nowLeaveList, ContactDTO::getPhone);
                newPhoneList.addAll(nowLeavePhoneList);
            }
        }

        if(ListUtil.isEmpty(newPhoneList)) {
            return;
        }
        //获取离职的人脉列表
        List<Connection> connectionList = this.list(Wrappers.lambdaQuery(Connection.class)
                .eq(Connection::getAuditStatus,FlowApprovalStatus.APPROVE.getValue())
                .in(Connection::getPhone, newPhoneList));
        if(ListUtil.isEmpty(connectionList)) {
            return;
        }
        List<Long> connectionIds = ListUtil.map(connectionList, Connection::getConnectionId);
        //根据人脉id列表获取所有所有的从业经历
        Map<Long, List<ConnectionEmployment>> employmentListMap = connectionEmploymentService.getEmploymentListMap(connectionIds);
        List<ConnectionDTO> connectionDTOList = connectionConverter.toDtoByEntity(connectionList);
        //清空人脉的基本信息现任职企业，负责品牌，行业类目，所在部门，岗位数据，更新到从业经历里
        connectionDTOList.forEach(connectionDTO -> {
            connectionDTO.setCustomerId(null);
            connectionDTO.setCurrentEmployer(null);
            connectionDTO.setResponsibleBrand(ListUtil.emptyList());
            connectionDTO.setIndustryCategory(null);
            connectionDTO.setDepartmentName(null);
            connectionDTO.setPostName(null);
            //获取从业经历
            List<ConnectionEmployment> employmentList = employmentListMap.get(connectionDTO.getConnectionId());
            if(ListUtil.isNotEmpty(employmentList)) {
                connectionDTO.setConnectionEmploymentDTOS(connectionEmploymentConverter.toDtoByEntity(employmentList));
            }
            updateConnection(connectionDTO, false);
        });
    }

    /**
     * 如果从客户管理里更新了客户名称和品牌业务线，那么人脉管理-基本信息中的任职企业名称和品牌业务线同步更新
     * @param customerId
     * @param name
     * @param lineBusiness
     * @param unnecessaryPhoneList 需要排除的手机号列表
     */
    @Override
    public void updateBatchCustomerInfo(Long customerId, String name, String lineBusiness, List<String> unnecessaryPhoneList) {
        //获取所有需要变更的人脉信息列表
        List<Connection> queryUpdateConnectionList = this.list(Wrappers.lambdaQuery(Connection.class)
            .eq(Connection::getCustomerId, customerId)
            .notIn(ListUtil.isNotEmpty(unnecessaryPhoneList), Connection::getPhone, unnecessaryPhoneList));
        if(ListUtil.isEmpty(queryUpdateConnectionList)) {
            return;
        }
        List<Long> connectionIds = ListUtil.map(queryUpdateConnectionList, Connection::getConnectionId);

        //根据人脉id列表获取所有所有的从业经历
        Map<Long, List<ConnectionEmployment>> employmentListMap = connectionEmploymentService.getEmploymentListMap(connectionIds);

        List<ConnectionDTO> connectionDTOList = connectionConverter.toDtoByEntity(queryUpdateConnectionList);
        connectionDTOList.forEach(connectionDTO -> {
            connectionDTO.setCurrentEmployer(name);
            connectionDTO.setResponsibleBrand(Collections.singletonList(lineBusiness));
            //获取从业经历
            List<ConnectionEmployment> employmentList = employmentListMap.get(connectionDTO.getConnectionId());
            if(ListUtil.isNotEmpty(employmentList)) {
                connectionDTO.setConnectionEmploymentDTOS(connectionEmploymentConverter.toDtoByEntity(employmentList));
            }
            //更新人脉信息的现任职企业和品牌/业务线
            updateConnection(connectionDTO, false);
        });
    }

    /**
     * 获取所有人脉基本信息中的的任职企业名称
     * @param name 客户名称
     * @return
     */
    @Override
    public List<String> listCurrentEmployerList(String name) {
        List<Connection> connectionList = this.list(Wrappers.lambdaQuery(Connection.class)
            .select(Connection::getCurrentEmployer)
            .like(StringUtils.isNotEmpty(name), Connection::getCurrentEmployer, name)
            .eq(Connection::getAuditStatus,FlowApprovalStatus.APPROVE.getValue())
            .eq(Connection::getDelFlag, DelFlag.NOT_DELETED));

        List<String> resultList = new ArrayList<>();
        if(ListUtil.isEmpty(connectionList)) {
            return resultList;
        }

        //组装所有不重复的企业名称
        connectionList.forEach(connection -> {
            if(Objects.nonNull(connection) && StringUtils.isNotBlank(connection.getCurrentEmployer())
                    && !resultList.contains(connection.getCurrentEmployer())) {
                resultList.add(connection.getCurrentEmployer());
            }
        });
        return resultList;
    }

    /**
     * 根据企业名称获取品牌/业务线列表
     * @param query 查询条件
     * @return 品牌/业务线列表
     */
    @Override
    public List<String> listResponsibleBrand(ConnectionBrandQuery query) {
        if(StringUtils.isEmpty(query.getName())) {
            return ListUtil.emptyList();
        }

        //获取客户管理中该客户下的品牌/业务线列表
        List<String> responsibleBrandList = customerService.listResponsibleBrandByName(query.getName(), query.getResponsibleBrand());

        //获取人脉基本信息中企业下的品牌/业务线列表
        List<Connection> connectionList = this.list(Wrappers.<Connection>lambdaQuery()
                .select(Connection::getResponsibleBrand, Connection::getConnectionId)
                .like(StringUtils.isNotBlank(query.getResponsibleBrand()), Connection::getResponsibleBrand, query.getResponsibleBrand())
                .eq(Connection::getCurrentEmployer, query.getName())
                .eq(Connection::getAuditStatus, FlowApprovalStatus.APPROVE.getValue())
                .eq(Connection::getDelFlag, DelFlag.NOT_DELETED));
        if(ListUtil.isNotEmpty(connectionList)) {
            connectionList.forEach(connection -> {
                if(Objects.nonNull(connection) && ListUtil.isNotEmpty(connection.getResponsibleBrand())) {
                    connection.getResponsibleBrand().forEach(responsibleBrand -> {
                        //去重
                        if(StringUtils.isNotBlank(responsibleBrand) && !responsibleBrandList.contains(responsibleBrand)) {
                            //符合筛选条件
                            if(StringUtils.isBlank(query.getResponsibleBrand()) || responsibleBrand.contains(query.getResponsibleBrand())) {
                                responsibleBrandList.add(responsibleBrand);
                            }
                        }
                    });
                }
            });
        }

        //获取人脉从业经历中企业下的品牌/业务线列表
        List<ConnectionEmployment> employmentList = connectionEmploymentService.list(Wrappers.<ConnectionEmployment>lambdaQuery()
                .select(ConnectionEmployment::getResponsibleBrand, ConnectionEmployment::getEmploymentId)
                .eq(ConnectionEmployment::getCurrentEmployer, query.getName())
                .like(StringUtils.isNotBlank(query.getResponsibleBrand()), ConnectionEmployment::getResponsibleBrand, query.getResponsibleBrand())
                .eq(ConnectionEmployment::getDelFlag, DelFlag.NOT_DELETED));
        if(ListUtil.isNotEmpty(employmentList)) {
            employmentList.forEach(connection -> {
                if(Objects.nonNull(connection) && ListUtil.isNotEmpty(connection.getResponsibleBrand())) {
                    connection.getResponsibleBrand().forEach(responsibleBrand -> {
                        //去重
                        if(StringUtils.isNotBlank(responsibleBrand) && !responsibleBrandList.contains(responsibleBrand)) {
                            //符合筛选条件
                            if(StringUtils.isBlank(query.getResponsibleBrand()) || responsibleBrand.contains(query.getResponsibleBrand())) {
                                responsibleBrandList.add(responsibleBrand);
                            }
                        }
                    });
                }
            });
        }
        return responsibleBrandList;
    }

    /**
     * 查询人脉是否在审批中通过手机号
     * @param phoneList 手机号
     * @return 人脉
     */
    @Override
    public List<Connection> listConnectionByPhoneList(List<String> phoneList) {
        if(ListUtil.isEmpty(phoneList)){
            return ListUtil.emptyList();
        }
        return this.list(Wrappers.<Connection>lambdaQuery()
                .in(Connection::getPhone, phoneList)
                .in(Connection::getAuditStatus, FlowApprovalStatus.WAIT_APPROVAL.getValue(), FlowApprovalStatus.IN_EXAMINATION_AND_APPROVAL.getValue()));
    }

    /**
     * 修改审批状态和时间
     * @param connectionId
     * @param auditStatus
     * @return
     */
    @Override
    public Boolean updateAuditAfter(Long connectionId, Integer auditStatus) {
        if(Objects.isNull(connectionId) || Objects.isNull(auditStatus)) {
            throw new ServiceException("必填项为空");
        }

        LocalDateTime nowTime = LocalDateTime.now();
        LocalDateTime auditTime = null;
        //校验是否审核通过
        if(Objects.equals(FlowApprovalStatus.APPROVE.getValue(), auditStatus)) {
            auditTime = LocalDateTime.now();
            //获取人脉详情
            Connection connection = this.getById(connectionId);
            if(Objects.isNull(connection)) {
                throw new ServiceException(ResultCode.DATA_ABNORMAL_OR_MISSING);
            }
            // 新增人脉管理-人脉流转数据
            ConnectionWander connectionWander = new ConnectionWander();
            connectionWander.setConnectionId(connectionId);
            connectionWander.setServiceUserId(connection.getCreateId());
            connectionWander.setServiceUserDept(connection.getCreateDept());
            connectionWander.setCreateAndUpdateInfo(connection.getCreateBy(), connection.getCreateId(), connection.getCreateDept());
            connectionWander.setCreateTime(nowTime);
            connectionWander.setUpdateTime(nowTime);
            connectionWander.setIsHidden(true);
            connectionWanderService.save(connectionWander);
        }

        //修改人脉状态
        return this.update(Wrappers.<Connection>lambdaUpdate()
                .set(Connection::getAuditStatus, auditStatus)
                .set(Connection::getAuditTime, auditTime)
                .set(Connection::getCreateTime, nowTime)
                .set(Connection::getUpdateTime, nowTime)
                .eq(Connection::getConnectionId, connectionId));
    }

    /**
     * 获取人脉详情及从业经历列表
     * @param connectionId
     * @return
     */
    @Override
    public ConnectionVO selectConnectionInfoById(Long connectionId) {
        //获取人脉详情
        ConnectionVO connectionVO = selectConnectionById(connectionId);
        //查询人脉下的从业经历表列表
        connectionVO.setEmploymentList(connectionEmploymentService.listConnectionEmployment(connectionId));

        //查询快照是否存在
        long snapshotInfo = snapshotService.querySnapshotInfo(connectionId, connectionVO.getVersion(), SnapshotBizType.CONNECTION.getValue());
        // 添加快照
        if(snapshotInfo == 0) {
            Snapshot snapshot = new Snapshot();
            snapshot.setBizId(connectionId);
            snapshot.setVersion(connectionVO.getVersion());
            snapshot.setBizType(SnapshotBizType.CONNECTION.getValue());
            snapshot.setSnapshotInfo(JsonUtil.obj2String(connectionVO));
            snapshotService.insert(snapshot);
        }

        connectionVO.setBizType(ApprovalBizType.InsertConnection);
        return connectionVO;
    }

    /**
     * 获取所有的有数据权限的人脉id列表
     * @param query
     * @return
     */
    @Override
    public List<Long> listConnectionIds(ConnectionQuery query) {
        ShowTableNameLambdaQueryWrapper<Connection> wrapper = new ShowTableNameLambdaQueryWrapper(Connection.class);
        LambdaQueryWrapper<ConnectionEmployment> employmentWrapper = new ShowTableNameLambdaQueryWrapper<>(ConnectionEmployment.class);
        if(!query.getIsDeleted()){
            //封装人脉管理查询条件
            wrapper = setConnectionWrapper(query);
            SqlHelper.filterUpdateInfoTableAlias(wrapper, query,"crmconnection");
            SqlHelper.filterCreateListInfo(wrapper, query,"crmconnection");

            //现任行业类目
            SqlHelper.mathInSet(wrapper,Connection::getIndustryCategory, query.getIndustryCategoryList());

            //历任条件查询从业经历
            employmentWrapper = setConnectionEmploymentWrapper(query);
            SqlHelper.mathInSet(employmentWrapper, ConnectionEmployment::getIndustryCategory, query.getHistoryIndustryCategoryList());
            employmentWrapper.setParamAlias(ConnectionMapper.CONNECTION_EMPLOYMENT_WRAPPER_ALIAS);

        } else {
            wrapper.eq(Connection::getStatus, query.getStatus());
        }
        wrapper.setTableAlias("crmconnection");
        //获取所有的有数据权限的人脉id列表
        return connectionMapper.listConnectionIds(query, wrapper,employmentWrapper);
    }

    /**
     * 人脉分配
     * @param dto
     * @return
     */
    @Override
    public boolean allocationServiceUserIds(ConnectionCirculationDTO dto) {

        //根据人脉id删除人脉分配数据
        connectionWanderService.deleteByConnectionId(dto.getConnectionId());
        // 分配记录
        List<AuthUser> authUsers = userService.selectUserById(dto.getServiceUserIdList());
        //获取分配人的部门
        Map<Long, String> serviceUserDeptMap = ListUtil.toMap(authUsers, AuthUser::getUserId, AuthUser::getDeptName);
        // 添加分配
        List<ConnectionWanderDTO> connectionWanderDTOList = new ArrayList<>();
        dto.getServiceUserIdList().forEach(userId -> {
            ConnectionWanderDTO connectionWanderDTO = new ConnectionWanderDTO();
            connectionWanderDTO.setConnectionId(dto.getConnectionId());
            connectionWanderDTO.setServiceUserId(userId);
            connectionWanderDTO.setServiceUserDept(serviceUserDeptMap.get(userId));
            connectionWanderDTO.setIsHidden(false);
            connectionWanderDTOList.add(connectionWanderDTO);
        });
        connectionWanderService.insertBatch(connectionWanderDTOList);

        // 根据用户信息组装服务人员
        String content = customerService.getServiceUserName(authUsers, "，");

        //新增人脉分配记录
        CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
        crmUpdateRecordDTO.addEvent(RecordBizType.CONNECTIONS.getValue(), dto.getConnectionId()
                , content, RecordMenuType.CIRCULATION.getValue(), dto.getRemark(), null);
        return crmUpdateRecordService.insertCrmUpdateRecord(crmUpdateRecordDTO);
    }

    /**
     * 获取新增的人脉
     * @param supCustomer 客户
     * @param categoryIdList 行业类目ID
     * @param addConnectionList 新增的人脉
     * @param addConnectionEmploymentList  新增的人脉的从业经历
     * @param phoneSoleSet <手机号，人脉>
     * @param crmUpdateRecordDTOList 更新记录
     * @param contact 企业联系人
     */
    private void getAddConnection(SupCustomer supCustomer, List<Long> categoryIdList,
                                  List<ConnectionUpdateDTO> addConnectionList,
                                  List<ConnectionEmployment> addConnectionEmploymentList,
                                  Map<String, ConnectionUpdateDTO> phoneSoleSet,
                                  List<CrmUpdateRecordDTO> crmUpdateRecordDTOList, SupContact contact) {
        ConnectionUpdateDTO connectionUpdate = new ConnectionUpdateDTO();
        Long connectionId = uidConfig.sequence().nextId();
        connectionUpdate.setConnectionId(connectionId);
        connectionUpdate.setConnectionName(contact.getConnectionName());
        connectionUpdate.setWechatNumber(contact.getWechatNumber());
        connectionUpdate.setOtherNumber(contact.getOtherNumber());
        connectionUpdate.setPhone(contact.getPhone());
        //在职，需要添加企业数据
        if(Objects.equals(EmploymentStatus.INCUMBENCY.getValue(), contact.getEmploymentStatus())) {
            connectionUpdate.setCustomerId(supCustomer.getCustomerId());
            connectionUpdate.setCurrentEmployer(supCustomer.getName());
            connectionUpdate.setResponsibleBrand(Collections.singletonList(supCustomer.getLineBusiness()));
            connectionUpdate.setDepartmentName(contact.getDepartmentName());
            connectionUpdate.setIndustryCategory(categoryIdList);
            connectionUpdate.setPostName(contact.getPostName());
        }

        addConnectionList.add(connectionUpdate);
        phoneSoleSet.put(contact.getPhone(), connectionUpdate);

        ConnectionVO connectionVO = connectionConverter.toVoByUpdateDto(connectionUpdate);
        //拼接更新记录
        List<CrmUpdateRecordDTO> updateRecordDTOList = montageCrmUpdateRecord(connectionVO, connectionUpdate.getIndustryCategory(), connectionUpdate.getConnectionId());
        crmUpdateRecordDTOList.addAll(updateRecordDTOList);
        //离职，添加从业经历
        if(Objects.equals(EmploymentStatus.LEAVE_OFFICE.getValue(), contact.getEmploymentStatus())) {
            this.splicingConnectionEmployment(supCustomer, categoryIdList,
                    addConnectionEmploymentList, crmUpdateRecordDTOList,
                    contact, connectionId);
        }
    }

    /**
     * 拼接从业经历
     * @param supCustomer 客户信息
     * @param categoryIdList 行业类目id
     * @param addConnectionEmploymentList 新增的从业经历
     * @param crmUpdateRecordDTOList
     * @param contact
     * @param connectionId
     */
    private void splicingConnectionEmployment(SupCustomer supCustomer,
                                              List<Long> categoryIdList,
                           List<ConnectionEmployment> addConnectionEmploymentList,
                           List<CrmUpdateRecordDTO> crmUpdateRecordDTOList,
                           SupContact contact, Long connectionId) {
        //拼接从业经历
        ConnectionEmployment employment = new ConnectionEmployment();
        employment.setConnectionId(connectionId);
        employment.setCustomerId(supCustomer.getCustomerId());
        employment.setCurrentEmployer(supCustomer.getName());
        employment.setResponsibleBrand(Collections.singletonList(supCustomer.getLineBusiness()));
        employment.setDepartmentName(contact.getDepartmentName());
        employment.setIndustryCategory(categoryIdList);
        employment.setPostName(contact.getPostName());
        employment.setSort(1);
        // 获取修改的从业经历的排序
        ConnectionEmployment connectionEmploymentLast = connectionEmploymentService.getLastOne(connectionId);
        if(connectionEmploymentLast != null){
            employment.setSort(connectionEmploymentLast.getSort() + 1);
        }
        employment.setCreateAndUpdateInfo(supCustomer.getCreateBy(), supCustomer.getCreateId(), supCustomer.getCreateDept());
        addConnectionEmploymentList.add(employment);
        //拼接从业经历更新记录
        setConnectionEmploymentRecord(Collections.singletonList(employment), connectionId, crmUpdateRecordDTOList);
    }

    /**
     * 获取数据库没有的人脉的新增的从业经历
     * @param supCustomer 客户
     * @param categoryIdList 行业类目ID
     * @param connectionEmploymentList 新增从业经历集合
     * @param crmUpdateRecordDTOList 更新记录
     * @param phoneSoleSet <手机号，人脉>
     * @param connectionIdSortMap 排序<人脉Id,排序>
     * @param contact 企业联系人
     */
    private void getNotAddConnectionEmployment(SupCustomer supCustomer, List<Long> categoryIdList,
                           List<ConnectionEmployment> connectionEmploymentList, List<CrmUpdateRecordDTO> crmUpdateRecordDTOList,
                           Map<String, ConnectionUpdateDTO> phoneSoleSet, Map<Long, Integer> connectionIdSortMap,
                                               SupContact contact) {
        ConnectionUpdateDTO connectionUpdate = phoneSoleSet.get(contact.getPhone());
        ConnectionEmployment connectionEmployment = new ConnectionEmployment();
        connectionEmployment.setEmploymentId(uidConfig.sequence().nextId());
        connectionEmployment.setConnectionId(connectionUpdate.getConnectionId());
        connectionEmployment.setCustomerId(supCustomer.getCustomerId());
        connectionEmployment.setCurrentEmployer(supCustomer.getName());
        connectionEmployment.setDepartmentName(contact.getDepartmentName());
        connectionEmployment.setPostName(contact.getPostName());
        connectionEmployment.setResponsibleBrand(Collections.singletonList(supCustomer.getLineBusiness()));
        connectionEmployment.setIndustryCategory(categoryIdList);
        Integer sort = connectionIdSortMap.get(connectionUpdate.getConnectionId());
        if(sort == null){
            sort =+ 1;
        }
        connectionEmployment.setSort(sort);
        connectionEmploymentList.add(connectionEmployment);
        connectionIdSortMap.put(connectionUpdate.getConnectionId(),sort);
        CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
        String content = "从业经历-新增ID:" + connectionEmployment.getSort() +
                "【任职企业】" + "“" + connectionEmployment.getCurrentEmployer() + "”";
        crmUpdateRecordDTO.addEvent(RecordBizType.CONNECTIONS.getValue(),connectionUpdate.getConnectionId(),
                content,RecordMenuType.UPDATE_RECORD.getValue(),null,null);
        crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
    }

    /**
     * 获取修改的人脉集合
     * @param supCustomer 客户
     * @param categoryIdList 行业类目ID
     * @param crmUpdateRecordDTOList 更新记录
     * @param editConnectionList 修改的人脉集合
     * @param contact 企业联系人
     * @param connectionUpdateDTO 修改前的人脉
     * @param phoneSupCustomerMap <手机号，客户信息></>
     * @param connectionEmploymentList 新增的从业经历
     */
    private void getEditConnection(SupCustomer supCustomer, List<Long> categoryIdList,
                                   List<CrmUpdateRecordDTO> crmUpdateRecordDTOList, List<ConnectionUpdateDTO> editConnectionList,
                                   SupContact contact, ConnectionUpdateDTO connectionUpdateDTO,
                                   Map<String, SupCustomer> phoneSupCustomerMap, List<ConnectionEmployment> connectionEmploymentList) {
        ConnectionUpdateDTO connectionUpdate = new ConnectionUpdateDTO();
        connectionUpdate.setConnectionId(connectionUpdateDTO.getConnectionId());
        connectionUpdate.setConnectionName(contact.getConnectionName());
        connectionUpdate.setWechatNumber(contact.getWechatNumber());
        connectionUpdate.setPhone(contact.getPhone());
        connectionUpdate.setOtherNumber(contact.getOtherNumber());
        connectionUpdate.setPostName(contact.getPostName());
        //不论在职离职，都需要更新
        connectionUpdate.setCustomerId(supCustomer.getCustomerId());
        connectionUpdate.setCurrentEmployer(supCustomer.getName());
        connectionUpdate.setResponsibleBrand(Collections.singletonList(supCustomer.getLineBusiness()));
        connectionUpdate.setDepartmentName(contact.getDepartmentName());
        connectionUpdate.setIndustryCategory(categoryIdList);
        editConnectionList.add(connectionUpdate);
        // 添加从业经历
        SupCustomer updateCustomer = phoneSupCustomerMap.get(connectionUpdate.getPhone());
        if(updateCustomer != null){
            this.splicingConnectionEmployment(updateCustomer, updateCustomer.getIndustryCategoryIdList(),
                    connectionEmploymentList, crmUpdateRecordDTOList,
                    contact, connectionUpdate.getConnectionId());
        }
        List<BaseModifyDiff<?>> baseModifyDiffs = objectComparator.compareObject(connectionUpdateDTO,
                connectionUpdate, ConnectionUpdateDTO.class);
        baseModifyDiffs.forEach(baseModifyDiff -> {
            CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
            if(DictConstant.INDUSTRY_CATEGORY_FIELD.equals(ListUtil.firstOrThrow(baseModifyDiff.getFieldPath()))){
                List<String> beforeList = sysDictDataService.selectByDictList(DictConstant.INDUSTRY_CATEGORY, baseModifyDiff.getBefore());
                List<String> afterList = sysDictDataService.selectByDictList(DictConstant.INDUSTRY_CATEGORY, baseModifyDiff.getAfter());
                String before = ListUtil.isNotEmpty(beforeList) ? StringUtils.convertStringList(beforeList) : Constants.DEFAULT_CONNECTOR;
                String after = ListUtil.isNotEmpty(afterList) ? StringUtils.convertStringList(afterList) : Constants.DEFAULT_CONNECTOR;
                if(!StringUtils.equals(Constants.DEFAULT_CONNECTOR, before) || !StringUtils.equals(Constants.DEFAULT_CONNECTOR, after)) {
                    String content = "基本信息-【" + baseModifyDiff.getFieldPath().get(0) + "】从“" + before + "”更新为“" + after + "”";
                    crmUpdateRecordDTO.addEvent(RecordBizType.CONNECTIONS.getValue(), connectionUpdateDTO.getConnectionId(),
                            content, RecordMenuType.UPDATE_RECORD.getValue(), null, null);
                    crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
                }
            } else {
                crmUpdateRecordDTO = crmUpdateRecordDTO.valueOf(baseModifyDiff);
                crmUpdateRecordDTO.setBizId(connectionUpdateDTO.getConnectionId());
                crmUpdateRecordDTO.setBizType(RecordBizType.CONNECTIONS.getValue());
                crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
            }
        });
    }

    /**
     * 获取已有人脉的新增的从业经历
     * @param supCustomer 客户
     * @param categoryIdList 行业类目ID
     * @param connectionEmploymentList 新增从业经历集合
     * @param crmUpdateRecordDTOList 更新记录
     * @param connectionIdSortMap 排序<人脉Id,排序>
     * @param contact 企业联系人
     * @param connectionUpdateDTO 人脉
     */
    private void getAddConnectionEmployment(SupCustomer supCustomer, List<Long> categoryIdList,
                                            List<ConnectionEmployment> connectionEmploymentList, List<CrmUpdateRecordDTO> crmUpdateRecordDTOList,
                                            Map<Long, Integer> connectionIdSortMap, SupContact contact, ConnectionUpdateDTO connectionUpdateDTO) {
        ConnectionEmployment connectionEmployment = new ConnectionEmployment();
        connectionEmployment.setEmploymentId(uidConfig.sequence().nextId());
        connectionEmployment.setConnectionId(connectionUpdateDTO.getConnectionId());
        connectionEmployment.setCustomerId(supCustomer.getCustomerId());
        connectionEmployment.setCurrentEmployer(supCustomer.getName());
        connectionEmployment.setDepartmentName(contact.getDepartmentName());
        connectionEmployment.setPostName(contact.getPostName());
        connectionEmployment.setResponsibleBrand(Arrays.asList(supCustomer.getLineBusiness()));
        connectionEmployment.setIndustryCategory(categoryIdList);
        Integer sort = connectionIdSortMap.get(connectionUpdateDTO.getConnectionId());
        if(sort == null){
            ConnectionEmployment lastOne = connectionEmploymentService.getLastOne(connectionUpdateDTO.getConnectionId());
            sort = lastOne == null ?  1 : lastOne.getSort() + 1;
        } else {
            sort =+ 1;
        }
        connectionEmployment.setSort(sort);
        connectionEmploymentList.add(connectionEmployment);
        connectionIdSortMap.put(connectionUpdateDTO.getConnectionId(),sort);
        CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
        String content = "从业经历-新增ID:" + connectionEmployment.getSort() +
                "【任职企业】" + "“" + connectionEmployment.getCurrentEmployer() + "”";
        crmUpdateRecordDTO.addEvent(RecordBizType.CONNECTIONS.getValue(), connectionUpdateDTO.getConnectionId(),
                content,RecordMenuType.UPDATE_RECORD.getValue(),null,null);
        crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
    }

    /**
     * 异步-人脉批量分配
     * @param query
     */
    @Override
    public void importConnectionServiceUserInfo(ConnectionImportQuery query) {
        taskRecodeService.editTaskBegin(query.getTaskId());
        try {
            byte[] download = ossService.download(query.getFileKey());
            InputStream inputStream = new ByteArrayInputStream(download);
            ExcelUtil<ConnectionImportModel> util = new ExcelUtil<>(ConnectionImportModel.class);
            List<ConnectionImportModel> exportList = util.importExcel(inputStream,0);
            ImportTaskResult importTaskResult = new ImportTaskResult();

            //校验必填项等
            List<RowVerifyError<ConnectionImportModel>> errorRowVerifyList = objectValidator.verifyImportList(exportList, ConnectionImportModel.getVerifyConfig());
            if(ListUtil.isEmpty(errorRowVerifyList)) {
                errorRowVerifyList = new ArrayList<>();
            }
            //需要进行人脉分配的数据
            List<ConnectionImportModel> connectionServiceList = new ArrayList<>();
            //校验客户姓名、服务人员、手机号及该条数据在数据库中是否存在
            List<RowVerifyError<ConnectionImportModel>> checkConnectionInfoList = checkConnectionInfo(exportList, connectionServiceList);
            errorRowVerifyList.addAll(checkConnectionInfoList);

            //批量分配服务人员
            if(ListUtil.isNotEmpty(connectionServiceList)) {
                connectionServiceList.forEach(connectionImportModel -> {
                    ConnectionCirculationDTO connectionCirculationDTO = new ConnectionCirculationDTO();
                    connectionCirculationDTO.setConnectionId(connectionImportModel.getConnectionId());
                    connectionCirculationDTO.setServiceUserIdList(connectionImportModel.getServiceUserIdList());
                    allocationServiceUserIds(connectionCirculationDTO);
                });
            }

            importTaskResult.setSucceedFileName(query.getFileName());
            importTaskResult.setSucceed(connectionServiceList.size());
            importTaskResult.setIsSuccess(true);
            importTaskResult.setError(0);
            // 导出错误信息
            if (ListUtil.any(errorRowVerifyList)) {
                List<RowVerifyError<ConnectionExportErrorModel>> rowVerifyErrors = this.connectionConverter.toExportByConnectionImport(errorRowVerifyList);
                String fileName = "人脉管理-批量分配导入错误信息.xlsx";
                ExcelSheetConfig<RowVerifyError<ConnectionExportErrorModel>> excelConfig = this.getConnectionErrorConfig();
                this.exportError(rowVerifyErrors, importTaskResult, fileName, excelConfig);
                taskRecodeService.taskImportFailed(query.getTaskId(), importTaskResult);
                return;
            }
            taskRecodeService.taskImportCompleted(query.getTaskId(), importTaskResult);
        }catch(Exception ex) {
            log.error("人脉管理-批量分配错误：", ex);
            ImportTaskResult importTaskResult = new ImportTaskResult();
            importTaskResult.setIsSuccess(false);
            taskRecodeService.taskImportFailed(query.getTaskId(),importTaskResult);
        }
    }

    /**
     * 校验客户姓名、服务人员、手机号及该条数据在数据库中是否存在
     * @param exportList
     * @param connectionServiceList 需要进行人脉分配的数据
     * @return
     */
    private List<RowVerifyError<ConnectionImportModel>> checkConnectionInfo(List<ConnectionImportModel> exportList,
                                                                            List<ConnectionImportModel> connectionServiceList) {
        List<RowVerifyError<ConnectionImportModel>> resultList = new ArrayList<>();
        // 根据客户名称查询对应的人脉id列表
        List<String> connectionNames = ListUtil.distinctMap(exportList, ConnectionImportModel::getConnectionName);
        Map<String, List<Connection>> connectionMap = getConnectionInfo(connectionNames);

        //截取所有服务人员的姓名
        Set<String> serviceUserNameSet = this.splitServiceUserName(exportList);
        //分组查询人员姓名对应的人员数据
        Map<String, List<SysUser>> userNameMap = userService.getUserNameMap(serviceUserNameSet);
        //校验项目资源id是否存在
        for(ConnectionImportModel importModel : exportList) {
            if(StringUtils.isNotEmpty(importModel.getConnectionName())) {
                List<Connection> connectionListByName = connectionMap.get(importModel.getConnectionName());
                if(ListUtil.isEmpty(connectionListByName)) {
                    //校验客户名称是否在库中存在
                    resultList.add(new RowVerifyError<>(importModel.getSerialNumber(), importModel, Collections.singletonList("人脉姓名不存在")));
                } else {
                    //校验客户姓名是否唯一，如果唯一
                    if(connectionListByName.size() == 1) {
                        Connection connection = connectionListByName.get(0);
                        //如果没有输入手机号、或者输入的手机号和库中的这条数据一致，则代表该数据成功匹配人脉数据
                        if(StringUtils.isBlank(importModel.getPhone()) || StringUtils.equals(importModel.getPhone(), connection.getPhone())) {
                            importModel.setConnectionId(connection.getConnectionId());
                        } else {
                            resultList.add(new RowVerifyError<>(importModel.getSerialNumber(), importModel, Collections.singletonList("人脉姓名对应的电话不正确")));
                        }
                    } else {
                        //客户姓名不唯一
                        if(StringUtils.isBlank(importModel.getPhone())) {
                            resultList.add(new RowVerifyError<>(importModel.getSerialNumber(), importModel, Collections.singletonList("人脉姓名重复需要输入手机号")));
                        } else {
                            //根据手机号匹配到人脉数据
                            Map<String, Long> phoneConnectionMap = ListUtil.toMap(connectionListByName, Connection::getPhone, Connection::getConnectionId);
                            Long connectionId = phoneConnectionMap.get(importModel.getPhone());
                            if(Objects.isNull(connectionId)) {
                                resultList.add(new RowVerifyError<>(importModel.getSerialNumber(), importModel, Collections.singletonList("人脉姓名对应的电话不正确")));
                            } else {
                                importModel.setConnectionId(connectionId);
                            }
                        }
                    }
                }
            }

            //匹配服务人员
            if(StringUtils.isBlank(importModel.getServiceUser())) {
                continue;
            }
            if(ListUtil.isEmpty(importModel.getServiceUserList())) {
                resultList.add(new RowVerifyError<>(importModel.getSerialNumber(), importModel, Collections.singletonList("服务人员不存在")));
            }

            Set<Long> serviceUserIds = new HashSet<>();
            //遍历数据
            for(ConnectionImportModel.ServiceUser serviceUser: importModel.getServiceUserList()) {
                if(StringUtils.isBlank(serviceUser.getServiceUserName()) && StringUtils.isBlank(serviceUser.getServiceUserPhone())) {
                    continue;
                }
                if(StringUtils.isBlank(serviceUser.getServiceUserName())) {
                    resultList.add(new RowVerifyError<>(importModel.getSerialNumber(), importModel, Collections.singletonList(String.format("服务人员中手机号%s对应的人员姓名不存在", serviceUser.getServiceUserPhone()))));
                    importModel.setConnectionId(null);
                    continue;
                }
                //根据姓名获取所有的用户数据
                List<SysUser> userList = userNameMap.get(serviceUser.getServiceUserName());
                if(ListUtil.isEmpty(userList)) {
                    //校验客户名称是否在库中存在
                    resultList.add(new RowVerifyError<>(importModel.getSerialNumber(), importModel, Collections.singletonList(String.format("服务人员中%s对应的姓名不存在", serviceUser.getServiceUserName()))));
                    importModel.setConnectionId(null);
                    continue;
                }
                //校验人员姓名是否唯一，如果唯一
                if(userList.size() == 1) {
                    SysUser user = userList.get(0);
                    //如果没有输入手机号、或者输入的手机号和库中的这条数据一致，则代表该数据成功匹配人脉数据
                    if(StringUtils.isBlank(serviceUser.getServiceUserPhone()) || StringUtils.equals(serviceUser.getServiceUserPhone(), user.getPhoneNumber())) {
                        serviceUserIds.add(user.getUserId());
                    } else {
                        String nameAndPhone = StringUtils.isBlank(serviceUser.getServiceUserPhone()) ? serviceUser.getServiceUserName() :
                                serviceUser.getServiceUserName() + "," + serviceUser.getServiceUserPhone();
                        resultList.add(new RowVerifyError<>(importModel.getSerialNumber(), importModel, Collections.singletonList(String.format("服务人员中%s未匹配到服务人员数据", nameAndPhone))));
                        importModel.setConnectionId(null);
                    }
                } else {
                    //人员姓名不唯一
                    if(StringUtils.isBlank(serviceUser.getServiceUserPhone())) {
                        resultList.add(new RowVerifyError<>(importModel.getSerialNumber(), importModel, Collections.singletonList(String.format("服务人员中%s重复需要输入手机号", serviceUser.getServiceUserName()))));
                        importModel.setConnectionId(null);
                    } else {
                        //根据手机号匹配到人脉数据
                        Map<String, Long> phoneUserMap = ListUtil.toMap(userList, SysUser::getPhoneNumber, SysUser::getUserId);
                        Long userId = phoneUserMap.get(serviceUser.getServiceUserPhone());
                        if(Objects.isNull(userId)) {
                            String nameAndPhone = StringUtils.isBlank(serviceUser.getServiceUserPhone()) ? serviceUser.getServiceUserName() :
                                    serviceUser.getServiceUserName() + "," + serviceUser.getServiceUserPhone();
                            resultList.add(new RowVerifyError<>(importModel.getSerialNumber(), importModel, Collections.singletonList(String.format("服务人员中%s未匹配到服务人员数据", nameAndPhone))));
                            importModel.setConnectionId(null);
                        } else {
                            serviceUserIds.add(userId);
                        }
                    }
                }
            }
            if(Objects.nonNull(importModel.getConnectionId())) {
                importModel.setServiceUserIdList(serviceUserIds);
                connectionServiceList.add(importModel);
            }
        }
        return resultList;
    }

    /**
     * 截取所有服务人员的姓名
     * @param exportList
     * @return
     */
    private Set<String> splitServiceUserName(List<ConnectionImportModel> exportList) {
        // 拆分服务人员
        Set<String> directLeaderPhoneSet = new LinkedHashSet<>();
        for(ConnectionImportModel userImportModel : exportList) {
            // 获取直属主管名称
            List<ConnectionImportModel.ServiceUser> serviceUserList = new ArrayList<>();
            String serviceUser = userImportModel.getServiceUser();
            if(StringUtils.isBlank(serviceUser)) {
                continue;
            }
            //替换
            serviceUser = serviceUser.replaceAll("，", ",");

            //截取
            String[] directLeaderArray = serviceUser.split("、");
            for(String direct : directLeaderArray) {
                String[] phoneArray = direct.split(",");
                ConnectionImportModel.ServiceUser directLeaderStr = new ConnectionImportModel.ServiceUser();
                if(phoneArray.length < 2) {
                    directLeaderStr.setServiceUserName(phoneArray[0]);
                } else {
                    directLeaderStr.setServiceUserName(phoneArray[0]);
                    directLeaderStr.setServiceUserPhone(phoneArray[1]);
                }
                directLeaderPhoneSet.add(phoneArray[0]);
                serviceUserList.add(directLeaderStr);
            }

            userImportModel.setServiceUserList(serviceUserList);
        }
        return directLeaderPhoneSet;
    }

    /**
     * 根据客户名称查询对应的人脉id列表
     * @param connectionNames
     * @return
     */
    private Map<String, List<Connection>> getConnectionInfo(List<String> connectionNames) {
        if(ListUtil.isEmpty(connectionNames)) {
            return ListUtil.emptyMap();
        }

        //根据客户名称查询所有的人脉id列表
        ConnectionQuery query = new ConnectionQuery();
        //查询条件拼接
        LambdaQueryWrapper<Connection> queryWrapper = Wrappers.lambdaQuery(Connection.class)
                .eq(Connection::getAuditStatus, FlowApprovalStatus.APPROVE.getValue())
                .in(Connection::getConnectionName, connectionNames);

        //根据客户名称查询对应权限下的人脉id列表
        List<Connection> connectionAllList = connectionMapper.selectByConnectionNames(query, queryWrapper);

        return ListUtil.toGroup(connectionAllList, Connection::getConnectionName);
    }


    /**
     * 导出错误数据
     * @param rowVerifyErrors 错误信息
     * @param importTaskResult
     * @param fileName 文件名
     * @param excelConfig
     */
    private void exportError(List<RowVerifyError<ConnectionExportErrorModel>> rowVerifyErrors,
                             ImportTaskResult importTaskResult,
                             String fileName,
                             ExcelSheetConfig<RowVerifyError<ConnectionExportErrorModel>> excelConfig) {
        rowVerifyErrors = RowVerifyError.mergeSameRowNoError(rowVerifyErrors);
        // 排序
        List<RowVerifyError<ConnectionExportErrorModel>> verifyErrors = rowVerifyErrors.stream()
                .sorted(Comparator.comparing(RowVerifyError::getRowNo))
                .collect(Collectors.toList());
        //导出错误
        MultiSheetExcelExporter excelExporter = new MultiSheetExcelExporter();
        excelExporter.addSheetConfig(verifyErrors, excelConfig, "人脉管理批量分配错误信息");
        byte[] bytes = excelExporter.exportExcelToByte("人脉管理");
        String file = this.ossService.uploadFileToOss(bytes, fileName);
        importTaskResult.setIsSuccess(false);
        importTaskResult.setError(rowVerifyErrors.size());
        importTaskResult.setErrorFileName(fileName);
        importTaskResult.setKey(file);
    }

    /**
     * 获取人脉管理-批量分配导出错误配置
     * @return 收票管理导出配置
     */
    private ExcelSheetConfig<RowVerifyError<ConnectionExportErrorModel>> getConnectionErrorConfig() {
        ExcelSheetConfig<RowVerifyError<ConnectionExportErrorModel>> config = new ExcelSheetConfig<>();
        config.setNeedSerialNumber(false)
                .addConfig(RowVerifyError::getErrorInfo, "错误信息提示")
                //设置颜色
                .appendStyleModifier(style -> style.setFillForegroundColor(IndexedColors.RED.index))
                // 设置导出信息
                .includeSubConfig(RowVerifyError::getErrorObject,this.getConnectionImportErrorConfig());
        return config;
    }

    /**
     * 获取人脉-批量分配导出配置
     *
     * @return 人脉-批量分配导出配置
     */
    public ExcelSheetConfig<ConnectionExportErrorModel> getConnectionImportErrorConfig() {
        ExcelSheetConfig<ConnectionExportErrorModel> config = new ExcelSheetConfig<>();
        config.setNeedSerialNumber(false)
                .addConfig(ConnectionExportErrorModel::getConnectionName, "*人脉姓名")
                .addConfig(ConnectionExportErrorModel::getPhone, "电话")
                .addConfig(ConnectionExportErrorModel::getServiceUser, "*服务人员");
        return config;
    }

    @Override
    public void execute(ConnectionImportQuery args) {
        importConnectionServiceUserInfo(args);
    }

    @Override
    public Class<ConnectionImportQuery> argsClass() {
        return ConnectionImportQuery.class;
    }
}
