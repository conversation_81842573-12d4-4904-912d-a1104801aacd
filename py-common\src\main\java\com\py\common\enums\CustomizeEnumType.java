package com.py.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 自定义列菜单类型
 * <AUTHOR>
 * @version CustomizeEnumType 2024/5/15 9:51
 */
@Getter
@AllArgsConstructor
public enum CustomizeEnumType implements IDict<Integer> {

    /** 资源 */
    RESOURCE(0, "资源"),
    /** 项目 */
    PROJECT(1, "项目"),
    /** 执行表单 */
    EXECUTE_THE_FORM(2, "执行表单"),
    ;

    private final Integer value;

    private final String label;
}
