package com.py.web.controller.system.hp;

import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.enums.BusinessType;
import com.py.system.shortcut.domain.dto.HpShortcutDTO;
import com.py.system.shortcut.domain.vo.HpShortcutVO;
import com.py.system.shortcut.service.IHpShortcutService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.py.common.core.domain.R;

import javax.annotation.Resource;

/**
 * 快捷方式Controller
 *
 * <AUTHOR>
 * @date 2023-08-09
 */
@Api(tags = "快捷方式")
@RestController
@RequestMapping("/shortcut")
public class HpShortcutController extends BaseController {

    /** 快捷方式服务 */
    @Resource
    private IHpShortcutService hpShortcutService;

    /**
     * 查询当前人下的快捷方式列表
     *
     * @return 快捷方式列表
     */
    @ApiOperation("查询快捷方式列表")
    @PreAuthorize("@ss.hasPermi('hp:shortcut:list')")
    @GetMapping("/listByUserId")
    public R<HpShortcutVO> listByUserId() {
        return R.success(hpShortcutService.listByUserId());
    }

    /**
     * 新增快捷方式
     *
     * @param dto 快捷方式修改参数
     * @return 是否成功
     */
    @ApiOperation("新增快捷方式")
    @PreAuthorize("@ss.hasPermi('hp:shortcut:add')")
    @Log(title = "快捷方式", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Boolean> add(@Validated @RequestBody HpShortcutDTO dto) {
        return R.success(hpShortcutService.insertHpShortcut(dto));
    }

}
