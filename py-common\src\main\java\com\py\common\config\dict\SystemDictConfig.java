package com.py.common.config.dict;

import com.google.common.base.CaseFormat;
import com.google.common.base.Converter;
import com.py.common.enums.IDict;
import com.py.common.utils.EnumUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字典配置
 * <AUTHOR>
 */
@Log4j2
@Component
public class SystemDictConfig {

    /** 系统字典Map */
    @Getter
    Map<String, Class<IDict<?>>> systemDictMap = new HashMap<>();

    public SystemDictConfig(Collection<AbstractDictModule> dictModules){
        Converter<String, String> converter = CaseFormat.UPPER_CAMEL.converterTo(CaseFormat.LOWER_UNDERSCORE);

        for(AbstractDictModule dictModule : dictModules) {
            dictModule.init();
            for(Class<IDict<?>> dictClass : dictModule.getDictList()) {
                if(this.systemDictMap.containsKey(dictClass.getSimpleName())) {
                    throw new IllegalArgumentException("字典配置初始化失败, 字典类重复注册：" + dictClass.getSimpleName());
                }
                this.verifySameValue(dictClass);

                String dictType = converter.convert(dictClass.getSimpleName());
                this.systemDictMap.put(dictType, dictClass);
            }
        }
    }

    /**
     * 验证字典类是否有相同的值
     * @param dictClass 字典类
     * @return true: 有相同的值
     */
    private void verifySameValue(Class<IDict<?>> dictClass) {
        List<IDict<?>> dictList = EnumUtils.getEnumConstants(dictClass);
        List<?> sameKeyList = ListUtil.sameKeyList(dictList, IDict::getValue);
        if(ListUtil.any(sameKeyList)){
            log.error("字典配置初始化失败, 字典类值重复：{}", StringUtils.convertStringList(sameKeyList, Object::toString));
            throw new IllegalArgumentException("字典配置初始化失败, 字典类值重复：" + dictClass.getSimpleName());
        }
    }

}
