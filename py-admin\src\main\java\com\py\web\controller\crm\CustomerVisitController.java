package com.py.web.controller.crm;
import com.github.pagehelper.PageInfo;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.crm.customer.customervisit.domian.vo.CustomerVisitVO;
import com.py.crm.customer.customervisit.service.ICustomerVisitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *<AUTHOR>
 *
 */
@Api(tags = "客户管理-客户拜访")
@RestController
@RequestMapping("/crm/customerVisit")
public class CustomerVisitController extends BaseController {
    /**客户管理-*/
    @Resource
    private ICustomerVisitService customerVisitService;

    /**
     *根据客户id分页查询客户
     * @param customerId 客户id
     * @return 客户管理-客户-客户拜访日志分页
     */
    @ApiOperation("分页查询客户管理-客户-客户拜访日志列表信息")
    @GetMapping("/pageCustomerVisit")
    public R<PageInfo<CustomerVisitVO>> pageCustomerVisit(Long customerId){
        PageInfo<CustomerVisitVO> voList = this.customerVisitService.pageCustomerVisitList(customerId);
        return R.success(voList);
    }

}
