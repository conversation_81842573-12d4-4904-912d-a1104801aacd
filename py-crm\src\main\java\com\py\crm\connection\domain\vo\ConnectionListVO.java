package com.py.crm.connection.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.core.domain.vo.IUpdateInfoVO;
import com.py.common.typehandler.impl.LongSetTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 人脉管理表列表视图模型
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
@ApiModel("人脉管理表列表视图模型")
public class ConnectionListVO implements IUpdateInfoVO {
    private static final long serialVersionUID = 1L;

    /** 自增id */
    @ApiModelProperty("自增id")
    private Long id;

    /** 人脉Id */
    @ApiModelProperty("人脉Id")
    private Long connectionId;

    /** 客户名称 */
    @ApiModelProperty("客户名称")
    private String connectionName;

    /** 状态（0无效；1有效） */
    @ApiModelProperty("状态（0无效；1有效）")
    private Integer status;

    /** 电话 */
    @ApiModelProperty("电话")
    private String phone;

    /** 微信 */
    @ApiModelProperty("微信")
    private String wechatNumber;

    /** 钉钉/飞书/其它 */
    @ApiModelProperty("钉钉/飞书/其它")
    private String otherNumber;

    /** 现任职企业id(客户id) */
    @ApiModelProperty("现任职企业id(客户id)")
    private Long customerId;

    /** 现任职企业（手动输入） */
    @ApiModelProperty("现任职企业（手动输入）")
    private String currentEmployer;

    /** 负责品牌/业务线 */
    @ApiModelProperty("负责品牌/业务线")
    private List<String> responsibleBrand;
    private String responsibleBrandStr;

    /** 行业类目(来源于数据字典) */
    @ApiModelProperty("行业类目(来源于数据字典)")
    @TableField(typeHandler = LongSetTypeHandler.class)
    private List<Long> industryCategory;
    @ApiModelProperty("行业类目(来源于数据字典)")
    private String industryCategoryStr;
    /** 所在部门 */
    @ApiModelProperty("所在部门")
    private String departmentName;

    /** 岗位名称 */
    @ApiModelProperty("岗位名称")
    private String postName;

    /** 对派芽信任度（数据字典） */
    @ApiModelProperty("对派芽信任度（数据字典）")
    private String pyTrustLevel;
    @ApiModelProperty("对派芽信任度（数据字典）")
    private String pyTrustLevelStr;
    /** 人脉地址 */
    @ApiModelProperty("人脉地址")
    private String connectionAddress;
    @ApiModelProperty("备注" )
    private String remark;
    /** 目标服务人员id */
    @ApiModelProperty("目标服务人员id")
    private Long serviceUserId;
    private List<Long> serviceUserIdList;
    @ApiModelProperty("目标服务人员")
    private String serviceUserName;

    /** 创建人部门 */
    @ApiModelProperty("创建人部门")
    private String createDept;
    @ApiModelProperty("录入人")
    private String createBy;
    @ApiModelProperty("创建人")
    private String createUser;

    private Long createId;

    /** 更新人部门 */
    @ApiModelProperty("更新人部门")
    private String updateDept;
    /** 更新者*/
    @ApiModelProperty("更新者")
    private Long updateId;

    /** 更新者*/
    @ApiModelProperty("更新者")
    private String updateBy;

    /** 更新时间*/
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 更新时间*/
    @ApiModelProperty("更录入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 是否有所有权限 true 所有 */
    @ApiModelProperty("是否有所有权限 true 所有 ")
    private Boolean isQuery;
}
