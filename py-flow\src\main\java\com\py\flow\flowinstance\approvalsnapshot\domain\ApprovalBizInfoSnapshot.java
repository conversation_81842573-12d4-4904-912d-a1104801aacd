package com.py.flow.flowinstance.approvalsnapshot.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.py.common.core.domain.BaseAddOnlyEntity;
import com.py.common.typehandler.AutoJsonTypeHandler;
import com.py.flow.domain.enums.ApprovalBizType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 审批业务详情快照
 * <AUTHOR>
 */
@Data
@TableName(value = "py_flow_approval_biz_info_snapshot", autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
public class ApprovalBizInfoSnapshot extends BaseAddOnlyEntity {

    /** 流程实例ID */
    @TableId(type = IdType.INPUT)
    private Long flowInstanceId;

    /** 审批业务ID */
    private ApprovalBizType bizType;

    /** 审批业务ID */
    private Long bizId;

    /** 业务详情 */
    private String info;
}
