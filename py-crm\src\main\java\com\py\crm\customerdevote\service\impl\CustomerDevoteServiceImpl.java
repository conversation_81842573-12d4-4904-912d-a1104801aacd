package com.py.crm.customerdevote.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.github.pagehelper.PageInfo;
import com.py.common.constant.DictConstant;
import com.py.common.core.domain.entity.SysDictData;
import com.py.common.enums.CustomerDevoteMenuTypeStatus;
import com.py.common.enums.ProjectStatus;
import com.py.common.exception.ServiceException;
import com.py.common.mybatisplus.ShowTableNameLambdaQueryWrapper;
import com.py.common.oss.IOssService;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.tools.multisheetexcelexporter.MultiSheetExcelExporter;
import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTask;
import com.py.common.utils.DateUtils;
import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.PageUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.JoinUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.compareddraft.converter.ComparedDraftConverter;
import com.py.crm.compareddraft.domain.ComparedDraft;
import com.py.crm.compareddraft.domain.dto.CustomerDevoteComparedDraftExportModel;
import com.py.crm.compareddraft.domain.vo.ComparedDraftListVO;
import com.py.crm.compareddraft.mapper.ComparedDraftMapper;
import com.py.crm.compareddraft.service.IComparedDraftService;
import com.py.crm.customer.converter.CustomerConverter;
import com.py.crm.customer.customercategory.domain.CustomerCategory;
import com.py.crm.customer.customercategory.service.ICustomerCategoryService;
import com.py.crm.customer.customercontribute.domain.ComparedDraftUser;
import com.py.crm.customer.customercontribute.service.IComparedDraftUserService;
import com.py.crm.customer.domain.SupCustomer;
import com.py.crm.customer.domain.vo.ComparedDraftMoneyVO;
import com.py.crm.customer.service.ICustomerService;
import com.py.crm.customerdevote.domain.dto.CrmProjectListExportVO;
import com.py.crm.customerdevote.domain.dto.CustomerDevoteBadDebtExportModel;
import com.py.crm.customerdevote.domain.query.CustomerDevoteQuery;
import com.py.crm.customerdevote.domain.vo.CloseCaseMoneyListVO;
import com.py.crm.customerdevote.domain.vo.CrmProjectListVO;
import com.py.crm.customerdevote.domain.vo.CustomerDevoteListVO;
import com.py.crm.customerdevote.domain.vo.CustomerDevoteTotal;
import com.py.crm.customerdevote.service.ICrmProjectService;
import com.py.crm.customerdevote.service.ICustomerDevoteBadDebtService;
import com.py.crm.customerdevote.service.ICustomerDevoteService;
import com.py.system.dict.service.ISysDictDataService;
import com.py.system.tools.reusableasynctask.domain.ExportTaskResult;
import com.py.system.tools.reusableasynctask.service.ITaskRecodeService;
import com.py.system.user.service.ISysUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.Adler32;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 客户贡献管理
 *
 * <AUTHOR>
 * @version CustomerDevoteServiceImpl 2023/8/9 11:23
 */
@Service
public class CustomerDevoteServiceImpl implements ICustomerDevoteService, ReusableAsyncTask<CustomerDevoteQuery> {
    /** 客户服务 */
    @Resource
    private ICustomerService customerService;
    @Resource
    private CustomerConverter customerConverter;
    /** 客户管理-比稿管理服务 */
    @Resource
    private IComparedDraftService comparedDraftService;

    /** 客户管理-比稿关联模型转换器 */
    @Resource
    private ComparedDraftConverter comparedDraftConverter;

    /** 客户管理-比稿策划人关联Service接口 */
    @Resource
    private IComparedDraftUserService comparedDraftUserService;

    /** 用户 业务层 */
    @Resource
    private ISysUserService userService;

    @Resource
    private ISysDictDataService dictDataService;

    /** 客户管理-客户与行业类目关联Service接口 */
    @Resource
    private ICustomerCategoryService customerCategoryService;

    /** 异步任务执行记录服务 */
    @Resource
    private ITaskRecodeService taskRecodeService;

    @Resource
    private IOssService ossService;

    @Resource
    private ICrmProjectService projectService;

    /** 收入坏账信息服务 */
    @Resource
    private ICustomerDevoteBadDebtService badDebtService;

    /** 字典 业务层 */
    @Resource
    private ISysDictDataService sysDictDataService;

    /** 客户贡献-比稿管理mapper*/
    @Resource
    private  ComparedDraftMapper comparedDraftMapper;

    /**
     * 查询客户贡献管理列表
     *
     * @param query 查询
     * @return 客户贡献管理列表
     */
    @Override
    public PageInfo<CustomerDevoteListVO> pageCustomerDevote(CustomerDevoteQuery query) {
        //合作时间、结案时间转换
        convertTime(query);

        //行业类目
        if (ListUtil.isNotEmpty(query.getIndustryCategoryIdList())){
            //根据行业类目id获取对应的客户id列表
            List<Long> customerIdList = this.customerCategoryService
                    .selectCustomerIdByCategoryIdList(query.getIndustryCategoryIdList());
            query.setCustomerIds(customerIdList);
        }

        PageUtils.startPage();
        //根据查询条件查询出客户id
        List<SupCustomer> list = customerService.getCustomerDevoteList(query);
        if (CollectionUtils.isEmpty(list)) {
            return ListUtil.emptyPage();
        }
        //客户id
        List<Long> customerIds = ListUtil.distinctMap(list, SupCustomer::getCustomerId);
        query.setCustomerIds(customerIds);
        //比稿金额 按时间和客户id传讯项目id(项目数量需要去除分页限制)
        PageUtils.startPage(0, 0, false);
        List<Long> projectIds = customerService.getCustomerDevoteProjectIdList(query);
        if (ListUtil.isEmpty(projectIds)){
            return ListUtil.emptyPage();
        }
        query.setProjectIds(projectIds);

        //获取已经结案的客户id列表
        List<Long> closedCustomerIds = projectService.selectClosedCustomerIds(query, customerIds);
        //获取金额数据列表
        List<CustomerDevoteListVO> customerDevoteList = getCustomerDevoteAmountList(query, list, projectIds, closedCustomerIds);
        //获取客户行业类目字典值
        List<SysDictData> dataList = this.dictDataService.selectDictDataByType("contribution_industry_category");
        Map<String, String> dictDataMap = ListUtil.toMap(dataList, SysDictData::getDictValue, SysDictData::getDictLabel);

        // 行业类目
        List<CustomerCategory> customerCategoryList = customerCategoryService.selectCustomerCategory(query.getCustomerIds());
        if(ListUtil.isNotEmpty(customerCategoryList)){
            JoinUtils.oneByMany(customerDevoteList, customerCategoryList,
                    CustomerDevoteListVO::getCustomerId, CustomerCategory::getCustomerId,
                    (customer, categoryList) -> {
                        List<Long> industryCategoryIdList = ListUtil.distinctMap(categoryList, CustomerCategory::getIndustryCategoryId);
                        if(ListUtil.isNotEmpty(industryCategoryIdList)) {
                            List<String> categoryNameList = ListUtil.emptyList();
                            for (Long categoryId : customer.getIndustryCategoryIdList()) {
                                String categoryName = dictDataMap.get(String.valueOf(categoryId));
                                if(StringUtils.isNotBlank(categoryName)) {
                                    categoryNameList.add(categoryName);
                                }
                            }
                            customer.setIndustryCategoryName(String.join(",", categoryNameList));
                        }
                    });
        }
        return ListUtil.pageConvert(list, customerDevoteList);
    }

    /**
     * 获取比稿金额
     * @param query 客户id列表-结案时间-合作时间
     * @return <客户id,比稿总金额>
     */
    private Map<Long, BigDecimal> getCustomerDevoteMap(CustomerDevoteQuery query) {
        //获取比稿金额
        List<ComparedDraftMoneyVO> comparedDraftMoneyList = customerService.getCustomerDevoteProjectList(query);
        Map<Long, BigDecimal> comparedDraftMoneyMap;
        if(ListUtil.isNotEmpty(comparedDraftMoneyList)) {
            comparedDraftMoneyMap = ListUtil.toMap(comparedDraftMoneyList, ComparedDraftMoneyVO::getCustomerId, ComparedDraftMoneyVO::getComparedDraftMoneySum);
        } else {
            comparedDraftMoneyMap = new HashMap<>(0);
        }
        return comparedDraftMoneyMap;
    }

    /**
     * 获取金额数据
     * @param query
     * @param list
     * @param projectIds
     * @param closedCustomerIds 已经结案的客户id列表
     * @return
     */
    public List<CustomerDevoteListVO> getCustomerDevoteAmountList(CustomerDevoteQuery query,
                                                                  List<SupCustomer> list,
                                                                  List<Long> projectIds,
                                                                  List<Long> closedCustomerIds) {
        //获取比稿金额-根据客户id,合作时间,结案时间查询
        Map<Long, BigDecimal> comparedDraftMoneyMap = getCustomerDevoteMap(query);

        //获取所有的客户有比稿明细的客户id列表
        List<Long> existedDraftCustomerIds = comparedDraftService.listExistedCustomerIds(query.getCustomerIds());
        //结案金额
        List<CloseCaseMoneyListVO> closeCaseMoneyList = customerService.getCloseCaseMoneyMap(projectIds);
        Map<Long, BigDecimal> closeCaseMap = ListUtil.toMapIgnoreNull(closeCaseMoneyList, CloseCaseMoneyListVO::getCustomerId, CloseCaseMoneyListVO::getCloseCaseMoney);
        Map<Long, BigDecimal> marginMap = ListUtil.toMapIgnoreNull(closeCaseMoneyList, CloseCaseMoneyListVO::getCustomerId, CloseCaseMoneyListVO::getMargin);
        //收入坏账金额
        List<CustomerDevoteListVO> badeMoneyList = customerService.getBadeMoneyMap(query);
        Map<Long, BigDecimal> badeMoneyMap = ListUtil.toMap(badeMoneyList, CustomerDevoteListVO::getCustomerId, CustomerDevoteListVO::getBadDebtMoney);

        //根据客户id获取对应的行业类目名称
        List<Long> customerIdList = ListUtil.map(list, SupCustomer::getCustomerId);
        List<CustomerCategory> customerCategoryList = this.customerCategoryService.selectCustomerCategory(customerIdList);
        Map<Long, List<Long>> map = ListUtil.toGroup(customerCategoryList, CustomerCategory::getCustomerId,CustomerCategory::getIndustryCategoryId);

        //利润率
        List<CustomerDevoteListVO> customerDevoteList = this.customerConverter.toCustomerDevoteListVoByEntity(list);
        customerDevoteList.forEach(customerDevote -> {
            Long customerId = customerDevote.getCustomerId();
            //获取客户行业类目
            if (map.get(customerId) != null){
                customerDevote.setIndustryCategoryIdList(ListUtil.distinctMap(map.get(customerId),item->item));
            }
            //收入坏账金额
            BigDecimal badDebtMoney = badeMoneyMap.get(customerId);
            if (Objects.nonNull(badDebtMoney)) {
                customerDevote.setBadDebtMoney(badDebtMoney);
            }
            //结案金额
            BigDecimal closeCaseMoney = closeCaseMap.get(customerId);
            if (Objects.nonNull(closeCaseMoney)) {
                customerDevote.setCloseCaseMoney(closeCaseMoney);
            }
            //利润率
            BigDecimal profitMargin = marginMap.get(customerId);
            if (profitMargin != null){
                customerDevote.setProfitMargin(profitMargin.multiply(new BigDecimal(100))
                        .setScale(2, RoundingMode.HALF_UP));
            }
            //比稿金额赋值
            customerDevote.setComparedDraftMoney(comparedDraftMoneyMap.get(customerId));

            //校验是否有按钮的权限
            //比稿明细
            customerDevote.setHasComparedDraft(existedDraftCustomerIds.contains(customerId));
            //项目明细
            customerDevote.setHasProject(true);
            //结案明细
            customerDevote.setHasCase(closedCustomerIds.contains(customerId));
            //坏账明细
            customerDevote.setHasDebt(Objects.nonNull(badDebtMoney));
        });
        return customerDevoteList;
    }
    /**
     * 勾选合计
     * @param query 客户贡献管理查询模型
     * @return 客户贡献管理统计
     */
    @Override
    public CustomerDevoteTotal getCustomerDevoteTotal(CustomerDevoteQuery query) {
        //非全选,选中的客户id
        List<Long> chooseCustomerIds = query.getCustomerIds();
        //合作时间、结案时间转换
        convertTime(query);
        //行业类目查询条件过滤客户id
        if (ListUtil.isNotEmpty(query.getIndustryCategoryIdList())){
            List<Long> customerIdList = this.customerCategoryService
                    .selectCustomerIdByCategoryIdList(query.getIndustryCategoryIdList());
            query.setCustomerIds(customerIdList);
        }
        //根据查询条件查询出客户id
        List<SupCustomer> list = customerService.getCustomerDevoteList(query);
        if (CollectionUtils.isEmpty(list)) {
            return new CustomerDevoteTotal(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }
        if (query.getIsSelectAll()) {
            //全选使用查询条件的客户id
            List<Long> customerIds = ListUtil.distinctMap(list, SupCustomer::getCustomerId);
            query.setCustomerIds(customerIds);
            //比稿金额 按时间和客户id传讯项目id
        } else {
            if (CollectionUtils.isEmpty(chooseCustomerIds)) {
                throw new ServiceException("请至少选中一条");
            }
            //不是全选,使用选中的客户id
            query.setCustomerIds(chooseCustomerIds);
            //比稿金额 按时间和客户id传讯项目id
        }
        return setCustomerDevoteTotal(query, list);
    }

    /**
     * 选中的客户贡献
     * @param query 客户贡献管理查询模型
     * @return 客户贡献管理
     */
    @Override
    public List<CustomerDevoteListVO> listCustomerDevote(CustomerDevoteQuery query) {
        //行业类目查询条件过滤客户id
        if (ListUtil.isNotEmpty(query.getIndustryCategoryIdList())){
            List<Long> customerIdList = this.customerCategoryService
                    .selectCustomerIdByCategoryIdList(query.getIndustryCategoryIdList());
            query.setCustomerIds(customerIdList);
        }
        List<SupCustomer> list = customerService.getCustomerDevoteList(query);
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("请至少选中一条");
            }
        if (query.getIsSelectAll()) {

            List<Long> customerIds = ListUtil.distinctMap(list, SupCustomer::getCustomerId);
            query.setCustomerIds(customerIds);
            //比稿金额 按时间和客户id传讯项目id
        } else {
            if (CollectionUtils.isEmpty(query.getCustomerIds())) {
                throw new ServiceException("请至少选中一条");
            }
            //比稿金额 按时间和客户id传讯项目id
        }
        List<Long> projectIds = customerService.getCustomerDevoteProjectIdList(query);
        if (CollectionUtils.isEmpty(projectIds)){
            return ListUtil.emptyList();
        }
        query.setProjectIds(projectIds);
        return getCustomerDevoteAmountList(query, list, projectIds, query.getCustomerIds());
    }

    /**
     * 客户贡献表头
     * @return 模版
     */
    @Override
    public ExcelSheetConfig<CustomerDevoteListVO> getCustomerDevoteConfig() {

        ExcelSheetConfig<CustomerDevoteListVO> config = new ExcelSheetConfig<>();
        config.setNeedSerialNumber(false)
                .addConfig(CustomerDevoteListVO::getName,"客户名称")
                .addConfig(CustomerDevoteListVO::getLineBusiness,"品牌/业务线")
                .addConfig(CustomerDevoteListVO::getIndustryCategoryName,"行业类目")
                .addConfig(CustomerDevoteListVO::getComparedDraftMoneyStr,"比稿金额(元)")
                .addConfig(item->StringUtils.formatBigDecimal(item.getTransactionMoney()),"成交金额(元)")
                .addConfig(item->StringUtils.formatBigDecimal(item.getCloseCaseMoney()),"结案金额(元)")
                .addConfig(item->StringUtils.formatBigDecimal(item.getBadDebtMoney()),"收入坏账金额(元)")
                .addConfig(item->StringUtils.formatBigDecimal(item.getProfitMargin()),"利润率(%)");
        return config;
    }

    /**
     * 比稿
     * @return 模版
     */
    @Override
    public ExcelSheetConfig<CustomerDevoteComparedDraftExportModel> getComparedDraftConfig() {
        ExcelSheetConfig<CustomerDevoteComparedDraftExportModel> config = new ExcelSheetConfig<>();
        config.setNeedSerialNumber(false)
                .addConfig(CustomerDevoteComparedDraftExportModel::getCustomerName,"客户名称")
                .addConfig(CustomerDevoteComparedDraftExportModel::getCustomerCooperateName,"客户合作主体")
                .addConfig(CustomerDevoteComparedDraftExportModel::getBrandName,"品牌/业务线")
                .addConfig(CustomerDevoteComparedDraftExportModel::getComparedDraftName,"比稿项目名称")
                .addConfig(CustomerDevoteComparedDraftExportModel::getCommercialBidFruitsStr,"商务标比稿结果")
                .addConfig(CustomerDevoteComparedDraftExportModel::getTechniqueBidFruitsStr,"技术标比稿结果")
                .addConfig(CustomerDevoteComparedDraftExportModel::getFinalFruitsStr,"最终比稿结果")
                .addConfig(CustomerDevoteComparedDraftExportModel::getComparedDraftMoneyStr,"比稿金额(元)")
                .addConfig(item->StringUtils.formatBigDecimal(item.getTurnoverMoney()),"成交金额(元)")
                .addConfig(CustomerDevoteComparedDraftExportModel::getPyTypeStr,"派芽业务类型")
                .addConfig(CustomerDevoteComparedDraftExportModel::getPlanTypeStr,"方案类型")
                .addConfig(CustomerDevoteComparedDraftExportModel::getUpdateBy,"比稿人")
                .addConfig(CustomerDevoteComparedDraftExportModel::getPlotterName,"策划人")
                .addConfig(CustomerDevoteComparedDraftExportModel::getComparedDraftTimeStr,"比稿时间")
                .addConfig(CustomerDevoteComparedDraftExportModel::getEnteringTimeStr,"录入时间")
                ;
        return config;
    }

    /**
     * 项目明细、结案明细
     * @return 模版
     */
    @Override
    public ExcelSheetConfig<CrmProjectListExportVO> getCrmProjectConfig() {
        ExcelSheetConfig<CrmProjectListExportVO> config = new ExcelSheetConfig<>();
        config.setNeedSerialNumber(false)
                .addConfig(CrmProjectListExportVO::getProjectName,"项目名称")
                .addConfig(CrmProjectListExportVO::getProjectStatusStr,"项目状态")
                .addConfig(CrmProjectListExportVO::getProjectAmountStr,"立项项目金额(元)")
                .addConfig(item->StringUtils.formatBigDecimal(item.getGrossMargin()),"立项毛利率预估(%)")
                .addConfig(item->StringUtils.formatBigDecimal(item.getFinishProjectIncome()),"已完成项目收入(元)")
                .addConfig(item->StringUtils.formatBigDecimal(item.getFinishProjectCost()),"已完成项目成本(元)")
                .addConfig(item->StringUtils.formatBigDecimal(item.getFinishProjectProfit()),"已完成项目利润(元)")
                .addConfig(item->StringUtils.formatBigDecimal(item.getFinishProjectGrossMargin()),"已完成项目毛利率(%)")
                .addConfig(CrmProjectListExportVO::getCustomerName,"客户名称")
                .addConfig(CrmProjectListExportVO::getCustomerMainstayName,"客户合作主体")
                .addConfig(CrmProjectListExportVO::getLineBusiness,"品牌/业务线")
                .addConfig(CrmProjectListExportVO::getMainstayName,"派芽合作主体")
                .addConfig(CrmProjectListExportVO::getCreateBy,"立项人")
                .addConfig(CrmProjectListExportVO::getCreateDept,"立项人部门")
                .addConfig(CrmProjectListExportVO::getProjectApprovalTime,"立项时间")
                .addConfig(CrmProjectListExportVO::getProjectStartTime,"项目开始时间")
                .addConfig(CrmProjectListExportVO::getExpectedEndTime,"预计结束时间")
                .addConfig(CrmProjectListExportVO::getCloseCaseTime,"结案时间")
        ;
        return config;
    }

    /**
     * 坏账
     * @return 模版
     */
    @Override
    public ExcelSheetConfig<CustomerDevoteBadDebtExportModel> getBadDebtConfig() {
        ExcelSheetConfig<CustomerDevoteBadDebtExportModel> config = new ExcelSheetConfig<>();
        config.setNeedSerialNumber(false)
                .addConfig(item->StringUtils.formatBigDecimal(item.getTotalBadDebtAmount()),"历史坏账金额（元）")
                .addConfig(CustomerDevoteBadDebtExportModel::getProjectName,"项目名称")
                .addConfig(CustomerDevoteBadDebtExportModel::getPyMainstayName,"派芽合作主体")
                .addConfig(CustomerDevoteBadDebtExportModel::getCustomerName,"客户名称")
                .addConfig(CustomerDevoteBadDebtExportModel::getCustomerMainstayName,"客户合作主体")
                .addConfig(CustomerDevoteBadDebtExportModel::getBrandLine,"品牌/业务线")
        ;
        return config;
    }


    /**
     * 请求并设置合计值
     * @param query 客户贡献管理查询模型
     * @param list 客户对象
     * @return 客户贡献管理统计
     */
    public CustomerDevoteTotal setCustomerDevoteTotal(CustomerDevoteQuery query, List<SupCustomer> list){
        List<Long> projectIds = customerService.getCustomerDevoteProjectIdList(query);
        if (CollectionUtils.isEmpty(projectIds)) {
            return new CustomerDevoteTotal(BigDecimal.ZERO,BigDecimal.ZERO,BigDecimal.ZERO,BigDecimal.ZERO, BigDecimal.ZERO );
        }
        query.setProjectIds(projectIds);
        //获取比稿金额-根据客户id,合作时间,结案时间查询
        Map<Long, BigDecimal> comparedDraftMoneyMap = getCustomerDevoteMap(query);
        //结案金额
        List<CloseCaseMoneyListVO> closeCaseMoneyList = customerService.getCloseCaseMoneyMap(projectIds);
        Map<Long, BigDecimal> marginMap = ListUtil.toMapIgnoreNull(closeCaseMoneyList, CloseCaseMoneyListVO::getCustomerId, CloseCaseMoneyListVO::getMargin);
        Map<Long, BigDecimal> closeCaseMap = ListUtil.toMapIgnoreNull(closeCaseMoneyList, CloseCaseMoneyListVO::getCustomerId, CloseCaseMoneyListVO::getCloseCaseMoney);

        //收入坏账金额
        List<CustomerDevoteListVO> badeMoneyList = customerService.getBadeMoneyMap(query);
        Map<Long,BigDecimal> badeMoneyMap =  ListUtil.toMap(badeMoneyList, CustomerDevoteListVO::getCustomerId, CustomerDevoteListVO::getBadDebtMoney);

        //利润率
        List<CustomerDevoteListVO> customerDevoteList = this.customerConverter.toCustomerDevoteListVoByEntity(list);
        BigDecimal profitMargin = null;
        BigDecimal transactionMoney = null;
        for (CustomerDevoteListVO customerDevoteListVO : customerDevoteList) {

            BigDecimal margin = marginMap.get(customerDevoteListVO.getCustomerId());
            if (margin != null) {
                profitMargin = NullMergeUtils.nullMerge(profitMargin,BigDecimal.ZERO).add(margin);
            }
            //成本
            if (customerDevoteListVO.getTransactionMoney() != null){
                transactionMoney = NullMergeUtils.nullMerge(transactionMoney,BigDecimal.ZERO)
                        .add(customerDevoteListVO.getTransactionMoney());
            }
        }
        //坏账
        BigDecimal badDebtMoney = null;
        if (CollectionUtils.isNotEmpty(badeMoneyMap)){
            for (BigDecimal value : badeMoneyMap.values()) {
                badDebtMoney = NullMergeUtils.nullMerge(badDebtMoney,BigDecimal.ZERO).add(value);
            }
        }
        //结案
        BigDecimal closeCaseMoney = null;
        if (CollectionUtils.isNotEmpty(closeCaseMap)){
            for (BigDecimal value : closeCaseMap.values()) {
                closeCaseMoney = NullMergeUtils.nullMerge(closeCaseMoney,BigDecimal.ZERO).add(value);
            }
        }
        //比稿
        BigDecimal compareDraftMoney = null;
        if (CollectionUtils.isNotEmpty(comparedDraftMoneyMap)){
            for (BigDecimal value : comparedDraftMoneyMap.values()) {
                compareDraftMoney = NullMergeUtils.nullMerge(compareDraftMoney,BigDecimal.ZERO).add(value);
            }
        }

        //所有毛利率/客户数量(可能有顾客没有毛利率)
        if (profitMargin != null){
            profitMargin= profitMargin.divide(new BigDecimal(customerDevoteList.size()), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        return new CustomerDevoteTotal(compareDraftMoney, transactionMoney, closeCaseMoney, badDebtMoney, profitMargin);

    }
    /**
     * 比稿明细
     * @param query 客户贡献管理查询模型
     * @return  比稿列表
     */
    @Override
    public PageInfo<ComparedDraftListVO> listComparedDraft(CustomerDevoteQuery query) {
        //合作时间,结案时间转换
        convertTime(query);
        ShowTableNameLambdaQueryWrapper<ComparedDraft> queryWrapper = new ShowTableNameLambdaQueryWrapper<>(ComparedDraft.class);
        queryWrapper.eq(ComparedDraft::getCustomerId,query.getCustomerId())
                    .orderByDesc(ComparedDraft::getEnteringTime);
        queryWrapper.setTableAlias("draft");
        PageUtils.startPage();
        List<ComparedDraft> comparedDraftList;
        //没有合作时间,结案时间就查客户的比稿信息,否则就关联项目查询比稿信息
        if (StringUtils.isBlank(query.getCooperateBeginTime()) && StringUtils.isBlank(query.getCooperateEndTime())
        && StringUtils.isBlank(query.getCloseCaseBeginTime()) && StringUtils.isBlank(query.getCloseCaseEndTime())){
            comparedDraftList = comparedDraftMapper.listComparedDraftByCustomerId(queryWrapper);
        }else {
            comparedDraftList = comparedDraftMapper.listComparedDraftByContribute(query,queryWrapper);
        }
        List<ComparedDraftListVO> comparedDraftVoList = comparedDraftConverter.toListVoByEntity(comparedDraftList);
        if(ListUtil.isEmpty(comparedDraftVoList)){
            return ListUtil.emptyPage();
        }
        List<Long> comparedDraftIdList = ListUtil.distinctMap(comparedDraftVoList, ComparedDraftListVO::getComparedDraftId);
        List<ComparedDraftUser> comparedDraftUserList = comparedDraftUserService.listComparedDraftIds(comparedDraftIdList);
        // 将策划人的名加入出参
        JoinUtils.oneByMany(comparedDraftVoList,comparedDraftUserList
                ,ComparedDraftListVO::getComparedDraftId,ComparedDraftUser::getComparedDraftId
                , (comparedDraftVO,comparedDraftUser) -> {
                    List<String> plotterNameList = ListUtil.distinctMap(comparedDraftUser, ComparedDraftUser::getUserName);
                    comparedDraftVO.setPlotterNameList(plotterNameList);
                });
        //派芽业务类型
        List<SysDictData> pyTypeList = dictDataService.selectDictDataByType(DictConstant.PY_TYPE);
        Map<String,String> pyTypeMap = ListUtil.toMap(pyTypeList,SysDictData::getDictValue,SysDictData::getDictLabel);
        List<SysDictData> planTypeList = dictDataService.selectDictDataByType(DictConstant.PLAN_TYPE);
        Map<String,String> planTypeMap = ListUtil.toMap(planTypeList,SysDictData::getDictValue,SysDictData::getDictLabel);
        comparedDraftVoList.forEach(s->{
            if (Objects.nonNull(s.getPyType())){
                s.setPyTypeStr(pyTypeMap.get(s.getPyType().toString()));
            }
            if (Objects.nonNull(s.getPlanType())){
                s.setPlanTypeStr(planTypeMap.get(s.getPlanType().toString()));
            }
        });
        userService.relatedUpdateInfo(comparedDraftVoList);
        return ListUtil.pageConvert(comparedDraftList,comparedDraftVoList);
    }

    /**
     * 导出比稿明细
     * @param query 客户贡献管理查询模型
     * @return 客户贡献比稿下载列表
     */
    @Override
    public List<CustomerDevoteComparedDraftExportModel> queryComparedDraftList(CustomerDevoteQuery query) {
        //合作时间,结案时间转换
        convertTime(query);
        ShowTableNameLambdaQueryWrapper<ComparedDraft> queryWrapper = new ShowTableNameLambdaQueryWrapper<>(ComparedDraft.class);
        queryWrapper.setTableAlias("draft");
        queryWrapper.eq(ComparedDraft::getCustomerId,query.getCustomerId());
        if(query.getIsSelectAll()){
            queryWrapper.notIn(CollectionUtils.isNotEmpty(query.getComparedDraftIds()),ComparedDraft::getComparedDraftId,query.getComparedDraftIds());
        }else{
            if (CollectionUtils.isEmpty(query.getComparedDraftIds())){
                throw new ServiceException("请至少选中一条记录");
            }
            queryWrapper.in(ComparedDraft::getComparedDraftId,query.getComparedDraftIds());
        }
        queryWrapper.orderByDesc(ComparedDraft::getEnteringTime);
        List<ComparedDraft> comparedDraftList;
        //没有合作时间,结案时间就差客户的比稿金额,否则就关联项目
        if (StringUtils.isBlank(query.getCooperateBeginTime()) && StringUtils.isBlank(query.getCooperateEndTime())
        && StringUtils.isBlank(query.getCloseCaseBeginTime()) && StringUtils.isBlank(query.getCloseCaseEndTime())){
            comparedDraftList = comparedDraftMapper.listComparedDraftByCustomerId(queryWrapper);
        }else {
            comparedDraftList = comparedDraftMapper.listComparedDraftByContribute(query,queryWrapper);
        }
        List<CustomerDevoteComparedDraftExportModel> comparedDraftExport = comparedDraftConverter.toCustomerDevoteExportModelByEntity(comparedDraftList);
        if(ListUtil.isEmpty(comparedDraftExport)){
           return ListUtil.emptyList();
        }
        List<Long> comparedDraftIdList = ListUtil.distinctMap(comparedDraftExport, CustomerDevoteComparedDraftExportModel::getComparedDraftId);
        List<ComparedDraftUser> comparedDraftUserList = comparedDraftUserService.listComparedDraftIds(comparedDraftIdList);
        // 将策划人的名加入出参
        JoinUtils.oneByMany(comparedDraftExport,comparedDraftUserList
                ,CustomerDevoteComparedDraftExportModel::getComparedDraftId,ComparedDraftUser::getComparedDraftId
                , (comparedDraftExportModel,comparedDraftUser) -> {
                    List<String> plotterNameList = ListUtil.distinctMap(comparedDraftUser, ComparedDraftUser::getUserName);
                    comparedDraftExportModel.setPlotterNameList(plotterNameList);
                });
        //派芽业务类型
        List<SysDictData> pyTypeList = dictDataService.selectDictDataByType(DictConstant.PY_TYPE);
        Map<String,String> pyTypeMap = ListUtil.toMap(pyTypeList,SysDictData::getDictValue,SysDictData::getDictLabel);
        List<SysDictData> planTypeList = dictDataService.selectDictDataByType(DictConstant.PLAN_TYPE);
        Map<String,String> planTypeMap = ListUtil.toMap(planTypeList,SysDictData::getDictValue,SysDictData::getDictLabel);
        comparedDraftExport.forEach(s->{
            if (CollectionUtils.isNotEmpty(s.getPlotterNameList())){
                s.setPlotterName(String.join(",", s.getPlotterNameList()));
            }
            if (Objects.nonNull(s.getPyType())){
                s.setPyTypeStr(pyTypeMap.get(s.getPyType().toString()));
            }
            if (Objects.nonNull(s.getPlanType())){
                s.setPlanTypeStr(planTypeMap.get(s.getPlanType().toString()));
            }
            s.setComparedDraftMoneyStr(s.getComparedDraftMoney() == null
                    ? "--" : StringUtils.formatBigDecimal(s.getComparedDraftMoney()));
            s.setTurnoverMoneyStr(s.getTurnoverMoney() == null
                    ? "--" : StringUtils.formatBigDecimal(s.getTurnoverMoney()));
        });
        userService.relatedUpdateInfo(comparedDraftExport);
        return comparedDraftExport;
    }

    /**
     * 导出比稿明细
     *
     * @param query 导出查询参数
     * @return 导出数据
     */
    @Override
    public void exportComparedDraft(CustomerDevoteQuery query) {
        taskRecodeService.editTaskBegin(query.getTaskId());
        ExportTaskResult exportTaskResult = new ExportTaskResult();

        try {
            //根据客户id查询比稿明细列表
            List<CustomerDevoteComparedDraftExportModel> exportList = queryComparedDraftList(query);
            ExcelUtil<CustomerDevoteComparedDraftExportModel> util = new ExcelUtil<>(CustomerDevoteComparedDraftExportModel.class);

            //文件名称
            StringBuilder fileNameBuilder = new StringBuilder("比稿明细-")
                    //客户名称
                    .append(getCustomerNameById(query.getCustomerId()))
                    .append("-")
                    .append(DateUtils.format(LocalDateTime.now(), DateUtils.YYYY_MM_DD_COLON_HH_MM_SS))
                    .append(".xlsx");
            File file = util.exportExcelFile(exportList, "比稿明细");

            FileInputStream input = new FileInputStream(file);
            OssUploadResult upload = ossService.upload(input, fileNameBuilder.toString(), true);
            input.close();

            exportTaskResult.setFileName(fileNameBuilder.toString());
            exportTaskResult.setIsSuccess(true);
            exportTaskResult.setKey(upload.getOssKey());
        } catch (Exception e) {
            e.printStackTrace();
            exportTaskResult.setIsSuccess(false);
        }
        taskRecodeService.taskExportCompleted(query.getTaskId(), exportTaskResult);
    }

    /**
     * 导出项目明细
     *
     * @param query 导出查询参数
     * @return 导出数据
     */
    @Override
    public void exportProject(CustomerDevoteQuery query) {
        taskRecodeService.editTaskBegin(query.getTaskId());
        ExportTaskResult exportTaskResult = new ExportTaskResult();

        try {
            //根据客户id查询项目明细列表
            List<CrmProjectListVO> projectListVOList = this.projectService.pageProjectList(query).getList();
            List<CrmProjectListExportVO> exportList = this.customerConverter.toCrmProjectList(projectListVOList);
            exportList.forEach(model->{
                if (model.getProjectAmount() != null){
                    model.setProjectAmountStr(StringUtils.formatBigDecimal(model.getProjectAmount()));
                }
                if (model.getGrossMargin() != null){
                    model.setGrossMarginStr(StringUtils.formatBigDecimal(model.getGrossMargin()));
                }
                if (model.getFinishProjectProfit() != null){
                    model.setFinishProjectProfitStr(StringUtils.formatBigDecimal(model.getFinishProjectProfit()));
                }
                if (model.getFinishProjectCost() != null){
                    model.setFinishProjectCostStr(StringUtils.formatBigDecimal(model.getFinishProjectCost()));
                }
                if (model.getFinishProjectGrossMargin() != null){
                    model.setFinishProjectGrossMarginStr(StringUtils.formatBigDecimal(model.getFinishProjectGrossMargin()));
                }
                if (model.getFinishProjectIncome() != null){
                    model.setFinishProjectIncomeStr(StringUtils.formatBigDecimal(model.getFinishProjectIncome()));
                }
            });
            ExcelUtil<CrmProjectListExportVO> util = new ExcelUtil<>(CrmProjectListExportVO.class);

            //文件名称
            StringBuilder fileNameBuilder = new StringBuilder("项目明细-")
                    //客户名称
                    .append(getCustomerNameById(query.getCustomerId()))
                    .append("-")
                    .append(DateUtils.format(LocalDateTime.now(), DateUtils.YYYY_MM_DD_COLON_HH_MM_SS))
                    .append(".xlsx");
            File file = util.exportExcelFile(exportList, "项目明细");

            FileInputStream input = new FileInputStream(file);
            OssUploadResult upload = ossService.upload(input, fileNameBuilder.toString(), true);
            input.close();

            exportTaskResult.setFileName(fileNameBuilder.toString());
            exportTaskResult.setIsSuccess(true);
            exportTaskResult.setKey(upload.getOssKey());
        } catch (Exception e) {
            e.printStackTrace();
            exportTaskResult.setIsSuccess(false);
        }
        taskRecodeService.taskExportCompleted(query.getTaskId(), exportTaskResult);
    }

    /**
     * 导出坏账明细
     *
     * @param query 导出查询参数
     * @return 导出数据
     */
    @Override
    public void exportBadDebt(CustomerDevoteQuery query) {
        taskRecodeService.editTaskBegin(query.getTaskId());
        ExportTaskResult exportTaskResult = new ExportTaskResult();

        try {

            //根据客户id查询坏账明细列表
            List<CustomerDevoteBadDebtExportModel> exportList = this.badDebtService.exportBadDebt(query);
            exportList.forEach(item->{
                item.setTotalBadDebtAmountStr(StringUtils.formatBigDecimal(item.getTotalBadDebtAmount()));
            });
            ExcelUtil<CustomerDevoteBadDebtExportModel> util = new ExcelUtil<>(CustomerDevoteBadDebtExportModel.class);

            StringBuilder fileNameBuilder = new StringBuilder("坏账明细-")
                    //客户名称
                    .append(getCustomerNameById(query.getCustomerId()))
                    .append("-")
                    .append(DateUtils.format(LocalDateTime.now(), DateUtils.YYYY_MM_DD_COLON_HH_MM_SS))
                    .append(".xlsx");

            File file = util.exportExcelFile(exportList, "坏账明细");

            FileInputStream input = new FileInputStream(file);
            OssUploadResult upload = ossService.upload(input, fileNameBuilder.toString(), true);
            input.close();

            exportTaskResult.setFileName(fileNameBuilder.toString());
            exportTaskResult.setIsSuccess(true);
            exportTaskResult.setKey(upload.getOssKey());
        } catch (Exception e) {
            e.printStackTrace();
            exportTaskResult.setIsSuccess(false);
        }
        taskRecodeService.taskExportCompleted(query.getTaskId(), exportTaskResult);
    }

    /**
     * 导出结案明细
     *
     * @param query 导出查询参数
     */
    @Override
    public void exportCloseCaseProject(CustomerDevoteQuery query) {
        taskRecodeService.editTaskBegin(query.getTaskId());
        ExportTaskResult exportTaskResult = new ExportTaskResult();

        try {
            //根据客户id查询项目明细列表
            List<CrmProjectListExportVO> exportList = this.projectService.exportCustomerDevoteCloseCase(query);
            exportList.forEach(model->{
                if (model.getProjectAmount() != null){
                    model.setProjectAmountStr(StringUtils.formatBigDecimal(model.getProjectAmount()));
                }
                if (model.getGrossMargin() != null){
                    model.setGrossMarginStr(StringUtils.formatBigDecimal(model.getGrossMargin()));
                }
                model.setFinishProjectIncomeStr(model.getFinishProjectIncomeStr());
                model.setFinishProjectCostStr(model.getFinishProjectCostStr());
                model.setFinishProjectProfitStr(model.getFinishProjectProfitStr());
                model.setFinishProjectGrossMarginStr(model.getFinishProjectGrossMarginStr());
            });
            ExcelUtil<CrmProjectListExportVO> util = new ExcelUtil<>(CrmProjectListExportVO.class);

            //文件名称
            StringBuilder fileNameBuilder = new StringBuilder("结案明细-")
                    //客户名称
                    .append(getCustomerNameById(query.getCustomerId()))
                    .append("-")
                    .append(DateUtils.format(LocalDateTime.now(), DateUtils.YYYY_MM_DD_COLON_HH_MM_SS))
                    .append(".xlsx");
            File file = util.exportExcelFile(exportList, "结案明细");

            FileInputStream input = new FileInputStream(file);
            OssUploadResult upload = ossService.upload(input, fileNameBuilder.toString(), true);
            input.close();

            exportTaskResult.setFileName(fileNameBuilder.toString());
            exportTaskResult.setIsSuccess(true);
            exportTaskResult.setKey(upload.getOssKey());
        } catch (Exception e) {
            e.printStackTrace();
            exportTaskResult.setIsSuccess(false);
        }
        taskRecodeService.taskExportCompleted(query.getTaskId(), exportTaskResult);
    }

    /**
     * 时间转换
     * @param query
     */
    private void convertTime(CustomerDevoteQuery query) {
        //合作时间转换
        if(StringUtils.isNotBlank(query.getCooperateBeginTime()) && StringUtils.isNotBlank(query.getCooperateEndTime())) {
            query.setCooperateBeginDate(query.getCooperateBeginTime() + " 00:00:00");
            query.setCooperateEndDate(query.getCooperateEndTime() + " 23:59:59");
        }
        //结案时间转换
        if(StringUtils.isNotBlank(query.getCloseCaseBeginTime()) && StringUtils.isNotBlank(query.getCloseCaseEndTime())) {
            query.setCloseCaseBeginDate(query.getCloseCaseBeginTime() + " 00:00:00");
            query.setCloseCaseEndDate(query.getCloseCaseEndTime() + " 23:59:59");
        }
    }

    /**
     * 根据客户id获取客户名称
     * @param customerId
     * @return
     */
    private String getCustomerNameById(Long customerId) {
        if(Objects.isNull(customerId)) {
            return null;
        }

        //根据客户id获取客户名称
        SupCustomer customer = customerService.getInfo(customerId);
        if(Objects.isNull(customer)) {
            return null;
        }
        return customer.getName();
    }

    @Override
    public void execute(CustomerDevoteQuery args) {
        if(Objects.isNull(args.getMenuType())) {
            //导出客户贡献管理列表数据
            exportCustomerDevote(args);
            return;
        }

        //导出比稿明细
        if(Objects.equals(CustomerDevoteMenuTypeStatus.COMPARED_DRAFT, args.getMenuType())) {
            exportComparedDraft(args);
        } else if (Objects.equals(CustomerDevoteMenuTypeStatus.PROJECT, args.getMenuType())) {
            //导出项目明细
            exportProject(args);
        } else if (Objects.equals(CustomerDevoteMenuTypeStatus.CLOSE_CASE_PROJECT, args.getMenuType())) {
            //导出结案明细
            exportCloseCaseProject(args);
        } else {
            //导出坏账明细
            exportBadDebt(args);
        }
    }

    /**
     * 导出结案明细
     *
     * @param query 导出查询参数
     * @return 导出数据
     */
    @Override
    public void exportCustomerDevote(CustomerDevoteQuery query) {
        taskRecodeService.editTaskBegin(query.getTaskId());
        ExportTaskResult exportTaskResult = new ExportTaskResult();

        try {
            convertTime(query);
            //读取处理数据
            List<CustomerDevoteListVO> customerDevoteList = listCustomerDevote(query);

            //获取客户行业类目字典值
            List<SysDictData> dataList = this.dictDataService.selectDictDataByType("contribution_industry_category");
            Map<String, String> dictDataMap = ListUtil.toMap(dataList, SysDictData::getDictValue, SysDictData::getDictLabel);

            customerDevoteList.forEach(customer->{
                List<String> categoryNameList = ListUtil.emptyList();
                if (ListUtil.any(customer.getIndustryCategoryIdList())){
                    for (Long categoryId : customer.getIndustryCategoryIdList()) {
                        String categoryName = dictDataMap.get(String.valueOf(categoryId));
                        if(StringUtils.isNotBlank(categoryName)) {
                            categoryNameList.add(categoryName);
                        }
                    }
                    customer.setIndustryCategoryName(String.join(",", categoryNameList));
                }
            });

            query.setIsSelectAll(true);
            //获取sheet页config
            ExcelSheetConfig<CustomerDevoteListVO> customerDevoteConfig = getCustomerDevoteConfig();
            ExcelSheetConfig<CustomerDevoteComparedDraftExportModel> comparedDraftConfig = getComparedDraftConfig();
            ExcelSheetConfig<CrmProjectListExportVO> crmProjectConfig = getCrmProjectConfig();
            ExcelSheetConfig<CustomerDevoteBadDebtExportModel> badDebtConfig = getBadDebtConfig();
            String fileDateFormat = DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss");
            String zipName ="客户贡献管理-"+ fileDateFormat;
            File zipFile = File.createTempFile(zipName, ".zip");
            FileOutputStream fileOutputStream = new FileOutputStream(zipFile);
            CheckedOutputStream cos = new CheckedOutputStream(fileOutputStream, new Adler32());
            //用于将数据压缩成zip文件格式
            ZipOutputStream zos = new ZipOutputStream(cos);
            //循环便利数据对象处理
            for (CustomerDevoteListVO customerDevoteListVO : customerDevoteList) {
                MultiSheetExcelExporter excelExporter = new MultiSheetExcelExporter();
                //添加sheet页配置（数据，config，sheet名字）
                excelExporter.addSheetConfig(Collections.singletonList(customerDevoteListVO),customerDevoteConfig,"客户贡献管理");
                query.setCustomerId(customerDevoteListVO.getCustomerId());
                //比稿
                List<CustomerDevoteComparedDraftExportModel> exportList = queryComparedDraftList(query);
                excelExporter.addSheetConfig(exportList,comparedDraftConfig,"比稿明细");
                //项目
                List<CrmProjectListExportVO> projectList = this.projectService.exportCustomerDevoteProject(query);
                excelExporter.addSheetConfig(projectList,crmProjectConfig,"项目明细");
                //结案
                List<CrmProjectListExportVO> closeCaseProjectList = projectList.stream()
                        .filter(s->s.getProjectStatus().equals(ProjectStatus.CLOSED_CASE.getValue())).collect(Collectors.toList());
                excelExporter.addSheetConfig(closeCaseProjectList,crmProjectConfig,"结案明细");
                //坏账
                List<CustomerDevoteBadDebtExportModel> badDebtExportList = this.badDebtService.exportCustomerDevoteBadDebt(query);
                excelExporter.addSheetConfig(badDebtExportList,badDebtConfig,"坏账明细");

                StringBuilder fileNameBuilder = new StringBuilder()
                        .append("客户贡献管理-")
                        .append(customerDevoteListVO.getName())
                        .append("-")
                        .append(fileDateFormat)
                        .append( ".xlsx");
                byte[] bytes = excelExporter.exportExcelToByte(fileNameBuilder.toString());
                ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
                zos.putNextEntry(new ZipEntry(fileNameBuilder.toString()));
                int bytesRead;
                // 向压缩文件中输出数据
                while ((bytesRead = inputStream.read()) != -1) {
                    zos.write(bytesRead);
                }
                inputStream.close();
                // 当前文件写完，定位为写入下一条项目
                zos.closeEntry();
            }
            zos.close();
            FileInputStream fis = new FileInputStream(zipFile);
            OssUploadResult upload = ossService.upload(fis, zipName + ".zip", true);

            exportTaskResult.setFileName(zipName + ".zip");
            exportTaskResult.setIsSuccess(true);
            exportTaskResult.setKey(upload.getOssKey());
        } catch (Exception e) {
            e.printStackTrace();
            exportTaskResult.setIsSuccess(false);
        }
        taskRecodeService.taskExportCompleted(query.getTaskId(), exportTaskResult);
    }

    @Override
    public Class<CustomerDevoteQuery> argsClass() {
        return CustomerDevoteQuery.class;
    }
}
