package com.py.common.utils.collection.queryable;

import com.github.pagehelper.PageInfo;
import com.py.common.utils.StringUtils;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 可查询集合测试类
 * <AUTHOR>
 */
@Log4j2
class PageQueryableTest {

    @Test
    void pageFor() {
        PageQueryable<Long> pageQueryable = getPageQueryable();

        // 执行
        pageQueryable.pageFor((list) -> log.info("查询到值：{}", StringUtils.convertLongList(list)));
    }


    @Test
    void forEach() {
        PageQueryable<Long> pageQueryable = getPageQueryable();
        for(Long value : pageQueryable) {
            log.info("查询到值：{}", value);
        }
    }

    private PageQueryable<Long> getPageQueryable() {
        // 准备
        int total = 42;
        return new PageQueryable<>((pageNum, pageSize) -> {
            log.info("查询第{}页，页大小为{}", pageNum, pageSize);
            List<Long> result = Stream.iterate(1L, i -> i + 1)
                    .skip((long) (pageNum - 1) * pageSize)
                    .limit(pageSize)
                    .collect(Collectors.toList());

            PageInfo<Long> pageInfo = new PageInfo<>(result);
            pageInfo.setTotal(total);
            pageInfo.setHasNextPage(pageNum - 1 < total / pageSize);
            return pageInfo;
        }, 5);
    }

}
