package com.py.flow.event;

import com.py.common.enums.flow.FlowApprovalStatus;
import com.py.flow.domain.enums.ApprovalBizType;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

/**
 * 派芽审批状态变更事件
 * <AUTHOR>
 */
@Data
@ApiModel("派芽审批状态变更事件")
@NoArgsConstructor
public class FlowApprovalStatusChangeEvent {

    /** 审批类型 */
    private ApprovalBizType approvalType;

    /** 审批业务ID */
    private Long bizId;

    /** 变更的审批状态 */
    private FlowApprovalStatus approvalStatus;

    /**
     * 构造审批状态变更事件
     * @param approvalType 审批类型
     * @param bizId 审批业务ID
     * @param approvalStatus 变更的审批状态
     */
    public FlowApprovalStatusChangeEvent(ApprovalBizType approvalType, Long bizId, FlowApprovalStatus approvalStatus) {
        Assert.notNull(approvalType, "审批类型不能为空");
        Assert.notNull(bizId, "审批业务ID不能为空");
        Assert.notNull(approvalStatus, "审批状态不能为空");

        this.approvalType = approvalType;
        this.bizId = bizId;
        this.approvalStatus = approvalStatus;
    }
}
