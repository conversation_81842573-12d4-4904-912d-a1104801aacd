package com.py.web.controller.system.mainstayparam;


import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.system.mainstayparam.domain.dto.DeleteMainstayDTO;
import com.py.system.mainstayparam.domain.dto.SystemMainstayParamDTO;
import com.py.system.mainstayparam.domain.query.SystemMainstayHasCriteria;
import com.py.system.mainstayparam.domain.query.SystemMainstayParamQuery;
import com.py.system.mainstayparam.domain.vo.MainstayCountVO;
import com.py.system.mainstayparam.domain.vo.MainstayParamListVO;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamListVO;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统设置-主体参数设置Controller
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@Api(tags = "系统设置-主体参数设置")
@RestController
@RequestMapping("/system/systemMainstayParam")
public class SystemMainstayParamController extends BaseController {

    /** 系统设置-主体参数设置服务 */
    @Resource
    private ISystemMainstayParamService systemMainstayParamService;

    /**
     * 查询系统设置-主体参数设置列表
     *
     * @param query 系统设置-主体参数设置查询参数
     * @return 系统设置-主体参数设置列表
     */
    @ApiOperation("查询系统设置-主体参数设置列表")
    @PreAuthorize("@ss.hasPermi('system:systemMainstayParam:list')")
    @GetMapping("/listSystemMainstayParam")
    public R<List<MainstayParamListVO>> listSystemMainstayParam(SystemMainstayParamQuery query) {
        List<MainstayParamListVO> voList = this.systemMainstayParamService.listSystemMainstayParam(query);
        return R.success(voList);
    }

    /**
     * 分页查询系统设置-主体参数设置列表
     *
     * @param query 系统设置-主体参数设置查询参数
     * @return 系统设置-主体参数设置分页
     */
    @ApiOperation("分页查询询系统设置-主体参数设置列表")
    @PreAuthorize("@ss.hasPermi('system:systemMainstayParam:list')")
    @GetMapping("/pageSystemMainstayParam")
    public R<PageInfo<SystemMainstayParamListVO>> pageSystemMainstayParam(SystemMainstayParamQuery query) {
        PageInfo<SystemMainstayParamListVO> voList = this.systemMainstayParamService.pageSystemMainstayParamList(query);
        return R.success(voList);
    }

    /**
     * 获取系统设置-主体参数设置详细信息
     * @param id 系统设置-主体参数设置主键
     * @return 系统设置-主体参数设置视图模型
     */
    @ApiOperation("获取系统设置-主体参数设置详细信息")
    @PreAuthorize("@ss.hasPermi('system:systemMainstayParam:query')")
    @GetMapping(value = "/{id}")
    public R<SystemMainstayParamVO> getInfo(@PathVariable("id") Long id) {
        return R.success(systemMainstayParamService.selectSystemMainstayParamById(id));
    }

    /**
     * 修改该主题是否展示在合作分析查询条件
     * @param criteria  修改信息
     * @return 操作结果
     */
    @ApiOperation("修改系统设置-该主题是否展示在合作分析查询条件")
    @PreAuthorize("@ss.hasPermi('system:systemMainstayParam:query')")
    @PostMapping("/edit")
    public R<Void> edit(@Validated @RequestBody SystemMainstayHasCriteria criteria) {
        systemMainstayParamService.editSystemMainStayHasCriteria(criteria);
        return R.success();
    }
    /**
     * 新增系统设置-主体参数设置
     *
     * @param dto 系统设置-主体参数设置修改参数
     * @return 是否成功
     */
    @ApiOperation("新增系统设置-主体参数设置")
    @PreAuthorize("@ss.hasPermi('system:systemMainstayParam:add')")
    @Log(title = "系统设置-主体参数设置", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody SystemMainstayParamDTO dto) {
        return R.success(systemMainstayParamService.insertSystemMainstayParam(dto));
    }

    /**
     * 修改系统设置-主体参数设置
     *
     * @param dto 系统设置-主体参数设置修改参数
     * @return 是否成功
     */
    @ApiOperation("修改系统设置-主体参数设置")
    @PreAuthorize("@ss.hasPermi('system:systemMainstayParam:edit')")
    @Log(title = "系统设置-主体参数设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody SystemMainstayParamDTO dto) {
        return R.success(systemMainstayParamService.updateSystemMainstayParam(dto));
    }

    /**
     * 删除系统设置-主体参数设置
     * @param dto 需要删除的系统设置-主体参数设置主键集合
     * @return 是否成功
     */
    @ApiOperation("删除系统设置-主体参数设置" )
    @PreAuthorize("@ss.hasPermi('system:systemMainstayParam:remove')")
    @Log(title = "系统设置-主体参数设置", businessType = BusinessType.DELETE)
    @PostMapping("/remove" )
    public R<Boolean> remove(@RequestBody DeleteMainstayDTO dto) {
        return R.success(systemMainstayParamService.deleteSystemMainstayParamByIds(dto.getMainstayIdList()));
    }

    /**
     * 是否隐藏主体
     *
     * @param dto 系统设置-主体参数设置修改参数
     * @return 是否成功
     */
    @ApiOperation("是否隐藏主体")
    @PreAuthorize("@ss.hasPermi('system:systemMainstayParam:edit')")
    @Log(title = "系统设置-是否隐藏主体", businessType = BusinessType.UPDATE)
    @PutMapping("/updateHasHide")
    public R<Boolean> updateHasHide(@RequestBody SystemMainstayParamDTO dto) {
        return R.success(systemMainstayParamService.updateHasHide(dto));
    }

    /**
     * 获取主体参数数量
     *
     * @return 主体数量
     */
    @ApiOperation("获取主体参数数量")
    @PreAuthorize("@ss.hasPermi('system:systemMainstayParam:list')")
    @GetMapping("/queryMainstayCount")
    public R<MainstayCountVO> queryMainstayCount() {
        return R.success(this.systemMainstayParamService.queryMainstayCount());
    }

}
