package com.py.common.tools.multisheetexcelexporter;


import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;

import java.util.Collection;

/**
 * 提供导出多个sheet的Excel的配置项
 * <AUTHOR>
 */
public class MultiSheetExcelExportConfigItem<T> {

    /**
     * 导出数据源
     */
    private final Collection<T> source;

    /**
     * 导出配置
     */
    private final ExcelSheetConfig<T> config;

    /**
     * 导出Sheet名
     */
    private final String sheetName;

    /**
     * 提供导出多个sheet的Excel的配置项
     * @param source 导出数据源
     * @param config 导出配置
     * @param sheetName 导出Sheet名
     */
    public MultiSheetExcelExportConfigItem(Collection<T> source, ExcelSheetConfig<T> config, String sheetName) {
        this.source = source;
        this.config = config;
        this.sheetName = sheetName;
    }

    public Collection<T> getSource() {
        return source;
    }

    public ExcelSheetConfig<T> getConfig() {
        return config;
    }

    public String getSheetName() {
        return sheetName;
    }
}
