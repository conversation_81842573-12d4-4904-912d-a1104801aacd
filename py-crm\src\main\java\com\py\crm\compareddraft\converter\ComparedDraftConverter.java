package com.py.crm.compareddraft.converter;

import com.py.common.core.converter.BaseDomainModelConverter;
import com.py.crm.compareddraft.domain.ComparedDraft;
import com.py.crm.compareddraft.domain.dto.ComparedDraftDTO;
import com.py.crm.compareddraft.domain.dto.ComparedDraftExportModel;
import com.py.crm.compareddraft.domain.dto.CustomerDevoteComparedDraftExportModel;
import com.py.crm.compareddraft.domain.vo.ComparedDraftListVO;
import com.py.crm.compareddraft.domain.vo.ComparedDraftVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 客户管理-比稿关联模型转换器
 * <AUTHOR>
 * @version ComparedDraftConverter 2023/7/17 10:37
 */
@Mapper(componentModel = "spring")
public interface ComparedDraftConverter extends BaseDomainModelConverter<ComparedDraft, ComparedDraftVO, ComparedDraftDTO> {
    /**
     * 将Entity转换为导出模型
     * @param entityList entity列表
     * @return 导出模型列表
     */
    List<ComparedDraftExportModel> toExportModel(List<ComparedDraft> entityList);

    /**
     * 将Entity转换为导出模型
     * @param entityList entity列表
     * @return 导出模型列表
     */
    List<ComparedDraftListVO> toListVoByEntity(List<ComparedDraft> entityList);

    /**
     * 将Entity转换为客户贡献比稿导出模型
     * @param comparedDraftList
     * @return
     */
    List<CustomerDevoteComparedDraftExportModel> toCustomerDevoteExportModelByEntity(List<ComparedDraft> comparedDraftList);
}
