package com.py.web.controller.project;


import com.github.pagehelper.PageInfo;
import com.py.common.constant.Constants;
import com.py.common.core.domain.R;
import com.py.common.exception.ServiceException;
import com.py.project.projectbatchupdaterecord.domain.query.ProjectBatchUpdateRecordApprovalQuery;
import com.py.project.projectbatchupdaterecord.domain.vo.ProjectBatchUpdateRecordApprovalCountVO;
import com.py.project.projectbatchupdaterecord.domain.vo.ProjectBatchUpdateRecordApprovalSumVO;
import com.py.project.projectbatchupdaterecord.domain.vo.ProjectBatchUpdateRecordApprovalVO;
import com.py.project.projectbatchupdaterecord.service.ProjectBatchUpdateRecordService;
import com.py.project.projectpaymentrecord.domain.query.ProjectPaymentAuditSelectQuery;
import com.py.system.mainstayparam.domain.vo.MainstayParamListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "项目管理-批量修改")
@RestController
@RequestMapping("/project/batchUpdateRecord")
public class ProjectBatchUpdateController {

    @Resource
    private ProjectBatchUpdateRecordService projectBatchUpdateRecordService;

    /**
     * 派财务-审批查询-批量修改列表查询
     * @param query
     * @return
     */
    @ApiOperation("派财务-审批查询-批量修改列表查询")
//    @PreAuthorize("@ss.hasPermi('project:projectBatchUpdateRecord:approval:pagelist')")
    @GetMapping("/pageProjectBatchUpdateRecordApproval")
    public R<PageInfo<ProjectBatchUpdateRecordApprovalVO>> pageProjectConfirmRecordApproval(ProjectBatchUpdateRecordApprovalQuery query) {
        if (query.getPyMainstayId() == null){
            throw new ServiceException("主体id不能为空");
        }
        PageInfo<ProjectBatchUpdateRecordApprovalVO> voList = this.projectBatchUpdateRecordService.pageProjectBatchUpdateRecordApproval(query);
        return R.success(voList);
    }

    /**
     * 派财务-审批查询-下载批量修改审批列表
     * @param query
     * @return
     */
    @ApiOperation("派财务-审批查询-下载批量修改审批列表")
//    @PreAuthorize("@ss.hasPermi('project:projectBatchUpdateRecord:list')")
    @PostMapping("/downloadProjectBatchUpdateRecordList")
    public R<String> downloadProjectBatchUpdateRecordList(@Validated @RequestBody ProjectPaymentAuditSelectQuery query) {
//        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPaiyaMainstayId());
//        String fileName = "付款审批查询列表数据-"+systemMainstayParamVO.getMainstayName()+"-"+ DateUtils.format(LocalDateTime.now(),"yyyy-MM-dd-HH：mm：ss")+".xlsx";
//        query.setFileName(fileName);
//        reusableAsyncTaskService.addTask("付款审批查询下载", TaskType.Export,query, ProjectPaymentRecordAuditSelectServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 派财务-审批查询-查询审批查询有权限的主体列表
     * 主体tab列表
     * @param
     * @return
     */
    @ApiOperation("派财-审批查询-批量修改审批查询查询公司主体")
//    @PreAuthorize("@ss.hasPermi('system:systemMainstayParam:list')")
    @GetMapping("/listSystemMainstayParam")
    public R<List<MainstayParamListVO>> listSystemMainstayParam() {
        return R.success(projectBatchUpdateRecordService.listSystemMainstayParam());
    }

    @ApiOperation("派财-审批查询-审批数量统计（按主体分组）")
//    @PreAuthorize("@ss.hasPermi('project:projectBatchUpdateRecord:approcal:sumlist')")
    @GetMapping("/countBatchUpdateApprovalByMainstayId")
    public R<List<ProjectBatchUpdateRecordApprovalCountVO>> countBatchUpdateApprovalByMainstayId() {
        return R.success(this.projectBatchUpdateRecordService.countBatchUpdateApprovalByMainstayId());
    }

    /**
     * 批量修改审批列表 - 列表合计统计
     * @param query
     * @return
     */
    @ApiOperation("派财-审批查询-批量修改审批查询合计")
//    @PreAuthorize("@ss.hasPermi('project:projectBatchUpdateRecord:approcal:sumIds')")
    @GetMapping("/sumProjectBatchUpdateRecordByIds")
    public R<ProjectBatchUpdateRecordApprovalSumVO> sumConfirmApprovalByIds(ProjectBatchUpdateRecordApprovalQuery query) {
        return R.success(this.projectBatchUpdateRecordService.sumBatchUpdateApprovalByIds(query));
    }
}
