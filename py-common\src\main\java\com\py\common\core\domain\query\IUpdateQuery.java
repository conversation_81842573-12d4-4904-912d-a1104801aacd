package com.py.common.core.domain.query;

import java.time.LocalDate;
import java.util.List;

/**
 * 更新信息查询接口
 * <AUTHOR>
 */
public interface IUpdateQuery {

    /**
     * 获取更新者Id
     * @return 更新者Id
     */
    List<Long> getUpdateUser();

    /**
     * 获取更新部门Id
     * @return 更新部门Id
     */
    List<Long> getUpdateDept();

    /**
     * 获取更新时间-开始
     * @return 更新时间-开始
     */
    LocalDate getStartUpdateDate();

    /**
     * 获取更新时间-结束
     * @return 更新时间-结束
     */
    LocalDate getEndUpdateDate();
}
