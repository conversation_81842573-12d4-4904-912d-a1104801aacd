package com.py.common.tools.verify.domain;

import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.collection.ListUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 导入返回值
 * @param <T> 导入输入类型
 * @param <R> 导入输出类型
 * <AUTHOR>
 */
@Getter
@Setter
public class ImportResult<T, R> {

	/** 前端换行符 */
	private static final String NEW_LINE = "\n";

	/** 合法数据列表 */
	private List<R> legalDataList = null;

	/** 错误数据列表 */
	private List<T> errorDataList = new ArrayList<>();

	/** 错误消息列表 */
	private List<String> errorMsgList = new ArrayList<>();

	/** 错误数据条数 */
	public int getErrorNumber() {
		return this.getErrorDataList().size();
	}

	public int getSuccessNumber() {
		return NullMergeUtils.nullMerge(this.legalDataList, list -> list.size(), 0);
	}

	/**
	 * 导入是否成功
	 * @return true: 成功
	 */
	public boolean isSuccess() {
		return this.getErrorNumber() == 0 && this.getSuccessNumber() != 0;
	}

	/**
	 * 添加错误信息
	 * @param format 格式化错误信息
	 * @param args 参数
	 */
	public void addFormatErrorMsg(String format, Object... args) {
		this.errorMsgList.add(String.format(format, args));
	}

	/**
	 * 添加错误信息
	 * @param errorMsg 错误信息
	 * @param errorItem 错误的项
	 */
	public void addErrorMsg(String errorMsg, T errorItem) {
		this.errorMsgList.add(errorMsg);
		this.errorDataList.add(errorItem);
	}

	/**
	 * 添加错误信息
	 * @param errorMsg 错误信息
	 * @param errorItemList 错误的项
	 */
	public void addErrorMsg(String errorMsg, List<T> errorItemList) {
		this.errorMsgList.add(errorMsg);
		this.errorDataList.addAll(errorItemList);
	}

	/**
	 * 生成提示消息
	 * @return 提示消息
	 */
	public String generateMsg() {
		if(this.isSuccess()) {
			String msgHeader = String.format("导入成功!成功导入 %d 条数据", this.getSuccessNumber());
			if(ListUtil.isEmpty(errorMsgList) == true) {
				return msgHeader;
			}
			String body = this.errorMsgList.stream().distinct().sorted().collect(Collectors.joining(NEW_LINE));
			return msgHeader + NEW_LINE + body;
		} else {
			String msgHeader = String.format("导入异常, 成功导入 %s 条, 失败导入 %s 条", this.getSuccessNumber(), this.getErrorNumber());
			String body = this.errorMsgList.stream().distinct().sorted().collect(Collectors.joining(NEW_LINE));
			return msgHeader + NEW_LINE + body;
		}
	}

}
