package com.py.crm.customer.customerlaundry.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import java.time.LocalDate;

/**
 * 客户管理-查看清单视图模型
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Data
@ApiModel("客户管理-查看清单视图模型" )
public class CustomerLaundryVO {
    private static final long serialVersionUID = 1L;

    /** 自增id*/
    @ApiModelProperty("自增id")
    private Long id;

    /** 清单id*/
    @ApiModelProperty("清单id")
    private Long laundryId;

    /** 业务id*/
    @ApiModelProperty("业务id")
    private Long bizId;

    /** 创建者Id*/
    @ApiModelProperty("创建者Id")
    private Long createId;

    /** 创建者*/
    @ApiModelProperty("创建者")
    private String createBy;

    /** 创建人部门*/
    @ApiModelProperty("创建人部门")
    private String createDept;

    /** 创建时间*/
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /** 更新者Id*/
    @ApiModelProperty("更新者Id")
    private Long updateId;

    /** 更新者*/
    @ApiModelProperty("更新者")
    private String updateBy;

    /** 更新人部门*/
    @ApiModelProperty("更新人部门")
    private String updateDept;

    /** 更新时间*/
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /** 删除标志*/
    @ApiModelProperty("删除标志")
    private String delFlag;


    /** 业务类型（0客户，1人脉） */
    @ApiModelProperty("业务类型（0客户，1人脉）")
    private Integer bizType;
}
