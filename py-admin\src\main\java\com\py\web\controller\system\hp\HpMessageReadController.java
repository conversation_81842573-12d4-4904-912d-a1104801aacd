package com.py.web.controller.system.hp;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.system.messageread.domain.dto.HpMessageReadDTO;
import com.py.system.messageread.domain.query.HpMessageReadQuery;
import com.py.system.messageread.domain.vo.HpMessageReadCountVO;
import com.py.system.messageread.domain.vo.HpMessageReadListVO;
import com.py.system.messageread.service.IHpMessageReadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 首页-消息中心-用户是否已读Controller
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Api(tags = "首页-消息中心-用户是否已读")
@RestController
@RequestMapping("/message/read")
public class HpMessageReadController extends BaseController {

    /** 首页-消息中心-用户是否已读服务 */
    @Resource
    private IHpMessageReadService hpMessageReadService;

    /**
     * 分页查询首页-消息中心-用户是否已读列表
     *
     * @param query 首页-消息中心-用户是否已读查询参数
     * @return 首页-消息中心-用户是否已读分页
     */
    @ApiOperation("分页查询询首页-消息中心-用户是否已读列表")
    @PreAuthorize("@ss.hasPermi('message:message:list')")
    @GetMapping("/pageMessage")
    public R<PageInfo<HpMessageReadListVO>> pageHpMessageRead(HpMessageReadQuery query) {
        PageInfo<HpMessageReadListVO> voList = this.hpMessageReadService.pageMessageRead(query);
        return R.success(voList);
    }

    /**
     * 获取各状态下消息数量
     * @return 消息数量
     */
    @ApiOperation("获取各状态下消息数量")
    @PreAuthorize("@ss.hasPermi('message:message:list')")
    @GetMapping(value = "/selectStatisticsCount")
    public R<HpMessageReadCountVO> selectStatisticsCount() {
        return R.success(hpMessageReadService.selectStatisticsCount(null));
    }

    /**
     * 修改首页-消息中心-用户是否已读
     *
     * @param dto 首页-消息中心-用户是否已读修改参数
     * @return 是否成功
     */
    @ApiOperation("修改首页-消息中心-用户是否已读")
    @PreAuthorize("@ss.hasPermi('message:message:edit')")
    @Log(title = "首页-消息中心-用户是否已读", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public R<Boolean> edit(@Validated @RequestBody HpMessageReadDTO dto) {
        return R.success(hpMessageReadService.updateHpMessageRead(dto));
    }
}
