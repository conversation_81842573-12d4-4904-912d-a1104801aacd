package com.py.common.oss.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.crypto.digest.MD5;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.aliyun.oss.model.DownloadFileRequest;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.py.common.exception.ServiceException;
import com.py.common.file.FileAnnex;
import com.py.common.file.FileInfoVO;
import com.py.common.oss.IOssService;
import com.py.common.oss.OssConfig;
import com.py.common.oss.annotation.OssUrl;
import com.py.common.oss.annotation.RichText;
import com.py.common.oss.model.OssDownloadQuery;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.oss.model.TempToken;
import com.py.common.utils.RegexUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.common.utils.spring.SpringUtils;
import com.py.common.utils.uuid.IdUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import rx.functions.Action2;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 阿里云对象存储服务
 * <AUTHOR> pyr
 **/
@Service
@Slf4j
public class OssServiceImpl implements IOssService {

    /** 多媒体链接匹配正则表达式 */
    private static final Pattern MULTIMEDIA_LINK_MATH = Pattern.compile("(?<head><(?:img|video)\\s+src=\\s*\")(?<link>[^\"]+)(?<tail>\")", Pattern.MULTILINE);

    /** OSS客户端实例 */
    private final OSS ossClient;

    @Value("${oss.bucketName}")
    private String bucketName;

    /** 阿里云对象存储配置 */
    private final OssConfig ossConfig;

    public OssServiceImpl(OssConfig ossConfig) {
        this.ossConfig = ossConfig;

        this.ossClient = new OSSClientBuilder()
                .build(ossConfig.getEndpoint(),
                        ossConfig.getAccessKey(),
                        ossConfig.getAccessKeySecret());
    }

    /**
     * http转换成https
     */
    private static String getHttpsUrl(URL url) throws URISyntaxException {
        String urlStr = url.toURI().toString();
        return urlStr.substring(0, urlStr.indexOf(":")) + "s" + urlStr.substring(urlStr.indexOf(":"));
    }

    /**
     * 获取OSS客户端实例
     * @return OSS客户端实例
     */
    private OSS getOssClient() {
        return ossClient;
    }

    /**
     * 根据文件key集合批量获取访问url  有时间限制
     * @param keyList ossKey列表
     * @return URL列表
     */
    @Override
    public List<String> listUrlByKey(List<String> keyList) {
        if(CollectionUtils.isEmpty(keyList)) {
            return null;
        }

        List<String> urlList = new ArrayList<>();
        for(String key : keyList) {
            String urlByKey = getUrlByKey(key);
            urlList.add(urlByKey);
        }
        return urlList;
    }

    /**
     * 根据文件key获取访问url  有时间限制
     * @param key ossKey
     * @return fileUrl
     */
    @Override
    public String getUrlByKey(String key) {
        if(StringUtils.isBlank(key)) {
            return null;
        }
        OSS oss = this.getOssClient();
        try {
            GeneratePresignedUrlRequest request = this.createTempUrlRequest(key);
            URL url = oss.generatePresignedUrl(request);
            if(Objects.isNull(url)) {
                log.error("获取文件访问url出错");
                return null;
            }
            return getHttpsUrl(url);
        } catch(URISyntaxException e) {
            log.error("获取文件访问url出错");
            return null;
        }
    }

    /**
     * 批量关联Oss访问地址
     * @param list 数据源列表
     * @param ossKeySelector ossKey选择器
     * @param ossUrlSetCallback ossUrl设置回调
     */
    @Override
    public <T> void relationOssUrl(
            List<T> list,
            @NonNull Function<T, String> ossKeySelector,
            @NonNull Action2<T, String> ossUrlSetCallback) {
        if(ListUtil.isEmpty(list)) {
            return;
        }

        OSS oss = this.getOssClient();
        for(T item : list) {
            if(item == null) {
                continue;
            }
            try {
                String ossKey = ossKeySelector.apply(item);
                if(StringUtils.isBlank(ossKey)) {
                    continue;
                }
                GeneratePresignedUrlRequest request = this.createTempUrlRequest(ossKey);
                URL url = oss.generatePresignedUrl(request);
                if(url == null) {
                    log.error("地址 {} 获取文件访问url出错", ossKey);
                    ossUrlSetCallback.call(item, null);
                    continue;
                }
                String ossUrl = getHttpsUrl(url);
                ossUrlSetCallback.call(item, ossUrl);
            } catch(URISyntaxException e) {
                log.error("获取文件访问url出错");
            }
        }
    }

    /**
     * 上传输入流
     * @param inputStream 输入流
     * @param fileName 文件名称
     * @param needUrl 是否需要返回访问Url
     * @return 上传结果
     */
    @Override
    public OssUploadResult upload(InputStream inputStream, String fileName, boolean needUrl) {
        OSS ossClient = this.getOssClient();
        try {
            String ossKey = this.encodeOssKey(fileName);
            ossClient.putObject(this.ossConfig.getBucketName(), ossKey, inputStream);

            OssUploadResult result = new OssUploadResult();
            result.setUploadFileName(fileName);
            result.setOssKey(ossKey);
            // 请求临时访问地址
            if(needUrl == true) {
                GeneratePresignedUrlRequest tempUrlRequest = this.createTempUrlRequest(ossKey);
                URL ossUrl = ossClient.generatePresignedUrl(tempUrlRequest);
                result.setOssUrl(getHttpsUrl(ossUrl));
            }
            return result;
        } catch(Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }


    /**
     * 文件上传
     * @param fileList 文件列表
     * @param needUrl 是否需要返回访问Url
     * @return 上传结果
     */
    @Override
    public List<OssUploadResult> upload(List<MultipartFile> fileList, boolean needUrl) {
        if(ListUtil.isEmpty(fileList)) {
            return ListUtil.emptyList();
        }

        OSS ossClient = this.getOssClient();
        try {
            List<OssUploadResult> resultList = new ArrayList<>(fileList.size());
            for(MultipartFile file : fileList) {
                if(file.isEmpty()) {
                    continue;
                }

                String ossKey = this.encodeOssKey(file.getOriginalFilename());
                ossClient.putObject(this.ossConfig.getBucketName(), ossKey, file.getInputStream());

                OssUploadResult result = new OssUploadResult();
                result.setUploadFileName(file.getOriginalFilename());
                result.setOssKey(ossKey);
                // 请求临时访问地址
                if(needUrl == true) {
                    GeneratePresignedUrlRequest tempUrlRequest = this.createTempUrlRequest(ossKey);
                    URL ossUrl = ossClient.generatePresignedUrl(tempUrlRequest);
                    result.setOssUrl(getHttpsUrl(ossUrl));
                }
                resultList.add(result);
            }
            return resultList;
        } catch(Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取oss临时凭证
     * @return 临时凭证视图模型
     */
    @Override
    public TempToken getAliYunOssConfig() {
        try {
            // 添加endpoint（直接使用STS endpoint，前两个参数留空，无需添加region ID）
            DefaultProfile.addEndpoint(this.ossConfig.getEndpoint(), "Sts", this.ossConfig.getStsEndpoint());
            // 构造default profile（参数留空，无需添加region ID）
            IClientProfile profile = DefaultProfile.getProfile(
                    this.ossConfig.getRegion(),
                    this.ossConfig.getAccessKey(),
                    this.ossConfig.getAccessKeySecret());
            // 用profile构造client
            DefaultAcsClient client = new DefaultAcsClient(profile);
            // 配置请求参数
            final AssumeRoleRequest request = new AssumeRoleRequest();
            request.setSysMethod(MethodType.POST);
            request.setRoleArn(this.ossConfig.getRoleArn());
            request.setRoleSessionName(this.ossConfig.getRoleSessionName());
            request.setDurationSeconds(1000L);

            // 请求临时凭证
            final AssumeRoleResponse.Credentials credentials = client.getAcsResponse(request).getCredentials();

            // 封装结果
            TempToken tempVoucherVO = new TempToken();
            tempVoucherVO.setAccessKey(credentials.getAccessKeyId());
            tempVoucherVO.setAccessKeySecret(credentials.getAccessKeySecret());
            tempVoucherVO.setToken(credentials.getSecurityToken());
            tempVoucherVO.setBucketName(this.ossConfig.getBucketName());
            tempVoucherVO.setRegion("oss-" + this.ossConfig.getRegion());
            return tempVoucherVO;
        } catch(ClientException e) {
            log.error("sts请求 {} 调用失败：Error code: {} , {}", e.getRequestId(), e.getErrCode(), e.getErrMsg());
            throw new ServiceException("Error code: " + e.getErrCode() + "," + e.getErrMsg());
        }
    }


    /**
     * 转换富文本中的Oss多媒体链接
     * @param richText 富文本
     * @return 转换后的富文本
     */
    @Override
    public String convertRichTextMultimediaKey(String richText) {
        if(StringUtils.isBlank(richText)) {
            return richText;
        }
        return RegexUtils.replace(richText, MULTIMEDIA_LINK_MATH, matcher -> {
            String ossKey = matcher.group("link");
            String ossUrl = this.getUrlByKey(ossKey);
            return matcher.group("head") + ossUrl + matcher.group("tail");
        });
    }

    /**
     * 下载Oss文件
     * @param query Oss下载请求
     * @param response 请求响应, 文件流将放置响应内
     */
    @Override
    public void download(OssDownloadQuery query, HttpServletResponse response) {
        OSS ossClient = this.getOssClient();
        try(OSSObject ossObject = ossClient.getObject(this.ossConfig.getBucketName(), query.getOssKey())) {
            IoUtil.copy(ossObject.getObjectContent(), response.getOutputStream());
        } catch(IOException e) {
            log.error("阿里云下载失败 ==> {}", e.getMessage());
            throw new ServiceException("文件下载失败");
        }
    }

    /**
     * 下载Oss文件
     * @param ossKey OssKey
     * @return 文件字节数组
     */
    @Override
    public byte[] download(String ossKey) {
        if(StringUtils.isBlank(ossKey)) {
            return null;
        }

        OSS ossClient = this.getOssClient();
        try(OSSObject ossObject = ossClient.getObject(this.ossConfig.getBucketName(), ossKey)) {
            return IoUtil.readBytes(ossObject.getObjectContent());
        } catch(IOException e) {
            log.error("阿里云下载失败 ==> {}", e.getMessage());
            throw new ServiceException("文件下载失败");
        }
    }

    /**
     * 下载Oss文件
     * @param ossKey OssKey
     * @param fileName 文件路径
     */
    @Override
    public void download(String ossKey, String fileName)  {
        if(StringUtils.isBlank(ossKey)) {
            return;
        }
        // 填写Object完整路径，例如exampledir/exampleobject.txt。Object完整路径中不能包含Bucket名称。
        OSS ossClient = this.getOssClient();
        try{
            // 请求10个任务并发下载。
            DownloadFileRequest downloadFileRequest = new DownloadFileRequest(bucketName, ossKey);
            // 指定Object下载到本地文件的完整路径，例如D:\\localpath\\examplefile.txt。
            downloadFileRequest.setDownloadFile(fileName);
            // 设置分片大小，单位为字节，取值范围为100 KB~5 GB。现在为100 MB。
            downloadFileRequest.setPartSize(100 * 1024 * 1024);
            // 设置分片下载的并发数，默认值为1。
            downloadFileRequest.setTaskNum(10);
            // 开启断点续传下载，默认关闭。
            downloadFileRequest.setEnableCheckpoint(true);
            // 设置断点记录文件的完整路径，例如D:\\localpath\\examplefile.txt.dcp。
            // 只有当Object下载中断产生了断点记录文件后，如果需要继续下载该Object，才需要设置对应的断点记录文件。下载完成后，该文件会被删除。
            //downloadFileRequest.setCheckpointFile("D:\\localpath\\examplefile.txt.dcp");

            // 下载文件。
            try {
                ossClient.downloadFile(downloadFileRequest);
            } catch(Throwable throwable) {
                throwable.printStackTrace();
            }

        } catch(Exception e) {
            log.error("阿里云下载失败 ==> {}", e.getMessage());
            throw new ServiceException("文件下载失败");
        }
    }

    /**
     * 转换输入对象字段上标记 RichText 注解字段的 富文本中的Oss多媒体链接
     * @param richTextBearer 富文本载体
     */
    @Override
    public <T> void convertRichTextMultimediaKey(T richTextBearer) {
        if(richTextBearer == null) {
            return;
        }

        for(Field field : ReflectUtil.getFields(richTextBearer.getClass())) {
            if(field.isAnnotationPresent(RichText.class)) {
                Object value = ReflectUtil.getFieldValue(richTextBearer, field);
                if(value == null) {
                    continue;
                }
                if(value instanceof String == false) {
                    throw new IllegalArgumentException("标记为富文本的字段必须为String");
                }
                ReflectUtil.setFieldValue(richTextBearer, field, this.convertRichTextMultimediaKey((String) value));
            }
        }
    }

    /**
     * 填充标记 OssUrl 注解的字段
     * @param ossUrlBearer url字段载体
     * @param <T> 载体类型
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public <T> void populateUrlField(T ossUrlBearer) {
        if(ossUrlBearer == null) {
            return;
        }

        Field[] fieldList = ReflectUtil.getFields(ossUrlBearer.getClass());
        for(Field field : fieldList) {
            OssUrl ossUrl = field.getAnnotation(OssUrl.class);
            if(ossUrl == null) {
                continue;
            }

            Class<?> fieldClass = field.getType();
            if(fieldClass.equals(String.class) == false && fieldClass.equals(List.class) == false) {
                throw new IllegalArgumentException("标记为Url的字段必须为String 或 List<String>");
            }

            Field keyField = ListUtil.first(fieldList, x -> x.getName().equals(ossUrl.value()));
            if(keyField == null) {
                String errorMsg = String.format("字段 %s 指定的来源字段 %s 不存在", field.getName(), ossUrl.value());
                throw new IllegalArgumentException(errorMsg);
            } else if(keyField.getType().equals(fieldClass) == false) {
                String errorMsg = String.format("字段 %s 指定和来源字段 %s 类型不一致, 转换时两个字段需类型一致", field.getName(), ossUrl.value());
                throw new IllegalArgumentException(errorMsg);
            }

            if(fieldClass.equals(String.class)) {
                String ossKey = (String) ReflectUtil.getFieldValue(ossUrlBearer, keyField);
                if(StringUtils.isBlank(ossKey)) {
                    continue;
                }

                ReflectUtil.setFieldValue(ossUrlBearer, field, this.getUrlByKey(ossKey));
            } else {

                List objectList = (List) ReflectUtil.getFieldValue(ossUrlBearer, keyField);
                if(ListUtil.isEmpty(objectList)) {
                    continue;
                }
                Object firstObject = ListUtil.firstOrThrow(objectList);
                if(firstObject instanceof String == false) {
                    throw new IllegalArgumentException("标记为Url的字段必须为String 或 List<String>");
                }

                ReflectUtil.setFieldValue(ossUrlBearer, field, this.listUrlByKey((List<String>) objectList));
            }
        }
    }

    /**
     * 编码Oss键
     * @param fileName 文件名
     * @return oss文件路径
     */
    private String encodeOssKey(String fileName) {
        Assert.hasText(fileName, "上传的文件名不能为空");

        String fileMd5 = MD5.create().digestHex16(fileName);
        return fileMd5 + fileName.substring(fileName.lastIndexOf("."));
    }

    /**
     * 获取创建临时Oss对象临时访问请求
     * @param key 请求Key
     * @return 临时访问请求对象
     */
    private GeneratePresignedUrlRequest createTempUrlRequest(String key) {
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(
                ossConfig.getBucketName(),
                key,
                HttpMethod.GET);
        // 附件地址有效期
        request.setExpiration(new Date(System.currentTimeMillis() + 90 * 60 * 1000));
        return request;
    }

    /**
     * 根据key获取文件对象信息
     * @param fileAnnexList 文件附件列表
     * @return 文件信息列表
     */
    @Override
    public List<FileInfoVO> getFileInfoByKey(List<FileAnnex> fileAnnexList) {
        if(ListUtil.isEmpty(fileAnnexList)){
            return ListUtil.emptyList();
        }

        return  fileAnnexList.stream().map(fileAnnex->{
                FileInfoVO fileInfoVO = new FileInfoVO();
                fileInfoVO.setFileKey(fileAnnex.getKey());
                fileInfoVO.setFileName(fileAnnex.getFileName());
                fileInfoVO.setFileUrl(this.getUrlByKey(fileInfoVO.getFileKey()));
                return fileInfoVO;
            }).collect(Collectors.toList());
    }

    /**
     * 将文件上传至OSS
     * @param imageData 图片字节流
     * @return 图片OssKey
     */
    @Override
    public String uploadFileToOss(byte[] imageData,String fileName) {
        fileName = IdUtils.fastUUID() + "/" + fileName +"." + "xlsx";
        IOssService ossService = SpringUtils.getBean(IOssService.class);
        try(ByteArrayInputStream inputStream = new ByteArrayInputStream(imageData)) {
            OssUploadResult ossUploadResult = ossService.upload(inputStream, fileName, true);
            return ossUploadResult.getOssKey();
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 设置Oss上传结果Url
     * @param ossUploadResultList 上传结果列表
     */
    @Override
    public void setUploadResultUrl(List<OssUploadResult> ossUploadResultList) {
        if(ListUtil.isEmpty(ossUploadResultList)){
            return;
        }

        for(OssUploadResult ossUploadResult : ossUploadResultList) {
            ossUploadResult.setOssUrl(this.getUrlByKey(ossUploadResult.getOssKey()));
        }
    }

    /**
     * 删除文件oss
     * @param ossKeyList ossKey
     */
    @Override
    public void deleteUpload(List<String> ossKeyList) {
        if(ListUtil.isEmpty(ossKeyList)){
            return;
        }
        OSS ossClient = this.getOssClient();
        try {
            // 批量删除oss文件
            DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(bucketName);
            deleteObjectsRequest.setKeys(ossKeyList);
            ossClient.deleteObjects(deleteObjectsRequest);
        } catch(Exception e){
            log.error("删除文件oss失败：",e);
        }
    }
}
