package com.py.crm.customer.customerlaundry.mapper;

import com.py.common.annotation.DataScope;
import com.py.common.datascope.DataScopePageType;
import com.py.common.mybatisplus.SuperMapper;
import com.py.crm.customer.customerlaundry.domain.CustomerLaundry;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryListDTO;
import com.py.crm.customer.customerlaundry.domain.query.CustomerLaundryQuery;
import com.py.crm.customer.customerlaundry.domain.vo.CustomerLaundryListVO;
import com.py.crm.customer.domain.SupCustomer;
import com.py.crm.customer.domain.vo.CustomerCountVO;

import java.util.List;

/**
 * 客户管理-查看清单Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
public interface CustomerLaundryMapper extends SuperMapper<CustomerLaundry> {

    /**
     * 分页客户管理-查看清单列表
     * @param query 客户管理-查看清单
     * @return 客户管理-查看清单分页
     */
    @DataScope(value = DataScopePageType.CRM_CustomerManage
            ,userIdAlias = "user_id",dateAlias = "audit_time",dateTableAlias = "customer")
    List<CustomerLaundryListVO> pageCustomerLaundryList(CustomerLaundryQuery query);

    /**
     * 客户管理-查看清单头部统计
     * @param query 客户管理-查看清单
     * @return 是否成功
     */
    @DataScope(value = DataScopePageType.CRM_CustomerManage
            ,userIdAlias = "user_id",dateAlias = "audit_time",dateTableAlias = "customer")
    CustomerCountVO customerLaundryCount(CustomerLaundryQuery query);

    /**
     * 客户管理-移除清单
     * @param dto 客户ID
     * @return 是否成功
     */
    Boolean removeLaundry(CustomerLaundryListDTO dto);
}
