package com.py.flow.tools.bpmnconverter;

import com.py.common.utils.JsonUtil;
import com.py.common.utils.spring.SpringUtils;
import com.py.flow.domain.enums.ApprovalBizType;
import com.py.flow.tools.bpmnconverter.model.StartNode;
import lombok.extern.log4j.Log4j2;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

/**
 * BPMN转换器测试类
 * <AUTHOR>
 */
@Log4j2
@SpringBootTest
@RunWith(SpringRunner.class)
public class BpmnConverterTest {

    /** 是否能进行转换 */
    @Ignore
    @Test
    public void can_convert() {
        // 准备
        BpmnConverter bpmnConverter = SpringUtils.getBean(BpmnConverter.class);

        // 执行
        bpmnConverter.convertBpmnXml("test", this.getTestBpmn(), ApprovalBizType.AddProject);

        // 验证
        String bpmnXml = bpmnConverter.getBpmnXml();
        Map<String, List<Long>> designationUserIdMap = bpmnConverter.getDesignationUserIdMap();

        log.info("bpmnXml: {}", bpmnXml);
        log.info("designationUserIdMap: {}", JsonUtil.obj2StringPretty(designationUserIdMap));
    }

    /** 是否能进行转换 带条件分支的 */
    @Ignore
    @Test
    public void can_convert2() {
        // 准备
        BpmnConverter bpmnConverter = SpringUtils.getBean(BpmnConverter.class);

        // 执行
        bpmnConverter.convertBpmnXml("test", this.getTestBpmn2(), ApprovalBizType.AddProject);

        // 验证
        String bpmnXml = bpmnConverter.getBpmnXml();
        Map<String, List<Long>> designationUserIdMap = bpmnConverter.getDesignationUserIdMap();

        log.info("bpmnXml: {}", bpmnXml);
        log.info("designationUserIdMap: {}", JsonUtil.obj2StringPretty(designationUserIdMap));
    }


    /**
     * 获取测试Bpmn
     * @return 测试Bpmn
     */
    private StartNode getTestBpmn() {
        String bpmnJson = "{\n" +
                "  \"nodeName\": \"新建项目\",\n" +
                "  \"type\": 1,\n" +
                "  \"childNode\": {\n" +
                "    \"nodeName\": \"部门审批\",\n" +
                "    \"type\": 2,\n" +
                "    \"approvalStrategy\": 1,\n" +
                "    \"selectModel\": 3,\n" +
                "    \"childNode\": {\n" +
                "      \"nodeName\": \"财务审批\",\n" +
                "      \"type\": 2,\n" +
                "      \"approvalStrategy\": 2,\n" +
                "      \"selectModel\": 2,\n" +
                "      \"childNode\":{\n" +
                "        \"nodeName\": \"总经理审批\",\n" +
                "        \"type\": 2,\n" +
                "        \"approvalStrategy\": 1,\n" +
                "        \"selectModel\": 1,\n" +
                "        \"selectedUserIdList\": [1]\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";
        return JsonUtil.string2Obj(bpmnJson, StartNode.class);
    }

    /**
     * 获取测试Bpmn - 带条件分支的
     * @return 测试Bpmn
     */
    private StartNode getTestBpmn2() {
        String bpmnJson = "{\n" +
                "  \"nodeName\": \"新建项目\",\n" +
                "  \"type\": 1,\n" +
                "  \"childNode\": {\n" +
                "    \"nodeName\": \"部门审批\",\n" +
                "    \"type\": 2,\n" +
                "    \"approvalStrategy\": 1,\n" +
                "    \"selectModel\": 3,\n" +
                "    \"childNode\": {\n" +
                "      \"nodeName\": \"部门分支\",\n" +
                "      \"type\": 4,\n" +
                "      \"conditionNodeList\":[\n" +
                "        {\n" +
                "          \"nodeName\": \"需经财务审批的部门\",\n" +
                "          \"type\": 3,\n" +
                "          \"mergeStrategy\": 1,\n" +
                "          \"conditionList\":[\n" +
                "            {\n" +
                "              \"property\": \"launcherDeptId\",\n" +
                "              \"condition\": 7,\n" +
                "              \"value\": \"1,321\"\n" +
                "            }\n" +
                "          ],\n" +
                "          \"childNode\": {\n" +
                "            \"nodeName\": \"财务审批\",\n" +
                "            \"type\": 2,\n" +
                "            \"approvalStrategy\": 2,\n" +
                "            \"selectModel\": 2\n" +
                "          }\n" +
                "        }\n" +
                "      ],\n" +
                "      \"childNode\":{\n" +
                "        \"nodeName\": \"总经理审批\",\n" +
                "        \"type\": 2,\n" +
                "        \"approvalStrategy\": 1,\n" +
                "        \"selectModel\": 1,\n" +
                "        \"selectedUserIdList\": [\n" +
                "          1\n" +
                "        ]\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";
        return JsonUtil.string2Obj(bpmnJson, StartNode.class);
    }
}
