package com.py.crm.customerdevote.service;

import com.github.pagehelper.PageInfo;
import com.py.crm.customerdevote.domain.dto.CustomerDevoteBadDebtExportModel;
import com.py.crm.customerdevote.domain.query.CustomerDevoteQuery;
import com.py.crm.customerdevote.domain.vo.CustomerDevoteBadDebtListVO;

import java.util.List;

/**
 * 收入坏账信息Service接口
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
public interface ICustomerDevoteBadDebtService  {

    /**
     * 分页查询收入坏账信息列表
     * @param query 收入坏账信息查询参数
     * @return 收入坏账信息分页
     */
    PageInfo<CustomerDevoteBadDebtListVO> pagePyIncomeBadDebtList(CustomerDevoteQuery query);

    /**
     * 查询要导出的坏账
     * @param query
     * @return
     */
    List<CustomerDevoteBadDebtExportModel> exportBadDebt(CustomerDevoteQuery query);
    /**
     * 查询要导出的坏账
     * @param query
     * @return
     */
    List<CustomerDevoteBadDebtExportModel> exportCustomerDevoteBadDebt(CustomerDevoteQuery query);
}
