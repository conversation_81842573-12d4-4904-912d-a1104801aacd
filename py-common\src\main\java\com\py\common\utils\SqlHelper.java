package com.py.common.utils;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import com.baomidou.mybatisplus.core.toolkit.support.ColumnCache;
import com.baomidou.mybatisplus.core.toolkit.support.LambdaMeta;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.py.common.core.domain.BaseEntity;
import com.py.common.core.domain.IBaseCreateInfo;
import com.py.common.core.domain.model.LoginUser;
import com.py.common.core.domain.query.BaseCreateListInfo;
import com.py.common.core.domain.query.ICreateQuery;
import com.py.common.core.domain.query.IProjectQuery;
import com.py.common.core.domain.query.IUpdateQuery;
import com.py.common.enums.EnumSetFilterType;
import com.py.common.enums.EnumSetUpdateType;
import com.py.common.enums.IBitEnum;
import com.py.common.utils.collection.ListUtil;
import lombok.NonNull;
import org.apache.ibatis.reflection.property.PropertyNamer;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Sql帮助类
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public final class SqlHelper {

    /**
     * 向更新包装器追加更新者信息
     * @param updateWrapper 更新包装器
     * @param entityClass 实体类型
     * @param <TEntity> 更新的实体类型
     */
    public static <TEntity extends BaseEntity> void appendUpdateInfo(
            LambdaUpdateWrapper<TEntity> updateWrapper,
            Class<TEntity> entityClass) {
        updateWrapper.setEntityClass(entityClass);
        updateWrapper.set(TEntity::getUpdateTime, DateUtils.getNowDate());

        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(loginUser == null) {
            return;
        }
        updateWrapper.set(TEntity::getUpdateId, loginUser.getUserId());
        updateWrapper.set(TEntity::getUpdateBy, loginUser.getUsername());
    }

    /**
     * 向更新包装器追加更新者信息
     * @param updateWrapper 更新包装器
     * @param <TEntity> 更新的实体类型
     */
    public static <TEntity extends BaseEntity> void appendUpdateInfo(
            LambdaUpdateWrapper<TEntity> updateWrapper) {
        Assert.notNull(updateWrapper.getEntityClass(), "使用不传 entityClass 版本的 appendUpdateInfo 时查询包装器初始化时必须设置 entityClass");
        appendUpdateInfo(updateWrapper, updateWrapper.getEntityClass());
    }

    /**
     * 向新增包装器追加更新者信息
     */
    public static <T extends BaseEntity> void appendAddUpdateInfo(BaseEntity baseEntity) {
        baseEntity.setUpdateTime(LocalDateTime.now());
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(loginUser == null) {
            return;
        }
        baseEntity.setUpdateId(loginUser.getUserId());
        baseEntity.setUpdateBy(loginUser.getUsername());
        baseEntity.setUpdateDept(loginUser.getDeptName());
    }

    /**
     * 向新增包装器追加更新者信息
     */
    public static <T extends BaseEntity> void appendAddUpdateInfo(List<? extends BaseEntity> baseEntityList) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        for(BaseEntity baseEntity : baseEntityList) {
            baseEntity.setUpdateTime(LocalDateTime.now());
            if(loginUser == null) {
                continue;
            }
            baseEntity.setUpdateId(loginUser.getUserId());
            baseEntity.setUpdateBy(loginUser.getUsername());
            baseEntity.setUpdateDept(loginUser.getDeptName());
        }
    }

    /**
     * 累加指定字段
     * <p>例1: selfIncrement(wrapper, ReserveIp::getReserveNumber, 5) => Set reserve_number = reserve_number + 5</p>
     * <p>例2: selfIncrement(wrapper, ReserveIp::getReserveNumber, -3) => Set reserve_number = reserve_number + -3</p>
     * @param updateWrapper 更新包装器
     * @param column 累加字段选择方法引用
     * @param reserveNumber 累加值, 可以为负数
     */
    public static <TEntity, TColumn extends Number> void selfIncrement(
            @NonNull LambdaUpdateWrapper<TEntity> updateWrapper,
            @NonNull SFunction<TEntity, TColumn> column,
            int reserveNumber) {
        String columnName = getColumnName(column, null);
        updateWrapper.setSql(String.format("%s = %s + %d", columnName, columnName, reserveNumber));
    }

    /**
     * 累加指定字段
     * <p>例1: selfIncrement(wrapper, ReserveIp::getReserveNumber, 5) => Set reserve_number = reserve_number + 5</p>
     * <p>例2: selfIncrement(wrapper, ReserveIp::getReserveNumber, -3) => Set reserve_number = reserve_number + -3</p>
     * @param updateWrapper 更新包装器
     * @param column 累加字段选择方法引用
     * @param reserveNumber 累加值, 可以为负数
     */
    public static <TEntity, TColumn extends Number> void selfIncrement(
            @NonNull LambdaUpdateWrapper<TEntity> updateWrapper,
            @NonNull SFunction<TEntity, TColumn> column,
            Long reserveNumber) {
        String columnName = getColumnName(column, null);
        updateWrapper.setSql(String.format("%s = %s + %d", columnName, columnName, reserveNumber));
    }

    /**
     * 更新枚举集合属性
     * @param updateWrapper 更新包装器
     * @param column 更新指定方法引用
     * @param updateType 更新类型
     * @param updateEnumItem 更新枚举集合项目
     * @param <TEntity> 实体类型
     * @param <TEnum> 枚举类型
     */
    public static <TEntity, TEnum extends Enum<TEnum> & IBitEnum> void updateEnumSetProperty(
            @NonNull LambdaUpdateWrapper<TEntity> updateWrapper,
            @NonNull SFunction<TEntity, EnumSet<TEnum>> column,
            @NonNull EnumSetUpdateType updateType,
            TEnum updateEnumItem) {
        updateEnumSetProperty(
                updateWrapper, column,
                updateType, Collections.singletonList(updateEnumItem));
    }

    /**
     * 更新枚举集合属性
     * @param updateWrapper 更新包装器
     * @param column 更新指定方法引用
     * @param updateType 更新类型
     * @param updateEnumList 更新枚举集合列表
     * @param <TEntity> 实体类型
     * @param <TEnum> 枚举类型
     */
    public static <TEntity, TEnum extends Enum<TEnum> & IBitEnum> void updateEnumSetProperty(
            @NonNull LambdaUpdateWrapper<TEntity> updateWrapper,
            @NonNull SFunction<TEntity, EnumSet<TEnum>> column,
            @NonNull EnumSetUpdateType updateType,
            List<TEnum> updateEnumList) {
        if(ListUtil.isEmpty(updateEnumList)) {
            return;
        }
        String configEnumBinaryStr = EnumUtils.toBinString(EnumSet.copyOf(updateEnumList));
        // 获取需要设置的列名
        String columnName = getColumnName(column, null);

        switch(updateType) {
            case append:
                // 按位或, 将位标志设置至数据库
                updateWrapper.setSql(String.format("%s = %s | b'%s'", columnName, columnName, configEnumBinaryStr));
                return;
            case remove:
                // 取反然后按位与, 清除数据库中指定的标志位
                updateWrapper.setSql(String.format("%s = %s & (~b'%s')", columnName, columnName, configEnumBinaryStr));
                return;
            case cover:
                // 直接覆盖
                updateWrapper.setSql(String.format("%s = b'%s'", columnName, configEnumBinaryStr));
                return;
            default:
                throw new UnsupportedOperationException(String.format("枚举: %s对应操作未实现", updateType));
        }
    }

    /**
     * 过滤枚举集合属性
     * @param <TEntity> 实体类型
     * @param <TEnum> 枚举类型
     * @param queryWrapper 查询包装器
     * @param column 过滤指定方法引用
     * @param filterType 过滤类型
     * @param filterEnumList 过滤枚举集合列表
     */
    public static <TEntity, TEnum extends Enum<TEnum> & IBitEnum> void filterEnumSetProperty(
            @NonNull LambdaQueryWrapper<TEntity> queryWrapper,
            @NonNull SFunction<TEntity, EnumSet<TEnum>> column,
            @NonNull EnumSetFilterType filterType,
            List<TEnum> filterEnumList) {
        if(ListUtil.isEmpty(filterEnumList)) {
            return;
        }
        // 将枚举转换为二进制字符串
        String configEnumBinaryStr = EnumUtils.toBinString(EnumSet.copyOf(filterEnumList));
        // 获取需要设置的列名
        String columnName = getColumnName(column, null);

        switch(filterType) {
            case all:
                queryWrapper.apply(String.format("%s & b'%s' = b'%s'", columnName, configEnumBinaryStr, configEnumBinaryStr));
                return;
            case any:
                queryWrapper.apply(String.format("%s & b'%s' != '0'", columnName, configEnumBinaryStr));
                return;
            case allNotExist:
                queryWrapper.apply(String.format("%s & b'%s' = '0'", columnName, configEnumBinaryStr));
                return;
            case ne:
                queryWrapper.apply(String.format("%s != b'%s'", columnName, configEnumBinaryStr));
                return;
            default:
                throw new UnsupportedOperationException(String.format("枚举: %s对应操作未实现", filterType));
        }
    }

    /**
     * 使用 Find_In_Set 函数过滤存在指定值的集合
     * <p>使用的对象字段数据库中必须已 , 分隔持久化列表数据</p>
     * @param wrapper 查询包装器
     * @param column 查询字段
     * @param value 查询指
     * @param <TEntity> 查询实体
     * @param <TValue> 查询枚举值
     */
    public static <TEntity, TValue> void findInSet(
            LambdaQueryWrapper<TEntity> wrapper,
            @NonNull SFunction<TEntity, Collection<TValue>> column,
            TValue value) {
        if(value == null) {
            return;
        }
        String columnName = getColumnName(column, null);
        wrapper.apply(String.format("Find_In_Set({0}, %s)", columnName), value);
    }

    /**
     * 使用 Find_In_Set 函数过滤存在指定值的集合
     * <p>使用的对象字段数据库中必须已 , 分隔持久化列表数据</p>
     * @param wrapper 查询包装器
     * @param column 查询字段
     * @param value 查询指
     * @param <TEntity> 查询实体
     * @param <TValue> 查询枚举值
     */
    public static <TEntity, TValue> void findInSetSingle(
            LambdaQueryWrapper<TEntity> wrapper,
            @NonNull SFunction<TEntity, TValue> column,
            TValue value) {
        if(value == null) {
            return;
        }
        String columnName = getColumnName(column, null);

        wrapper.apply(String.format("Find_In_Set(\"%s\", %s)", value,columnName));
    }


    /**
     * 使用正则表达式批量过滤存在指定值的集合
     * <p>使用的对象字段数据库中必须以 , 分隔持久化列表数据</p>
     * @param wrapper 查询包装器
     * @param column 查询字段
     * @param valueList 查询字段列表
     * @param <TEntity> 查询实体
     * @param <TValue> 查询枚举值
     */
    public static <TEntity, TValue> void mathInSet(
            LambdaQueryWrapper<TEntity> wrapper,
            @NonNull SFunction<TEntity, Collection<TValue>> column,
            List<TValue> valueList) {
        if(ListUtil.isEmpty(valueList)) {
            return;
        }
        // 如果只有一个值, 则直接使用 Find_In_Set 函数
        if(ListUtil.isSingleton(valueList)){
            findInSet(wrapper, column, ListUtil.singleOrThrow(valueList));
            return;
        }
        String columnName = getColumnName(column, null);
        String regex = RegexUtils.generateSetInRegex(valueList);
        wrapper.apply(String.format("%s Regexp '%s'", columnName, regex));
    }

    /**
     * 使用正则表达式批量过滤存在指定值的集合
     * <p>使用的对象字段数据库中必须以 , 分隔持久化列表数据</p>
     * @param wrapper 查询包装器
     * @param column 查询字段
     * @param valueList 查询字段列表
     * @param <TEntity> 查询实体
     * @param <TValue> 查询枚举值
     */
    public static <TEntity, TValue> void mathInSetString(
            LambdaQueryWrapper<TEntity> wrapper,
            @NonNull SFunction<TEntity, String> column,
            List<String> valueList) {
        if(ListUtil.isEmpty(valueList)) {
            return;
        }
        String columnName = getColumnName(column, null);
        String regex = valueList.stream().map(Objects::toString).collect(Collectors.joining("|"));
        wrapper.apply(String.format("%s Regexp '%s'", columnName, regex));
    }

    /**
     * 按天-过滤日期区间
     * <p>开始时间,结束时间任意值为空时不过滤</p>
     * @param wrapper 查询包装器
     * @param column 查询字段
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param <TEntity> 查询实体类型
     */
    public static <TEntity> void filterBetweenDay(
            LambdaQueryWrapper<TEntity> wrapper,
            SFunction<TEntity, ?> column,
            LocalDate startDate, LocalDate endDate) {

        if(startDate == null || endDate == null) {
            return;
        }
        wrapper.ge(column, DateUtils.getDayFirstTime(startDate))
                .le(column, DateUtils.getDayLastTime(endDate));
    }

    /**
     * 按天-过滤日期区间
     * <p>开始时间,结束时间任意值为空时不过滤</p>
     * @param wrapper 查询包装器
     * @param column 查询字段
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param <TEntity> 查询实体类型
     */
    public static <TEntity> void filterBetweenDay(
            LambdaQueryWrapper<TEntity> wrapper,
            SFunction<TEntity, ?> column,
            LocalDateTime startDate, LocalDateTime endDate) {

        if(startDate == null || endDate == null) {
            return;
        }
        wrapper.ge(column, DateUtils.getDayFirstTime(startDate))
                .le(column, DateUtils.getDayLastTime(endDate));
    }

    /**
     * 过滤近x天的数据
     * <p>x = null时, 显示全部数据</p>
     * <p>x = 0时, 显示当日数据</p>
     * <p>x = 1时, 昨天前的数据</p>
     * <p>x = 3时, 显示3天内的数据</p>
     * <p>x = 5时, 显示5天内的数据</p>
     * @param wrapper 查询包装器
     * @param column 查询字段
     * @param recentDay 过滤天数
     * @param <TEntity> 查询实体类型
     */
    public static <TEntity> void filterRecentDay(LambdaQueryWrapper<TEntity> wrapper, SFunction<TEntity, ?> column, Integer recentDay) {
        if(recentDay == null) {
            return;
        }

        Assert.isTrue(recentDay >= 0, "过滤天数必须大于等于0");
        if(recentDay == 1) {
            LocalDate yesterday = LocalDate.now().minusDays(1);
            wrapper.ge(column, DateUtils.getDayFirstTime(yesterday))
                    .le(column, DateUtils.getDayLastTime(yesterday));
        } else {
            wrapper.ge(column, DateUtils.getDayFirstTime(LocalDate.now().minusDays(recentDay)));
        }
    }

    /**
     * 过滤创建和更新相关信息
     * @param wrapper 查询包装器
     * @param query 查询条件
     * @param <TEntity> 查询实体类型
     * @param <TQuery> 查询条件类型
     */
    public static <TEntity extends BaseEntity, TQuery extends ICreateQuery&IUpdateQuery> void filterCreateAndUpdateInfo(
            @NonNull LambdaQueryWrapper<TEntity> wrapper, TQuery query) {
        filterCreateInfo(wrapper, query);
        filterUpdateInfo(wrapper, query);
    }

    /**
     * 过滤创建相关信息
     * @param wrapper 查询包装器
     * @param query 创建查询条件
     * @param <TEntity> 查询实体类型
     */
    public static <TEntity extends IBaseCreateInfo> void filterCreateInfo(@NonNull LambdaQueryWrapper<TEntity> wrapper, ICreateQuery query) {
        if(query == null) {
            return;
        }
        wrapper.like(StringUtils.isNotBlank(query.getCreateUser()), TEntity::getCreateBy, query.getCreateUser())
                .like(StringUtils.isNotBlank(query.getCreateDept()), TEntity::getCreateDept, query.getCreateDept());
        filterBetweenDay(wrapper, TEntity::getCreateTime, query.getStartCreateDate(), query.getEndCreateDate());
    }

    /**
     * 过滤更新相关信息
     * @param wrapper 查询包装器
     * @param query 更新查询条件
     * @param <TEntity> 查询实体类型
     */
    public static <TEntity extends BaseEntity> void filterUpdateInfo(@NonNull LambdaQueryWrapper<TEntity> wrapper, IUpdateQuery query) {
        if(query == null) {
            return;
        }
        // 更新人
        wrapper.in(ListUtil.any(query.getUpdateUser()), TEntity::getUpdateId, query.getUpdateUser());

        // 更新部门
        if(ListUtil.any(query.getUpdateDept())){
            String deptSql = String.format("update_Id in (Select Distinct user_id From sys_user_dept Where dept_id in( %s ))",
                        ListUtil.joining(query.getUpdateDept(), ",", deptId -> String.format("%d", deptId)));
            wrapper.apply(deptSql);
        }

        // 更新时间
        filterBetweenDay(wrapper, TEntity::getUpdateTime, query.getStartUpdateDate(), query.getEndUpdateDate());
    }

    /**
     * 过滤创建相关信息
     * @param wrapper 查询包装器
     * @param query 更新查询条件
     * @param tableAlias 表别名
     * @param <TEntity> 查询实体类型
     */
    public static <TEntity extends BaseEntity> void filterCreateListInfo(@NonNull LambdaQueryWrapper<TEntity> wrapper, BaseCreateListInfo query,String tableAlias) {
        if(query == null) {
            return;
        }
        // 创建人
        wrapper.in(ListUtil.any(query.getCreateUserIdList()), TEntity::getCreateId, query.getCreateUserIdList());

        // 创建部门
        if(ListUtil.any(query.getCreateDeptIdList())){
            String deptSql;
            if(StringUtils.isBlank(tableAlias)){
                deptSql = String.format("create_Id in (Select Distinct user_id From sys_user_dept Where dept_id in( %s ))",
                        ListUtil.joining(query.getCreateDeptIdList(), ",", deptId -> String.format("%d", deptId)));
            } else {
                deptSql = String.format("%s create_Id in (Select Distinct user_id From sys_user_dept Where dept_id in( %s ))",tableAlias + ".",
                        ListUtil.joining(query.getCreateDeptIdList(), ",", deptId -> String.format("%d", deptId)));
            }
            wrapper.apply(deptSql);
        }
//        if(ListUtil.any(query.getCreateDeptList())){
//          wrapper.in(ListUtil.any(query.getCreateDeptList()), TEntity::getCreateDept, query.getCreateDeptList());
//
//        }

        // 创建时间
        filterBetweenDay(wrapper, TEntity::getCreateTime, query.getStartCreateDate(), query.getEndCreateDate());
    }

    /**
     * 过滤部门list相关信息，用于发起人部门匹配查询sql
     * @param wrapper 查询包装器
     * @param query 更新查询条件
     * @param tableAlias 表别名
     * @param <TEntity> 查询实体类型
     */
    public static <TEntity extends BaseEntity> void filterCreateDeptListInfo(@NonNull LambdaQueryWrapper<TEntity> wrapper, BaseCreateListInfo query,  String tableAlias) {
        //如果传入查询部门list为空不拼接
        if (ListUtil.isEmpty(query.getCreateDeptList())) {
            return;
        }
        String createDept = "create_dept";
        //三元判断用于sql， 如果传入表别名为空直接拼接部门字段 ，如果不为空拼接表别名.字段
        tableAlias = StringUtils.isBlank(tableAlias) ? createDept : tableAlias + "." + createDept;

        wrapper.apply(String.format("CONCAT ( ',', %s , ',' ) REGEXP %s", tableAlias, "',(" + String.join("|", query.getCreateDeptList()) + "),'"));

    }


    /**
     * 过滤更新相关信息
     * @param wrapper 查询包装器
     * @param query 更新查询条件
     * @param tableAlias 表别名
     * @param <TEntity> 查询实体类型
     */
    public static <TEntity extends BaseEntity> void filterUpdateInfoTableAlias(@NonNull LambdaQueryWrapper<TEntity> wrapper, IUpdateQuery query,String tableAlias) {
        if(query == null) {
            return;
        }
        // 更新人
        wrapper.in(ListUtil.any(query.getUpdateUser()), TEntity::getUpdateId, query.getUpdateUser());

        // 更新部门
        if(ListUtil.any(query.getUpdateDept())){
            String deptSql;
            if(StringUtils.isBlank(tableAlias)){
                deptSql = String.format("update_Id in (Select Distinct user_id From sys_user_dept Where dept_id in( %s ))",
                        ListUtil.joining(query.getUpdateDept(), ",", deptId -> String.format("%d", deptId)));
            } else {
                deptSql = String.format("%s update_Id in (Select Distinct user_id From sys_user_dept Where dept_id in( %s ))",tableAlias + ".",
                        ListUtil.joining(query.getUpdateDept(), ",", deptId -> String.format("%d", deptId)));
            }
            wrapper.apply(deptSql);
        }

        // 更新时间
        filterBetweenDay(wrapper, TEntity::getUpdateTime, query.getStartUpdateDate(), query.getEndUpdateDate());
    }

    /**
     * 过滤项目信息
     * @param wrapper 查询包装器
     * @param query 查询条件
     * @param <TEntity> 查询实体类型
     */
    public static <TEntity extends BaseEntity> void filterProjectInfo(@NonNull LambdaQueryWrapper<TEntity> wrapper, IProjectQuery query) {

        if (query == null) {
            return;
        }
        // 项目名称
        if (StringUtils.isNotEmpty(query.getProjectName())) {
            String projectSql = "rebate.project_id in (Select Distinct project_id From py_project Where del_flag = 0 and project_name like '%" + query.getProjectName() + "%' )";
            wrapper.apply(projectSql);
        }
        if (CollectionUtils.isNotEmpty(query.getProjectUserList())) {
            String projectSql = String.format("project_id in (Select Distinct project_id From py_project_user Where del_flag = 0 and user_id in ( %s ))",
                    ListUtil.joining(query.getProjectUserList(), ",", userId -> String.format("%d", userId)));
            wrapper.apply(projectSql);
        }

    }

    /**
     * 获取 SerializedLambda 对应的列信息，从 lambda 表达式中推测实体类
     * <p>
     * 如果获取不到列信息，那么本次条件组装将会失败
     * @return 列
     * @throws com.baomidou.mybatisplus.core.exceptions.MybatisPlusException 获取不到列信息时抛出异常
     */
    public static <TEntity> String getColumnName(SFunction<TEntity, ?> column, Class<TEntity> entityClass) {
        LambdaMeta meta = LambdaUtils.extract(column);
        Class<?> instantiatedClass = NullMergeUtils.nullMerge(entityClass, meta.getInstantiatedClass());
        String fieldName = PropertyNamer.methodToProperty(meta.getImplMethodName());
        Map<String, ColumnCache> columnMap = LambdaUtils.getColumnMap(instantiatedClass);

        ColumnCache columnCache = columnMap.get(LambdaUtils.formatKey(fieldName));
        Assert.notNull(columnCache, "无法找到实体 [%s] 的 字段 [%s] 的列缓存信息, 如果使用不传 entityClass 的方法, 请尝试使用或创建传 entityClass 的版本",
                fieldName, instantiatedClass.getName());
        return columnCache.getColumn();
    }

    /**
     * 使用查询包装器, 查询Id
     * @param service 查询的服务
     * @param queryWrapper 仅查询Id的查询包装器
     * @param <TEntity> 查询实体类型
     * @return Id
     */
    public static <TEntity> Long selectId(IService<TEntity> service, Wrapper<TEntity> queryWrapper) {
        return service.getObj(queryWrapper, id -> (Long) id);
    }

    /**
     * 使用查询包装器, 查询Id
     * @param service 查询的服务
     * @param queryWrapper 仅查询Id的查询包装器
     * @param <TEntity> 查询实体类型
     * @return Id
     */
    public static <TEntity> List<Long> selectIdList(IService<TEntity> service, Wrapper<TEntity> queryWrapper) {
        return service.listObjs(queryWrapper, id -> (Long) id);
    }

    /**
     * 将字段拼接为 find_in_set() 函数正则
     * @param findInIdList 需要拼接的集合
     * @return find_in_set() 函数正则
     * 用法   SELECT 字段 FROM 表名 WHERE
     * CONCAT ( ',', 查询字段, ',' ) REGEXP ${本方法返回字段}
     * @param <T> list类型
     */
    public static<T> String findInSetStr(List<T> findInIdList){
        return "',(" + findInIdList.stream().distinct()
                .map(Object::toString)
                .collect(Collectors.joining("|")) +  "),'";
    }
}
