package com.py.crm.customer.invoicing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.py.common.tools.modifycomparator.IObjectComparator;
import com.py.common.tools.modifycomparator.model.BaseModifyDiff;
import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.SqlHelper;
import com.py.common.utils.collection.ListUtil;
import com.py.common.utils.PageUtils;
import com.py.common.mybatisplus.SuperServiceImpl;
import com.py.crm.crmupdaterecord.domain.dto.CrmUpdateRecordDTO;
import com.py.crm.crmupdaterecord.domain.enums.RecordBizType;
import com.py.crm.crmupdaterecord.domain.enums.RecordMenuType;
import com.py.crm.crmupdaterecord.service.ICrmUpdateRecordService;
import com.py.crm.customer.customeraccount.domain.CustomerAccount;
import com.py.crm.customer.invoicing.converter.InvoicingConverter;
import com.py.crm.customer.invoicing.domain.Invoicing;
import com.py.crm.customer.invoicing.domain.dto.InvoicingDTO;
import com.py.crm.customer.invoicing.domain.dto.InvoicingExportModel;
import com.py.crm.customer.invoicing.domain.vo.InvoicingListVO;
import com.py.crm.customer.invoicing.mapper.InvoicingMapper;
import com.py.crm.customer.invoicing.domain.vo.InvoicingVO;
import com.py.crm.customer.invoicing.domain.query.InvoicingQuery;
import com.py.crm.customer.invoicing.service.IInvoicingService;
import com.github.pagehelper.PageInfo;
import com.py.system.recyclebin.domain.dto.DeleteAddDTO;
import com.py.system.recyclebin.domain.enums.RecycleBizType;
import com.py.system.recyclebin.service.IRecycleBinService;
import com.py.system.user.service.ISysUserService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 客户管理-客户-开票信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Service
public class InvoicingServiceImpl
        extends SuperServiceImpl<InvoicingMapper, Invoicing>
        implements IInvoicingService {

    /** 客户管理-客户-开票信息模型转换器 */
    @Resource
    private InvoicingConverter invoicingConverter;

    /** 客户管理-客户-开票信息Mapper接口 */
    @Resource
    private InvoicingMapper invoicingMapper;

    /** 对象比较器 */
    @Resource
    private IObjectComparator objectComparator;

    /** 客户/人脉(状态)更新记录表服务 */
    @Resource
    private ICrmUpdateRecordService crmUpdateRecordService;

    /** 用户 业务层 */
    @Resource
    private ISysUserService userService;
    /** 回收站服务 */
    @Resource
    private final IRecycleBinService recycleBinService;

    public InvoicingServiceImpl(
            IRecycleBinService recycleBinService) {
        recycleBinService.registeredRestoreCallback(RecycleBizType.CUSTOMER_INVOICING, this::restore);
        this.recycleBinService = recycleBinService;
    }

    /**
     * 分页客户管理-客户-开票信息列表
     * @param query 客户管理-客户-开票信息
     * @return 客户管理-客户-开票信息分页
     */
    @Override
    public PageInfo<InvoicingListVO> pageinvoicingList(InvoicingQuery query) {
        PageUtils.startPage();
        List<Invoicing> invoicingList = this.list();
        List<InvoicingListVO> invoicingVoList = this.invoicingConverter.toListVoByEntity(invoicingList);
        return ListUtil.pageConvert(invoicingList, invoicingVoList);
    }

    /**
     * 分页查询客户管理-客户-开票信息列表--下拉列表
     *
     * @param query 客户管理-客户-开票信息查询参数
     * @return 客户管理-客户-开票信息分页
     */
    @Override
    public PageInfo<InvoicingListVO> pageInvoicingSelected(InvoicingQuery query) {
        PageUtils.startPage();
        List<Invoicing> invoicingList = this.list(Wrappers.<Invoicing>lambdaQuery()
                .eq(Invoicing::getCustomerId, query.getCustomerId())
                .orderByAsc(Invoicing::getShowId));
        List<InvoicingListVO> invoicingVoList = this.invoicingConverter.toListVoByEntity(invoicingList);
        if(ListUtil.isNotEmpty(invoicingVoList)) {
            invoicingVoList.forEach(invoicing->
                    invoicing.setDefaultInfo(String.format("开票名称:%s,税号:%s,开户银行:%s,银行账号:%s,公司地址:%s,电话:%s",
                            NullMergeUtils.blankMerge(invoicing.getInvoicingName(),"--"),
                            NullMergeUtils.blankMerge(invoicing.getDutyParagraph(),"--"),
                            NullMergeUtils.blankMerge(invoicing.getBankDeposit(),"--"),
                            NullMergeUtils.blankMerge(invoicing.getBankAccount(),"--"),
                            NullMergeUtils.blankMerge(invoicing.getCompanyAddress(),"--"),
                            NullMergeUtils.blankMerge(invoicing.getPhone(),"--"))));
        }
        return ListUtil.pageConvert(invoicingList, invoicingVoList);
    }

    /**
     * 根据客户id查看开票信息
     * @param customerIdList 客户id
     * @return 开票信息
     */
    @Override
    public List<InvoicingVO> listInvoicingByCustomerId(List<Long> customerIdList) {
        if(ListUtil.isEmpty(customerIdList)){
            return ListUtil.emptyList();
        }
        List<Invoicing> invoicingList = this.list(Wrappers.<Invoicing>lambdaQuery()
                .in(Invoicing::getCustomerId, customerIdList)
                .orderByAsc(Invoicing::getShowId));

        return this.invoicingConverter.toVoByEntity(invoicingList);
    }

    /**
     * 编辑客户时，如果未传开票信息数据，则需删除旧的开票信息数据
     * @param customerId
     * @param name
     */
    @Override
    public void deleteBatchInvoicing(Long customerId, String name) {
        List<CrmUpdateRecordDTO> crmUpdateRecordList = new ArrayList();

        //删除开票信息
        removeInvoicing(customerId, null, crmUpdateRecordList, name);

        //批量新增更新记录
        if(ListUtil.isNotEmpty(crmUpdateRecordList)) {
            crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordList);
        }
    }

    /**
     * 根据客户ID删除
     * @param bizIds 客户ID
     */
    @Override
    public void deleteByCustomerIds(List<Long> bizIds) {
        this.remove(Wrappers.<Invoicing>lambdaQuery()
                .in(Invoicing::getCustomerId,bizIds));
    }

    /**
     * 查询客户管理-客户-开票信息
     * @param id 客户管理-客户-开票信息主键
     * @return 客户管理-客户-开票信息视图模型
     */
    @Override
    public InvoicingVO selectinvoicingById(Long id) {
        Invoicing invoicing = this.getById(id);
        return this.invoicingConverter.toVoByEntity(invoicing);
    }

    /**
     * 查询客户管理-客户-开票信息详细信息
     * @param id 客户管理-客户-开票信息主键
     * @return 客户管理-客户-开票信息视图模型
     */
    @Override
    public List<InvoicingVO> selectinvoicingDetailById(Long id) {
        List<Invoicing> invoicingList = this.list(Wrappers.<Invoicing>lambdaQuery()
                .eq(Invoicing::getCustomerId, id)
                .orderByAsc(Invoicing::getShowId));
        List<InvoicingVO> invoicingVoS = this.invoicingConverter.toVoByEntity(invoicingList);
        this.userService.relatedUpdateInfo(invoicingVoS);
        return invoicingVoS;
    }

    /**
     * 新增客户管理-客户-开票信息
     * @param dto 客户管理-客户-开票信息修改参数
     * @return 是否成功
     */
    @Override
    public boolean insertinvoicing(InvoicingDTO dto) {
        Invoicing invoicing = this.invoicingConverter.toEntityByDto(dto);
        return this.save(invoicing);
    }

    /**
     * 修改客户管理-客户-开票信息
     * @param dto 客户管理-客户-开票信息修改参数
     * @return 是否成功
     */
    @Override
    public boolean updateinvoicing(InvoicingDTO dto) {
        Invoicing invoicing = this.invoicingConverter.toEntityByDto(dto);
        return this.updateById(invoicing);
    }

    /**
     * 批量删除客户管理-客户-开票信息
     * @param idList 需要删除的客户管理-客户-开票信息主键
     * @return 是否成功
     */
    @Override
    public boolean deleteinvoicingByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

    /**
     * 导出客户管理-客户-开票信息
     * @param query 导出查询参数
     * @return 导出数据
     */
    @Override
    public List<InvoicingExportModel> exportinvoicing(InvoicingQuery query) {
        List<Invoicing> invoicingList = this.list();
        return this.invoicingConverter.toExportModel(invoicingList);
    }

    /**
     * 导入客户管理-客户-开票信息
     * @param exportList 导入数据
     * @return 导入结果
     */
    @Override
    public String importinvoicing(List<InvoicingExportModel> exportList) {
        List<Invoicing> invoicingList = this.invoicingConverter.toEntityByExportModel(exportList);
        this.saveBatch(invoicingList);
        return "导入成功!";
    }

    /**
     * 批量新增开票信息数据
     * @param dtoList 开票信息数据传输模型
     * @param customerId 客户ID
     * @param isApprovalEdit 是否是审批驳回修改
     */
    @Override
    public void insertBatchInvoicing(List<InvoicingDTO> dtoList, Long customerId, Boolean isApprovalEdit) {
        if(isApprovalEdit){
            this.remove(Wrappers.<Invoicing>lambdaQuery().eq(Invoicing::getCustomerId,customerId));
        }
        if(ListUtil.isEmpty(dtoList)){
            return;
        }
        List<Invoicing> invoicingList = invoicingConverter.toEntityByDto(dtoList);
        int id = 1;
        for(Invoicing invoicing : invoicingList) {
            invoicing.setCustomerId(customerId);
            invoicing.setShowId(id);
            id++;
        }
        // 新增的更新记录
        List<CrmUpdateRecordDTO> crmUpdateRecordDTOList = new ArrayList<>();
        this.addUpdateRecord(crmUpdateRecordDTOList, invoicingList);
        crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordDTOList);
        SqlHelper.appendAddUpdateInfo(invoicingList);
        this.saveBatch(invoicingList);
    }

    /**
     * 批量修改开票信息数据
     * @param dtoList 开票信息数据传输模型
     * @param customerId 客户ID
     * @param name 客户名称
     */
    @Override
    public void updateBatchInvoicing(List<InvoicingDTO> dtoList, Long customerId, String name) {
        List<Long> editInvoicingIdList = dtoList.stream().map(InvoicingDTO::getInvoicingId).filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<CrmUpdateRecordDTO> crmUpdateRecordDTOList = new ArrayList<>();
        if(ListUtil.isNotEmpty(editInvoicingIdList)){
            List<Invoicing> invoicingList = this.list(Wrappers.<Invoicing>lambdaQuery()
                    .in(Invoicing::getInvoicingId, editInvoicingIdList));

            List<InvoicingDTO> invoicingDTOList = dtoList.stream().filter(invoicingDTO -> invoicingDTO.getInvoicingId() != null)
                    .collect(Collectors.toList());
            List<Invoicing> editInvoicings = invoicingConverter.toEntityByDto(invoicingDTOList);
            List<Invoicing> invoicings = new ArrayList<>();
            this.editInvoicing(crmUpdateRecordDTOList, invoicingList, editInvoicings,invoicings);
            if(ListUtil.isNotEmpty(invoicings)){
                this.updateBatchById(invoicings);
            }
        }
        this.removeInvoicing(customerId, editInvoicingIdList, crmUpdateRecordDTOList,name);
        List<InvoicingDTO> invoicingDTOList = dtoList.stream()
                .filter(invoicingDTO -> invoicingDTO.getInvoicingId() == null).collect(Collectors.toList());
        if(ListUtil.isNotEmpty(invoicingDTOList)){
            List<Invoicing> addInvoicings = invoicingConverter.toEntityByDto(invoicingDTOList);
            Integer showId = invoicingMapper.getShowId(customerId);
            showId = showId == null ? 1 : showId;
            for(Invoicing invoicing : addInvoicings) {
                invoicing.setCustomerId(customerId);
                invoicing.setShowId(showId);
                showId++;
            }
            this.addUpdateRecord(crmUpdateRecordDTOList, addInvoicings);
            SqlHelper.appendAddUpdateInfo(addInvoicings);
            this.saveBatch(addInvoicings);
        }
        crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordDTOList);
    }

    /**
     * 新增更新记录
     * @param crmUpdateRecordDTOList 更新记录
     * @param addInvoicings 新增的开票
     */
    private void addUpdateRecord(List<CrmUpdateRecordDTO> crmUpdateRecordDTOList, List<Invoicing> addInvoicings) {
        addInvoicings.forEach(invoicing -> {
            List<BaseModifyDiff<?>> baseModifyDiffs = objectComparator.compareObject(new Invoicing(), invoicing, Invoicing.class);
            baseModifyDiffs.forEach(baseModifyDiff -> {
                CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                String content = "企业开票信息-新增ID: " + invoicing.getShowId() + "【" + ListUtil.last(baseModifyDiff.getFieldPath()) + "】" + "“" + baseModifyDiff.getAfter() + "”";
                crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(),invoicing.getCustomerId()
                        ,content, RecordMenuType.UPDATE_RECORD.getValue(),null,null);
                crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
            });
        });
    }

    /**
     * 删除
     * @param customerId 客户id
     * @param editInvoicingIdList 修改的开票id
     * @param crmUpdateRecordDTOList 更新记录
     * @param name 客户名称
     */
    private void removeInvoicing(Long customerId, List<Long> editInvoicingIdList, List<CrmUpdateRecordDTO> crmUpdateRecordDTOList, String name) {
        LambdaQueryWrapper<Invoicing> wrapper = Wrappers.<Invoicing>lambdaQuery()
                .in(Invoicing::getCustomerId, customerId)
                .notIn(ListUtil.isNotEmpty(editInvoicingIdList),Invoicing::getInvoicingId, editInvoicingIdList);
        List<Invoicing> invoicingList = list(wrapper);
        if(ListUtil.isNotEmpty(invoicingList)){
            invoicingList.forEach(invoicing -> {
                CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                String content = "企业开票信息-删除ID: " + invoicing.getShowId() + "【开票名称】" + "“" + invoicing.getInvoicingName() + "”";
                crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(),invoicing.getCustomerId()
                        ,content, RecordMenuType.UPDATE_RECORD.getValue(),null,null);
                crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
            });
            this.remove(wrapper);
            List<DeleteAddDTO> deleteAddDTOList = new ArrayList<>();
            invoicingList.forEach(invoicing -> {
                DeleteAddDTO deleteAddDTO = new DeleteAddDTO();
                deleteAddDTO.setBizId(invoicing.getInvoicingId());
                deleteAddDTO.setContent(name + "-企业开票信息:" + invoicing.getInvoicingName() );
                deleteAddDTOList.add(deleteAddDTO);
            });
            recycleBinService.addDeleteRecord(RecycleBizType.CUSTOMER_INVOICING,deleteAddDTOList);
        }
    }

    /**
     * 修改字段对比
     * @param crmUpdateRecordDTOList 更新记录
     * @param invoicingList 修改的原记录
     * @param editInvoicings 修改的数据
     */
    private void editInvoicing(List<CrmUpdateRecordDTO> crmUpdateRecordDTOList,  List<Invoicing> invoicingList,  List<Invoicing> editInvoicings,List<Invoicing> invoicings) {
        Map<Long, Invoicing> invoicingIdMap = ListUtil.toMap(editInvoicings, Invoicing::getInvoicingId);
        invoicingList.forEach(invoicing -> {
            Invoicing editInvoicing = invoicingIdMap.get(invoicing.getInvoicingId());
            List<BaseModifyDiff<?>> baseModifyDiffs = objectComparator.compareObject(invoicing, editInvoicing, Invoicing.class);
            if(ListUtil.isNotEmpty(baseModifyDiffs)){
                baseModifyDiffs.forEach(baseModifyDiff -> {
                    CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                    Object source = baseModifyDiff.getBefore() != null ? baseModifyDiff.getBefore() : "--";
                    Object after = baseModifyDiff.getAfter() != null ? baseModifyDiff.getAfter() : "--";
                    String content = "企业开票信息-ID:" + invoicing.getShowId() +"【" + ListUtil.last(baseModifyDiff.getFieldPath()) + "】从" + "“" + source + "”更新为“" + after + "”";
                    crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(),invoicing.getCustomerId()
                            ,content, RecordMenuType.UPDATE_RECORD.getValue(),null,null);
                    crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
                });
                invoicings.add(editInvoicing);
            }
        });
    }
}
