package com.py.crm.connection.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.tools.modifycomparator.annotation.CompareField;
import com.py.crm.connectionemployment.domain.vo.ConnectionEmploymentListVO;
import com.py.flow.domain.enums.ApprovalBizType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 人脉管理表视图模型
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
@ApiModel("人脉管理表视图模型" )
public class ConnectionVO {
    private static final long serialVersionUID = 1L;

    /** 自增id */
    @ApiModelProperty("自增id")
    private Long id;

    /** 人脉Id */
    @ApiModelProperty("人脉Id")
    private Long connectionId;

    /** 客户名称 */
    @ApiModelProperty("客户名称")
    @CompareField(value = "人脉姓名")
    private String connectionName;

    /** 状态（0无效；1有效） */
    @ApiModelProperty("状态（0无效；1有效）")
    private Integer status;

    /** 电话 */
    @ApiModelProperty("电话")
    @CompareField(value = "电话")
    private String phone;

    /** 微信 */
    @ApiModelProperty("微信")
    @CompareField(value = "微信")
    private String wechatNumber;

    /** 钉钉/飞书/其它 */
    @ApiModelProperty("钉钉/飞书/其它")
    @CompareField(value = "钉钉/飞书/其它")
    private String otherNumber;

    /** 现任职企业id(客户id) */
    @ApiModelProperty("现任职企业id(客户id)")
    private Long customerId;

    /** 现任职企业（手动输入） */
    @ApiModelProperty("现任职企业（手动输入）")
    @CompareField(value = "现任职企业")
    private String currentEmployer;

    /** 负责品牌/业务线 */
    @ApiModelProperty("负责品牌/业务线")
    @CompareField(value = "负责品牌/业务线")
    private List<String> responsibleBrand;
    private String responsibleBrandStr;

    /** 行业类目(来源于数据字典) */
    @ApiModelProperty("行业类目(来源于数据字典)")
    @CompareField(value = "行业类目")
    private List<Long> industryCategory;
    @ApiModelProperty("行业类目(来源于数据字典)")
    private String industryCategoryStr;
    /** 所在部门 */
    @ApiModelProperty("所在部门")
    @CompareField(value = "所在部门")
    private String departmentName;

    /** 岗位名称 */
    @ApiModelProperty("岗位")
    @CompareField(value = "岗位")
    private String postName;

    /** 对派芽信任度（数据字典） */
    @ApiModelProperty("对派芽信任度（数据字典）")
    @CompareField(value = "对派芽信任度")
    private String pyTrustLevel;
    @ApiModelProperty("对派芽信任度（数据字典）")
    private String pyTrustLevelStr;
    /** 人脉地址 */
    @ApiModelProperty("人脉地址")
    @CompareField(value = "人脉地址")
    private String connectionAddress;
    @ApiModelProperty("备注" )
    @CompareField(value = "备注")
    private String remark;
    /** 目标服务人员id */
    @ApiModelProperty("目标服务人员id")
    private Long serviceUserId;

    /** 创建人部门 */
    @ApiModelProperty("发起人部门")
    private String createDept;

    /** 更新人部门 */
    @ApiModelProperty("更新人部门")
    private String updateDept;
    @ApiModelProperty("发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime initiationTime;
    @ApiModelProperty("发起人")
    private String createBy;

    /** 版本*/
    @ApiModelProperty("版本")
    private Integer version;

    /** 审批业务类型 */
    private ApprovalBizType bizType;

    /** 从业经历列表 */
    @ApiModelProperty("从业经历列表")
    private List<ConnectionEmploymentListVO> employmentList;
}
