package com.py.web.controller.monitor;

import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.AjaxResult;
import com.py.common.core.page.TableDataInfo;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.utils.BeanConvertUtils;
import com.py.common.utils.JsonUtil;
import com.py.common.utils.collection.ListUtil;
import com.py.system.log.domain.SysOperLog;
import com.py.system.log.domain.query.SysOperatingLogQuery;
import com.py.system.log.domain.vo.SysOperLogVO;
import com.py.system.log.service.ISysOperLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 操作日志记录
 * <AUTHOR>
 */
@Api(tags = "操作日志")
@RestController
@RequestMapping("/monitor/operlog")
public class SysOperlogController extends BaseController {
    @Resource
    private ISysOperLogService operLogService;

    /**
     * 分页查询操作日志
     * @param operLog 查询参数
     * @return 日志列表
     */
    @ApiOperation("分页查询操作日志")
    @PreAuthorize("@ss.hasPermi('monitor:operlog:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysOperatingLogQuery operLog) {
        startPage();
        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
        List<SysOperLogVO> voList = list.stream().map(item -> {
            SysOperLogVO operLogVO = BeanConvertUtils.convert(item, SysOperLogVO.class);
            operLogVO.setOperParam(JsonUtil.string2Obj(item.getOperParam(), Object.class));
            operLogVO.setCnOperParam(JsonUtil.string2Obj(item.getCnOperParam(), Object.class));
            operLogVO.setJsonResult(JsonUtil.string2Obj(item.getJsonResult(), Object.class));
            operLogVO.setCnJsonResult(JsonUtil.string2Obj(item.getCnJsonResult(), Object.class));
            return operLogVO;
        }).collect(Collectors.toList());
        return getDataTable(ListUtil.pageConvert(list, voList));
    }

    /**
     * 导出操作日志
     * @param response 请求响应
     * @param query 操作日志查询条件
     */
    @ApiOperation("导出操作日志")
    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysOperatingLogQuery query) {
        List<SysOperLog> list;
        if(ListUtil.any(query.getExportLogIdList())) {
            list = this.operLogService.selectOperLogListById(query.getExportLogIdList());
        } else {
            list = this.operLogService.selectOperLogList(query);
        }
        ExcelUtil<SysOperLog> util = new ExcelUtil<>(SysOperLog.class);
        util.exportExcel(response, list, "操作日志");
    }

    /**
     * 删除操作日志
     * @param operIds 日志Id列表
     * @return 是否成功
     */
    @ApiOperation("删除操作日志")
    @Log(title = "操作日志", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @DeleteMapping("/{operIds}")
    public AjaxResult remove(@PathVariable List<Long> operIds) {
        return toAjax(operLogService.deleteOperLogByIds(operIds));
    }

    /**
     * 清空操作日志
     * @return 是否成功
     */
    @ApiOperation("清空操作日志")
    @Log(title = "操作日志", businessType = BusinessType.CLEAN)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @DeleteMapping("/clean")
    public AjaxResult clean() {
        operLogService.cleanOperLog();
        return AjaxResult.success();
    }
}
