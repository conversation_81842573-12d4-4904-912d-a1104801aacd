package com.py.common.tools.codegenerator;

import com.py.common.core.redis.RedisCache;
import com.py.common.tools.codegenerator.enums.SerialNumberBizType;
import com.py.common.tools.codegenerator.functional.SerialNumberInitCallback;
import com.py.common.utils.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.concurrent.TimeUnit;

/**
 * 业务序列号生成器
 * <AUTHOR>
 */
@Component
public class BusinessSerialNumberGenerator implements IBusinessSerialNumberGenerator {

    /** 编码键 */
    private static final String BUSINESS_CODE_KEY = "BusinessProductCode";

    /** redis模板 */
    @Resource
    private RedisTemplate<String, Long> redisTemplate;

    /** spring redis 工具类 */
    @Resource
    private RedisCache redisCache;


    /**
     * 生成序列号
     * @param bizType 序列号业务类型
     * @param initCallback 初始化回调, 缓存中没有序列号时调用该方法, 用户缓存失效时使用数据库里的序列号初始化缓存
     * @return 序列号
     */
    @Override
    public long generateSerialNumber(
            SerialNumberBizType bizType,
            SerialNumberInitCallback initCallback) {
        return this.generateSerialNumber(bizType, null, initCallback);
    }

    /**
     * 生成序列号
     * @param bizType 序列号业务类型
     * @param otherId 其他识别码
     * @param initCallback 初始化回调, 缓存中没有序列号时调用该方法, 用户缓存失效时使用数据库里的序列号初始化缓存
     * @return 序列号
     */
    @Override
    public long generateSerialNumber(
            SerialNumberBizType bizType,
            String otherId,
            SerialNumberInitCallback initCallback) {
        return this.generateSerialNumber(bizType, otherId, initCallback, 30, TimeUnit.DAYS);
    }

    /**
     * 生成序列号
     * @param bizType 序列号业务类型
     * @param otherId 其他识别码
     * @param initCallback 初始化回调, 缓存中没有序列号时调用该方法, 用户缓存失效时使用数据库里的序列号初始化缓存
     * @param timeout 缓存失效时间
     * @param timeUnit 缓存失效时间单位
     * @return 序列号
     */
    @Override
    public long generateSerialNumber(
            SerialNumberBizType bizType,
            String otherId,
            SerialNumberInitCallback initCallback,
            Integer timeout,
            TimeUnit timeUnit) {
        // 初始化 Key
        String key;
        if(StringUtils.isBlank(otherId)) {
            key = String.format("%s:%s", BUSINESS_CODE_KEY, bizType.name());
        } else {
            key = String.format("%s:%s%s", BUSINESS_CODE_KEY, bizType.name(), otherId);
        }

        // 获取序列号
        Long serialNo = this.redisTemplate.opsForValue().increment(key);
        if(serialNo != null && serialNo != 1) {
            this.redisTemplate.expire(key, timeout, timeUnit);
            return serialNo;
        }
        // 序列号未初始化时, 调用回调函数初始化序列号
        long newSerialNo = initCallback.init();
        this.redisCache.setCacheObject(key, BigInteger.valueOf(newSerialNo), timeout, timeUnit);
        return newSerialNo;
    }

    /**
     * 删除缓存
     * @param bizType 序列号业务类型
     */
    @Override
    public Boolean clearSerialNumberCache(SerialNumberBizType bizType) {
        String key = String.format("%s:%s", BUSINESS_CODE_KEY, bizType.name());
        return this.redisCache.deleteObject(key);
    }
}
