package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.exception.ServiceException;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.project.primecost.domin.dto.PaymentCostImportModel;
import com.py.project.primecost.domin.query.PrimeCostQuery;
import com.py.project.primecost.domin.vo.*;
import com.py.project.primecost.service.IPrimeCostService;
import com.py.project.primecost.service.impl.PrimeCostServiceImpl;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 成本管理
 * <AUTHOR>
 * @version PrimeCostController 2023/8/3 15:53
 */
@Api(tags = "成本管理")
@RestController
@RequestMapping("/prime/cost")
public class PrimeCostController extends BaseController {

    /** 成本管理 */
    @Resource
    private IPrimeCostService primeCostService;

    /** 系统设置-主体参数设置服务 */
    @Resource
    private ISystemMainstayParamService systemMainstayParamService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 查询项目资源表列表
     *
     * @param query 项目资源表查询参数
     * @return 项目资源表列表
     */
    @ApiOperation("查询项目资源表列表")
    @PostMapping("/listProjectResource")
    public R<PageInfo<PrimeCostListVO>> pagePrimeCost(@RequestBody @Validated PrimeCostQuery query){
        return R.success(primeCostService.pagePrimeCost(query));
    }

    /**
     * 查询项目资源表列表-确认成本提交人部门
     *
     * @param query 项目资源表查询参数(查询部门)
     * @return 项目资源表列表,部门数据
     */
    @ApiOperation("查询项目资源表列表-确认成本提交人部门")
    @PostMapping("/findProjectResourceCreateDept")
    public R<List<String>> findProjectResourceCreateDept(@RequestBody PrimeCostQuery query){
        return R.success(primeCostService.findProjectResourceCreateDept(query));
    }

    /**
     * 付款详情
     *
     * @param query 项目资源id
     * @return 付款详情
     */
    @ApiOperation("付款详情")
    @PreAuthorize("@ss.hasPermi('prime:cost:getPaymentDetails')")
    @GetMapping("/getPaymentDetails")
    public R<PaymentDetailsVO> getPaymentDetails(PrimeCostQuery query) {
        return R.success(primeCostService.getPaymentDetails(query));
    }

    /**
     * 退款详情
     *
     * @param query 项目资源id
     * @return 付款详情
     */
    @ApiOperation("退款详情")
    @PreAuthorize("@ss.hasPermi('prime:cost:getPaymentDetails')")
    @GetMapping("/getRefundRecordDetails")
    public R<PaymentDetailsVO> getRefundRecordDetails(PrimeCostQuery query) {
        return R.success(primeCostService.getRefundRecordDetails(query));
    }

    /**
     * 合计
     *
     * @param query 查询条件
     * @return 合计
     */
    @ApiOperation("合计")
    @PreAuthorize("@ss.hasPermi('prime:cost:getPrimeCostCount')")
    @PostMapping("/getPrimeCostCount")
    public R<PrimeCostCountVO> getPrimeCostCount(@Validated @RequestBody PrimeCostQuery query){
        return R.success(primeCostService.getPrimeCostCount(query));
    }

    /**
     * 头部统计
     *
     * @return 头部统计
     */
    @ApiOperation("头部统计")
    @PreAuthorize("@ss.hasPermi('prime:cost:getPaymentCount')")
    @GetMapping("/getPaymentCount")
    public R<PaymentTypeCountVO> getPaymentCount(@Validated PrimeCostQuery query){
        return R.success(primeCostService.getPaymentCount(query));
    }

    /**
     * 下载
     * @param query 查询条件
     * @return 下载
     */
    @ApiOperation("下载")
    @PreAuthorize("@ss.hasPermi('prime:cost:download')")
    @PostMapping("/download")
    public R<OssUploadResult> download(@Validated @RequestBody PrimeCostQuery query){
        String fileName = "成本管理列表数据-" + DateUtils.format(LocalDateTime.now(),"yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("项目管理-成本管理", TaskType.Export,query, PrimeCostServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 下载(财务)
     * @param query 查询条件
     * @return 下载(财务)
     */
    @ApiOperation("下载(财务)")
    @PreAuthorize("@ss.hasPermi('prime:cost:downloadFinance')")
    @PostMapping("/downloadFinance")
    public R<OssUploadResult> downloadFinance(@Validated @RequestBody PrimeCostQuery query){
        if(query.getPaiyaMainstayId() == null){
            throw new ServiceException("派芽合作主体为空");
        }
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPaiyaMainstayId());
        String fileName = "成本管理列表-" + systemMainstayParamVO.getMainstayName() + "-" + DateUtils.format(LocalDateTime.now(),"yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("财务管理-成本管理", TaskType.Export, query, PrimeCostServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 查询项目资源表列表(财务)
     *
     * @param query 项目资源表查询参数
     * @return 项目资源表列表
     */
    @ApiOperation("查询项目资源表列表(财务)")
    @PreAuthorize("@ss.hasPermi('prime:cost:pageFinancePrimeCost')")
    @PostMapping("/pageFinancePrimeCost")
    public R<PageInfo<PrimeCostListVO>> pageFinancePrimeCost(@Validated @RequestBody PrimeCostQuery query){
        return R.success(primeCostService.pageFinancePrimeCost(query));
    }

    /**
     * 查询项目资源id根据查询条件
     *
     * @param query 项目资源表查询参数
     * @return 项目id
     */
    @ApiOperation("查询项目资源id根据查询条件")
    @PreAuthorize("@ss.hasPermi('prime:cost:getProjectByQuery')")
    @GetMapping("/getProjectByQuery")
    public R<List<Long>> getProjectByQuery(@Validated PrimeCostQuery query){
        return R.success(primeCostService.getProjectByQuery(query));
    }

    /**
     * 获取所有的付款渠道-成本管理列表
     *
     * @param query 项目-成本管理表查询参数
     * @return 付款渠道列表
     */
    @ApiOperation("获取所有的付款渠道")
    @GetMapping("/listChannelsName")
    public R<List<String>> listChannelsName(@Validated PrimeCostQuery query) {
        return R.success(this.primeCostService.listChannelsName(query));
    }

    /**
     * 获取批量付款模版
     */
    @ApiOperation("获取批量付款模版")
    @PostMapping("/importStencil")
    public void importStencil(HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/payment_cost_inport.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("成本管理批量付款", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = null;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入批量付款
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @ApiOperation("导入批量付款")
    @PreAuthorize("@ss.hasPermi('prime:cost:importPayment')" )
    @Log(title = "导入批量付款" , businessType = BusinessType.IMPORT)
    @PostMapping("/importPayment" )
    public R<PaymentCostBatchVO> importPayment(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        ExcelUtil<PaymentCostImportModel> util = new ExcelUtil<>(PaymentCostImportModel.class);
        List<PaymentCostImportModel> modelList = util.importExcel(file.getInputStream());
        if(ListUtil.isEmpty(modelList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        return R.success(this.primeCostService.importSelectData(modelList));
    }

}
