package com.py.common.core.domain.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.py.common.core.domain.enums.UserType;
import com.py.common.core.domain.vo.user.AuthUser;
import com.py.common.core.domain.vo.user.AuthUserDeptVO;
import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.collection.ListUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 登录用户身份权限
 * <AUTHOR>
 */
@Getter
@Setter
public class LoginUser implements UserDetails {
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    private Long userId;

    /** 部门ID */
    private List<Long> deptIdList;

    /** 部门名称 */
    private String deptName;

    /** 用户唯一标识 */
    private String token;

    /** 登录时间 */
    private Long loginTime;

    /** 过期时间 */
    private Long expireTime;

    /** 登录IP地址 */
    private String ipaddr;

    /** 登录地点 */
    private String loginLocation;

    /** 浏览器类型 */
    private String browser;

    /** 操作系统 */
    private String os;

    /** 权限列表 */
    private Set<String> permissions;

    /** 数据权限 */
    private UserDataPermission dataPermission;

    /** 用户信息 */
    private AuthUser user;

    /** 用户类型 */
    private UserType userType;

    public LoginUser() {
    }

    public LoginUser(Long userId, AuthUser user, Set<String> permissions) {
        this.userId = userId;
        this.deptIdList = ListUtil.map(user.getDeptList(), AuthUserDeptVO::getDeptId);
        this.user = user;
        this.permissions = permissions;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return null;
    }

    @JSONField(serialize = false)
    @Override
    public String getPassword() {
        return user.getPassword();
    }

    @Override
    public String getUsername() {
        return NullMergeUtils.nullMerge(user, AuthUser::getUserName);
    }

    /**
     * 是否是管理员
     * @return true: 是管理员
     */
    public boolean isAdmin() {
        return NullMergeUtils.nullMerge(user, AuthUser::isAdmin, false);
    }

    /** 账户是否未过期,过期无法验证 */
    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * 指定用户是否解锁,锁定的用户无法进行身份验证
     */
    @JSONField(serialize = false)
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * 指示是否已过期的用户的凭据(密码),过期的凭据防止认证
     */
    @JSONField(serialize = false)
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * 是否可用 ,禁用的用户不能身份验证
     */
    @JSONField(serialize = false)
    @Override
    public boolean isEnabled() {
        return true;
    }
}
