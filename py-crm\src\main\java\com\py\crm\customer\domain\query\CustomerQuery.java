package com.py.crm.customer.domain.query;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.py.common.core.domain.model.LoginUser;
import com.py.common.core.domain.query.BaseCreateListInfo;
import com.py.common.core.domain.query.ICreateQuery;
import com.py.common.core.domain.query.IUpdateQuery;
import com.py.common.datascope.IDataScopeArgs;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTaskArgs;
import com.py.crm.customer.domain.SupCustomer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * 客户管理-客户查询对象
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("客户管理-客户查询对象" )
public class CustomerQuery extends BaseCreateListInfo implements IUpdateQuery, ICreateQuery,IDataScopeArgs,ReusableAsyncTaskArgs {
    private static final long serialVersionUID = 1L;
    /** 合作状态(0.合作中 1.暂停合作 2.意向合作)*/
    @ApiModelProperty("合作状态(0.合作中 1.暂停合作 2.意向合作)" )
    private Integer cooperationStatus;

    /** 客户id*/
    @ApiModelProperty("客户id" )
    private Long customerId;


    /** 删除标志*/
    @ApiModelProperty("删除标志" )
    private String delFlag;

    /** 行业类目id*/
    @ApiModelProperty("行业类目id" )
    private List<Long> industryCategoryIdList;

    /** 品牌/业务线*/
    @ApiModelProperty("品牌/业务线" )
    private String lineBusiness;

    /** 客户名称*/
    @ApiModelProperty("客户名称" )
    private String name;

    /** 目标服务人员id*/
    @ApiModelProperty("目标服务人员id" )
    private List<Long> serviceUserIdList;
    /** 目标服务人员部门id*/
    @ApiModelProperty("目标服务人员部门id" )
    private List<String> serviceDeptIdList;

    /** 版本*/
    @ApiModelProperty("版本" )
    private Integer version;


    /**
     * 获取创建者名字
     */
    @ApiModelProperty("获取创建者名字" )
    private String createUser;

    /**
     * 获取创建者部门
     */
    @ApiModelProperty("获取创建者部门")
    private String createDept;

    /**
     * 获取创建时间-开始
     */
    @ApiModelProperty("获取创建时间-开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startCreateDate;

    /**
     * 获取创建时间-结束
     */
    @ApiModelProperty("获取创建时间-结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endCreateDate;

    /**
     * 获取更新者Id
     */
    @ApiModelProperty("获取更新者Id")
    private List<Long> updateUser;

    /**
     * 获取更新部门Id
     */
    @ApiModelProperty("获取更新部门Id")
    private List<Long> updateDept;

    /**
     * 获取更新时间-开始
     */
    @ApiModelProperty("获取更新时间-开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startUpdateDate;

    /**
     * 获取更新时间-结束
     */
    @ApiModelProperty("获取更新时间-结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endUpdateDate;

    /** 数据权限 */
    @ApiModelProperty(hidden = true)
    private String dataScopeSql;

    /** 查询条件的Wrapper */
    @ApiModelProperty(hidden = true)
    private LambdaQueryWrapper<SupCustomer> queryWrapper;


    /** 客户id*/
    @ApiModelProperty("客户id" )
    private List<Long> customerIdList;
    /** 任务ID*/
    @ApiModelProperty(hidden = true)
    private Long taskId;

    /** 下载文件名 */
    private String fileName;

    private String userName;

    /**  */
    private LoginUser loginUser;

    /** 导入文件key */
    private String fileKey;

    /**下拉两者都选的客户id*/
    @ApiModelProperty("下拉两者都选的客户id" )
    private List<Long> downCustomerIdList;

    /**下拉仅选客户名List*/
    @ApiModelProperty("下拉仅选客户名List" )
    private List<String> downCustomerNameList;

    /**下拉仅选业务线名List*/
    @ApiModelProperty("下拉仅选业务线名List" )
    private List<String> downBusinessLineNameList;

    /**下拉客户id,客户名,业务线id,三者是否都为空使用查询条件*/
    private boolean downStatus;

    /**部门名称*/
    @ApiModelProperty("部门名称" )
    private String deptName;

}
