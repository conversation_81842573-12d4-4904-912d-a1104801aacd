package com.py.common.tools.multisheetexcelexporter;

import com.aliyun.oss.OSSException;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.py.common.oss.IOssService;
import com.py.common.tools.multisheetexcelexporter.config.CellStyleModifier;
import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;
import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfigItem;
import com.py.common.tools.multisheetexcelexporter.domain.HyperlinkPackage;
import com.py.common.tools.multisheetexcelexporter.domain.HyperlinkPathPackage;
import com.py.common.tools.multisheetexcelexporter.domain.ImagePackage;
import com.py.common.tools.multisheetexcelexporter.enums.ExportType;
import com.py.common.tools.poiexcel.annotation.ImageStorageLocation;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.common.utils.file.FileTypeUtils;
import com.py.common.utils.file.ImageUtils;
import com.py.common.utils.spring.SpringUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFHyperlink;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;

/**
 * Excel Sheet 数据填充器
 * <AUTHOR>
 */
@Log4j2
public class ExcelSheetDataFiller<T> {

    /** 基础高度, 标准样式一行的高度 */
    private static final short BASE_HEIGHT = 280;
    /** 修改后的样式缓存 */
    private final Cache<CellStyleModifier, CellStyle> modifiedStyleCache = CacheBuilder.newBuilder().build();
    /** 工作薄对象 */
    private Workbook workbook;
    /** 工作表对象 */
    private Sheet sheet;
    /** 样式列表 */
    private Map<String, CellStyle> styles;
    /** 导入导出数据列表 */
    private Collection<T> list;
    /** 扩展功能的Excel配置 */
    private ExcelSheetConfig<T> config;
    /** 顶部表头行号 */
    private int topTitleRowNo;
    /** 次级表头行号 */
    private int subTitleRowNo;
    /** 基础表头行号 */
    private int baseTitleRowNo;
    /** 表头行数 */
    private int rowCount = 0;

    /** 最大高度 */
    private short maxHeight;

    /** 默认返回值 */
    private final String DEFAULT_VALUE = "--";

    /**
     * 获取sheet中的最大列号
     * @param sheet sheet
     * @return 最大列号
     */
    private static int findMaxCol(Sheet sheet, ExcelSheetConfig<?> config) {
        int lastRowNum = Math.min(config.getExistHeaderRowNo(), sheet.getLastRowNum());

        int maxCol = 0;
        for(int rowIndex = 0; rowIndex < lastRowNum; rowIndex++) {
            maxCol = Math.max(sheet.getRow(rowIndex).getLastCellNum(), maxCol);
        }
        return maxCol;
    }

    /**
     * 向指定的Sheet中根据配置填充数据
     * @param sheet 目标填充的Sheet
     * @param sheetConfigItem Sheet配置
     * @param sheetStyle Sheet样式配置
     */
    public void fillSheet(Sheet sheet, Workbook workbook, MultiSheetExcelExportConfigItem<T> sheetConfigItem, Map<String, CellStyle> sheetStyle) {
        this.sheet = sheet;
        this.workbook = workbook;
        this.styles = sheetStyle;
        this.config = sheetConfigItem.getConfig();
        this.list = sheetConfigItem.getSource();

        int maxHeight = ListUtil.maxInt(this.config.getBaseHeaderConfig(), ExcelSheetConfigItem::getHeight);
        this.maxHeight = (short) (maxHeight * 20);

        this.initSheet();
        this.createTitle();
        this.createHeader();
        this.fillExcelData();
    }

    /** 初始化Sheet */
    private void initSheet() {
        if(this.config.getExportType() == ExportType.INSERT) {
            int insertDataHeaderCount = this.config.getBaseHeaderCount();
            if(this.config.isNeedSerialNumber()) {
                insertDataHeaderCount++;
            }

            int maxCol = findMaxCol(this.sheet, this.config) - 1;
            int startInsertRowNo = this.config.getStartInsertRowNo();
            if(startInsertRowNo < maxCol) {
                // 右移原始数据, 留出插入数据的插入空间
                this.sheet.shiftColumns(startInsertRowNo, maxCol - startInsertRowNo, insertDataHeaderCount);
            }
            // 插入开始行不为0时, 修正配置的表头插入起始位置
            if(startInsertRowNo != 0) {
                this.config.shiftHeader(startInsertRowNo);
            }
        }
    }

    /** 创建表头 */
    private void createHeader() {
        this.createTopHeader();
        this.createSubHeader();
        this.createBaseHeader();
        if(this.config.isConfiguredTopHeader() && this.config.isMergeEmptyHeader()) {
            this.mergeEmptyHeader();
        }
    }


    /** 填充excel数据 */
    private void fillExcelData() {
        int rowIndex = this.rowCount;
        if(this.config.getExportType() == ExportType.INSERT) {
            rowIndex = Math.max(rowIndex, this.config.getExistHeaderRowNo());
        }

        if(this.config.getRowSpecifier() != null) {
            this.fillExcelDataByRowSpecifier(rowIndex, this.config.getRowSpecifier());
        } else {
            this.fillExcelDataByDataOrder(rowIndex);
        }

    }

    /**
     * 获取行
     * <p>存在时返回既有的, 不存在时新建一行</p>
     * @param sheet sheet
     * @param rowIndex 需获取的行号
     * @return 行
     */
    private Row getRow(Sheet sheet, int rowIndex) {
        Row row = sheet.getRow(rowIndex);
        return row != null ? row : sheet.createRow(rowIndex);
    }

    /**
     * 获取单元格
     * <p>存在时返回既有的, 不存在时创建一个新的单元格</p>
     * @param row 行
     * @param column 列
     * @return 单元格
     */
    private Cell getCell(Row row, int column) {
        Cell cell = row.getCell(column);
        return cell != null ? cell : row.createCell(column);
    }

    /**
     * 根据行指定器填充报表数据
     * @param startRow 添加起始行
     * @param rowSpecifier 导出行指定器
     */
    private void fillExcelDataByRowSpecifier(int startRow, Function<T, Integer> rowSpecifier) {
        for(T data : this.list) {
            Integer dataSpecifiedRow = rowSpecifier.apply(data);
            Assert.isTrue(dataSpecifiedRow >= startRow, "配置错误, 指定的行小于起始行");
            Row row = this.getRow(sheet, dataSpecifiedRow);
            this.fillExcelRowData(data, row);
        }

        this.rowCount += this.list.stream()
                .mapToInt(item -> rowSpecifier.apply(item))
                .max().orElse(0);
    }

    /**
     * 添加报表行数据
     * @param data 需填充的数据
     * @param row 添加的报表行
     */
    private void fillExcelRowData(T data, Row row) {
        int column = this.config.getStartInsertRowNo();

        // 填充序号
        if(this.config.isNeedSerialNumber() == true) {
            this.addCell(String.valueOf(row.getRowNum() - this.rowCount), row, column++, null);
        }

        // 填充数据
        for(ExcelSheetConfigItem<T> configItem : this.config.getBaseHeaderConfig()) {
            switch(configItem.getCellType()) {
                case GENERAL:
                    column = this.fillGeneralCell(data, row, column, configItem);
                    break;
                case DROP_DOWN:
                    column = this.fillDropDownCell(data, row, column, configItem);
                    break;
                case IMAGE:
                    column = this.fillImageCell(data, row, column, configItem);
                    break;
                case OSS_FILE:
                    column = this.fillFileCell(data,row,column,configItem);
                    break;
                case PATH_FILE:
                    column = this.fillPathFileCell(data,row,column,configItem);
                    break;
                default:
                    throw new UnsupportedOperationException(String.format("枚举: %s对应操作未实现", configItem.getCellType()));
            }
        }
    }

    /**
     * 填充相对路径附件单元格内容
     * @param data 填充的对象
     * @param row 填充行
     * @param column 填充起始列
     * @param configItem 配置
     * @return 填充后列的位置
     */
    private int fillPathFileCell(T data, Row row, int column, ExcelSheetConfigItem<T> configItem) {
        HyperlinkPathPackage hyperlinkPathPackage = (HyperlinkPathPackage)configItem.getContextSelector().apply(data);
        if (hyperlinkPathPackage == null){
            this.addCell(DEFAULT_VALUE, row, column++, configItem);
            return column+1;
        }
        Hyperlink hyperlink = sheet.getWorkbook().getCreationHelper().createHyperlink(HyperlinkType.FILE);
        hyperlink.setLabel(hyperlinkPathPackage.getFileName());
        hyperlink.setAddress(hyperlinkPathPackage.getPathUrl());
        this.addCell(hyperlinkPathPackage.getFileName(),row,column,configItem);

        Cell cell = row.getCell(column);
        cell.setHyperlink(new XSSFHyperlink(hyperlink));
        return column+1;
    }

    /**
     * 填充附件单元格内容
     * @param data 填充的对象
     * @param row 填充行
     * @param column 填充起始列
     * @param configItem 配置
     * @return 填充后列的位置
     */
    private int fillFileCell(T data, Row row, int column, ExcelSheetConfigItem<T> configItem) {
        HyperlinkPackage hyperlinkPackage = (HyperlinkPackage)configItem.getContextSelector().apply(data);
        if (hyperlinkPackage == null || StringUtils.isBlank(hyperlinkPackage.getOssUrl())){
            this.addCell(DEFAULT_VALUE, row, column, configItem);
            return column+1;
        }
        Hyperlink hyperlink = sheet.getWorkbook().getCreationHelper().createHyperlink(HyperlinkType.FILE);
        hyperlink.setLabel(hyperlinkPackage.getFileName());
        hyperlink.setAddress(hyperlinkPackage.getOssUrl());
        this.addCell(hyperlinkPackage.getFileName(),row,column,configItem);

        Cell cell = row.getCell(column);
        cell.setHyperlink(new XSSFHyperlink(hyperlink));
        return column+1;
    }

    /**
     * 填充图片单元格内容
     * @param data 填充的对象
     * @param row 填充行
     * @param column 填充起始列
     * @param configItem 配置
     * @return 填充后列的位置
     */
    private int fillImageCell(T data, Row row, int column, ExcelSheetConfigItem<T> configItem) {
        ImagePackage imagePackage = (ImagePackage)configItem.getContextSelector().apply(data);
        if (imagePackage == null || StringUtils.isBlank(imagePackage.getImagePath()) || imagePackage.getImagePath().equals(DEFAULT_VALUE)){
            this.addCell(DEFAULT_VALUE, row, column, configItem);
            return column + 1;
        }
        byte[] image ;
        if(imagePackage.getStorageLocation().equals(ImageStorageLocation.LOCAL)){
            image =  ImageUtils.getImage(imagePackage.getImagePath());
        }else{
            image = this.getImageDataByOss(imagePackage.getImagePath());
            if (image == null || image.length == 0){
                return column +1;
            }
        }

        ClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) column, row.getRowNum(), (short) (column + 1), row.getRowNum() + 1);
        anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
        if(image != null){
            this.addCell(null, row, column, configItem);

            int imageType = getImageType(image);
                getDrawingPatriarch(sheet)
                        .createPicture(anchor, sheet.getWorkbook().addPicture(image, imageType));
        }
        return column + 1;
    }

    /**
     * 获取图片类型,设置图片插入类型
     */
    private int getImageType(byte[] value) {
        String type = FileTypeUtils.getFileExtendName(value);
        if("JPG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_JPEG;
        } else if("PNG".equalsIgnoreCase(type)) {
            return Workbook.PICTURE_TYPE_PNG;
        }
        return Workbook.PICTURE_TYPE_JPEG;
    }

    /**
     * 获取画布
     */
    private static Drawing<?> getDrawingPatriarch(Sheet sheet) {
        if(sheet.getDrawingPatriarch() == null) {
            sheet.createDrawingPatriarch();
        }
        return sheet.getDrawingPatriarch();
    }

    /**
     * 通过OSS获取图片字节流
     * @param imagePath 图片OssKey
     * @return 图片字节流
     */
    private byte[] getImageDataByOss(String imagePath) {
        try {
            IOssService ossService = SpringUtils.getBean(IOssService.class);
            return ossService.download(imagePath);
        }catch (OSSException e){
            log.error("OSS获取图片字节流异常： ", e);
            return new byte[0];
        }
    }


    /**
     * 根据数据数组顺序填充报表数据
     * @param rowIndex 添加起始行
     */
    private void fillExcelDataByDataOrder(int rowIndex) {
        for(T data : this.list) {
            Row row = this.getRow(sheet, rowIndex++);
            this.fillExcelRowData(data, row);
        }

        this.rowCount += this.list.size();
    }

    /**
     * 填充下拉框单元格内容
     * @param data 填充的对象
     * @param row 填充行
     * @param column 填充起始列
     * @param configItem 配置
     * @return 填充后列的位置
     */
    private int fillDropDownCell(T data, Row row, int column, ExcelSheetConfigItem<T> configItem) {
        Assert.notNull(configItem.getDropDownOptionSelector(), "填充下拉框单元格时下拉框选项内容获取函数不能为null");

        List<String> dropDownOptionList = configItem.getDropDownOptionSelector().apply(data);
        if(ListUtil.isEmpty(dropDownOptionList)) {
            return column + 1;
        }
        DataValidationHelper dataValidationHelper = sheet.getDataValidationHelper();
        DataValidationConstraint capacityConstraint = dataValidationHelper.createExplicitListConstraint(dropDownOptionList.toArray(new String[0]));
        // 设置下拉数据的单元格范围
        CellRangeAddressList capacityList = new CellRangeAddressList(row.getRowNum(), row.getRowNum(), column, column);
        DataValidation capacityDataValidation = dataValidationHelper.createValidation(capacityConstraint, capacityList);
        sheet.addValidationData(capacityDataValidation);

        return column + 1;
    }

    /**
     * 填充普通单元格内容
     * @param data 填充的对象
     * @param row 填充行
     * @param column 填充起始列
     * @param configItem 配置
     * @return 填充后列的位置
     */
    private int fillGeneralCell(T data, Row row, int column, ExcelSheetConfigItem<T> configItem) {
        Assert.notNull(configItem.getContextSelector(), "填充普通单元格时提取导出内容的函数不能为null");
        if(configItem.isList()) {
            @SuppressWarnings("unchecked")
            List<Object> contextList = (List<Object>) configItem.getContextSelector().apply(data);
            int contextListSize = contextList.size();
            int titleListSize = configItem.getHeaderList().size();
            for(int contextIndex = 0; contextIndex < titleListSize; contextIndex++) {
                String context = contextIndex < contextListSize ? this.formatContent(contextList.get(contextIndex)) : StringUtils.EMPTY;

                this.addCell(context, row, column++, configItem);
            }
            return column;
        }

        Object obj;
        try {
            obj = configItem.getContextSelector().apply(data);
        } catch(Exception e) {
            log.warn("标题为{}的导入字段获取异常: {}", configItem.getHeaderList().get(0), e.getMessage());
            this.addCell(StringUtils.EMPTY, row, column++, configItem);
            return column;
        }
        String content = this.formatContent(obj);
        this.addCell(content, row, column++, configItem);
        return column;
    }

    /**
     * 格式化填充单元格内容
     * @param obj 单元格填入对象
     * @return 格式化后的内容
     */
    private String formatContent(Object obj) {
        if(obj == null) {
            return DEFAULT_VALUE;
        }
        if (obj instanceof String) {
            if (StringUtils.isBlank((String) obj)){
                return DEFAULT_VALUE;
            }
        }
        if(obj instanceof BigDecimal) {
            BigDecimal bigDecimal = (BigDecimal) obj;
            return bigDecimal.setScale(2, RoundingMode.HALF_UP).toString();
        } else {
            return obj.toString();
        }
    }

    /** 添加单元格 */
    private void addCell(String context, Row row, int column, ExcelSheetConfigItem<T> configItem) {
        // 设置行高
        row.setHeight(this.maxHeight);

        // 创建cell
        Cell cell = this.getCell(row, column);
        CellStyle cellStyle = this.getCellStyle();
        cell.setCellStyle(cellStyle);

        // 赋值
        cell.setCellValue(context);

        if(configItem == null) {
            return;
        }
        // 设置列宽
        if(configItem.getWidth() != null){
            sheet.setColumnWidth(column, (int) ((configItem.getWidth() + 0.72) * 256));
        }

        if(configItem.getStyleModifier() != null) {
            this.modifyCellStyle(cell, cellStyle, configItem.getStyleModifier());
        }
    }

    /**
     * 修改单元格样式
     * @param cell 需修改样式的单元格
     * @param defaultStyle 单元格默认样式
     * @param styleModifier 单元格样式调整器
     */
    private void modifyCellStyle(Cell cell, CellStyle defaultStyle, CellStyleModifier styleModifier) {
        try {
            CellStyle newStyle = this.modifiedStyleCache.get(styleModifier, () -> {
                CellStyle style = this.workbook.createCellStyle();
                style.cloneStyleFrom(defaultStyle);
                styleModifier.modify(style);
                return style;
            });

            cell.setCellStyle(newStyle);
        } catch(ExecutionException e) {
            e.printStackTrace();
            log.error("样式修改缓存失效", e.getCause());
        }
    }

    /**
     * 创建Excel标题
     */
    private void createTitle() {
        int headerCount = this.getTotalHeaderCount();
        String title = this.config.getTitle();
        if(title != null) {
            Row row = this.getRow(sheet,this.rowCount++);
            row.setHeightInPoints(48);
            this.createRangRowCell(title, row, 0, headerCount, styles.get("title"));
        }

        String subTitle = this.config.getSubTitle();
        if(subTitle != null) {
            Row row = this.getRow(sheet,this.rowCount++);
            row.setHeightInPoints(32);
            this.createRangRowCell(subTitle, row, 0, headerCount, styles.get("subTitle"));
        }
    }

    /**
     * 获取表头总数(包括自动生成的表头)
     * @return 表头总数
     */
    private int getTotalHeaderCount() {
        int headerCount = this.config.getBaseHeaderCount() - 1;
        if(this.config.isNeedSerialNumber() == true) {
            headerCount += 1;
        }
        return headerCount;
    }

    /** 创建顶部表头*/
    private void createTopHeader() {
        if(this.config.isConfiguredTopHeader() == false) {
            return;
        }

        this.topTitleRowNo = this.rowCount++;
        int serialNumberOffset = this.config.isNeedSerialNumber() == true ? 1 : 0;

        Row row = this.getRow(this.sheet, topTitleRowNo);
        for(ExcelSheetConfigItem<?> configItem : this.config.getTopHeaderConfig()) {
            int startPoint = configItem.getHeaderStartPoint() + serialNumberOffset;
            int endPoint = configItem.getHeaderEndPoint() + serialNumberOffset;
            String headerName = configItem.getHeaderList().get(0);
            this.createRangRowCell(headerName, row, startPoint, endPoint, getHeaderStyle());
        }
    }

    /** 创建次级表头*/
    private void createSubHeader() {
        if(this.config.isConfiguredSubHeader() == false) {
            return;
        }

        this.subTitleRowNo = this.rowCount++;
        int serialNumberOffset = this.config.isNeedSerialNumber() == true ? 1 : 0;

        Row row = this.getRow(this.sheet, this.subTitleRowNo);
        for(ExcelSheetConfigItem<?> configItem : this.config.getSubHeaderConfig()) {
            int startPoint = configItem.getHeaderStartPoint() + serialNumberOffset;
            int endPoint = configItem.getHeaderEndPoint() + serialNumberOffset;
            String headerName = configItem.getHeaderList().get(0);
            this.createRangRowCell(headerName, row, startPoint, endPoint, getHeaderStyle());
        }
    }

    /** 创建各个字段的表头名称*/
    private void createBaseHeader() {
        this.baseTitleRowNo = this.rowCount++;
        Row row = this.getRow(sheet, baseTitleRowNo);
        int column = this.config.getStartInsertRowNo();

        if(this.config.isNeedSerialNumber() == true) {
            // 创建序号表头
            this.createHeader("序号", row, column++);
        }

        // 创建表头
        for(ExcelSheetConfigItem<?> configItem : this.config.getBaseHeaderConfig()) {
            if(configItem.isList()) {
                for(String titleName : configItem.getHeaderList()) {
                    this.createHeader(titleName, row, column++);
                }
            } else {
                String titleName = configItem.getHeaderList().get(0);
                this.createHeader(titleName, row, column++);
            }
        }
    }

    /**
     * 创建跨列的合并表头
     * @param context 表头内容
     * @param row 行
     * @param startColumn 起始列
     * @param endColumn 结束列
     */
    private void createRangRowCell(String context, Row row, int startColumn, int endColumn, CellStyle titleStyle) {
        Cell cell = this.getCell(row, startColumn);
        cell.setCellValue(context);
        cell.setCellStyle(titleStyle);
        for(int i = startColumn + 1; i <= endColumn; i++) {
            Cell subCell = this.getCell(row, i);
            subCell.setCellStyle(titleStyle);
        }

        if (startColumn == endColumn){
            return;
        }

        CellRangeAddress rangCell = new CellRangeAddress(row.getRowNum(), row.getRowNum(), startColumn, endColumn);
        this.sheet.addMergedRegion(rangCell);
    }

    /**
     * 创建表头
     * @param context 表头内容
     * @param row 行
     * @param column 列
     */
    private void createHeader(String context, Row row, int column) {
        // 创建列
        Cell cell = this.getCell(row, column);
        // 写入列信息
        cell.setCellValue(context);
        setDataValidation(row, column);
        cell.setCellStyle(getHeaderStyle());
    }


    /** 合并没有顶部表头的单元格*/
    private void mergeEmptyHeader() {
        Row topHeaderRow = this.sheet.getRow(this.topTitleRowNo);
        Row subHeaderRow = this.sheet.getRow(this.subTitleRowNo);
        Row baseHeaderRow = this.sheet.getRow(this.baseTitleRowNo);

        int serialNumberOffset = 0;
        if(this.config.isNeedSerialNumber() == true) {
            this.mergedHeader(topHeaderRow, baseHeaderRow, serialNumberOffset);
            serialNumberOffset += 1;
        }

        long headerCount = this.config.getBaseHeaderCount();
        for(int i = this.config.getStartInsertRowNo(); i < headerCount + this.config.getStartInsertRowNo(); i++) {
            int targetCol = i + serialNumberOffset;

            if(this.config.isConfiguredTopHeader(i) == false) {
                if(this.config.isConfiguredSubHeader(i) == true) {
                    this.mergedHeader(topHeaderRow, subHeaderRow, targetCol);
                } else {
                    this.mergedHeader(topHeaderRow, baseHeaderRow, targetCol);
                }
            } else if(this.config.isConfiguredSubHeader() == true && this.config.isConfiguredSubHeader(i) == false) {
                this.mergedHeader(subHeaderRow, baseHeaderRow, targetCol);
            }
        }
    }

    /**
     * 合并表头
     * @param targetRwo 合并开始行
     * @param baseRow 合并结束行
     * @param targetCol 目标列表
     */
    private void mergedHeader(Row targetRwo, Row baseRow, int targetCol) {
        Cell mergedTopCell = this.getCell(targetRwo,targetCol);
        mergedTopCell.setCellValue(baseRow.getCell(targetCol).getStringCellValue());
        mergedTopCell.setCellStyle(this.getHeaderStyle());

        CellRangeAddress rangCell = new CellRangeAddress(targetRwo.getRowNum(), baseRow.getRowNum(), targetCol, targetCol);
        this.sheet.addMergedRegion(rangCell);
    }

    /** 设置表格样式*/
    private void setDataValidation(Row row, int column) {
        // 设置列宽
        sheet.setColumnWidth(column, (int) ((16.72) * 256));
        row.setHeight(this.maxHeight);
    }

    /** 取得表头样式*/
    private CellStyle getHeaderStyle() {
        switch(this.config.getStyleType()) {
            case BlackAndWhite:
                return styles.get("cellStyle");

            case Default:
            default:
                return styles.get("header");
        }
    }

    /** 取得单元格样式*/
    private CellStyle getCellStyle() {
        switch(this.config.getStyleType()) {
            case BlackAndWhite:
                return styles.get("cellStyle");

            case Default:
            default:
                return styles.get("data");
        }
    }
}
