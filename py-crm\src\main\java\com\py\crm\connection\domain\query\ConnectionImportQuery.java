package com.py.crm.connection.domain.query;

import com.py.common.core.domain.model.LoginUser;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTaskArgs;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人脉管理批量分配导入表查询对象
 *
 * <AUTHOR>
 * @date 2023-09-28
 */
@Data
@ApiModel("人脉管理批量分配导入表查询对象" )
public  class ConnectionImportQuery implements ReusableAsyncTaskArgs {
    private static final long serialVersionUID = 1L;

    /** 人脉Id */
    @ApiModelProperty("人脉Id")
    private Long connectionId;

    /** 任务id */
    private Long taskId;

    /** 文件名 */
    private String fileName;

    /** 文件名 */
    private String fileKey;

    /** 用户信息 */
    private LoginUser loginUser;
}
