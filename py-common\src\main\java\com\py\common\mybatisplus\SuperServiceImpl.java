package com.py.common.mybatisplus;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.Collection;

/**
 * 自定义ServiceImpl
 * <AUTHOR>
 */
public class SuperServiceImpl<TMapper extends SuperMapper<TEntity>, TEntity>
        extends ServiceImpl<TMapper, TEntity> {

    /**
     * 恢复指定主键逻辑删除的记录
     * @param id 主键ID
     * @return 是否成功
     */
    public boolean restore(Serializable id) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
        Assert.isTrue(tableInfo.havePK() && tableInfo.isWithLogicDelete(), "要求实体类必须有主键且为逻辑删除");
        return this.baseMapper.restoreDeleteData(id) > 0;
    }

    @Override
    public boolean removeById(Serializable id) {
        return SqlHelper.retBool(getBaseMapper().deleteById(id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<?> list) {
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        return SqlHelper.retBool(getBaseMapper().deleteBatchIds(list));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByIds(Collection<?> list, int batchSize) {
        return removeBatchByIds(list, batchSize, false);
    }

}
