<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.py.crm.crmupdaterecord.mapper.CrmUpdateRecordMapper">

    <resultMap type="com.py.crm.crmupdaterecord.domain.CrmUpdateRecord" id="CrmUpdateRecordResult">
        <result property="id" column="id" />
        <result property="recordId" column="record_id" />
        <result property="bizType" column="biz_type" />
        <result property="bizId" column="biz_id" />
        <result property="content" column="content" />
        <result property="menuType" column="menu_type" />
        <result property="reason" column="reason" />
        <result property="createId" column="create_id" />
        <result property="createBy" column="create_by" />
        <result property="createDept" column="create_dept" />
        <result property="createTime" column="create_time" />
        <result property="updateId" column="update_id" />
        <result property="updateBy" column="update_by" />
        <result property="updateDept" column="update_dept" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
        <result property="enableStatus" column="enable_status" />
    </resultMap>

</mapper>
