package com.py.common.datascope.functional;

import com.py.common.datascope.DataScopeContext;

/**
 * 自定义时间权限SQL生成器
 * <AUTHOR>
 */
@FunctionalInterface
public interface CustomDatePermissionSqlGenerator {

    /**
     * 生成自定义时间权限SQL
     * @param limitTimeStr 限制时间字符串
     * @param dataScopeContext 数据权限上下文
     * @return 自定义时间权限SQL
     */
    String generate(String limitTimeStr, DataScopeContext dataScopeContext);
}
