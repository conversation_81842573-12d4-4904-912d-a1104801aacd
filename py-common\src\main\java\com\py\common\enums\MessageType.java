package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 消息类型(1.审批中心，2.系统公告，3.财务报表)
 * <AUTHOR>
 */
@AllArgsConstructor
public enum MessageType implements IDict<Integer> {

    /** 审批中心 */
    APPROVAL_CENTER(1, "审批中心"),
    /** 系统公告 */
    SYSTEM_ANNOUNCEMENT(2, "系统公告"),
    /** 业务报表 */
    FINANCIAL_STATEMENTS(3, "业务报表"),

    /** 企业报表 */
    ENTERPRISE_STATEMENTS(4, "企业报表"),

    /** 交接 */
    HANDOVER(5, "交接"),
    ;

    private final Integer value;

    private final String label;


    @Override
    public Integer getValue() {
        return this.value;
    }

    @Override
    public String getLabel() {
        return this.label;
    }

}
