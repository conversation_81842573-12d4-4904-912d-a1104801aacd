package com.py.flow.domain.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程用户事件类型
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FlowUserEventType implements IDict<Integer> {

    /** 审批申请 */
    ApprovalApply(0,"审批申请"),
    /** 抄送通知 */
    CarbonCopy(1,"抄送通知"),
    /** 审批通过 - 整个审批通过 */
    ApprovalPass(2,"审批通过"),
    /** 审批拒绝 */
    ApprovalReject(3,"审批拒绝"),
    /** 审批撤回 */
    ApprovalCancel(4,"审批撤回"),
    /** 审批退回 */
    ApprovalReturn(5,"审批退回"),
    /** 审批作废 */
    ApprovalInvalidated(6,"审批作废"),
    /** 审批同意 - 单个审批通过 */
    ApprovalAgree(7,"审批同意"),
    /** 审批转发 */
    ApprovalForward(8, "审批转发"),

    ;

    private final Integer value;
    private final String label;
}
