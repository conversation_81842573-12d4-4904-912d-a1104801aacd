package com.py.common.utils;

import cn.hutool.core.util.DesensitizedUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022/3/18
 * @Description
 **/
public class StringUtil {

    /**
     * 将字符串从右至左每三位加一逗号</>
     *
     * @param str 需要加逗号的字符串
     * @return 以从右至左每隔3位加一逗号显示
     */
    public static String displayWithComma(String str) {
        if(StringUtils.isBlank(str)) {
            return str;
        }

        // 先将字符串颠倒顺序
        str = new StringBuffer(str).reverse().toString();
        String str2 = "";
        // 每三位取一长度
        int size = (str.length() % 3 == 0) ? (str.length() / 3) : (str.length() / 3 + 1);

        /*
         * 比如把一段字符串分成n段,第n段可能不是三个数,有可能是一个或者两个,
         * 现将字符串分成两部分.一部分为前n-1段,第二部分为第n段.前n-1段，每一段加一",".而第n段直接取出即可
         */

        // 前n-1段
        for (int i = 0; i < size - 1; i++) {
            str2 += str.substring(i * 3, i * 3 + 3) + ",";
        }
        // 第n段
        for (int i = size - 1; i < size; i++) {
            str2 += str.substring(i * 3, str.length());
        }

        str2 = new StringBuffer(str2).reverse().toString();
        return str2;
    }

    /**
     * 将数字从右至左每三位加一逗号</>
     *
     * @param value 需要加逗号的数字
     * @return 以从右至左每隔3位加一逗号显示
     */
    public static String displayWithComma(Long value) {
        if(Objects.isNull(value)) {
            return "0";
        }

        return displayWithComma(value.toString());
    }

    /**
     * 对手机号进行脱敏
     * @param mobile 手机号
     * @return 脱敏后的手机号
     */
    public static String mobileDesensitization(String mobile) {
        if(StringUtils.isBlank(mobile)) {
            return null;
        }

        //对手机号进行脱敏
        return DesensitizedUtil.mobilePhone(mobile);
    }

    /**
     * 对身份证号进行脱敏
     * @param idCardNum 身份证号
     * @return 脱敏后的身份证号
     */
    public static String idCardNumDesensitization(String idCardNum) {
        if(StringUtils.isBlank(idCardNum)) {
            return null;
        }

        //对身份证号进行脱敏
        return DesensitizedUtil.idCardNum(idCardNum, 1, 2);
    }

    private static final Pattern WORD_PATTERN = Pattern.compile("\\b(\\w)");

    /**
     * 将汉字转换为首字母大写缩写
     * @param input
     * @return
     */
    public static String convertToFirstLetter(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();

        Matcher matcher = WORD_PATTERN.matcher(input);

        while (matcher.find()) {
            result.append(matcher.group(1));
        }

        // 将结果转换为大写并返回
        return result.toString().toUpperCase();
    }

}
