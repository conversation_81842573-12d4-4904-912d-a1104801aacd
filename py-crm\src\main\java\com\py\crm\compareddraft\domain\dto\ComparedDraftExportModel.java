package com.py.crm.compareddraft.domain.dto;

import com.py.common.core.domain.vo.IUpdateInfoVO;
import com.py.common.oss.model.OssObjectInfo;
import com.py.common.tools.multisheetexcelexporter.domain.HyperlinkPackage;
import com.py.common.tools.poiexcel.annotation.Excel;
import com.py.common.utils.DateUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 下载模板
 * <AUTHOR>
 * @version ComparedDraftExportModel 2023/7/20 18:18
 */
@Data
public class ComparedDraftExportModel implements IUpdateInfoVO {
    private static final long serialVersionUID = 1L;

    /**
     * 比稿id
     */
    @ApiModelProperty("比稿id")
    private Long comparedDraftId;

    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    private Long customerId;

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    @Excel(name = "客户名称")
    private String customerName;

    /**
     * 客户合作主体ID
     */
    @ApiModelProperty("客户合作主体ID")
    private Long customerCooperateId;

    /**
     * 客户合作主体
     */
    @ApiModelProperty("客户合作主体")
    @Excel(name = "客户合作主体")
    private String customerCooperateName;

    /**
     * 品牌/业务线名
     */
    private String brandName;

    /**
     * 比稿项目名称
     */
    @ApiModelProperty("比稿项目名称")
    @Excel(name = "比稿项目名称")
    private String comparedDraftName;

    /**
     * 商务标比稿结果
     */
    @ApiModelProperty("商务标比稿结果")
    private Integer commercialBidFruits;

    /**
     * 技术标比稿结果
     */
    @ApiModelProperty("技术标比稿结果")
    private Integer techniqueBidFruits;

    /**
     * 最终比稿结果
     */
    @ApiModelProperty("最终比稿结果")
    private Integer finalFruits;
    /**
     * 商务标比稿结果
     */
    @ApiModelProperty("商务标比稿结果")
    private String commercialBidFruitsStr;

    /**
     * 技术标比稿结果
     */
    @ApiModelProperty("技术标比稿结果")
    private String techniqueBidFruitsStr;

    /**
     * 最终比稿结果
     */
    @ApiModelProperty("最终比稿结果")
    private String finalFruitsStr;

    /**
     * 比稿金额(元)
     */
    @ApiModelProperty("比稿金额(元)")
    @Excel(name = "比稿金额(元)")
    private BigDecimal comparedDraftMoney;

    /**
     * 成交金额(元)
     */
    @ApiModelProperty("成交金额(元)")
    @Excel(name = "成交金额(元)")
    private BigDecimal turnoverMoney;

    /**
     * 比稿金额(元)
     */
    @ApiModelProperty("比稿金额(元)")
    private String comparedDraftMoneyStr;

    /**
     * 成交金额(元)
     */
    @ApiModelProperty("成交金额(元)")
    private String turnoverMoneyStr;

    /**
     * 派芽业务类型
     */
    @ApiModelProperty("派芽业务类型")
    @Excel(name = "派芽业务类型")
    private Integer pyType;
    /**
     * 派芽业务类型
     */
    private String pyTypeDict;
    @Excel(name = "派芽业务类型")
    private String pyTypeName;

    /**
     * 方案类型
     */
    @ApiModelProperty("方案类型")
    private Integer planType;
    /**
     * 方案类型
     */
    private String planTypeDict;
    @Excel(name = "方案类型")
    private String planTypeName;

    /** 比稿人Id */
    private Long updateId;

    /** 比稿人 */
    @Excel(name = "比稿人")
    @ApiModelProperty("比稿人")
    private String updateBy;

    /** 比稿人部门 */
    @ApiModelProperty("比稿人部门")
    private String updateDept;

    /** 策划人 */
    @ApiModelProperty("策划人")
    private List<Long> userIdList;
    @Excel(name = "策划人")
    private String userName;

    /**
     * 比稿时间
     */
    @ApiModelProperty("比稿时间")
    @Excel(name = "比稿时间",dateFormat = "yyyy-MM-dd")
    private LocalDate comparedDraftTime;
    private String comparedDraftTimeStr;

    /**
     * 录入时间
     */
    @ApiModelProperty("录入时间")
    @Excel(name = "录入时间",dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enteringTime;
    private String enteringTimeStr;

    /**
     * 行业类目
     */
    @ApiModelProperty("行业类目（数据字典Id）")
    private List<String> categoryId;

    /**
     * 行业类目名称
     */
    @ApiModelProperty("行业类目名称")
    private List<String> categoryName;
    private String categoryNameStr;

    /**
     * 品牌阶段
     */
    private Integer brandStage;
    private String brandStageDict;
    private String brandStageStr;

    /**
     * 合作部门
     */
    private String collaborateDapt;


    /**
     * 比稿方案
     */
    private List<OssObjectInfo> comparedDraftPlan;

    /**
     * 比稿方案
     */
    private List<HyperlinkPackage> comparedDraftPlanPackage;

    /**
     * 比稿策略复盘
     */
    private String strategyReview;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 相对路径
     */
    private String relativePath;

    public String getBrandStageDict() {
        if(brandStage == null){
            return null;
        }
        return brandStage.toString();
    }

    public HyperlinkPackage getComparedDraftPlanIndex(int index) {
        if(ListUtil.isEmpty(comparedDraftPlanPackage)){
            return new HyperlinkPackage();
        }
        if(index >= comparedDraftPlanPackage.size()){
            return new HyperlinkPackage();
        }
        return comparedDraftPlanPackage.get(index);
    }

    public String getCommercialBidFruitsStr() {
        if(commercialBidFruits == null){
            return null;
        }
        if(commercialBidFruits == 0){
            return "失败";
        }
        if(commercialBidFruits == 1){
            return "成功";
        }
        return null;
    }

    public String getTechniqueBidFruitsStr() {
        if(techniqueBidFruits == null){
            return null;
        }
        if(techniqueBidFruits == 0){
            return "失败";
        }
        if(techniqueBidFruits == 1){
            return "成功";
        }
        return null;
    }

    public String getFinalFruitsStr() {
        if(finalFruits == null){
            return null;
        }
        if(finalFruits == 0){
            return "失败";
        }
        if(finalFruits == 1){
            return "成功";
        }
        return null;
    }

    public String getComparedDraftTimeStr() {
        if(comparedDraftTime == null){
            return null;
        }
        return DateUtils.format(comparedDraftTime,"yyyy-MM-dd");
    }

    public String getEnteringTimeStr() {
        if(enteringTime == null){
            return null;
        }
        return DateUtils.format(enteringTime,"yyyy-MM-dd HH:mm:ss");
    }

    public String getComparedDraftMoneyStr() {
        if(comparedDraftMoney == null){
            return null;
        }
        return StringUtils.fmtMicrometer(comparedDraftMoney.stripTrailingZeros().toPlainString());
    }

    public String getTurnoverMoneyStr() {
        if(turnoverMoney == null){
            return null;
        }
        return StringUtils.fmtMicrometer(turnoverMoney.stripTrailingZeros().toPlainString());
    }

    public String getPyTypeDict() {
        if(pyType == null){
            return null;
        }
        return pyType.toString();
    }

    public String getPlanTypeDict() {
        if(planType == null){
            return null;
        }
        return planType.toString();
    }
}
