package com.py.common.tools.reusableasynctask.model;

import com.py.common.core.domain.model.LoginUser;
import lombok.Data;

/**
 * 查询参数
 * <AUTHOR>
 * @version ReusableAsyncTaskArgs 2023/8/21 14:25
 */
public interface ReusableAsyncTaskArgs{
    /** 任务id */
     Long getTaskId() ;
    /** 任务id */
     void setTaskId(Long taskId);

    /** 导出文件名 */
    String getFileName();

    /** 用户信息 */
    LoginUser getLoginUser();
}
