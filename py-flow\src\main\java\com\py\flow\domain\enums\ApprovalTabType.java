package com.py.flow.domain.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审批中心tab类型
 * <AUTHOR>
 * @date 2023/7/31 10:07
 */
@Getter
@AllArgsConstructor
public enum ApprovalTabType implements IDict<Integer> {

    /** 待我审批 */
    WaitApproval(0,"待我审批"),

    /** 我发起的 */
    MyInitiated(1,"我发起的"),

    /** 我审批的 */
    MyApproved(2,"我审批的"),

    /** 抄送我的 */
    MyCarbonCopy(3,"抄送我的");

    private final Integer value;
    private final String label;

}
