package com.py.common.tools.infocompletionratecalculator.functional;

import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 字段否填写判断方法生成函数
 * @param <T> 判断主体类型
 * <AUTHOR>
 */
@FunctionalInterface
public interface IsSetPredicateGenerator<T> {

    /**
     * 生成字段否填写判断方法
     * @param fieldValueGetter 字段值获取方法
     * @return 字段否填写判断方法
     */
    Predicate<T> generate(Function<T, Object> fieldValueGetter);
}
