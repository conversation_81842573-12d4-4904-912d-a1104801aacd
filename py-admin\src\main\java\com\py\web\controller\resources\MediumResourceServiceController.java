package com.py.web.controller.resources;


import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.exception.ServiceException;
import com.py.common.oss.IOssService;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.EnumUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.resources.downloadcontrol.service.IDownloadControlService;
import com.py.resources.enums.LaundryListType;
import com.py.resources.mediumlaundrylist.domain.dto.LaundryListRemoveDTO;
import com.py.resources.mediumlaundrylist.service.IMediumLaundryListService;
import com.py.resources.mediumresourcesensation.domain.vo.ErrorInfoVO;
import com.py.resources.mediumresourcesensation.domain.vo.MediumDisableInfoVO;
import com.py.resources.mediumresourcesensation.excel.search.model.ImportSelectModel;
import com.py.resources.mediumresourcesensation.excel.search.model.ImportSelectResultVO;
import com.py.resources.mediumresourceservice.converter.MediumResourceServiceConverter;
import com.py.resources.mediumresourceservice.domain.dto.MediumResourceServiceDTO;
import com.py.resources.mediumresourceservice.domain.dto.MediumResourceServiceExportModel;
import com.py.resources.mediumresourceservice.domain.dto.ResourceUpdateDTO;
import com.py.resources.mediumresourceservice.domain.query.ImportType;
import com.py.resources.mediumresourceservice.domain.query.LaundryListServiceQuery;
import com.py.resources.mediumresourceservice.domain.query.MediumResourceServiceQuery;
import com.py.resources.mediumresourceservice.domain.vo.MediumResourceServiceDeptVO;
import com.py.resources.mediumresourceservice.domain.vo.MediumResourceServiceListVO;
import com.py.resources.mediumresourceservice.domain.vo.MediumResourceServiceVO;
import com.py.resources.mediumresourceservice.excel.importer.add.MediumResourceServiceAddImporter;
import com.py.resources.mediumresourceservice.excel.importer.add.model.MediumResourceAccountInfoImportModel;
import com.py.resources.mediumresourceservice.excel.importer.add.model.MediumResourceInvoicingInfoImportModel;
import com.py.resources.mediumresourceservice.excel.importer.add.model.MediumResourceServiceInfoImportModel;
import com.py.resources.mediumresourceservice.excel.importer.update.MediumResourceServiceUpdateImporter;
import com.py.resources.mediumresourceservice.excel.importer.update.model.MediumResourceAccountUpdateImportModel;
import com.py.resources.mediumresourceservice.excel.importer.update.model.MediumResourceInvoicingUpdateImportModel;
import com.py.resources.mediumresourceservice.excel.importer.update.model.MediumResourceServiceInfoUpdateModel;
import com.py.resources.mediumresourceservice.excel.search.MediumResourceServiceSearchImporter;
import com.py.resources.mediumresourceservice.service.IMediumResourceServiceService;
import com.py.resources.mediumresourceservice.service.impl.MediumResourceServiceServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

import static com.py.resources.mediumresourceservice.excel.MediumResourceServiceExcelConstant.SheetName;

/**
 * 媒介资源管理-媒介资源服务商Controller
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Api(tags = "媒介资源管理-媒介资源服务商")
@RestController
@RequestMapping("/resources/mediumResourceService")
public class MediumResourceServiceController extends BaseController {

    /** 媒介资源管理-媒介资源服务商服务 */
    @Resource
    private IMediumResourceServiceService mediumResourceServiceService;

    @Resource
    private IOssService ossService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    @Resource
    private IMediumLaundryListService laundryListService;

    @Resource
    private MediumResourceServiceConverter serviceConverter;

    @Resource
    private IDownloadControlService downloadControlService;

    /** 媒体资源服务商导入查询器 */
    @Resource
    private MediumResourceServiceSearchImporter serviceSearchImporter;

    /**
     * 媒介资源(服务商)-查看清单
     *
     * @param query 资源清单查询参数
     * @return 媒介资源管理-媒介资源服务商列表
     */
    @ApiOperation("媒介资源(服务商)-查看清单")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:list')")
    @GetMapping("/pageServiceLaundryList")
    public R<PageInfo<MediumResourceServiceListVO>> pageServiceLaundryList(LaundryListServiceQuery query) {
        PageInfo<MediumResourceServiceListVO> voList = this.mediumResourceServiceService.pageServiceLaundryList(query);
        return R.success(voList);
    }

    /**
     * 分页查询媒介资源管理-媒介资源服务商列表
     *
     * @param query 媒介资源管理-媒介资源服务商查询参数
     * @return 媒介资源管理-媒介资源服务商分页
     */
    @ApiOperation("分页查询询媒介资源管理-媒介资源服务商列表")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:list')")
    @PostMapping("/pageMediumResourceService")
    public R<MediumDisableInfoVO<PageInfo<MediumResourceServiceListVO>>> pageMediumResourceService(@RequestBody MediumResourceServiceQuery query) {
        return R.success(this.mediumResourceServiceService.pageMediumResourceServiceList(query));
    }

    /**
     * 查询媒介资源管理-媒介资源服务商列表,更新人部门
     *
     * @param query 媒介资源管理-媒介资源服务商查询参数,更新人部门
     * @return 媒介资源管理-媒介资源服务商,更新人部门信息
     */
    @ApiOperation("查询询媒介资源管理-媒介资源服务商列表,更新部门")
    @PostMapping("/findMediumResourceServiceUpdateDept")
    public R<List<MediumResourceServiceDeptVO>> findMediumResourceServiceUpdateDept(@RequestBody MediumResourceServiceQuery query) {
        return R.success(this.mediumResourceServiceService.findMediumResourceServiceUpdateDept(query));
    }

    /**
     * 获取媒介资源管理-媒介资源服务商详细信息
     * @param id 媒介资源管理-媒介资源服务商主键
     * @return 媒介资源管理-媒介资源服务商视图模型
     */
    @ApiOperation("获取媒介资源管理-媒介资源服务商详细信息")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:query')")
    @GetMapping(value = "/{id}")
    public R<MediumResourceServiceVO> getInfo(@PathVariable("id") Long id) {
        return R.success(mediumResourceServiceService.selectMediumResourceServiceById(id));
    }

    /**
     * 新增媒介资源管理-媒介资源服务商
     *
     * @param dto 媒介资源管理-媒介资源服务商修改参数
     * @return 是否成功
     */
    @ApiOperation("新增媒介资源管理-媒介资源服务商")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:add')")
    @Log(title = "媒介资源管理-媒介资源服务商", businessType = BusinessType.INSERT)
    @PostMapping
    public R<String> add(@RequestBody MediumResourceServiceDTO dto) {
        String service = mediumResourceServiceService.insertMediumResourceService(dto);
        if(StringUtils.isBlank(service)){
            return R.success();
        }
        return R.failed(8004,service,null);
    }

    /**
     * 修改媒介资源管理-媒介资源服务商
     *
     * @param dto 媒介资源管理-媒介资源服务商修改参数
     * @return 是否成功
     */
    @ApiOperation("修改媒介资源管理-媒介资源服务商")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:edit')")
    @Log(title = "媒介资源管理-媒介资源服务商", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<ErrorInfoVO> edit(@RequestBody MediumResourceServiceDTO dto) {
        ErrorInfoVO errorInfoVO  = mediumResourceServiceService.updateMediumResourceService(dto);
        if(Objects.isNull(errorInfoVO)){
            return R.success();
        }
        if (StringUtils.isNotBlank(errorInfoVO.getErrorInfo())){
            return R.success(errorInfoVO);
        }
        return R.failed(8004,errorInfoVO.getErrorResourceName(),null);
    }

    /**
     * 删除列表
     * @param query 查询列表实体
     * @return 列表
     */
    @ApiOperation("删除列表" )
    @PostMapping("/queryRemoveList" )
    public R<PageInfo<MediumResourceServiceListVO>> queryRemoveList(@RequestBody MediumResourceServiceQuery query) {
        return R.success(mediumResourceServiceService.queryRemoveList(query));
    }

    /**
     * 删除媒介资源管理-媒介资源服务商
     * @param query 需要删除的媒介资源管理-媒介资源服务商主键集合
     * @return 是否成功
     */
    @ApiOperation("删除媒介资源管理-媒介资源服务商" )
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:remove')")
    @Log(title = "媒介资源管理-媒介资源服务商", businessType = BusinessType.DELETE)
    @PostMapping("/remove" )
    public R<Boolean> remove(@RequestBody MediumResourceServiceQuery query) {
        return R.success(mediumResourceServiceService.deleteMediumResourceServiceByIds(query));
    }

    /**
     * 查看清单-批量下载
     * @param query 导出查询参数
     */
    @ApiOperation("查看清单-批量下载")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:export')")
    @Log(title = "查看清单-批量下载", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@RequestBody MediumResourceServiceQuery query) throws IOException {

        Integer residueNum = this.downloadControlService.residueNum(query.getUserId());
        if (residueNum != null && residueNum == 0){
            throw new ServiceException("本人下载条数已达最大限制,请联系管理员");
        }


        String label = EnumUtils.getLabelByValue(query.getMenuType(), LaundryListType.class);
        String fileName = label+"(服务商) " + DateUtils.getTimeCn() + ".xlsx";
        query.setFileName(fileName);

        List<MediumResourceServiceExportModel> exportModels = this.mediumResourceServiceService.exportMediumResourceService(query);

        if (ListUtil.any(exportModels)){
            List<Long> resourceIdList = ListUtil.map(exportModels, MediumResourceServiceExportModel::getResourceId);
            //删除加入清单接口
            LaundryListRemoveDTO removeDTO = this.serviceConverter.toQuery(query);
            removeDTO.setTabType(1);
            removeDTO.setResourceIdList(resourceIdList);
            List<Long> deleteLaundryIdList = this.laundryListService.deleteLaundryIdList(removeDTO);
            query.setDeleteLaundryIdList(deleteLaundryIdList);
        }

        reusableAsyncTaskService.addTask(String.format("%s(服务商)-下载资源",label), TaskType.Export,query, MediumResourceServiceServiceImpl.class);

        return R.success(Constants.SUBMIT_SUCCESS);
    }


    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @ApiOperation("批量导入")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:import')" )
    @Log(title = "媒介资源管理-媒介资源服务商" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {

        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        ExcelUtil.verifyImportSheetCompleteness(file.getInputStream(), MediumResourceServiceInfoImportModel.class,SheetName.RESOURCE_SERVICE_INFO, true);
        ExcelUtil.verifyImportSheetCompleteness(file.getInputStream(), MediumResourceAccountInfoImportModel.class, SheetName.ACCOUNT_INFO);
        ExcelUtil.verifyImportSheetCompleteness(file.getInputStream(), MediumResourceInvoicingInfoImportModel.class,SheetName.INVOICING_INFO);

        MediumResourceServiceQuery query = new MediumResourceServiceQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        query.setImportType(ImportType.INSERT);
        reusableAsyncTaskService.addTask("资源管理-服务商批量导入", TaskType.Import,query, MediumResourceServiceAddImporter.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @ApiOperation("获取导入模板")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:import')" )
    @Log(title = "媒介资源管理-媒介资源服务商" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_service.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("资源管理(服务商)-下载模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = null;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * 批量更新
     * @param file 导入文件
     * @return 导入结果
     */
    @ApiOperation("批量更新")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:import')" )
    @Log(title = "媒介资源管理-媒介资源服务商" , businessType = BusinessType.IMPORT)
    @PostMapping("/importUpdateData" )
    public R<String> importUpdateData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        ExcelUtil.verifyImportSheetCompleteness(file.getInputStream(),MediumResourceServiceInfoUpdateModel.class,SheetName.RESOURCE_SERVICE_INFO);
        ExcelUtil.verifyImportSheetCompleteness(file.getInputStream(), MediumResourceAccountUpdateImportModel.class, SheetName.ACCOUNT_INFO);
        ExcelUtil.verifyImportSheetCompleteness(file.getInputStream(), MediumResourceInvoicingUpdateImportModel.class,SheetName.INVOICING_INFO);

        MediumResourceServiceQuery query = new MediumResourceServiceQuery();
        OssUploadResult upload = this.ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        query.setImportType(ImportType.UPDATE);
        reusableAsyncTaskService.addTask("资源管理-服务商批量更新", TaskType.Import,query, MediumResourceServiceUpdateImporter.class);

        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 禁用
     *
     * @param dto 修改状态参数
     * @return 是否成功
     */
    @ApiOperation("使用/禁用")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:edit')")
    @Log(title = "禁用", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    public R<Boolean> updateStatus(@RequestBody ResourceUpdateDTO dto) {
        return R.success(mediumResourceServiceService.updateStatus(dto));
    }

    /**
     * 获取导入查询模板
     * @param response 请求响应
     */
    @ApiOperation("获取导入查询模板")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:import')" )
    @Log(title = "媒介资源管理-媒介资源服务商" , businessType = BusinessType.IMPORT)
    @PostMapping("/importSelectTemplate" )
    public void importSelectTemplate(HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_select.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("选资源(服务商)-导入查询下载模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = null;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入查询
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @ApiOperation("导入查询")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:import')" )
    @Log(title = "导入查询" , businessType = BusinessType.IMPORT)
    @PostMapping("/importSelectData" )
    public R<ImportSelectResultVO> importSelectData(MultipartFile file) throws Exception {

        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        ExcelUtil<ImportSelectModel> util = new ExcelUtil<>(ImportSelectModel.class);
        List<ImportSelectModel> importSelectModelList = util.importExcel(file.getInputStream());
        if(ListUtil.isEmpty(importSelectModelList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        return R.success(this.serviceSearchImporter.importSelectData(importSelectModelList));
    }

    /**
     * 获取资源列表
     * @param query 项目资源名称
     * @return 媒介资源管理-媒介资源服务商列表
     */
    @ApiOperation("获取资源列表")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:list')")
    @GetMapping("/queryResourceList")
    public R<PageInfo<MediumResourceServiceListVO>> queryResourceList(MediumResourceServiceQuery query) {
        return R.success(this.mediumResourceServiceService.queryResourceList(query));
    }

}
