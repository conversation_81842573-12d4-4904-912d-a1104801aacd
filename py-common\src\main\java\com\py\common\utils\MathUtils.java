package com.py.common.utils;

import com.py.common.utils.collection.ListUtil;
import org.springframework.util.Assert;
import rx.functions.Func2;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;

/**
 * 数学工具类
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class MathUtils {

    /**
     * 判断BigDecimal是否相等,
     * @param a 值a
     * @param b 值b
     * @return true: 两值相等, 入参为null必定返回false
     */
    public static boolean equal(BigDecimal a, BigDecimal b) {
        if(a == null || b == null) {
            return false;
        }

        return a.compareTo(b) == 0;
    }

    /**
     * 判断 Long 是否相等,
     * @param a 值a
     * @param b 值b
     * @return true: 两值相等, 入参为null必定返回false
     */
    public static boolean equal(Long a, Long b) {
        if(a == null && b == null) {
            return true;
        }else if(a == null || b == null){
            return false;
        }

        return a.compareTo(b) == 0;
    }

    /**
     * 判断 a < b 是否成立
     * @param a 值a
     * @param b 值b
     * @return true: a < b
     */
    public static boolean less(BigDecimal a, BigDecimal b) {
        Assert.isTrue(a != null && b != null, "入参不能为null");

        return a.compareTo(b) < 0;
    }

    /**
     * 判断 a > b 是否成立
     * @param a 值a
     * @param b 值b
     * @return true: a > b
     */
    public static boolean greater(BigDecimal a, BigDecimal b) {
        Assert.isTrue(a != null && b != null, "入参不能为null");

        return a.compareTo(b) > 0;
    }

    /**
     * 相加
     * @param a a
     * @param b b
     * @return 和
     */
    public static Long add(Long a, Long b) {
        return NullMergeUtils.nullMerge(a, 0L) + NullMergeUtils.nullMerge(b, 0L);
    }

    /**
     * 相加
     * @param a a
     * @param b b
     * @return 和
     */
    public static BigDecimal add(BigDecimal a, BigDecimal b) {
        return NullMergeUtils.nullMerge(a, BigDecimal.ZERO).add(NullMergeUtils.nullMerge(b, BigDecimal.ZERO));
    }

    /**
     * 相加, 如果入参为都是null返回null
     * @param a a
     * @param b b
     * @return 和
     */
    public static BigDecimal addIfNull(BigDecimal a, BigDecimal b) {
        if(a == null && b == null){
            return null;
        }
        if(a == null){
            return b;
        }
        if(b == null){
            return a;
        }
        return a.add(b);
    }

    /**
     * 相加
     * @param a a
     * @param b b
     * @param c c
     * @return 和
     */
    public static BigDecimal add(BigDecimal a, BigDecimal b,BigDecimal c) {
        return NullMergeUtils.nullMerge(a, BigDecimal.ZERO)
                .add(NullMergeUtils.nullMerge(b, BigDecimal.ZERO))
                .add(NullMergeUtils.nullMerge(c, BigDecimal.ZERO));
    }

    /**
     * 相加, 如果入参为都是null返回null
     * @param a a
     * @param b b
     * @param c c
     * @return 和
     */
    public static BigDecimal addIfNull(BigDecimal a, BigDecimal b,BigDecimal c) {
        if(a == null && b == null && c == null){
            return null;
        }
        return NullMergeUtils.nullMerge(a, BigDecimal.ZERO)
                .add(NullMergeUtils.nullMerge(b, BigDecimal.ZERO))
                .add(NullMergeUtils.nullMerge(c, BigDecimal.ZERO));
    }


    /**
     * 相减
     * @param a a
     * @param b b
     * @return 和
     */
    public static BigDecimal subtract(BigDecimal a, BigDecimal b) {
        return NullMergeUtils.nullMerge(a, BigDecimal.ZERO).subtract(NullMergeUtils.nullMerge(b, BigDecimal.ZERO));
    }

    /**
     * 相减
     * @param a a
     * @param b b
     * @return 和
     */
    public static BigDecimal subtractIfNull(BigDecimal a, BigDecimal b) {
        if(a == null && b == null){
            return null;
        }
        if(a == null){
            return b.negate();
        }
        if(b == null){
            return a;
        }
        return a.subtract(b);
    }

    /**
     * 相减
     * @param a a
     * @param b b
     * @param c c
     * @return 和
     */
    public static BigDecimal subtractIfNull(BigDecimal a, BigDecimal b,BigDecimal c) {
        if(a == null && b == null && c == null){
            return null;
        }
        if(a == null){
            return add(b,c).negate();
        }
        if(b == null){
            return a;
        }
        return subtract(a,add(b,c));
    }

    /**
     * 相加
     * @param a a
     * @param b b
     * @return 和
     */
    public static Integer add(Integer a, Integer b) {
        return NullMergeUtils.nullMerge(a, 0) + NullMergeUtils.nullMerge(b, 0);
    }


    /**
     * 批量合计
     * @param arr 需合计的数组
     * @return 合计值, 无效值返回 0
     */
    public static BigDecimal sum(BigDecimal ... arr){
        if(ListUtil.isEmpty(arr)){
            return BigDecimal.ZERO;
        }

        return Arrays.stream(arr).reduce(BigDecimal.ZERO, MathUtils::add);
    }

    /**
     * 将b中的键值对合并到a中, 相同的键的使用合并方法合并
     * @param target 被合并的MAP
     * @param source 合并的MAP源
     * @param <T> 键类型
     * @return 合并后的Map
     */
    public static <T> Map<T, Integer> intAddTo(Map<T, Integer> target, Map<T, Integer> source) {
        return addTo(target, source, MathUtils::add);
    }

    /**
     * 将b中的键值对合并到a中, 相同的键的使用合并方法合并
     * @param target 被合并的MAP
     * @param source 合并的MAP源
     * @param sumFunction 相同键的合并函数
     * @param <TKey> 键类型
     * @param <TValue> 值类型
     * @return 合并后的Map
     */
    public static <TKey, TValue> Map<TKey, TValue> addTo(Map<TKey, TValue> target, Map<TKey, TValue> source, Func2<TValue, TValue, TValue> sumFunction) {
        Assert.notNull(target, "被合并的Map不能为null");

        if(ListUtil.isEmpty(source)) {
            return target;
        }

        for(Map.Entry<TKey, TValue> entry : source.entrySet()) {
            // 不存在则直接添加
            if(target.containsKey(entry.getKey()) == false) {
                target.put(entry.getKey(), entry.getValue());
            } else {
                // 存在键时计算合计值
                TValue sum = sumFunction.call(target.get(entry.getKey()), entry.getValue());
                target.put(entry.getKey(), sum);
            }
        }
        return target;
    }

    /**
     * 数值转换，（千分位）
     * @param value 值
     * @return 数值
     */
    public static BigDecimal toBigDecimal(String value){
        if(StringUtils.isBlank(value)){
            return null;
        }
        value = value.replaceAll(",", "");
        if(!StringUtils.isNumber(value)){
            return null;
        }
        return new BigDecimal(value);
    }

    /**
     * 数值转换，（千分位）
     * @param value 值
     * @return 数值
     */
    public static Integer toInteger(String value){
        if(StringUtils.isBlank(value)){
            return null;
        }
        return Integer.parseInt(value.replaceAll(",",""));
    }
}
