package com.py.common.tools.infocompletionratecalculator.metadata;

import com.py.common.utils.MathUtils;
import com.py.common.utils.collection.ListUtil;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

/**
 * SPU信息完整度元数据
 * <AUTHOR>
 */
public class InfoCompletionRateMetadata<T> {

    /** 必填字段是否填写判断函数列表 */
    List<Predicate<T>> requiredFieldPredicateList;
    /** 选填字段是否填写判断函数列表 */
    List<Predicate<T>> optionalFieldPredicateList;

    /** 必填字段项目权重 */
    private BigDecimal requiredWeight;

    /** 选填字段项目权重 */
    private BigDecimal optionalWeight;

    public InfoCompletionRateMetadata() {
        this.requiredFieldPredicateList = new ArrayList<>();
        this.optionalFieldPredicateList = new ArrayList<>();
    }

    /**
     * 初始化计算权重
     * <p>设置权重是按权重分别计算</p>
     * <p>未设置权重时合并计算</p>
     * @param requiredWeight 必填字段项目权重
     * @param optionalWeight 选填字段项目权重
     */
    public void initWeight(@NonNull BigDecimal requiredWeight, @NonNull BigDecimal optionalWeight) {
        Assert.isTrue(BigDecimal.ZERO.compareTo(requiredWeight) > 0, "字段权重必须大于0");
        Assert.isTrue(BigDecimal.ZERO.compareTo(optionalWeight) > 0, "字段权重必须大于0");

        this.requiredWeight = requiredWeight;
        this.optionalWeight = optionalWeight;
    }

    /**
     * 通过元数据计算信息完整度
     * @param data 需计算的对象
     * @return 对象信息完整度
     */
    public BigDecimal calculation(T data) {
        // 未设置权重时合并计算
        if(this.requiredWeight == null && this.optionalWeight == null) {
            List<Predicate<T>> fieldPredicateList = ListUtil.merge(this.requiredFieldPredicateList, this.optionalFieldPredicateList);
            return this.calculationInfoCompletionRate(data, fieldPredicateList);
        } else {
            // 设置权重是按权重分别计算
            BigDecimal requiredPartRate = this.calculationInfoCompletionRate(data, this.requiredFieldPredicateList);
            BigDecimal optionalPartRate = this.calculationInfoCompletionRate(data, this.optionalFieldPredicateList);
            BigDecimal result = MathUtils.add(requiredPartRate.multiply(this.requiredWeight), optionalPartRate.multiply(this.optionalWeight));
            return result.divide(BigDecimal.ONE, 2, RoundingMode.HALF_UP);
        }
    }

    /**
     * 计算信息完整度
     * @param data 需计算的对象
     * @param isSetPredicateList 是否填写判断函数列表
     * @return 信息完整度
     */
    private BigDecimal calculationInfoCompletionRate(T data, List<Predicate<T>> isSetPredicateList) {
        if(ListUtil.isEmpty(isSetPredicateList)) {
            return BigDecimal.ONE;
        }

        long setFieldCount = isSetPredicateList.stream()
                .filter(predicate -> predicate.test(data))
                .count();
        BigDecimal allFieldCount = BigDecimal.valueOf(isSetPredicateList.size());
        return BigDecimal.valueOf(setFieldCount).divide(allFieldCount, 2, RoundingMode.HALF_UP);
    }

    /**
     * 合并相同类型的元数据
     * @param metadata 元数据
     */
    public void mergeSubModelMetadata(InfoCompletionRateMetadata<T> metadata) {
        if(ListUtil.any(metadata.requiredFieldPredicateList)) {
            this.requiredFieldPredicateList.addAll(metadata.requiredFieldPredicateList);
        }

        if(ListUtil.any(metadata.optionalFieldPredicateList)) {
            this.optionalFieldPredicateList.addAll(metadata.optionalFieldPredicateList);
        }
    }

    /**
     * 添加字段是否填写判断函数
     * @param required 该字段是否必填
     * @param isSetPredicate 字段是否填写判断函数
     */
    public void addIsSetPredicate(boolean required, Predicate<T> isSetPredicate) {
        if(required) {
            this.requiredFieldPredicateList.add(isSetPredicate);
        } else {
            this.optionalFieldPredicateList.add(isSetPredicate);
        }
    }
}
