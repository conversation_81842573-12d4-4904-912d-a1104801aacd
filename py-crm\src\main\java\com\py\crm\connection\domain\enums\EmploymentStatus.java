package com.py.crm.connection.domain.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version EmploymentStatus 2023/8/11 9:45
 */
@Getter
@AllArgsConstructor
public enum EmploymentStatus implements IDict<Integer> {

    /** 在职 */
    INCUMBENCY(0, "在职"),
    /** 离职 */
    LEAVE_OFFICE(1, "离职");

    private final Integer value;

    private final String label;
}

