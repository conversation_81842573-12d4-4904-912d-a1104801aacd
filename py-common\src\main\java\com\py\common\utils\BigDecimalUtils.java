package com.py.common.utils;

import java.math.BigDecimal;

/**
 * BigDecimal 工具类
 * <AUTHOR>
 */
public class BigDecimalUtils {

    /**
     * 格式化并去除多余的0
     * @param value 值
     * @return 格式化后的数据
     */
    public static String serialize(BigDecimal value){
        if(value == null){
            return null;
        }
        BigDecimal resultValue = value.setScale(4, BigDecimal.ROUND_HALF_UP);
        String result = String.format("%,.4f", resultValue);
        // 获取小数部分,
        String fractional = result.substring(result.indexOf(".") + 1);

        if("0000".equals(fractional)){
            result = result.substring(0, result.indexOf("."));
            // 如果小数部分为[1-9]0, 则去掉小数部分最后一位
        }else if(fractional.endsWith("000")){
            // 如果小数部分为0, 则去掉小数部分最后一位
            result = result.substring(0, result.length() - 3);
        }else if(fractional.endsWith("00")){
            // 如果小数部分为0, 则去掉小数部分最后一位
            result = result.substring(0, result.length() - 2);
        }else if(fractional.endsWith("0")){
            // 如果小数部分为0, 则去掉小数部分最后一位
            result = result.substring(0, result.length() - 1);
        }

        return result;
    }

    /**
     * 格式化金额
     * @param value 值
     * @return 格式化后的数据
     */
    public static String formatBigDecimal(BigDecimal value){
        if(value == null){
            return null;
        }
        BigDecimal resultValue = value.setScale(2, BigDecimal.ROUND_HALF_UP);
        String result = String.format("%.2f", resultValue);
        // 获取小数部分,
        String fractional = result.substring(result.indexOf(".") + 1 );

        if(fractional.endsWith("00")){
            // 如果小数部分为0, 则去掉小数部分最后一位
            result = result.substring(0, result.length() - 3);
        }else if(fractional.endsWith("0")){
            // 如果小数部分为0, 则去掉小数部分最后一位
            result = result.substring(0, result.length() - 1);
        }

        return result;
    }

    /**
     * 去除多余的0
     * @param value 值
     * @return 数据
     */
    public static String removeZero(BigDecimal value){
        if(value == null){
            return null;
        }
        BigDecimal resultValue = value.setScale(4, BigDecimal.ROUND_HALF_UP);
        String result = String.format("%.4f", resultValue);
        // 获取小数部分,
        String fractional = result.substring(result.indexOf(".") + 1);

        if("0000".equals(fractional)){
            result = result.substring(0, result.indexOf("."));
            // 如果小数部分为[1-9]0, 则去掉小数部分最后一位
        }else if(fractional.endsWith("000")){
            // 如果小数部分为0, 则去掉小数部分最后一位
            result = result.substring(0, result.length() - 3);
        }else if(fractional.endsWith("00")){
            // 如果小数部分为0, 则去掉小数部分最后一位
            result = result.substring(0, result.length() - 2);
        }else if(fractional.endsWith("0")){
            // 如果小数部分为0, 则去掉小数部分最后一位
            result = result.substring(0, result.length() - 1);
        }

        return result;
    }

    /**
     * 值是否为 0 或 null
     * @param value 值
     * @return true: 是  0 或 null
     */
    public static boolean isZeroOrNull(BigDecimal value) {
        return value == null
            || BigDecimal.ZERO.compareTo(value) == 0;
    }

    /**
     * 值是否不为 0 或 null
     * @param value 值
     * @return true: 不是  0 或 null
     */
    public static boolean isNotZeroOrNull(BigDecimal value) {
        return !isZeroOrNull(value);
    }

}
