package com.py.crm.customer.invoicing.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.core.domain.vo.BaseUpdateInfoVO;
import com.py.common.utils.DateUtils;
import com.py.common.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 客户管理-客户-开票信息视图模型
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("客户管理-客户-开票信息视图模型" )
public class InvoicingVO extends BaseUpdateInfoVO {
    private static final long serialVersionUID = 1L;

    /** 银行账号*/
    @ApiModelProperty("银行账号")
    private String bankAccount;

    /** 开户银行*/
    @ApiModelProperty("开户银行")
    private String bankDeposit;

    /** 公司地址*/
    @ApiModelProperty("公司地址")
    private String companyAddress;

    /** 创建者*/
    @ApiModelProperty("创建者")
    private String createBy;

    /** 创建人部门*/
    @ApiModelProperty("创建人部门")
    private String createDept;

    /** 创建者Id*/
    @ApiModelProperty("创建者Id")
    private Long createId;

    /** 创建时间*/
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 删除标志*/
    @ApiModelProperty("删除标志")
    private String delFlag;

    /** 税号*/
    @ApiModelProperty("税号")
    private String dutyParagraph;

    /** 自增id*/
    @ApiModelProperty("自增id")
    private Long id;

    /** 开票id*/
    @ApiModelProperty("开票id")
    private Long invoicingId;

    /** 开票名称*/
    @ApiModelProperty("开票名称")
    private String invoicingName;

    /** 电话*/
    @ApiModelProperty("电话")
    private String phone;

    /** 展示id */
    @ApiModelProperty("展示id")
    private Integer showId;

    private String updateTimeStr;
    public String getUpdateTimeStr() {
        if(getUpdateTime() == null){
            return StringUtils.EMPTY;
        }
        return DateUtils.format(getUpdateTime(),"yyyy-MM-dd HH:mm:ss");
    }
}
