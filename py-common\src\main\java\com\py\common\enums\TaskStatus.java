package com.py.common.enums;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TaskStatus implements IDict<Integer> {

    /** 未开始 */
    NotStarted(0, "未开始"),
    /** 运行中 */
    Running(1, "运行中"),
    /** 已完成 */
    Completed(2, "已完成"),
    /** 失败 */
    Failed(3, "失败"),
    /** 任务拒收 */
    reject(4,"任务拒收"),
    ;
    private final Integer value;
    private final String label;
}
