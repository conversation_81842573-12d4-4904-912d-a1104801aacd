package com.py.common.utils;

import com.py.common.constant.HttpStatus;
import com.py.common.core.domain.model.LoginUser;
import com.py.common.exception.ServiceException;
import org.apache.commons.codec.binary.Base64;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.spec.AlgorithmParameterSpec;
import java.util.List;

/**
 * 安全服务工具类
 * <AUTHOR>
 */
public class SecurityUtils {
    /**
     * 用户ID
     **/
    public static Long getUserId() {
        try {
            return getLoginUser().getUserId();
        } catch(Exception e) {
            throw new ServiceException("获取用户ID异常" , HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取租户Id
     * @return 租户Id
     */
    public static Long getTenantId() {
        throw new UnsupportedOperationException("不支持多租户");
    }

    /**
     * 获取部门ID
     **/
    public static List<Long> getDeptId() {
        try {
            return getLoginUser().getDeptIdList();
        } catch(Exception e) {
            throw new ServiceException("获取部门ID异常" , HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户账户
     **/
    public static String getUsername() {
        try {
            return getLoginUser().getUsername();
        } catch(Exception e) {
            throw new ServiceException("获取用户账户异常" , HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser() {
        try {
            return (LoginUser) getAuthentication().getPrincipal();
        } catch(Exception e) {
            return null;
        }
    }

    /** 当前用户是否为管理员 */
    public static boolean currentUserIsAdmin() {
        try {
            return getLoginUser().getUser().isAdmin();
        } catch(NullPointerException nullPointerException) {
            return false;
        }
    }


    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 生成BCryptPasswordEncoder密码
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 截取文本后 n 位字符生产密码
     * @param originalText 原始文本
     * @param truncateDigits 截取位数
     * @return 加密字符串
     */
    public static String encryptPasswordByLastText(String originalText, int truncateDigits) {
        if(StringUtils.isBlank(originalText) || originalText.length() < truncateDigits) {
            throw new IllegalArgumentException("原始文本过短");
        }
        String password = originalText.substring(originalText.length() - truncateDigits);
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     * @param rawPassword 真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 是否为管理员
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }


    //算法
    private static final String ALGO = "AES";
    private static final String ALGORITHMSTR = "AES/ECB/PKCS5Padding";
    private static final String AES_KEY = "cBssbHB3ZA==HKXT";
    private static final String ALGO_MODE_PATTERN = "AES/CBC/PKCS5Padding";

    /**
     * Base64解密
     */
    public static String decryptV(String skey, String ivData, String encryptedData) throws Exception {
        byte[] encData = Base64.decodeBase64(encryptedData);
        byte[] iv = Base64.decodeBase64(ivData);
        byte[] sessionKey = Base64.decodeBase64(skey);

        AlgorithmParameterSpec ivSpec = new IvParameterSpec(iv);
        Cipher cipher = Cipher.getInstance(ALGO_MODE_PATTERN);
        SecretKeySpec keySpec = new SecretKeySpec(sessionKey, ALGO);
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
        //解析解密后的字符串
        return new String(cipher.doFinal(encData), StandardCharsets.UTF_8);
    }
}
