package com.py.common.constant;

import io.jsonwebtoken.Claims;

/**
 * 通用常量信息
 * <AUTHOR>
 */
public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /** 逻辑删除_未删除标注 */
    public static final int LOGIC_NOT_DELETE_VALUE = 0;

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录用户编号 redis key
     */
    public static final String LOGIN_USERID_KEY = "login_userid:";

    /** 验证码 redis key */
    public static final String VERIFY_CODE_KEY = "verify_code:";

    /** 学生登录鉴权编码 redis key */
    public static final String STUDENT_CODE_KEY =  "student_code";

    /** 重置密码 redis key */
    public static final String RESET_PASSWORD_KEY = "reset_password_by_phone:";

    /**
     * 重置密码 邮箱验证码 key
     */
    public static final String EMAIL_CODE_KEY = "email_code:";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 顿号
     */
    public static final String STOP_SIGN = "、";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = Claims.SUBJECT;

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * RMI 远程方法调用
     */
    public static final String LOOKUP_RMI = "rmi:";

    /**
     * LDAP 远程方法调用
     */
    public static final String LOOKUP_LDAP = "ldap:";

    /**
     * LDAPS 远程方法调用
     */
    public static final String LOOKUP_LDAPS = "ldaps:";

    /**
     * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
     */
    public static final String[] JOB_WHITELIST_STR = {"com.ruoyi"};

    public static final String COMMA = ",";

    public static final String COMMA_CAPITAL = "，";

    public static final String COLON = ":";

    public static final String S = "s";

    public static final String EXPIRATION = "expiration";

    public static final String ACCESS_KEY_ID = "accessKeyId";

    public static final String SECURITY_TOKEN = "securityToken";

    public static final String REQUEST_ID = "requestId";

    public static final String BUCKET_NAME = "bucketName";

    public static final String ENDPOINT = "endpoint";

    public static final String ACCESS_KEY_SECRET = "accessKeySecret";

    public static final String FIFTY_NINE = "59";
    /** 树根节点Id */
    public static final long TREE_ROOT_ID = 0;

    /** XLSX */
    public static final String XLSX = ".xlsx";

	/** XLS */
    public static final String XLS = ".xls";

    /**
     * 定时任务违规的字符
     */
    public static final String[] JOB_ERROR_STR = {"java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
            "org.springframework", "org.apache", "com.py.common.utils.file"};

    /** 默认单次批处理大小 */
    public static final int BATCH_SIZE = 500;

    /** 密码截取位数 */
    public static final int PASSWORD_INTERCEPTION_DIGITS = 6;

    /** 下载记录序号缓存key */
    public static final String DOWNLOAD_NAME_NUMBER = "download_name_number_key";

    /** OA日常合同序号缓存的key */
    public static final String OA_DAILY_CONTRACT_NUMBER ="oa_daily_contract_key";

    /** 供应商合同序号缓存的key */
    public static final String SUPPLIER_CONTRACT_NUMBER ="supplier_contract_key";

    /** 客户合同序号缓存的key */
    public static final String CUSTOMER_CONTRACT_NUMBER ="customer_contract_key";

    /** 服务商序号缓存key */
    public static final String DOWNLOAD_SERVICE_NUMBER = "download_service_key";

    /** 红人序号缓存key */
    public static final String DOWNLOAD_SENSATION_NUMBER = "download_service_key";

    /** 进入项目缓存key */
    public static final String UPDATE_PROJECT = "update_project-";

    /** 项目管理进入返点申请key */
    public static final String PROJECT_RESOURCE_REBATE = "update_project_resource-";

    /** 项目管理进入返点申请用户key */
    public static final String PROJECT_RESOURCE_REBATE_USER = "update_project_resource_user-";

    /** 进入项目缓存当前登录人的项目id */
    public static final String LOGIN_USER_PROJECT_ID = "login_user_project_id-";

    /** 菜单：项目 */
    public final static Integer SED = 1;

    /** 菜单：财务 */
    public final static Integer FINANCE = 0;

    /** 连接符 */
    public final static String CONNECTOR = "——>";

    /** 一个连接符 */
    public final static String ONE_CONNECTOR = "-";

    /** 默认的连接符 */
    public final static String DEFAULT_CONNECTOR = "--";

    /** 人脉表名称 */
    public static final String CRM_CONNECTION = "py_crm_connection";

    /** 提交成功 */
    public static final String SUBMIT_SUCCESS = "提交成功";
    /*资源清单是空的**/
    public static final String ISNULL = "资源清单是空的";

    /** 通过 */
    public static final Integer PASS_STATUS = 2;
    /** 未通过 */
    public static final Integer FAILED_STATUS = 1;

    /** 通过-通过 */
    public static final Integer PASS_PASS_STATUS = 4;
    /** 通过_未通过 */
    public static final Integer PASS_FAILED_STATUS = 3;

    public static final Long ZERO_LONG = 0L;
    public static final String TOTAL_MONEY = "totalMoney";

    /** socketIo缓存用户的客户端ID */
    public static final String SOCKET_IO = "socketIo-";

    /** 金额有效的小数位 */
    public static final Integer DECIMAL_AMOUNT = 2;
}
