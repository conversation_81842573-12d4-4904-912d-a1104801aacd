<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.py.crm.connection.mapper.ConnectionMapper">

    <resultMap type="com.py.crm.connection.domain.Connection" id="ConnectionResult">
        <result property="id" column="id" />
        <result property="connectionId" column="connection_id" />
        <result property="connectionName" column="connection_name" />
        <result property="status" column="status" />
        <result property="phone" column="phone" />
        <result property="wechatNumber" column="wechat_number" />
        <result property="otherNumber" column="other_number" />
        <result property="customerId" column="customer_id" />
        <result property="currentEmployer" column="current_employer"/>
        <result property="responsibleBrand" column="responsible_brand"
                typeHandler="com.py.common.typehandler.impl.StringSetTypeHandler"/>
        <result property="industryCategory" column="industry_category"
                typeHandler="com.py.common.typehandler.impl.LongSetTypeHandler"/>
        <result property="departmentName" column="department_name" />
        <result property="postName" column="post_name" />
        <result property="pyTrustLevel" column="py_trust_level" />
        <result property="connectionAddress" column="connection_address" />
        <result property="remark" column="remark" />
        <result property="createId" column="create_id" />
        <result property="createBy" column="create_by" />
        <result property="createDept" column="create_dept" />
        <result property="createTime" column="create_time" />
        <result property="initiationTime" column="initiation_time" />
        <result property="updateId" column="update_id" />
        <result property="updateBy" column="update_by" />
        <result property="updateDept" column="update_dept" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
    </resultMap>
    <select id="getCount" resultType="java.lang.Long">
        select count(id) from py_crm_connection where phone =#{phone}
            and audit_status in(-1,0,1)
        <if test="connectionId != null and connectionId != ''">
            and connection_id != #{connectionId}
        </if>
        and del_flag = 0
    </select>

    <sql id="deptUserIds">
        select user_id from sys_user_dept where dept_id =#{deptId}
    </sql>

    <!--分页查询人脉-->
    <select id="selectConnection" resultMap="ConnectionResult">
        select distinct py_crm_connection.*
        from py_crm_connection py_crm_connection
        join py_crm_connection_wander wander on py_crm_connection.connection_id = wander.connection_id
        <where>
            <trim prefixOverrides="WHERE">
                ${ew.customSqlSegment}
            </trim>
            <if test="employmentWrapper.isEmptyOfNormal() != true">
                and py_crm_connection.connection_id in (select distinct connection_id from py_crm_connection_employment
                ${employmentWrapper.customSqlSegment} and del_flag='0')
            </if>
            <if test="query.serviceUserIds != null and query.serviceUserIds.size() > 0">
                and wander.service_user_id in (
                Select Distinct user_id From sys_user
                WHERE
                del_flag = 0 and user_id in
                <foreach collection="query.serviceUserIds" item="serviceUserId" open="(" close=")" separator=",">
                    #{serviceUserId}
                </foreach> )
                AND wander.is_hidden = 0
            </if>
            <if test="query.serviceUserDeptList != null and query.serviceUserDeptList.size() > 0">
                and wander.service_user_id in (Select Distinct ud.user_id From sys_user_dept ud join sys_dept dept on ud.dept_id = dept.dept_id
                WHERE
                dept.del_flag = 0
                and dept.dept_name in
                <foreach collection="query.serviceUserDeptList" item="serviceDept" open="(" close=")" separator=",">
                    #{serviceDept}
                </foreach>
                )
                AND wander.is_hidden = 0
            </if>
            <if test="query.dataScope != null and query.dataScope != ''">
                ${query.dataScope}
            </if>
            AND py_crm_connection.del_flag = 0
            AND wander.del_flag = 0
        </where>
        order by py_crm_connection.update_time desc, py_crm_connection.id desc
    </select>

    <select id="getByConnectionId" resultMap="ConnectionResult">
        select * from py_crm_connection where connection_id =#{id}
    </select>

    <!--查询人脉数量-->
    <select id="countConnection" resultType="java.lang.Long">
        select count(distinct py_crm_connection.connection_id)
        from py_crm_connection py_crm_connection
        join py_crm_connection_wander wander on py_crm_connection.connection_id = wander.connection_id
        <where>
            <trim prefixOverrides="WHERE">
                ${ew.customSqlSegment}
            </trim>
            <if test="query.dataScope != null and query.dataScope != ''">
                ${query.dataScope}
            </if>
            AND py_crm_connection.del_flag = 0
            AND wander.del_flag = 0
        </where>
    </select>

    <!--获取所有的有数据权限的人脉id列表-->
    <select id="listConnectionIds" resultType="java.lang.Long">
        select distinct crmconnection.connection_id
        from py_crm_connection crmconnection
        join py_crm_connection_wander wander on crmconnection.connection_id = wander.connection_id
        <where>
            <trim prefixOverrides="WHERE">
                ${ew.customSqlSegment}
            </trim>
            <if test="employmentWrapper.isEmptyOfNormal() != true">
                and py_crm_connection.connection_id in (select distinct connection_id from py_crm_connection_employment
                ${employmentWrapper.customSqlSegment} and del_flag='0')
            </if>
            <if test="query.serviceUserIds != null and query.serviceUserIds.size() > 0">
                and wander.service_user_id in (
                Select Distinct user_id From sys_user
                WHERE
                del_flag = 0 and user_id in
                <foreach collection="query.serviceUserIds" item="serviceUserId" open="(" close=")" separator=",">
                    #{serviceUserId}
                </foreach> )
                AND wander.is_hidden = 0
            </if>
            <if test="query.serviceUserDeptList != null and query.serviceUserDeptList.size() > 0">
                and wander.service_user_dept in (
                <foreach collection="query.serviceUserDeptList" item="serviceUserDept" open="" close="" separator=",">
                    #{serviceUserDept}
                </foreach> )
                AND wander.is_hidden = 0
            </if>
            <if test="query.dataScope != null and query.dataScope != ''">
                ${query.dataScope}
            </if>
            AND crmconnection.del_flag = 0
            AND wander.del_flag = 0
        </where>
    </select>

    <!--根据客户名称查询对应权限下的人脉id列表-->
    <select id="selectByConnectionNames" resultMap="ConnectionResult">
        select distinct py_crm_connection.*
        from py_crm_connection py_crm_connection
        join py_crm_connection_wander wander on py_crm_connection.connection_id = wander.connection_id
        <where>
            <trim prefixOverrides="WHERE">
                ${ew.customSqlSegment}
            </trim>
            <if test="query.dataScope != null and query.dataScope != ''">
                ${query.dataScope}
            </if>
            AND py_crm_connection.del_flag = 0
            AND wander.del_flag = 0
        </where>
    </select>

    <select id="findConnectionCreateDeptList" resultType="com.py.crm.connection.domain.Connection">
        select distinct py_crm_connection.create_dept
        from py_crm_connection
        join py_crm_connection_wander wander on py_crm_connection.connection_id = wander.connection_id
        <where>
            <trim prefixOverrides="WHERE">
                ${ew.customSqlSegment}
            </trim>
            <if test="query.dataScope != null and query.dataScope != ''">
                ${query.dataScope}
            </if>
            and py_crm_connection.del_flag = 0
            and wander.del_flag = 0
        </where>
       </select>

    <select id="findConnectionServiceUserDeptList" resultType="com.py.crm.connection.domain.Connection">
        select distinct dept.dept_name as create_dept
        from py_crm_connection
        join py_crm_connection_wander wander on py_crm_connection.connection_id = wander.connection_id
        left join sys_user_dept ud on ud.user_id = wander.service_user_id
        left join sys_dept dept on ud.dept_id = dept.dept_id
        <where>
            <trim prefixOverrides="WHERE">
                ${ew.customSqlSegment}
            </trim>
            and wander.is_hidden = 0
            and dept.del_flag = 0
            and dept.dept_name is not null
            <if test="query.dataScope != null and query.dataScope != ''">
                ${query.dataScope}
            </if>
            and py_crm_connection.del_flag = 0
            and wander.del_flag = 0
        </where>
    </select>
</mapper>
