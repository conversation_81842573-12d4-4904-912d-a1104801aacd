package com.py.common.tools.verify.domain;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 校验结果
 * <AUTHOR>
 */
@Getter
public class VerifyResult<T> {

    /** 错误提示 */
    private final List<String> errorMsgList = new ArrayList<>();
    private final List<T> legalDataList = new ArrayList<>();
    /** 错误数量 */
    private int errorNumber = 0;

    /**
     * 添加错误提示
     * @param errorMsg 错误提示消息
     */
    public void addErrorMsg(String errorMsg) {
        errorMsgList.add(errorMsg);
        errorNumber++;
    }

    /**
     * 添加合法数据
     * @param data 合法数据
     */
    public void addLegalData(T data) {
        this.legalDataList.add(data);
    }
}
