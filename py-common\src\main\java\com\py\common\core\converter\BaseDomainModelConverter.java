package com.py.common.core.converter;

import org.mapstruct.Mapping;

import java.util.List;

/**
 * 基础领域模型转换器接口
 * @param <TEntity> 实体类型
 * @param <TVo> 视图模型类
 * @param <TDto> 数据传输模型
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public interface BaseDomainModelConverter<TEntity, TVo, TDto> {

    /**
     * 将Entity转换为Vo
     * @param entity entity
     * @return vo
     */
    TVo toVoByEntity(TEntity entity);

    /**
     * 将 Dto 转换为 Entity
     * @param dto dto
     * @return Entity
     */
    TEntity toEntityByDto(TDto dto);

    /**
     * 将 VO 转换为 Dto
     * @param vo vo
     * @return Dto
     */
    TDto toDtoByVo(TVo vo);

    /**
     * 将 entity 转换为 Dto
     * @param entity entity
     * @return Dto
     */
    TDto toDtoByEntity(TEntity entity);

    /**
     * 将Entity转换为Vo
     * @param entityList entityList
     * @return vo列表
     */

    List<TVo> toVoByEntity(List<TEntity> entityList);

    /**
     * 将 Dto 转换为 Entity
     * @param dtoList dtoList
     * @return Entity列表
     */
    List<TEntity> toEntityByDto(List<TDto> dtoList);

    /**
     * 将 Entity 转换为 Dto
     * @param entityList entityList
     * @return Dto列表
     */
    List<TDto> toDtoByEntity(List<TEntity> entityList);

}
