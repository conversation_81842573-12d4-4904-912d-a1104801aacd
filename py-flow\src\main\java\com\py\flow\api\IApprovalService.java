package com.py.flow.api;

import com.py.flow.domain.dto.flow.ApprovalSubmitDTO;
import com.py.flow.domain.dto.flow.ReApprovalSubmitDTO;
import com.py.flow.domain.enums.ApprovalBizType;
import com.py.flow.flowinstance.domain.FlowInstance;
import com.py.flow.flowinstance.domain.query.ApprovalInfoQuery;
import com.py.flow.flowinstance.domain.vo.ApprovalFlowChartVO;
import com.py.flow.flowinstance.functional.DataDeleteCallback;
import com.py.system.recyclebin.domain.enums.RecycleBizType;
import org.flowable.engine.delegate.event.FlowableActivityEvent;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 审批服务
 * <AUTHOR>
 */
public interface IApprovalService  {

    /**
     * 发起流程（提交审批）
     * @param submitDto 提交Dto
     */
    void submitFlow(ApprovalSubmitDTO submitDto);

    /**
     * 再次提交流程（提交审批）
     * @param reSubmitDto 再次提交Dto
     */
    void reSubmitFlow(ReApprovalSubmitDTO reSubmitDto);

    /**
     * 获取审批流程图
     * @param query 查询参数
     * @return 流程图视图
     */
    ApprovalFlowChartVO getApprovalFlowChart(ApprovalInfoQuery query);

    /**
     * 通知流程用户节点完成
     * @param activityCompletedEvent 流程节点完成事件
     */
    void notifyUserTaskCompleted(FlowableActivityEvent activityCompletedEvent);

    /**
     * 删除30天前作废的数据
     * @return 数据集合
     */
    List<FlowInstance> selectDeleteFlow(int day);

    /**
     * 删除
     * @param list 删除的审批记录集合
     */
    void deleteFlowData(List<FlowInstance> list);
}
