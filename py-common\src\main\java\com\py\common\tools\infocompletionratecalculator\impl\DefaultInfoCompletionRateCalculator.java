package com.py.common.tools.infocompletionratecalculator.impl;

import com.py.common.tools.infocompletionratecalculator.metadata.InfoCompletionRateMetadata;

import java.math.BigDecimal;

/**
 * 默认信息完整度计算器
 * <AUTHOR>
 */
public class DefaultInfoCompletionRateCalculator<T> extends BaseInfoCompletionRateCalculator<T> {

    public DefaultInfoCompletionRateCalculator(Class<T> clazz) {
        super(clazz);
    }

    /**
     * 计算SPU信息完整度
     * @param target 需计算信息完整度的对象
     * @return SPU的信息完整度
     */
    @Override
    public BigDecimal calculation(T target) {
        InfoCompletionRateMetadata<T> infoCompletionRateMetadata = this.analyzingClassMetadata(this.clazz);
        return infoCompletionRateMetadata.calculation(target);
    }
}
