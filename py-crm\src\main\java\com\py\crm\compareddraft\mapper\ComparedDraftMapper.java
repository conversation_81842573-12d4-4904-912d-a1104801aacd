package com.py.crm.compareddraft.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.py.common.mybatisplus.SuperMapper;
import com.py.crm.compareddraft.domain.ComparedDraft;
import com.py.crm.compareddraft.domain.query.ComparedDraftQuery;
import com.py.crm.compareddraft.domain.vo.ComparedDraftAmountVO;
import com.py.crm.customer.domain.SupCustomer;
import com.py.crm.customerdevote.domain.query.CustomerDevoteQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户管理-比稿管理Mapper
 *
 * <AUTHOR>
 * @date 2023/07/17 09:36
 */
@Mapper
public interface ComparedDraftMapper extends SuperMapper<ComparedDraft> {


    /**
     * 客户管理-比稿管理合计
     * @param queryWrapper 要客户管理-比稿管理查询内容
     * @param wrapper
     * @return 是否成功
     */
    ComparedDraftAmountVO amount(@Param("query") ComparedDraftQuery query,
                                 @Param(Constants.WRAPPER) LambdaQueryWrapper<ComparedDraft> queryWrapper,
                                 @Param("wrapper")LambdaQueryWrapper<SupCustomer> wrapper);

    /**
     * 分页查询客户管理-比稿管理
     * @param query 权限
     * @param queryWrapper 查询条件
     * @return 比稿
     */
    List<ComparedDraft> listComparedDraft(@Param("query") ComparedDraftQuery query,
                                          @Param(Constants.WRAPPER) LambdaQueryWrapper<ComparedDraft> queryWrapper,
                                          @Param("wrapper") LambdaQueryWrapper<SupCustomer> wrapper);

    /**
     * 获取所有的客户有比稿明细的客户id列表
     * @param customerIds
     * @return
     */
    List<Long> listExistedCustomerIds(@Param("customerIds") List<Long> customerIds);


    /**
     * 客户贡献-比稿明细-分页列表及下载
     * @param query 查询条件(依据客户关联项目查结案合作时间)
     * @param queryWrapper 查询条件
     * @return 比稿明细
     */
    List<ComparedDraft> listComparedDraftByContribute(@Param("query") CustomerDevoteQuery query,
                                                      @Param(Constants.WRAPPER) LambdaQueryWrapper<ComparedDraft> queryWrapper);

    /**
     * 客户贡献-比稿明细-分页列表及下载-没有合作结案时间
     * @param queryWrapper 查询条件(客户id)
     * @return 比稿明细
     */
    List<ComparedDraft> listComparedDraftByCustomerId(@Param(Constants.WRAPPER) LambdaQueryWrapper<ComparedDraft> queryWrapper);


}
