package com.py.common.api;

import com.py.common.approve.ProjectPaymentInfoDTO;
import com.py.common.approve.ProjectRefundInfoDTO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 提供项目资源pi
 */
public interface MediumProjectResourceApi {

    /**
     * 查询已存在的项目资源id
     * @param ids 项目资源ids
     * @return 已存在的项目资源id列表
     */
    List<Long> selectProjectResouceIdByIdList(Collection<Long> ids);

    /**
     * 检测项目资源是否可交接
     * @param ids 项目资源id
     * @return 错误信息
     */
    Set<String> checkProjectResourceCanHandover(Collection<Long> ids);

    /**
     * 批量付款提交审批列表
     * @param recordIds 付款记录id列表
     * @return
     */
    List<ProjectPaymentInfoDTO> queryCommitPaymentInfo(List<Long> recordIds);

    /**
     * 退款提交审批信息
     * @param recordIds 退款记录id列表
     * @return 退款信息
     */
    List<ProjectRefundInfoDTO> queryCommitRefundInfo(List<Long> recordIds);

}
