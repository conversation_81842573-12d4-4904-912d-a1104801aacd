package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.exception.ServiceException;
import com.py.project.performancedetails.domain.query.PerformanceDetailsQuery;
import com.py.project.performancedetails.domain.vo.FinancialReportAmountVO;
import com.py.project.performancedetails.domain.vo.PerformanceDetailsListVO;
import com.py.project.performancedetails.domain.vo.PerformanceDetailsVO;
import com.py.project.performancedetails.service.IPerformanceDetailsService;
import com.py.project.projectresource.domain.vo.PerformanceResourceVO;
import com.py.project.pyincomebaddebt.domain.vo.PyFirstIncomeBadDeptInfoVO;
import com.py.project.pyresourcebaddebtflow.domain.vo.PerformanceRebateBadDebtsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

/**
 * 财报明细Controller
 *
 * <AUTHOR>
 * @date 2023-08-29
 */
@Slf4j
@Api(tags = "财报明细")
@RestController
@RequestMapping("/project/performanceDetails")
public class PerformanceDetailsController extends BaseController {

    /** 财报明细服务 */
    @Resource
    private IPerformanceDetailsService performanceDetailsService;

    /**
     * 分页查询财报明细列表
     *
     * @param query 财报明细查询参数
     * @return 财报明细分页
     */
    @ApiOperation("分页查询询财报明细列表")
    @PreAuthorize("@ss.hasPermi('project:performanceDetails:list')")
    @GetMapping("/listPerformanceDetails")
    public R<List<PerformanceDetailsListVO>> listPerformanceDetails(PerformanceDetailsQuery query) {
        List<PerformanceDetailsListVO> voList = this.performanceDetailsService.listPerformanceDetails(query);
        return R.success(voList);
    }

    /**
     * 获取财报明细详细信息
     * @param query 财报明细查询参数
     * @return 财报明细视图模型
     */
    @ApiOperation("获取财报明细详细信息")
    @PreAuthorize("@ss.hasPermi('project:performanceDetails:query')")
    @GetMapping(value = "/pagePerformanceDetails")
    public R<PageInfo<PerformanceDetailsVO>> pagePerformanceDetails(PerformanceDetailsQuery query) {
        return R.success(performanceDetailsService.pagePerformanceDetails(query));
    }

    @ApiOperation("财报明细复选框合计")
    @PostMapping(value = "/checkBox")
    public R<FinancialReportAmountVO> checkBox(@RequestBody PerformanceDetailsQuery query){
        return R.success(performanceDetailsService.pagePerformanceDetailsCheckBox(query));
    }


    /**
     * 确认收入明细
     * @param query 项目ID
     * @return 确认收入明细
     */
    @ApiOperation("确认收入明细")
    @PreAuthorize("@ss.hasPermi('project:performanceDetails:incomeDetails')")
    @GetMapping(value = "/getPerformanceDetailsIncome")
    public R<List<PerformanceResourceVO>> getPerformanceDetailsIncome(PerformanceDetailsQuery query){
        return R.success(performanceDetailsService.getPerformanceDetailsIncome(query));
    }

    /**
     * 返点坏账明细
     * @param query 项目ID
     * @return 确认收入明细
     */
    @ApiOperation("返点坏账明细")
    @PreAuthorize("@ss.hasPermi('project:performanceDetails:rebateBadDebts')")
    @GetMapping(value = "/getPerformanceDetailsRebateBadDebts")
    public R<List<PerformanceRebateBadDebtsVO>> getPerformanceDetailsRebateBadDebts(PerformanceDetailsQuery query){
        return R.success(performanceDetailsService.getPerformanceDetailsRebateBadDebts(query));
    }

    /**
     * 收入坏账明细
     * @param query 查询条件
     * @return
     */
    @ApiOperation("收入坏账明细")
    @PreAuthorize("@ss.hasPermi('project:performanceDetails:query')")
    @PostMapping(value = "/listIncomeBadDebt")
    public R<List<PyFirstIncomeBadDeptInfoVO>> listIncomeBadDebt(@RequestBody PerformanceDetailsQuery query) {
        return R.success(performanceDetailsService.listIncomeBadDebt(query));
    }

    /**
     * 执行定时任务(业务财报)
     * @param year
     * @param month
     * @return
     */
    @ApiOperation("执行定时任务(业务财报)")
    @GetMapping(value = "/taskPerformance")
    public R<Boolean> taskPerformance(Integer year, Integer month) {

        if (year == null){
            throw new ServiceException("请先选择年份");
        }

        LocalDate localDate = LocalDate.of(year, month,1)
                .with(TemporalAdjusters.lastDayOfMonth())
                .plusDays(1);
        try {
            performanceDetailsService.addMonthFinancialReport(localDate);
        } catch(Exception e) {
            log.error("生成财报错误：",e);
        }
        return R.success();
    }

}
