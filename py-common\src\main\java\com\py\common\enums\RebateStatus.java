package com.py.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RebateStatus implements IDict<Integer> {

    /**
     * 未返点
     */
    NO_REBATE(1, "未返点"),
    /**
     * 部分返点
     */
    PARTIAL_REBATE(2, "部分返点"),
    /**
     * 已返点
     */
    REBATED(3, "已返点"),
    ;

    private final Integer value;

    private final String label;

    /**
     * 判断是否 已返点
     * @param unReturnedAmount
     * @param returnedAmount
     * @return
     */
    public static Boolean rebated(BigDecimal unReturnedAmount, BigDecimal returnedAmount) {
        // 已返金额不为0，且未返金额=0
        return (unReturnedAmount == null || unReturnedAmount.compareTo(BigDecimal.ZERO) == 0)
                && returnedAmount != null && returnedAmount.compareTo(BigDecimal.ZERO) > 0;
    }

}
