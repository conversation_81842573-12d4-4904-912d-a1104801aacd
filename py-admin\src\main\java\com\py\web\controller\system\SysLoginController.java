package com.py.web.controller.system;


import com.py.common.constant.Constants;
import com.py.common.core.domain.AjaxResult;
import com.py.common.core.domain.entity.SysDept;
import com.py.common.core.domain.model.LoginBody;
import com.py.common.core.domain.model.LoginUser;
import com.py.common.core.domain.vo.user.AuthUser;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.framework.web.service.SysLoginService;
import com.py.framework.web.service.SysPermissionService;
import com.py.system.dept.service.ISysDeptService;
import com.py.system.menu.domain.SysMenu;
import com.py.system.menu.service.ISysMenuService;
import com.py.system.user.button.service.ISysUserApprovalButtonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "登录验证")
@RestController
public class SysLoginController {

    /** 系统设置-登录服务 */
    @Resource
    private SysLoginService loginService;

    /** 系统设置-菜单管理服务 */
    @Resource
    private ISysMenuService menuService;

    /** 系统设置-部门管理服务 */
    @Resource
    private ISysDeptService deptService;

    /** 系统设置-权限管理服务 */
    @Resource
    private SysPermissionService permissionService;


    /** 用户下一条审批按钮服务层 */
    @Resource
    private ISysUserApprovalButtonService sysUserApprovalButtonService;

    /**
     * 登录方法
     * @param loginBody 登录信息
     * @return 结果
     */
    @ApiOperation("登录")
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody) {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(
                loginBody.getUsername(),
                loginBody.getPassword(),
                loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @ApiOperation("获取用户信息")
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Assert.notNull(loginUser, "获取用户信息失败, 请重新登录");
        AuthUser user = loginUser.getUser();
        // 设置部门全名
        List<SysDept> deptList = this.deptService.selectDeptByIdList(user.getFullDeptIdList());
        user.setFullDeptNameList(ListUtil.map(deptList, SysDept::getDeptName));

        //获取用户是否打开了自动获取下一条审批
       Boolean hasNextApprovalButton = sysUserApprovalButtonService.getUserNextApprovalButtonStatus(user.getUserId()).getNextApproval();

        //获取用户是否打开了根据查询条件自动获取下一条审批
        Boolean hasQueryNextApprovalButton = sysUserApprovalButtonService.getUserNextApprovalButtonStatus(user.getUserId()).getQueryApproval();

        // 是否自动关闭审批完成的页签
        Boolean hasAutoCloseFinishLabel = sysUserApprovalButtonService.getUserNextApprovalButtonStatus(user.getUserId()).getHasAutoCloseFinishLabel();
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("permissions", permissions);
        ajax.put("hasQueryNextApprovalButton", hasQueryNextApprovalButton);
        ajax.put("hasNextApprovalButton", hasNextApprovalButton);
        ajax.put("hasAutoCloseFinishLabel", hasAutoCloseFinishLabel);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @ApiOperation("获取路由信息")
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
