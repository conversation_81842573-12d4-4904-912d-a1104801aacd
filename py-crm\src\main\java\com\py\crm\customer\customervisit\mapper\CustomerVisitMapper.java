package com.py.crm.customer.customervisit.mapper;

import com.py.common.mybatisplus.SuperMapper;
import com.py.crm.customer.customervisit.domian.CustomerVisit;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface CustomerVisitMapper extends SuperMapper<CustomerVisit> {
    /***
     * 获取展示id
     * @param customerId 客户id
     * @return showId
     */
    Integer getShowId(@Param("customerId") Long customerId);
}
