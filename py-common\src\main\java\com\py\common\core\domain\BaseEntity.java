package com.py.common.core.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.core.domain.model.LoginUser;
import com.py.common.enums.DelFlag;
import com.py.common.utils.collection.ListUtil;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Entity基类
 * <AUTHOR>
 */
@Data
public class BaseEntity implements Serializable, IBaseCreateInfo {

    private static final long serialVersionUID = 1L;

    /** 创建者Id */
    @TableField(fill = FieldFill.INSERT)
    private Long createId;

    /** 创建者 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建部门 */
    @TableField(fill = FieldFill.INSERT)
    private String createDept;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /** 更新者Id */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateId;

    /** 更新者 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新部门 */
    @TableField(exist = false)
    private String updateDept;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /** 删除标志（0代表存在 1代表删除） */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private DelFlag delFlag;

    /** 请求参数 */
    @TableField(exist = false)
    private Map<String, Object> params;

    public Map<String, Object> getParams()
    {
        if (params == null)
        {
            params = ListUtil.emptyMap();
        }
        return params;
    }

    public void setCreateInfo(LoginUser loginUser) {
        this.setCreateTime(LocalDateTime.now());
        if(loginUser == null) {
            return;
        }
        this.setCreateId(loginUser.getUserId());
        this.setCreateBy(loginUser.getUsername());
        this.setCreateBy(loginUser.getDeptName());
    }

    public void setUpdateInfo(LoginUser loginUser) {
        this.setUpdateTime(LocalDateTime.now());
        if(loginUser == null) {
            return;
        }
        this.setUpdateId(loginUser.getUserId());
        this.setUpdateBy(loginUser.getUsername());
        this.setUpdateDept(loginUser.getDeptName());
    }
}
