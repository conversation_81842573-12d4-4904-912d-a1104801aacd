package com.py.crm.compareddraft.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.core.domain.vo.BaseUpdateInfoVO;
import com.py.common.tools.poiexcel.annotation.Excel;
import com.py.common.utils.DateUtils;
import com.py.common.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version ComparedDraftListVO 2023/8/9 18:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerDevoteComparedDraftExportModel extends BaseUpdateInfoVO {

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    @Excel(name = "客户名称")
    private String customerName;

    /**
     * 客户合作主体
     */
    @ApiModelProperty("客户合作主体")
    @Excel(name = "客户合作主体")
    private String customerCooperateName;

    /**
     * 品牌/业务线名
     */
    @ApiModelProperty("品牌/业务线名")
    @Excel(name = "品牌/业务线")
    private String brandName;

    /**
     * 比稿项目名称
     */
    @Excel(name = "比稿项目名称")
    @ApiModelProperty("比稿项目名称")
    private String comparedDraftName;


    /**
     * 商务标比稿结果(0失败，1成功)
     */
    @Excel(name = "商务标比稿结果",readConverterExp = "0=失败,1=成功")
    @ApiModelProperty("商务标比稿结果(0失败，1成功)")
    private Integer commercialBidFruits;

    /**
     * 技术标比稿结果(0失败，1成功)
     */
    @ApiModelProperty("技术标比稿结果(0失败，1成功)")
    @Excel(name = "技术标比稿结果",readConverterExp = "0=失败,1=成功")
    private Integer techniqueBidFruits;

    /**
     * 最终比稿结果(0失败，1成功)
     */
    @ApiModelProperty("最终比稿结果(0失败，1成功)")
    @Excel(name = "最终比稿结果",readConverterExp = "0=失败,1=成功")
    private Integer finalFruits;
    /**
     * 商务标比稿结果
     */
    @ApiModelProperty("商务标比稿结果")
    private String commercialBidFruitsStr;

    /**
     * 技术标比稿结果
     */
    @ApiModelProperty("技术标比稿结果")
    private String techniqueBidFruitsStr;

    /**
     * 最终比稿结果
     */
    @ApiModelProperty("最终比稿结果")
    private String finalFruitsStr;

    /**
     * 比稿金额(元)
     */
    @ApiModelProperty("比稿金额(元)")
    private BigDecimal comparedDraftMoney;
    @Excel(name = "比稿金额(元)")
    private String comparedDraftMoneyStr;

    /**
     * 成交金额(元)
     */
    @ApiModelProperty("成交金额(元)")
    private BigDecimal turnoverMoney;
    @Excel(name = "成交金额(元)")
    private String turnoverMoneyStr;

    /**
     * 派芽业务类型
     */
    @ApiModelProperty("派芽业务类型")

    private Integer pyType;
    @Excel(name = "派芽业务类型")
    private String pyTypeStr;

    /**
     * 方案类型
     */
    @ApiModelProperty("方案类型")

    private Integer planType;
    @Excel(name = "方案类型")
    private String planTypeStr;
    /** 比稿人 */
    @Excel(name = "比稿人")
    @ApiModelProperty("比稿人")
    private String updateBy;

    /** 策划人 */
    @ApiModelProperty("策划人")

    private List<String> plotterNameList;
    @Excel(name = "策划人")
    private String plotterName;

    /**
     * 比稿时间
     */
    @ApiModelProperty("比稿时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "比稿时间",dateFormat = DateUtils.YYYY_MM_DD)
    private LocalDate comparedDraftTime;

    /**
     * 录入时间
     */
    @ApiModelProperty("录入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "录入时间",dateFormat = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime enteringTime;

    /**
     * 比稿id
     */
    @ApiModelProperty("比稿id")
    private Long comparedDraftId;

    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    private Long customerId;

    /**
     * 客户合作主体ID
     */
    @ApiModelProperty("客户合作主体ID")
    private Long customerCooperateId;

    /** 策划人id */
    @ApiModelProperty("策划人id")
    private List<Long> plotterIdList;

    public String getCommercialBidFruitsStr() {
        if(commercialBidFruits == null){
            return null;
        }
        if(commercialBidFruits == 0){
            return "失败";
        }
        if(commercialBidFruits == 1){
            return "成功";
        }
        return null;
    }

    public String getTechniqueBidFruitsStr() {
        if(techniqueBidFruits == null){
            return null;
        }
        if(techniqueBidFruits == 0){
            return "失败";
        }
        if(techniqueBidFruits == 1){
            return "成功";
        }
        return null;
    }

    public String getFinalFruitsStr() {
        if(finalFruits == null){
            return null;
        }
        if(finalFruits == 0){
            return "失败";
        }
        if(finalFruits == 1){
            return "成功";
        }
        return null;
    }

    public String getComparedDraftTimeStr() {
        if(comparedDraftTime == null){
            return null;
        }
        return DateUtils.format(comparedDraftTime,"yyyy-MM-dd");
    }

    public String getEnteringTimeStr() {
        if(enteringTime == null){
            return null;
        }
        return DateUtils.format(enteringTime,"yyyy-MM-dd HH:mm:ss");
    }

    public String getComparedDraftMoneyStr() {
        if(comparedDraftMoney == null){
            return null;
        }
        return StringUtils.fmtMicrometer(comparedDraftMoney.toString());
    }

    public String getTurnoverMoneyStr() {
        if(turnoverMoney == null){
            return null;
        }
        return StringUtils.fmtMicrometer(turnoverMoney.toString());
    }


}
