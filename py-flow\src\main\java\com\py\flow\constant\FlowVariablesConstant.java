package com.py.flow.constant;

/**
 * 流程变量常量
 * <AUTHOR>
 */
public class FlowVariablesConstant {

    /** 金额 */
    public static final String MONEY = "money";

    /** 发起人id */
    public static final String LAUNCHER_ID = "launcherId";

    /** 发起人部门id */
    public static final String LAUNCHER_DEPT_ID = "launcherDeptId";

    /** 直属主管 */
    public static final String DIRECT_MANAGER = "directManager";

    /** 部门主管 */
    public static final String DEPT_MANAGER = "deptManager";

    /** 审批状态 */
    public static final String NODE_STATUS = "NodeStatus";

    /** 审批操作 */
    public static final String OPERATE = "Operate";

    /** 节点类型 */
    public static final String NODE_TYPE = "NodeType";

    /**
     * 节点转交记录记录
     * <p>记录该节点上发起过转交的人</p>
     */
    public static final String FORWARD_USER_SET = "forwardUserSet";

    /** 向后加签节点信息列表 */
    public static final String AFTER_SIGN_NODE_INFO = "afterSignNodeInfo";

    /** 跳过节点信息列表 */
    public static final String SKIP_NODE_INFO = "skipNodeInfo";
}
