package com.py.crm.customer.invoicing.converter;

import com.py.common.core.converter.BaseDomainModelConverter;
import com.py.crm.customer.invoicing.domain.Invoicing;
import com.py.crm.customer.invoicing.domain.dto.InvoicingDTO;
import com.py.crm.customer.invoicing.domain.dto.InvoicingExportModel;
import com.py.crm.customer.invoicing.domain.vo.InvoicingVO;
import com.py.crm.customer.invoicing.domain.vo.InvoicingListVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 客户管理-客户-开票信息模型转换器
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Mapper(componentModel = "spring")
public interface InvoicingConverter extends BaseDomainModelConverter<Invoicing, InvoicingVO, InvoicingDTO> {

    /**
     * 将Entity转换为ListVo
     * @param entity entity
     * @return listVo
     */
    InvoicingListVO toListVoByEntity(Invoicing entity);

    /**
     * 将Entity转换为ListVo
     * @param entity entity
     * @return listVo
     */
    List<InvoicingListVO> toListVoByEntity(List<Invoicing> entity);

    /**
     * 将Entity转换为导出模型
     * @param entityList entity列表
     * @return 导出模型列表
     */
    List<InvoicingExportModel> toExportModel(List<Invoicing> entityList);

    /**
     * 将导出模型转换为Entity
     * @param exportList 导出数据列表
     * @return entity列表
     */
    List<Invoicing> toEntityByExportModel(List<InvoicingExportModel> exportList);

}
