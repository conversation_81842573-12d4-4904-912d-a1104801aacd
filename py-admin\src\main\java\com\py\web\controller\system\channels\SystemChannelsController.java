package com.py.web.controller.system.channels;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.system.channels.domain.dto.ChannelsDeleteDTO;
import com.py.system.channels.domain.dto.SystemChannelsDTO;
import com.py.system.channels.domain.query.SystemChannelsQuery;
import com.py.system.channels.domain.vo.SystemChannelsListVO;
import com.py.system.channels.domain.vo.SystemChannelsVO;
import com.py.system.channels.service.ISystemChannelsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统设置-付款渠道Controller
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@Api(tags = "系统设置-付款渠道")
@RestController
@RequestMapping("/system/systemChannels")
public class SystemChannelsController extends BaseController {

    /** 系统设置-付款渠道服务 */
    @Resource
    private ISystemChannelsService systemChannelsService;

    /**
     * 查询系统设置-付款渠道列表
     *
     * @param query 系统设置-付款渠道查询参数
     * @return 系统设置-付款渠道列表
     */
    @ApiOperation("查询系统设置-付款渠道列表")
    @PreAuthorize("@ss.hasPermi('system:systemChannels:list')")
    @GetMapping("/listSystemChannels")
    public R<List<SystemChannelsListVO>> listSystemChannels(SystemChannelsQuery query) {
        List<SystemChannelsListVO> voList = this.systemChannelsService.listSystemChannels(query);
        return R.success(voList);
    }

    /**
     * 分页查询系统设置-付款渠道列表
     *
     * @param query 系统设置-付款渠道查询参数
     * @return 系统设置-付款渠道分页
     */
    @ApiOperation("分页查询询系统设置-付款渠道列表")
    @PreAuthorize("@ss.hasPermi('system:systemChannels:list')")
    @GetMapping("/pageSystemChannels")
    public R<PageInfo<SystemChannelsListVO>> pageSystemChannels(SystemChannelsQuery query) {
        PageInfo<SystemChannelsListVO> voList = this.systemChannelsService.pageSystemChannelsList(query);
        return R.success(voList);
    }

    /**
     * 获取系统设置-付款渠道详细信息
     * @param id 系统设置-付款渠道主键
     * @return 系统设置-付款渠道视图模型
     */
    @ApiOperation("获取系统设置-付款渠道详细信息")
    @PreAuthorize("@ss.hasPermi('system:systemChannels:query')")
    @GetMapping(value = "/{id}")
    public R<SystemChannelsVO> getInfo(@PathVariable("id") Long id) {
        return R.success(systemChannelsService.selectSystemChannelsById(id));
    }

    /**
     * 新增系统设置-付款渠道
     *
     * @param dto 系统设置-付款渠道修改参数
     * @return 是否成功
     */
    @ApiOperation("新增系统设置-付款渠道")
    @PreAuthorize("@ss.hasPermi('system:systemChannels:add')")
    @Log(title = "系统设置-付款渠道", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SystemChannelsDTO dto) {
        return R.success(systemChannelsService.insertSystemChannels(dto));
    }

    /**
     * 修改系统设置-付款渠道
     *
     * @param dto 系统设置-付款渠道修改参数
     * @return 是否成功
     */
    @ApiOperation("修改系统设置-付款渠道")
    @PreAuthorize("@ss.hasPermi('system:systemChannels:edit')")
    @Log(title = "系统设置-付款渠道", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SystemChannelsDTO dto) {
        return R.success(systemChannelsService.updateSystemChannels(dto));
    }

    /**
     * 删除系统设置-付款渠道
     * @param dto 需要删除的系统设置-付款渠道主键集合
     * @return 是否成功
     */
    @ApiOperation("删除系统设置-付款渠道" )
    @PreAuthorize("@ss.hasPermi('system:systemChannels:remove')")
    @Log(title = "系统设置-付款渠道", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public R<Boolean> remove(@RequestBody ChannelsDeleteDTO dto) {
        return R.success(systemChannelsService.deleteSystemChannelsByIds(dto.getChannelsIdList()));
    }


    /**
     * 查询系统设置-付款渠道列表字体添加列表
     *
     * @param query 系统设置-付款渠道查询参数
     * @return 系统设置-付款渠道列表
     */
    @ApiOperation("查询系统设置-付款渠道列表字体添加列表")
    @PostMapping("/listSystemChannelsAutoAdd")
    public R<List<SystemChannelsListVO>> listSystemChannelsAutoAdd(@RequestBody SystemChannelsQuery query) {
        List<SystemChannelsListVO> voList = this.systemChannelsService.listSystemChannelsAutoAdd(query);
        return R.success(voList);
    }

}
