package com.py.crm.customer.customervisit.converter;

import com.py.common.core.converter.BaseDomainModelConverter;
import com.py.crm.customer.customervisit.domian.CustomerVisit;
import com.py.crm.customer.customervisit.domian.dto.CustomerVisitDTO;
import com.py.crm.customer.customervisit.domian.dto.CustomerVisitExportModel;
import com.py.crm.customer.customervisit.domian.vo.CustomerVisitVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface CustomerVisitConverter extends BaseDomainModelConverter<CustomerVisit, CustomerVisitVO, CustomerVisitDTO> {

    /**
     * Entity转Export
     * @param list Entity
     * @return Export
     */
    List<CustomerVisitExportModel> toExportModel(List<CustomerVisit> list);
}
