package com.py.flow.flowinstance.approvalsnapshot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.py.common.constant.SqlConstant;
import com.py.common.exception.ServiceException;
import com.py.common.utils.JsonUtil;
import com.py.common.utils.collection.ListUtil;
import com.py.flow.domain.enums.ApprovalBizType;
import com.py.flow.flowinstance.approvalsnapshot.IApprovalBizInfoHandler;
import com.py.flow.flowinstance.approvalsnapshot.domain.ApprovalBizInfoSnapshot;
import com.py.flow.flowinstance.approvalsnapshot.mapper.ApprovalSnapshotMapper;
import com.py.flow.flowinstance.approvalsnapshot.service.IApprovalSnapshotService;
import com.py.flow.flowinstance.domain.FlowInstance;
import com.py.flow.flowinstance.domain.vo.ApprovalFlowChartVO;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.Map;

/**
 * 审批快照服务实现类
 * <AUTHOR>
 */
@Service
public class ApprovalSnapshotServiceImpl
        extends ServiceImpl<ApprovalSnapshotMapper, ApprovalBizInfoSnapshot>
        implements IApprovalSnapshotService{

    /** 审批业务详情服务映射 */
    private final Map<ApprovalBizType, IApprovalBizInfoHandler> approvalBizInfoServiceMap;

    public ApprovalSnapshotServiceImpl(Collection<IApprovalBizInfoHandler> bizInfoServiceCollection) {
        this.approvalBizInfoServiceMap = ListUtil.toMap(bizInfoServiceCollection, IApprovalBizInfoHandler::getSupport);
    }

    /**
     * 获取审批业务信息
     * @param flowInstance 需要详情的审批流程实例
     * @return 审批业务的详情VO
     */
    @Override
    public Object getApprovalBizInfo(FlowInstance flowInstance) {
        Assert.notNull(flowInstance, "审批流程实例不能为空");

        if(flowInstance.getApprovalStatus().needSnapshotStatus()){
            ApprovalBizInfoSnapshot snapshot = this.selectSnapshot(flowInstance);
            if(snapshot == null){
                throw new ServiceException("审批快照不存在");
            }
            return snapshot.getInfo();
        }
        return this.getNowApprovalBizInfo(flowInstance.getBizId(), flowInstance.getBizType());
    }

    /**
     * 创建审批快照
     * @param flowInstance 需要创建快照的审批流程实例
     */
    @Override
    public void createBizSnapshot(FlowInstance flowInstance) {

        // 新增快照前, 先删除既有的快照
        LambdaQueryWrapper<ApprovalBizInfoSnapshot> removeWrapper = Wrappers.lambdaQuery(ApprovalBizInfoSnapshot.class)
                .eq(ApprovalBizInfoSnapshot::getBizId, flowInstance.getBizId())
                .eq(ApprovalBizInfoSnapshot::getBizType, flowInstance.getBizType());
        this.remove(removeWrapper);

        // 生成新快照
        Object approvalBizInfo = this.getNowApprovalBizInfo(flowInstance.getBizId(), flowInstance.getBizType());

        ApprovalBizInfoSnapshot snapshot = new ApprovalBizInfoSnapshot();
        snapshot.setFlowInstanceId(flowInstance.getFlowInstanceId());
        snapshot.setBizType(flowInstance.getBizType());
        snapshot.setBizId(flowInstance.getBizId());
        snapshot.setInfo(JsonUtil.obj2String(approvalBizInfo));
        this.save(snapshot);
    }

    /**
     * 创建审批快照流程图
     * @param approvalFlowChartVO 需要创建快照的审批流程实例
     */
    public Boolean createChartSnapshot(ApprovalFlowChartVO approvalFlowChartVO) {
        Long flowInstanceId = approvalFlowChartVO.getFlowInstanceId();
        // 新增快照前, 先删除既有的快照
        LambdaQueryWrapper<ApprovalBizInfoSnapshot> removeWrapper = Wrappers.lambdaQuery(ApprovalBizInfoSnapshot.class)
                .eq(ApprovalBizInfoSnapshot::getBizId, flowInstanceId);
        this.remove(removeWrapper);

        ApprovalBizInfoSnapshot snapshot = new ApprovalBizInfoSnapshot();
        snapshot.setFlowInstanceId(flowInstanceId);
        snapshot.setBizId(flowInstanceId);
        snapshot.setInfo(JsonUtil.obj2String(approvalFlowChartVO));
        return this.save(snapshot);
    }

    /**
     * 获取审批业务信息
     * @param flowInstanceId 需要详情的审批流程实例id
     * @return 审批业务的详情VO
     */
    @Override
    public String getSnapshot(Long flowInstanceId) {
        LambdaQueryWrapper<ApprovalBizInfoSnapshot> queryWrapper = Wrappers.lambdaQuery(ApprovalBizInfoSnapshot.class)
                .eq(ApprovalBizInfoSnapshot::getFlowInstanceId, flowInstanceId)
                .eq(ApprovalBizInfoSnapshot::getBizId, flowInstanceId)
                .last(SqlConstant.LimitOne);
        ApprovalBizInfoSnapshot infoSnapshot = this.getOne(queryWrapper);
        if(infoSnapshot != null){
            return infoSnapshot.getInfo();
        }
        return null;
    }

    /**
     * 获取当前审批业务信息
     * @param bizId 业务ID
     * @param bizType 业务类型
     * @return 审批业务的详情VO
     */
    private Object getNowApprovalBizInfo(Long bizId, ApprovalBizType bizType) {
        IApprovalBizInfoHandler approvalBizInfoService = this.approvalBizInfoServiceMap.get(bizType);
        if(approvalBizInfoService == null){
            throw new ServiceException("审批业务类型 %s 对应的审批详情服务未实现", bizType.getLabel());
        }
        return approvalBizInfoService.getApprovalBizInfo(bizId);
    }

    /**
     * 获取审批快照
     * @param flowInstance 审批流程实例ID
     * @return 审批快照
     */
    private ApprovalBizInfoSnapshot selectSnapshot(FlowInstance flowInstance) {
        LambdaQueryWrapper<ApprovalBizInfoSnapshot> queryWrapper = Wrappers.lambdaQuery(ApprovalBizInfoSnapshot.class)
                .eq(ApprovalBizInfoSnapshot::getBizId, flowInstance.getBizId())
                .eq(ApprovalBizInfoSnapshot::getBizType, flowInstance.getBizType())
                .last(SqlConstant.LimitOne);
        return this.getOne(queryWrapper);
    }
}
