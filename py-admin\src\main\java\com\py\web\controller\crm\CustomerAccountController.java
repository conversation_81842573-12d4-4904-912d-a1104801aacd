package com.py.web.controller.crm;

import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.crm.customer.customeraccount.domain.vo.CustomerAccountVO;
import com.py.crm.customer.customeraccount.domain.vo.CustomerAccountListVO;
import com.py.crm.customer.customeraccount.domain.query.CustomerAccountQuery;
import com.py.crm.customer.customeraccount.domain.dto.CustomerAccountDTO;
import com.py.crm.customer.customeraccount.domain.dto.CustomerAccountExportModel;
import com.py.crm.customer.customeraccount.service.ICustomerAccountService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户管理-客户-账户信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Api(tags = "客户管理-客户-账户信息")
@RestController
@RequestMapping("/crm/customerAccount")
public class CustomerAccountController extends BaseController {

    /** 客户管理-客户-账户信息服务 */
    @Resource
    private ICustomerAccountService customerAccountService;

    /**
     * 分页查询客户管理-客户-账户信息列表
     *
     * @param query 客户管理-客户-账户信息查询参数
     * @return 客户管理-客户-账户信息分页
     */
    @ApiOperation("分页查询询客户管理-客户-账户信息列表")
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAccount:list')")
    @GetMapping("/pageCustomerAccount")
    public R<PageInfo<CustomerAccountListVO>> pageCustomerAccount(CustomerAccountQuery query) {
        PageInfo<CustomerAccountListVO> voList = this.customerAccountService.pageCustomerAccountList(query);
        return R.success(voList);
    }

    /**
     * 获取客户管理-客户-账户信息详细信息
     * @param id 客户管理-客户-账户信息主键
     * @return 客户管理-客户-账户信息视图模型
     */
    @ApiOperation("获取客户管理-客户-账户信息详细信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAccount:query')")
    @GetMapping(value = "/{id}")
    public R<List<CustomerAccountVO>> getInfo(@PathVariable("id") Long id) {
        return R.success(customerAccountService.selectCustomerAccountDetailById(id));
    }

    /**
     * 新增客户管理-客户-账户信息
     *
     * @param dto 客户管理-客户-账户信息修改参数
     * @return 是否成功
     */
    @ApiOperation("新增客户管理-客户-账户信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAccount:add')")
    @Log(title = "客户管理-客户-账户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody CustomerAccountDTO dto) {
        return R.success(customerAccountService.insertCustomerAccount(dto));
    }

    /**
     * 修改客户管理-客户-账户信息
     *
     * @param dto 客户管理-客户-账户信息修改参数
     * @return 是否成功
     */
    @ApiOperation("修改客户管理-客户-账户信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAccount:edit')")
    @Log(title = "客户管理-客户-账户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody CustomerAccountDTO dto) {
        return R.success(customerAccountService.updateCustomerAccount(dto));
    }

    /**
     * 删除客户管理-客户-账户信息
     * @param ids 需要删除的客户管理-客户-账户信息主键集合
     * @return 是否成功
     */
    @ApiOperation("删除客户管理-客户-账户信息" )
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAccount:remove')")
    @Log(title = "客户管理-客户-账户信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(customerAccountService.deleteCustomerAccountByIds(ids));
    }

    /**
     * 导出客户管理-客户-账户信息
     * @param response 请求响应
     * @param query 导出查询参数
     */
    @ApiOperation("导出客户管理-客户-账户信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAccount:export')")
    @Log(title = "客户管理-客户-账户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CustomerAccountQuery query) {
        List<CustomerAccountExportModel> exportList = this.customerAccountService.exportCustomerAccount(query);

        ExcelUtil<CustomerAccountExportModel> util = new ExcelUtil<>(CustomerAccountExportModel. class);
        util.exportExcel(response, exportList, "客户管理-客户-账户信息数据" );
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAccount:import')" )
    @Log(title = "客户管理-客户-账户信息" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<CustomerAccountExportModel> util = new ExcelUtil<>(CustomerAccountExportModel.class);
        List<CustomerAccountExportModel> customerAccountList = util.importExcel(file.getInputStream());
        String message = this.customerAccountService.importCustomerAccount(customerAccountList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAccount:import')" )
    @Log(title = "客户管理-客户-账户信息" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<CustomerAccountExportModel> util = new ExcelUtil<>(CustomerAccountExportModel.class);
        util.importTemplateExcel(response, "客户管理-客户-账户信息数据" );
    }

}
