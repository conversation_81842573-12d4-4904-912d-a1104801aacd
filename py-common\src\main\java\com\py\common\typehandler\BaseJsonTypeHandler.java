package com.py.common.typehandler;

import com.py.common.utils.JsonUtil;
import com.py.common.utils.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 基础Json类型处理器
 * <p>实现将指定类型出入库自动json转换的功能</p>
 * <AUTHOR>
 */
public abstract class BaseJsonTypeHandler<T> extends BaseTypeHandler<T> {

    @Override
    public void setNonNullParameter(
            PreparedStatement preparedStatement,
            int i,
            T obj,
            JdbcType jdbcType) throws SQLException {
        preparedStatement.setString(i, JsonUtil.obj2String(obj));
    }

    @Override
    public T getNullableResult(ResultSet resultSet, String columnName) throws SQLException {
        return this.deserializeField(resultSet.getString(columnName));
    }

    @Override
    public T getNullableResult(ResultSet resultSet, int columnIndex) throws SQLException {
        return this.deserializeField(resultSet.getString(columnIndex));
    }

    @Override
    public T getNullableResult(CallableStatement callableStatement, int columnIndex) throws SQLException {
        return this.deserializeField(callableStatement.getString(columnIndex));
    }

    /**
     * 元素类型
     * @return 元素类型
     */
    protected abstract Class<T> getItemClass();

    /**
     * 反序列化接口字段
     * @param fieldJson 接口字段JSON
     * @return 接口字段对象
     */
    private T deserializeField(String fieldJson) {
        if(StringUtils.isBlank(fieldJson)) {
            return null;
        }
        return JsonUtil.string2Obj(fieldJson, this.getItemClass());
    }
}
