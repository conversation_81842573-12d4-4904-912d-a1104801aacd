package com.py.crm.customerdevote.domain.query;

import com.py.common.core.domain.model.LoginUser;
import com.py.common.datascope.IDataScopeArgs;
import com.py.common.enums.CustomerDevoteMenuTypeStatus;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTaskArgs;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户贡献管理查询模型
 * <AUTHOR>
 * @version CustomerDevoteQuery 2023/8/9 11:23
 */
@Data
public class CustomerDevoteQuery implements IDataScopeArgs, ReusableAsyncTaskArgs {

    /** 品牌/业务线*/
    @ApiModelProperty("品牌/业务线")
    private String lineBusiness;

    /** 客户名称*/
    @ApiModelProperty("客户名称")
    private String name;

    /** 行业类目id*/
    @ApiModelProperty("行业类目id")
    private List<String> industryCategoryIdList;
    private String industryCategoryString;

    /** 结案开始时间 */
    @ApiModelProperty("结案开始时间")
    private String closeCaseBeginTime;
    private String closeCaseBeginDate;

    /** 结案结束时间 */
    @ApiModelProperty("结案结束时间")
    private String closeCaseEndTime;
    private String closeCaseEndDate;

    /** 合作开始时间 */
    @ApiModelProperty("合作开始时间")
    private String cooperateBeginTime;
    private String cooperateBeginDate;

    /** 合作开始时间 */
    @ApiModelProperty("合作结束时间")
    private String cooperateEndTime;
    private String cooperateEndDate;

    @ApiModelProperty("选中客户目id")
    private List<Long> customerIds;
    @ApiModelProperty("选中客户目id")
    private List<Long> notCustomerIds;
    @ApiModelProperty("选中或取消的项目id")
    private List<Long> projectIds;

    /** 客户贡献-按钮菜单类型：1-比稿；2-项目；3-结案；4-坏账 */
    @ApiModelProperty("客户贡献-按钮菜单类型：1-比稿；2-项目；3-结案；4-坏账")
    private CustomerDevoteMenuTypeStatus menuType;

    /** 客户id */
    private Long customerId;

    /**
     * 项目状态
     */
    private Integer projectStatus;
    /** 是否全选 */
    @ApiModelProperty("是否全选")
    private Boolean isSelectAll;
    @ApiModelProperty("选中或取消的比稿id")
    private List<Long> comparedDraftIds;

    /** 数据权限 */
    @ApiModelProperty(hidden = true)
    private String dataScopeSql;

    /** 任务id */
    private Long taskId;

    /** 文件名 */
    private String fileName;

    /** 文件名 */
    private String fileKey;

    /** 用户信息
     *  */
    private LoginUser loginUser;

    private Integer pageSize;
    private Integer pageNum;
}
