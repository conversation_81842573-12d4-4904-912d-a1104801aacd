package com.py.flow.flowdatauser.converter;

import com.py.common.core.converter.BaseDomainModelConverter;
import com.py.flow.flowdatauser.domain.FlowDataUser;
import com.py.flow.flowdatauser.domain.dto.FlowDataUserDTO;
import com.py.flow.flowdatauser.domain.vo.FlowDataUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 审批数据与人员关联模型转换器
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
@Mapper(componentModel = "spring")
public interface FlowDataUserConverter extends BaseDomainModelConverter<FlowDataUser, FlowDataUserVO, FlowDataUserDTO> {

    /**
     * 深拷贝-忽略创建人,创建时间等基础数据
     * @param flowDataUserA 需深拷贝的数据
     * @return 拷贝结果
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createId", ignore = true)
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "createDept", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateId", ignore = true)
    @Mapping(target = "updateBy", ignore = true)
    @Mapping(target = "updateDept", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "updateInfo", ignore = true)
    @Mapping(target = "createInfo", ignore = true)
    FlowDataUser deepCopyIgBaseData(FlowDataUser flowDataUserA);
}
