package com.py.flow.domain.query;

import com.py.common.core.domain.model.LoginUser;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTaskArgs;
import com.py.flow.domain.dto.flow.BatchPrintFlowApprovalParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量打印审批查询对象
 *
 * <AUTHOR>
 */
@ApiModel("批量打印审批查询对象")
@Data
public class BatchPrintFlowApprovalQuery implements ReusableAsyncTaskArgs {

    /** 审批中心tab类型 */
    @ApiModelProperty(value = "批量打印流程参数列表", required = true)
    @NotEmpty(message = "批量打印流程参数列表不能为空")
    private List<BatchPrintFlowApprovalParam> printFlowApprovalDTOS;

    /** 任务ID*/
    private Long taskId;

    /** 下载文件名 */
    private String fileName;

    /** 登录用户 */
    private LoginUser loginUser;

}
