package com.py.web.controller.crm;

import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.crm.customer.contact.domain.vo.ContactListVO;
import com.py.crm.customer.contact.domain.query.ContactQuery;
import com.py.crm.customer.contact.domain.dto.ContactDTO;
import com.py.crm.customer.contact.domain.dto.ContactExportModel;
import com.py.crm.customer.contact.domain.vo.ContactVO;
import com.py.crm.customer.contact.service.IContactService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户管理-客户-企业联系人Controller
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Api(tags = "客户管理-客户-企业联系人")
@RestController
@RequestMapping("/com.py.web/contact")
public class ContactController extends BaseController {

    /** 客户管理-客户-企业联系人服务 */
    @Resource
    private IContactService contactService;

    /**
     * 分页查询客户管理-客户-企业联系人列表
     *
     * @param query 客户管理-客户-企业联系人查询参数
     * @return 客户管理-客户-企业联系人分页
     */
    @ApiOperation("分页查询询客户管理-客户-企业联系人列表")
    @PreAuthorize("@ss.hasPermi('com.py.web:contact:list')")
    @GetMapping("/pageContact")
    public R<PageInfo<ContactListVO>> pageContact(ContactQuery query) {
        PageInfo<ContactListVO> voList = this.contactService.pageContactList(query);
        return R.success(voList);
    }

    /**
     * 获取客户管理-客户-企业联系人详细信息
     * @param id 客户管理-客户-企业联系人主键
     * @return 客户管理-客户-企业联系人视图模型
     */
    @ApiOperation("获取客户管理-客户-企业联系人详细信息")
    @PreAuthorize("@ss.hasPermi('com.py.web:contact:query')")
    @GetMapping(value = "/{id}")
    public R<List<ContactVO>> getInfo(@PathVariable("id") Long id) {
        return R.success(contactService.selectContactDetailById(id));
    }

    /**
     * 新增客户管理-客户-企业联系人
     *
     * @param dto 客户管理-客户-企业联系人修改参数
     * @return 是否成功
     */
    @ApiOperation("新增客户管理-客户-企业联系人")
    @PreAuthorize("@ss.hasPermi('com.py.web:contact:add')")
    @Log(title = "客户管理-客户-企业联系人", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody ContactDTO dto) {
        return R.success(contactService.insertContact(dto));
    }

    /**
     * 修改客户管理-客户-企业联系人
     *
     * @param dto 客户管理-客户-企业联系人修改参数
     * @return 是否成功
     */
    @ApiOperation("修改客户管理-客户-企业联系人")
    @PreAuthorize("@ss.hasPermi('com.py.web:contact:edit')")
    @Log(title = "客户管理-客户-企业联系人", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody ContactDTO dto) {
        return R.success(contactService.updateContact(dto));
    }

    /**
     * 删除客户管理-客户-企业联系人
     * @param ids 需要删除的客户管理-客户-企业联系人主键集合
     * @return 是否成功
     */
    @ApiOperation("删除客户管理-客户-企业联系人" )
    @PreAuthorize("@ss.hasPermi('com.py.web:contact:remove')")
    @Log(title = "客户管理-客户-企业联系人", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(contactService.deleteContactByIds(ids));
    }

    /**
     * 导出客户管理-客户-企业联系人
     * @param response 请求响应
     * @param query 导出查询参数
     */
    @ApiOperation("导出客户管理-客户-企业联系人")
    @PreAuthorize("@ss.hasPermi('com.py.web:contact:export')")
    @Log(title = "客户管理-客户-企业联系人", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContactQuery query) {
        List<ContactExportModel> exportList = this.contactService.exportContact(query);

        ExcelUtil<ContactExportModel> util = new ExcelUtil<>(ContactExportModel. class);
        util.exportExcel(response, exportList, "客户管理-客户-企业联系人数据" );
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('com.py.web:contact:import')" )
    @Log(title = "客户管理-客户-企业联系人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<ContactExportModel> util = new ExcelUtil<>(ContactExportModel.class);
        List<ContactExportModel> contactList = util.importExcel(file.getInputStream());
        String message = this.contactService.importContact(contactList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('com.py.web:contact:import')" )
    @Log(title = "客户管理-客户-企业联系人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ContactExportModel> util = new ExcelUtil<>(ContactExportModel.class);
        util.importTemplateExcel(response, "客户管理-客户-企业联系人数据" );
    }

}
