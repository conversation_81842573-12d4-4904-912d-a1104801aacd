package com.py.crm.customer.customercontribute.domain;

import com.py.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户管理-比稿策划人关联对象
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@TableName("py_crm_compared_draft_user" )
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("客户管理-比稿策划人关联" )
public class ComparedDraftUser extends BaseEntity {
private static final long serialVersionUID=1L;

    /** 自增主键*/
    @ApiModelProperty("自增主键")
    private Long id;

    /** 关联id*/
    @ApiModelProperty("关联id")
    @TableId
    private Long relevanceId;

    /** 比稿id*/
    @ApiModelProperty("比稿id")
    private Long comparedDraftId;

    /** 策划人id*/
    @ApiModelProperty("策划人id")
    private Long userId;

    /** 策划人名称*/
    @ApiModelProperty("策划人名称")
    private String userName;

}
