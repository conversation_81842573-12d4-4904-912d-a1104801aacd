package com.py.crm.customer.wanderabout.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.py.common.mybatisplus.SuperServiceImpl;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.customer.wanderabout.converter.WanderAboutConverter;
import com.py.crm.customer.wanderabout.domain.WanderAbout;
import com.py.crm.customer.wanderabout.domain.dto.WanderAboutDTO;
import com.py.crm.customer.wanderabout.domain.vo.WanderAboutVO;
import com.py.crm.customer.wanderabout.mapper.WanderAboutMapper;
import com.py.crm.customer.wanderabout.service.IWanderAboutService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 客户管理-客户流转Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-16
 */
@Service
public class WanderAboutServiceImpl
        extends SuperServiceImpl<WanderAboutMapper, WanderAbout>
        implements IWanderAboutService{

    /** 客户管理-客户流转模型转换器 */
    @Resource
    private WanderAboutConverter wanderAboutConverter;

    /** 客户管理-客户流转Mapper接口 */
    @Resource
    private WanderAboutMapper wanderAboutMapper;

    /**
     * 查询客户管理-客户流转
     * @param id 客户管理-客户流转主键
     * @return 客户管理-客户流转视图模型
     */
    @Override
    public List<Long> selectWanderAboutById(Long id) {
        List<WanderAbout> wanderAboutList = this.list(Wrappers.<WanderAbout>lambdaQuery()
                .select(WanderAbout::getUserId)
                .eq(WanderAbout::getIsDistribution,true)
                .eq(WanderAbout::getCustomerId, id));
        return ListUtil.distinctMap(wanderAboutList,WanderAbout::getUserId);
    }

    /**
     * 新增客户管理-客户流转
     * @param dto 客户管理-客户流转修改参数
     * @return 是否成功
     */
    @Override
    public boolean insertWanderAbout(WanderAboutDTO dto) {
        WanderAbout wanderAbout = this.wanderAboutConverter.toEntityByDto(dto);
        return this.save(wanderAbout);
    }

    /**
     * 批量新增客户管理-客户流转
     * @param dtoList 客户管理-客户流转修改参数
     * @return 是否成功
     */
    @Override
    public boolean insertBatchWanderAbout(List<WanderAboutDTO> dtoList) {
        if(ListUtil.isEmpty(dtoList)){
            return true;
        }
        List<Long> customerIdList = ListUtil.distinctMap(dtoList,WanderAboutDTO::getCustomerId);
        this.update(Wrappers.<WanderAbout>lambdaUpdate()
                .set(WanderAbout::getIsDistribution,false)
                .set(WanderAbout::getIsCreate,false)
                .in(WanderAbout::getCustomerId, customerIdList));

        List<WanderAbout> wanderAboutList = this.wanderAboutConverter.toEntityByDto(dtoList);
        return this.saveBatch(wanderAboutList);
    }

    /**
     * 根据客户ID查询被分配的人员
     * @param customerIdList 客户id
     * @return 人员id
     */
    @Override
    public List<WanderAbout> listWanderAboutByCustomerId(List<Long> customerIdList) {
        List<WanderAbout> wanderAboutList = this.list(Wrappers.<WanderAbout>lambdaQuery()
                .select(WanderAbout::getUserId,WanderAbout::getCustomerId,WanderAbout::getIsDistribution)
                .in(WanderAbout::getCustomerId, customerIdList));
        return wanderAboutList;
    }

}
