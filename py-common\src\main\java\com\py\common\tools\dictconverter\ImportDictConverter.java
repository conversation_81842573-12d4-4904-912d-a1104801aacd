package com.py.common.tools.dictconverter;

import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ReflectUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableList;
import com.py.common.core.domain.entity.SysDictData;
import com.py.common.core.domain.service.IDictService;
import com.py.common.tools.dictconverter.model.ImportDictMapArgs;
import com.py.common.tools.poiexcel.annotation.Excel;
import com.py.common.tools.verify.domain.ISerialNumber;
import com.py.common.tools.verify.domain.RowVerifyError;
import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 导入字典转换器
 * <AUTHOR>
 */
@Log4j2
@SuppressWarnings("unused")
@Component
public class ImportDictConverter {

    /** 字典服务 */
    @Resource
    private IDictService dictService;

    /** 导入字典映射参数缓存 */
    private final Cache<Class<?>, ImmutableList<ImportDictMapArgs>> dictMapArgsCache = CacheBuilder.newBuilder()
            .expireAfterAccess(30, TimeUnit.MINUTES)
            .build();

    /**
     * 尝试转换导入数据中的字典值
     * <p>通过反射获取数据类型字段上的Excel注解</p>
     * <p>根据注解中配置的dictType解析对应的字典</p>
     * @param importModelList 转换的导入数据
     * @param clazz 转换的导入数据类型
     * @return 转换中产生的错误消息
     */
    public <T extends ISerialNumber> List<RowVerifyError<T>> tryConvertDictValue(List<T> importModelList, Class<T> clazz){
        if(ListUtil.isEmpty(importModelList)) {
            return ListUtil.emptyList();
        }

        ImmutableList<ImportDictMapArgs> dictMapArgsList = this.getDictMapArgs(clazz);
        Map<String, List<SysDictData>> availableDictMap = dictMapArgsList.stream().map(ImportDictMapArgs::getDictName)
                .distinct()
                .flatMap(dictName -> this.dictService.selectDictDataByType(dictName).stream())
                .collect(Collectors.groupingBy(SysDictData::getDictType));

        return importModelList.stream()
                .map(item -> this.tryConvertDictValue(item, dictMapArgsList, availableDictMap))
                .filter(RowVerifyError::hasError)
                .collect(Collectors.toList());
    }

    /**
     * 尝试转换导入数据中的字典值
     * @param importModel 转换的导入数据
     * @param dictMapArgsList 字典映射参数列表
     * @param availableDictMap 可用字典Map (字典英文名, 下属字典值列表)
     * @return 转换中产生的错误消息
     */
    private  <T extends ISerialNumber> RowVerifyError<T> tryConvertDictValue(T importModel, ImmutableList<ImportDictMapArgs> dictMapArgsList, Map<String, List<SysDictData>> availableDictMap) {
        RowVerifyError<T> verifyError = new RowVerifyError<>(importModel.getSerialNumber(), importModel);

        for(ImportDictMapArgs mapArgs : dictMapArgsList) {
            List<SysDictData> dictList = availableDictMap.get(mapArgs.getDictName());
            if(ListUtil.isEmpty(dictList)) {
                throw new IllegalArgumentException(String.format("导入配置错误, 字典 %s 没有配置对应的可用值" , mapArgs.getDictName()));
            }

            Object fieldValue = ReflectUtil.getFieldValue(importModel, mapArgs.getField());
            if(fieldValue instanceof String == false) {
                throw new IllegalArgumentException(String.format("导入配置错误, 字典属性必须为字符串, 错误字段: %s" , mapArgs.getField().getName()));
            }

            String dictValue = (String) fieldValue;
            if(StringUtils.isBlank(dictValue)) {
                continue;
            }

            // 关联值为列表时
            if(mapArgs.isDictList() != false) {
                List<String> dictValueList = StringUtils.convertStringList(dictValue,";");

                List<String> resultDictList = dictValueList.stream().map(value -> {
                    SysDictData matchedDict = ListUtil.first(dictList, dict -> dict.getDictLabel().equals(value));
                    return NullMergeUtils.nullMerge(matchedDict, SysDictData::getDictValue);
                }).collect(Collectors.toList());

                if(resultDictList.stream().anyMatch(Objects::isNull)) {
                    verifyError.addErrorMsg("[%s]字典值非法, 字典值为 %s" , mapArgs.getFieldName(),dictList.stream().map(SysDictData::getDictLabel).collect(Collectors.joining(";")) );
                    continue;
                }

                ReflectUtil.setFieldValue(importModel, mapArgs.getField(), StringUtils.convertStringList(resultDictList));
                // 不是列表时
            } else {
                SysDictData matchedDict = ListUtil.first(dictList, dict -> dict.getDictLabel().equals(dictValue));
                if(matchedDict == null) {
                    verifyError.addErrorMsg("[%s]字典值非法, 字典值为 %s" ,dictList.stream().map(SysDictData::getDictLabel).collect(Collectors.joining(";")));
                    continue;
                }
                ReflectUtil.setFieldValue(importModel, mapArgs.getField(), matchedDict.getDictValue());
            }
        }

        return verifyError;
    }

    /**
     * 反射获取字典映射参数
     * @param clazz 需映射的类型
     * @return 字典映射参数列表
     */
    private ImmutableList<ImportDictMapArgs> getDictMapArgs(Class<?> clazz) {
        try {
            return this.dictMapArgsCache.get(clazz, () ->
                    Arrays.stream(ClassUtil.getDeclaredFields(clazz))
                            .filter(field -> field.isAnnotationPresent(Excel.class))
                            .map(field -> {
                                Excel excel = field.getAnnotation(Excel.class);
                                if(StringUtils.isBlank(excel.dictType())) {
                                    return null;
                                }

                                return new ImportDictMapArgs(excel, field);
                            }).filter(Objects::nonNull)
                            .collect(ImmutableList.toImmutableList()));
        } catch(ExecutionException e) {
            log.error("缓存初始化失败: " , e);
            throw new RuntimeException(e.getCause());
        }
    }
}
