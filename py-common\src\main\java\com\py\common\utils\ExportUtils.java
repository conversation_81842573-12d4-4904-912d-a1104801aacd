
package com.py.common.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.py.common.exception.ServiceException;
import com.py.common.tools.excelhelper.functions.SameDataRowVerifyErrorGenerator;
import com.py.common.tools.verify.domain.ISerialNumber;
import com.py.common.tools.verify.domain.RowVerifyError;
import com.py.common.utils.collection.ListUtil;
import lombok.NonNull;
import lombok.var;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.multipart.MultipartFile;
import rx.functions.Action2;
import rx.functions.Func2;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 导出工具类
 * <AUTHOR>
 */
public class ExportUtils {

    /**
     * 设置导出的序列化
     * @param exportModelList 导出模型列表
     * @param serialNumberSetter 序列号设置器
     * @param startNo 起始序列化
     * @param <T> 需要设置序列化的列表元素类型
     */
    public static <T> void setSerialNumber(List<T> exportModelList, Action2<T, Integer> serialNumberSetter, int startNo) {
        if(ListUtil.isEmpty(exportModelList)) {
            return;
        }

        int index = startNo;
        for(T item : exportModelList) {
            serialNumberSetter.call(item, index++);
        }
    }

    /**
     * 设置导出的序列化, 从 1 开始
     * @param exportModelList 导出模型列表
     * @param serialNumberSetter 序列号设置器
     * @param <T> 需要设置序列化的列表元素类型
     */
    public static <T> void setSerialNumber(List<T> exportModelList, Action2<T, Integer> serialNumberSetter) {
        setSerialNumber(exportModelList, serialNumberSetter, 1);
    }

    /**
     * 设置导出的序列化
     * @param exportModelList 导出模型列表
     * @param startNo 起始序列化
     * @param <T> 需要设置序列化的列表元素类型
     */
    public static <T extends ISerialNumber> void setSerialNumber(List<T> exportModelList, int startNo) {
        setSerialNumber(exportModelList, ISerialNumber::setSerialNumber, startNo);
    }

    /**
     * 设置导出的序列化, 从 1 开始
     * @param exportModelList 导出模型列表
     * @param <T> 需要设置序列化的列表元素类型
     */
    public static <T extends ISerialNumber> void setSerialNumber(List<T> exportModelList) {
        setSerialNumber(exportModelList, ISerialNumber::setSerialNumber, 1);
    }

    /**
     * 导入表格文件格式校验
     * @param importExcelFile 导入的表格文件
     */
    public static void importExcelSchemeVerify(MultipartFile importExcelFile) {
        if(importExcelFile == null
                || importExcelFile.isEmpty()
                || StringUtils.isBlank(importExcelFile.getOriginalFilename())) {
            throw new ServiceException("上传文件不能为空");
        }

        String fileSuffix = "." + FileUtil.getSuffix(importExcelFile.getOriginalFilename().trim());
        if("xlsx".equals(fileSuffix) == false
        && "xlsm".equals(fileSuffix) == false
        && "xls".equals(fileSuffix) == false) {
            throw new ServiceException("文件类型错误: 请上传 .xlsx 或 .xls 文件");
        }
    }

    /**
     * 去除数据列表中错误数据的项
     * @param dataList 数据列表
     * @param rowVerifyErrorList 行验证错误列表
     * @param <T> 数据类型
     * @return 去除错误数据后的数据列表
     */
    public static <T extends ISerialNumber> List<T> removeErrorData(List<T> dataList, List<RowVerifyError<T>> rowVerifyErrorList) {
        if(ListUtil.isEmpty(dataList)){
            return ListUtil.emptyList();
        }
        if(ListUtil.isEmpty(rowVerifyErrorList)){
            return dataList;
        }

        Set<Integer> errorRowSet = ListUtil.toSet(rowVerifyErrorList, RowVerifyError::getRowNo);
        return dataList.stream()
                .filter(item -> errorRowSet.contains(item.getSerialNumber()) == false)
                .collect(Collectors.toList());
    }

    /** 错误信息版 */
    public static class ErrorMsg{

        /**
         * 验证是否存在重复项目
         * <p>重复的空元素也被认为是重复项目</p>
         * @param list 需要验证重复项的列表
         * @param sameVerifyItemSelector 重复项选择器
         * @param serialNumberSelector 导出元素序列化选择器
         * @param errorMsgBuilder 错误信息生成器
         * @param <T> 导入元素类型
         * @param <TSameVerifyItem> 重复项目类型
         * @return 重复内容错误消息, 没有错误内容返回null
         */
        public static <T, TSameVerifyItem> String verifySameItem(
                List<T> list,
                @NonNull Function<T, TSameVerifyItem> sameVerifyItemSelector,
                @NonNull Function<T, Integer> serialNumberSelector,
                @NonNull Func2<String, String, String> errorMsgBuilder) {
            if(ListUtil.isEmpty(list)) {
                return null;
            }

            var sameItemList = ListUtil.sameItemList(list, sameVerifyItemSelector);
            if(ListUtil.isEmpty(sameItemList)) {
                return null;
            }
            return generateSameErrorMsg(sameItemList, serialNumberSelector, errorMsgBuilder);
        }

        /**
         * 验证是否存在重复项目
         * <p>重复的空元素也被认为是重复项目</p>
         * @param list 需要验证重复项的列表
         * @param sameVerifyItemSelector 重复项选择器
         * @param errorMsgBuilder 错误信息生成器
         * @param <T> 导入元素类型
         * @param <TSameVerifyItem> 重复项目类型
         * @return 重复内容错误消息, 没有错误内容返回null
         */
        public static <T extends ISerialNumber, TSameVerifyItem> String verifySameItem(
                List<T> list,
                @NonNull Function<T, TSameVerifyItem> sameVerifyItemSelector,
                @NonNull Func2<String, String, String> errorMsgBuilder) {
            return verifySameItem(list, sameVerifyItemSelector, ISerialNumber::getSerialNumber, errorMsgBuilder);
        }

        /**
         * 生成重复内容错误消息
         * @param sameItemList 重复项目列表
         * @param serialNumberSelector 序列号选择器
         * @param errorMsgBuilder 错误信息生成器
         * @param <TSameItem> 重复项目类型
         * @param <T> 导入元素类型
         * @return 重复内容错误消息
         */
        public static <TSameItem, T> String generateSameErrorMsg(
                List<Map.Entry<TSameItem, List<T>>> sameItemList,
                Function<T, Integer> serialNumberSelector,
                Func2<String, String, String> errorMsgBuilder) {
            StringJoiner joiner = new StringJoiner(StringUtils.NEW_LINE);
            for(Map.Entry<TSameItem, List<T>> item : sameItemList) {
                List<String> sameItemSerialNumberList = ListUtil.map(item.getValue(), x -> serialNumberSelector.apply(x).toString());
                String errorMsg = errorMsgBuilder.call(String.join(",", sameItemSerialNumberList), item.getKey().toString());
                joiner.add(errorMsg);
            }
            return joiner.toString();
        }

        /**
         * 生成重复内容错误消息
         * @param sameItemList 重复项目列表
         * @param errorMsgBuilder 错误信息生成器
         * @param <TSameItem> 重复项目类型
         * @param <T> 导入元素类型
         * @return 重复内容错误消息
         */
        public static <TSameItem, T extends ISerialNumber> String generateSameErrorMsg(
                List<Map.Entry<TSameItem, List<T>>> sameItemList,
                Func2<String, String, String> errorMsgBuilder) {
            return generateSameErrorMsg(sameItemList, ISerialNumber::getSerialNumber, errorMsgBuilder);
        }

    }



    /** 行错误版 */
    public static class RowError{
        /**
         * 验证是否存在重复项目
         * <p>重复的空元素也被认为是重复项目</p>
         * @param list 需要验证重复项的列表
         * @param sameVerifyItemSelector 重复项选择器
         * @param errorGenerator 错误信息生成器
         * @param <T> 导入元素类型
         * @param <TSameVerifyItem> 重复项目类型
         * @return 重复内容错误消息, 没有错误内容返回空集合
         */
        public static <T extends ISerialNumber, TSameVerifyItem> List<RowVerifyError<T>> verifySameItem(
                List<T> list,
                @NonNull Function<T, TSameVerifyItem> sameVerifyItemSelector,
                @NonNull SameDataRowVerifyErrorGenerator<T> errorGenerator) {
            return verifySameItem(list, sameVerifyItemSelector, ISerialNumber::getSerialNumber, errorGenerator);
        }

        /**
         * 验证是否存在重复项目
         * <p>重复的空元素也被认为是重复项目</p>
         * @param list 需要验证重复项的列表
         * @param sameVerifyItemSelector 重复项选择器
         * @param serialNumberSelector 导出元素序列化选择器
         * @param errorGenerator 错误信息生成器
         * @param <T> 导入元素类型
         * @param <TSameVerifyItem> 重复项目类型
         * @return 重复内容错误消息, 没有错误内容返回空集合
         */
        public static <T, TSameVerifyItem> List<RowVerifyError<T>> verifySameItem(
                List<T> list,
                @NonNull Function<T, TSameVerifyItem> sameVerifyItemSelector,
                @NonNull Function<T, Integer> serialNumberSelector,
                @NonNull SameDataRowVerifyErrorGenerator<T> errorGenerator) {
            if(ListUtil.isEmpty(list)) {
                return null;
            }

            // 忽略空数据
            list = list.stream()
                    .filter(Objects::nonNull)
                    .filter(x -> {
                        TSameVerifyItem sameVerifyItem = sameVerifyItemSelector.apply(x);
                        if(sameVerifyItem == null){
                            return false;
                        }else if(sameVerifyItem instanceof Pair<?,?>){
                            Pair<?,?> pair = (Pair<?,?>)sameVerifyItem;
                            return pair.getKey() != null && pair.getValue() != null;
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            var sameItemList = ListUtil.sameItemList(list, sameVerifyItemSelector);
            if(ListUtil.isEmpty(sameItemList)) {
                return null;
            }
            return generateSameErrorMsg(sameItemList, serialNumberSelector, errorGenerator);
        }

        /**
         * 生成重复内容错误消息
         * @param sameItemList 重复项目列表
         * @param serialNumberSelector 序列号选择器
         * @param errorGenerator 错误信息生成器
         * @param <TSameItem> 重复项目类型
         * @param <T> 导入元素类型
         * @return 重复内容错误消息
         */
        public static <TSameItem, T> List<RowVerifyError<T>> generateSameErrorMsg(
                List<Map.Entry<TSameItem, List<T>>> sameItemList,
                Function<T, Integer> serialNumberSelector,
                @NonNull SameDataRowVerifyErrorGenerator<T> errorGenerator) {

            return sameItemList.stream().flatMap(sameEntry -> {
                List<T> sameList = sameEntry.getValue();

                List<String> sameSerialNumberList = ListUtil.map(sameList, item -> String.valueOf(serialNumberSelector.apply(item)));
                String sameRowsStr = StringUtils.convertStringList(sameSerialNumberList);
                return sameList.stream().map(sameItem -> errorGenerator.create(serialNumberSelector.apply(sameItem), sameItem,sameRowsStr));
            }).collect(Collectors.toList());
        }

        /**
         * 生成重复内容错误消息
         * @param sameItemList 重复项目列表
         * @param errorGenerator 错误信息生成器
         * @param <TSameItem> 重复项目类型
         * @param <T> 导入元素类型
         * @return 重复内容错误消息
         */
        public static <TSameItem, T extends ISerialNumber> List<RowVerifyError<T>> generateSameErrorMsg(
                List<Map.Entry<TSameItem, List<T>>> sameItemList,
                @NonNull SameDataRowVerifyErrorGenerator<T> errorGenerator) {
            return generateSameErrorMsg(sameItemList, ISerialNumber::getSerialNumber, errorGenerator);
        }
    }

    /**
     * 输入是否为合法的手机号
     * @param phoneNumber 手机号
     * @return true: 是合法的手机号
     */
    public static boolean isPhoneNumber(String phoneNumber) {
        return RegexUtils.PHONE_REGEX.asPredicate().test(phoneNumber);
    }

    /**
     * 将导入文件流读取读取为
     * @param importFile 导入文件
     * @return 读取出的字节流
     */
    public static byte[] readImportByteData(MultipartFile importFile) throws IOException {
        try(InputStream fileStream = importFile.getInputStream();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()
        ) {
            IoUtil.copy(fileStream, byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        }
    }
}
