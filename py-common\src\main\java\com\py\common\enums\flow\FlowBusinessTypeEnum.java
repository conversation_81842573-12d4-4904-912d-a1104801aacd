package com.py.common.enums.flow;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @Description 业务类型（0：非业务；1：业务）
 * @Date 2023/7/27 10:05
 */
@AllArgsConstructor
public enum FlowBusinessTypeEnum implements IDict<Integer> {

    /**
     * 非业务
     */
    NON_BUSINESS(0,"非业务"),

    /**
     * 业务
     */
    BUSINESS(1,"业务");

    private final Integer value;

    private final String label;

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public Integer getValue() {
        return value;
    }
}
