package com.py.web.controller.crm;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.exception.ServiceException;
import com.py.common.oss.IOssService;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.connection.domain.dto.ConnectionCirculationDTO;
import com.py.crm.connection.domain.dto.ConnectionDTO;
import com.py.crm.connection.domain.dto.ConnectionEnableDTO;
import com.py.crm.connection.domain.dto.ConnectionImportModel;
import com.py.crm.connection.domain.query.ConnectionBrandQuery;
import com.py.crm.connection.domain.query.ConnectionImportQuery;
import com.py.crm.connection.domain.query.ConnectionQuery;
import com.py.crm.connection.domain.vo.ConnectionCount;
import com.py.crm.connection.domain.vo.ConnectionListVO;
import com.py.crm.connection.domain.vo.ConnectionVO;
import com.py.crm.connection.service.IConnectionService;
import com.py.crm.connection.service.impl.ConnectionServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

/**
 * 人脉管理表Controller
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Api(tags = "人脉管理表")
@RestController
@RequestMapping("/connection")
public class ConnectionController extends BaseController {

    /** 人脉管理表服务 */
    @Resource
    private IConnectionService connectionService;

    @Resource
    private IOssService ossService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 查询人脉管理表列表
     *
     * @param query 人脉管理表查询参数
     * @return 人脉管理表列表
     */
    @ApiOperation("查询人脉管理表列表")
    @PreAuthorize("@ss.hasPermi('crm:connection:list')")
    @GetMapping("/listConnection")
    public R<List<ConnectionListVO>> listConnection(ConnectionQuery query) {
        List<ConnectionListVO> voList = this.connectionService.listConnection(query);
        return R.success(voList);
    }

    /**
     * 查询人脉管理表列表有效无效数量
     *
     * @return 查询人脉管理表列表有效无效数量
     */
    @ApiOperation("查询人脉管理表列表有效无效数量")
    @PreAuthorize("@ss.hasPermi('crm:connection:list')")
    @GetMapping("/connectionCount")
    public R<ConnectionCount> connectionCount() {
        return R.success(this.connectionService.connectionCount());
    }

    /**
     * 分页查询人脉管理表列表
     *
     * @param query 人脉管理表查询参数
     * @return 人脉管理表分页
     */
    @ApiOperation("分页查询询人脉管理表列表")
    @PreAuthorize("@ss.hasPermi('crm:connection:list')")
    @GetMapping("/pageConnection")
    public R<PageInfo<ConnectionListVO>> pageConnection(ConnectionQuery query) {
        PageInfo<ConnectionListVO> voList = this.connectionService.pageConnectionList(query);
        return R.success(voList);
    }


    /**
     * 人脉管理表列表,模糊查询录入人部门
     *
     * @param query 人脉管理表查询参数(录入人部门)
     * @return 人脉管理表列表-录入人部门
     */
    @ApiOperation("人脉管理表列表-录入人部门查询")
    @PostMapping("/findConnectionCreateDeptList")
    public R<List<String>> findConnectionCreateDeptList(@RequestBody ConnectionQuery query) {
        List<String> voList = this.connectionService.findConnectionCreateDeptList(query);
        return R.success(voList);
    }

    /**
     * 人脉管理表列表,模糊查询服务人员部门
     *
     * @param query 人脉管理表查询参数(服务人员部门)
     * @return 人脉管理表列表-服务人员部门
     */
    @ApiOperation("人脉管理表列表-服务人员部门查询")
    @PostMapping("/findConnectionServiceUserDeptList")
    public  R<List<String>> findConnectionServiceUserDeptList(@RequestBody ConnectionQuery query) {
        List<String> voList = this.connectionService.findConnectionServiceUserDeptList(query);
        return R.success(voList);
    }

    /**
     * 获取人脉管理表详细信息
     * @param id 人脉管理表主键
     * @return 人脉管理表视图模型
     */
    @ApiOperation("获取人脉管理表详细信息")
    @PreAuthorize("@ss.hasPermi('crm:connection:query')")
    @GetMapping(value = "/{id}")
    public R<ConnectionVO> getInfo(@PathVariable("id") Long id) {
        return R.success(connectionService.selectConnectionById(id));
    }

    /**
     * 新增人脉管理表
     *
     * @param dto 人脉管理表修改参数
     * @return 是否成功
     */
    @ApiOperation("新增人脉管理表")
    @PreAuthorize("@ss.hasPermi('crm:connection:add')")
    @Log(title = "人脉管理表", businessType = BusinessType.INSERT)
    @PostMapping("/addConnection")
    public R<Long> add(@RequestBody @Validated ConnectionDTO dto) {
        return R.success(connectionService.insertConnection(dto));
    }

    /**
     * 新增人脉管理表（再次发起审批）
     *
     * @param dto 人脉管理表修改参数
     * @return 是否成功
     */
    @ApiOperation("新增人脉管理表")
    @PreAuthorize("@ss.hasPermi('crm:connection:add')")
    @Log(title = "人脉管理表", businessType = BusinessType.INSERT)
    @PostMapping("/auditEdit")
    public R<Long> auditEdit(@RequestBody @Validated ConnectionDTO dto) {
        dto.setIsApprovalEdit(true);
        return R.success(connectionService.insertConnection(dto));
    }

    /**
     * 修改人脉管理表
     *
     * @param dto 人脉管理表修改参数
     * @return 是否成功
     */
    @ApiOperation("修改人脉管理表")
    @PreAuthorize("@ss.hasPermi('crm:connection:edit')")
    @Log(title = "人脉管理表", businessType = BusinessType.UPDATE)
    @PutMapping("/updateConnection")
    public R<Boolean> edit(@RequestBody ConnectionDTO dto) {
        return R.success(connectionService.updateConnection(dto, true));
    }

    /**
     * 删除人脉管理表
     * @param ids 需要删除的人脉管理表主键集合
     * @return 是否成功
     */
    @ApiOperation("删除人脉管理表" )
    @PreAuthorize("@ss.hasPermi('crm:connection:remove')")
    @Log(title = "人脉管理表", businessType = BusinessType.DELETE)
    @DeleteMapping("/deleteConnection/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(connectionService.deleteConnectionByIds(ids));
    }

    /**
     * 禁用启用人脉管理表
     * @param dto 需要禁用启用人脉管理表的数据
     * @return 是否成功
     */
    @ApiOperation("禁用启用人脉管理表")
    @PreAuthorize("@ss.hasPermi('crm:connection:enable')")
    @Log(title = "人脉管理表", businessType = BusinessType.UPDATE)
    @PostMapping("/enableConnection")
    public R<Boolean> enableConnection(@RequestBody ConnectionEnableDTO dto) {
        return R.success(connectionService.enableConnection(dto));
    }

    /**
     * 人脉分配
     * @param dto 需要人脉流转的数据
     * @return 是否成功
     */
    @ApiOperation("人脉分配")
    @PreAuthorize("@ss.hasPermi('crm:connection:circulation')")
    @Log(title = "人脉管理表", businessType = BusinessType.UPDATE)
    @PostMapping("/exchangeConnection")
    public R<Boolean> exchangeConnection(@Valid @RequestBody ConnectionCirculationDTO dto) {
        return R.success(connectionService.allocationServiceUserIds(dto));
    }

    /**
     * 批量分配-获取导入模板
     * @param response 请求响应
     */
    @ApiOperation("批量分配-获取导入模板")
    @PreAuthorize("@ss.hasPermi('crm:connection:import')" )
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_connection.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("人脉批量分配模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 分配 - 批量导入
     * @param file 批量导入返点登记导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @ApiOperation("分配 - 批量导入")
    @PreAuthorize("@ss.hasPermi('crm:connection:import')" )
    @Log(title = "人脉管理" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }
        ExcelUtil<ConnectionImportModel> util = new ExcelUtil<>(ConnectionImportModel.class);
        List<ConnectionImportModel> projectRebateList = util.importExcel(file.getInputStream());

        if(ListUtil.isEmpty(projectRebateList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }

        ConnectionImportQuery query = new ConnectionImportQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(), false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        reusableAsyncTaskService.addTask("人脉管理-批量分配", TaskType.Import, query, ConnectionServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }


    /**
     * 根据企业名称获取品牌/业务线列表
     * @param query 查询条件
     * @return 品牌/业务线列表
     */
    @ApiOperation("获取人脉管理表详细信息")
    @GetMapping(value = "/listResponsibleBrand")
    public R<List<String>> listResponsibleBrand(ConnectionBrandQuery query) {
        return R.success(connectionService.listResponsibleBrand(query));
    }
}
