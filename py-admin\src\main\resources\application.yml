# 项目相关配置
ruoyi:
  # 名称
  name: py
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  dempynabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /home/<USER>/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math
  # 是否忽略菜单权限判断
  ignorePermissionVerify: true
  # 启用文件上传/下载日志
  uploadAndDownloadLogEnabled: false
  # 启用Multi-tenant
  multiTenantEnable: false
  # 新增时是否填充更新信息
  fill-update-info-on-insert: true

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8083
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100
  compression:
    # 是否启用压缩
    enabled: true
    # 压缩类型
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    # 压缩最小尺寸
    min-response-size: 2048

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  20MB
       # 设置总上传的文件大小
       max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
#  redis:
#    # 地址
#    host: **********
#    # 端口，默认为6379
#    port: 6379
#    # 数据库索引
#    database: 2
#    # 密码
#    password: bysoft
#    # 连接超时时间
#    timeout: 10s
#    lettuce:
#      pool:
#        # 连接池中的最小空闲连接
#        min-idle: 0
#        # 连接池中的最大空闲连接
#        max-idle: 8
#        # 连接池的最大数据库连接数
#        max-active: 8
#        # #连接池最大阻塞等待时间（使用负值表示没有限制）
#        max-wait: -1ms
  mail:
    host: smtp.163.com
    port: 25
    # 邮箱地址
    username: <EMAIL>
    # 163邮箱pop3/smtp服务的授权码
    password: DQCFWHRLOVVATCED
    properties:
      mail:
        #  调试时这里可以用true，可以看到发送邮箱的整个流程调试信息
        debug: true
        smtp:
          ssl:
            trust: smtp.163.com
          auth: true
          starttls:
            # 这里用true
            enable: true
            # 这里用true
            required: true

# token配置
token:
    # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认60分钟）当前为: 1天
  web-user-expireTime: 1440
  # 是否允许账户多终端同时登录（true允许 false不允许）
  soloLogin: true
  # 验证码有效时间(秒): 300秒 == 5分钟
  verify-code-expireTime: 300

# MyBatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.py.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml

# MyBatis-plus配置
mybatis-plus:
  typeEnumsPackage: com.py.common.enums;com.py.common.enums.*;com.py.*.domain.enums;com.py.*.*.domain.enums
  global-config:
    db-config:
      logic-delete-field: delFlag # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: ID_WORKER
      #字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: IGNORED

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

oss:
  # 访问键
  accessKey: LTAI5tAa2mLDVxUmSZ4qjcL6
  # 访问密钥
  accessKeySecret: ******************************
  # 存储桶名
  bucketName: odc-test
  # 访问点
  endpoint: oss-cn-hangzhou.aliyuncs.com
  # 临时凭证访问点
  stsEndpoint: sts.aliyuncs.com
  # 临时凭证角色名称
  roleArn: acs:ram::1453943521041258:role/aliyunossstsrole
  # 角色会话名
  roleSessionName: py-knowledge-platform

sms:
  product: Dysmsapi
  domain: dysmsapi.aliyuncs.com
  region-id: cn-hangzhou
  sms-sign-name: 阿里云短信测试专用
  access-key-id: LTAI5tDrsRpwkEGdCryzYY8Y
  access-key-secret: ******************************
  key: code
  code: SMS_226310197

knife4j:
  # 开启增强配置
  enable: true

# 多租户配置
tenant:
  enable: true
  ignoreTable: sys_menu,sys_config,sys_dict_data,sys_dict_type,sys_role_data_rule,gen_table,columns,tables,gen_table_column,sys_role_menu,sys_logininfor,sys_tenant,sys_user,sys_user_post,sys_role_dept,sys_role_menu,sys_role,sys_dept,sys_user_role,sys_job,sys_job_log,sys_oper_log

#登录类型 1:名称登录 2.手机号登录 3.账号/手机号登录
login:
  type: 1

# 钉钉配置
ding-talk:
  # 钉钉开发者后台应用的appKey和appSecret
  appKey: dingy6sgxjrsbhdswpvg
  appSecret: 9P7UuoThxomxklfdgK-8fku_Z3NA4j_bQVr4oUmT2IZUPS4afmoHeLKP6vO6vPKY
  enableDingTalkNotice: true

# 异步任务线程池配置
thread-pool:
#  核心池大小
  corePoolSize: 2
#  最大池大小
  maxPoolSize: 10
#  BlockingQueue 的容量
  queueCapacity: 100
#  保持活动秒数 可以在运行时修改
  keepAliveSeconds: 300

  #socket.io 配置
socketio:
  port: 8088
  # 设置最大每帧处理数据的长度，防止他人利用大数据来攻击服务器
  maxFramePayloadLength: 1048576
  # 设置 http 交互最大内容长度
  maxHttpContentLength: 1048576
  # socket连接数大小（如只监听一个端口 boss 线程组为 1 即可）
  bossCount: 5
  workCount: 1000
  allowCustomRequests: true
  # 协议升级超时时间（毫秒），默认 10 秒。HTTP握手升级为 ws 协议超时时间
  upgradeTimeout: 20000
  # Ping 消息超时时间（毫秒），默认 60 秒，这个时间间隔内没有接收到心跳消息就会发送超时事件
  pingTimeout: 60000
  # Ping 消息间隔（毫秒），默认 25 秒。客户端向服务器发送一条心跳消息间隔
  pingInterval: 25000


