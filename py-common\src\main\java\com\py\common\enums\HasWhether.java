package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 是否枚举
 * <AUTHOR>
 */
@AllArgsConstructor
public enum HasWhether implements IDict<Integer>{

    /**
     * 是
     */
    TRUE(1,"是"),
    /**
     * 否
     */
    FALSE(2,"否");

    private final Integer value;
    private final String label;

    @Override
    public String getLabel() {
        return this.label;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
