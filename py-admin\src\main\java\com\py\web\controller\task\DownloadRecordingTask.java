package com.py.web.controller.task;

import com.py.resources.download.service.IDownloadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version DownloadRecordingTask 2023/7/20 17:50
 */
@Slf4j
@Component
@EnableScheduling
public class DownloadRecordingTask implements ApplicationRunner {


    /** 媒介资源管理-资源下载记录服务 */
    @Resource
    private IDownloadService downloadService;

    /**
     * Callback used to run the bean.
     * @param args incoming application arguments
     * @throws Exception on error
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
//        this.removeRecording();
    }

    @Scheduled(cron = "0 0 1 * * ?")
    public void removeRecording(){
        log.info("定时删除资源下载记录：记录当前开始时间：" + LocalDateTime.now());
        downloadService.deleteDownloadByIds();
        log.info("定时删除资源下载记录：记录当前结束时间：" + LocalDateTime.now());
    }
}
