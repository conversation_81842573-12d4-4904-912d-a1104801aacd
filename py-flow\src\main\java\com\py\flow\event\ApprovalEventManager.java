package com.py.flow.event;

import com.py.common.utils.collection.ListUtil;
import com.py.flow.domain.enums.ApprovalBizType;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;

/**
 * 审批事件处理器
 * <AUTHOR>
 */
@Log4j2
@Component
public class ApprovalEventManager {

    /**
     * 审批事件处理程序Map
     */
    private final Map<ApprovalBizType, IApprovalEventHandler> eventHandlerMap;

    public ApprovalEventManager(Collection<IApprovalEventHandler> eventHandlers) {
        this.eventHandlerMap = ListUtil.toMap(eventHandlers, IApprovalEventHandler::getSupport);
    }

    /**
     * 推送审批记录状态变更事件
     * @param event 事件
     */
    public void publishEvent(FlowApprovalStatusChangeEvent event) {
        IApprovalEventHandler eventHandler = this.eventHandlerMap.get(event.getApprovalType());
        if (eventHandler == null) {
            log.error("审批业务类型{}没有对应的事件处理程序", event.getApprovalType());
            return;
        }

        eventHandler.handle(event.getBizId(), event.getApprovalStatus());
    }
}
