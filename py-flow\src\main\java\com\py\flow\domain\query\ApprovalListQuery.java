package com.py.flow.domain.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.py.common.core.domain.query.BaseCreateListInfo;
import com.py.common.enums.flow.FlowApprovalStatus;
import com.py.common.utils.DateUtils;
import com.py.common.utils.EnumUtils;
import com.py.flow.domain.enums.ApprovalKind;
import com.py.flow.domain.enums.ApprovalTabType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 审批列表查询对象
 * <AUTHOR>
 * @date 2023/7/31 9:39
 */
@ApiModel("审批列表查询对象")
@Data
public class ApprovalListQuery extends BaseCreateListInfo {

    /** 审批中心tab类型 */
    @ApiModelProperty(value = "审批中心tab类型", required = true)
    @NotNull(message = "审批中心tab类型不能为空")
    private ApprovalTabType approvalTabType;

    /** 业务类型 */
    @ApiModelProperty(value = "业务类型", required = true)
//    @NotNull(message = "业务类型不能为空")
    private ApprovalKind businessType;

    /** 项目名称 */
    @ApiModelProperty("项目名称")
    private String projectName;

    /** 发起人 */
    @ApiModelProperty("发起人")
    private List<String> launcher;

    /** 发起人部门 */
    @ApiModelProperty("发起人部门")
    private List<String> launcherDept;

    /** 发起时间-开始查询时间 */
    @ApiModelProperty("发起时间-开始查询时间")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate startLaunchTime;
    private LocalDateTime launchStartTime;

    /** 发起时间-结束查询时间 */
    @ApiModelProperty("发起时间-结束查询时间")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate endLaunchTime;
    private LocalDateTime launchEndTime;

    /** 审批名称 */
    @ApiModelProperty("审批名称")
    private String processName;

    /** 业务类型列表 */
    @ApiModelProperty("业务类型列表")
    private List<String> bizTypeList;

    /** 审批状态 */
    @ApiModelProperty("审批状态")
    private List<FlowApprovalStatus> approvalStatusList;

    /** 审批时间-开始查询时间 */
    @ApiModelProperty("审批时间-开始查询时间")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime approvalStartTime;

    /** 审批时间-结束查询时间 */
    @ApiModelProperty("审批时间-结束查询时间")
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private LocalDateTime approvalEndTime;

    @ApiModelProperty("排序顺序(0.发起时间倒序 1.发起时间正序 2.审批时间倒序 3.审批时间正序)")
    private Integer orderBy;

    public void setApprovalTabType(Integer approvalTabType) {
        this.approvalTabType = EnumUtils.toEnumNullable(approvalTabType, ApprovalTabType.class);
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = EnumUtils.toEnumNullable(businessType, ApprovalKind.class);
    }

    public void setApprovalStatusList(List<Integer> approvalStatusList) {
        this.approvalStatusList = EnumUtils.toEnumListNullable(approvalStatusList, FlowApprovalStatus.class);
    }


    /** 流程实例ID */
    @ApiModelProperty("流程实例ID")
    private Long flowInstanceId;

    /** 审批时候是否根据查询条件跳下一条,true条/false不跳  */
    @ApiModelProperty("审批时候是否根据查询条件跳下一条,true条/false不跳")
    private Boolean queryApproval = false;

    /** 发起人部门 */
    @ApiModelProperty("发起人部门")
    private String deptName;

    /** 流程实例id */
    @ApiModelProperty("流程实例id")
    private List<String> processInstanceIdList;

    /** 表别名 */
    @ApiModelProperty("表别名")
    private String tableAlias;

    /** 第一条新版审批ID */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Long firstNewVersionFlowInstanceId;


    /**
     * 审批完结开始时间(流程结束开始时间)
     */
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate approvalTimeStart;
    private LocalDateTime approvalTimeStartStr;

    /**
     * 审批完结结束时间(流程结束,结束时间)
     */
    @DateTimeFormat(pattern = DateUtils.YYYY_MM_DD)
    private LocalDate approvalTimeEnd;
    private LocalDateTime approvalTimeEndStr;

    /**
     * 业务审批,派芽主体名称
     */
    @ApiModelProperty("业务审批,派芽主体名称")
    private List<String> paiyaMainstayNameList;

    /**
     * 非业务主体查询条件
     */
    @ApiModelProperty("非业务主体查询条件")
    private List<String> nonBusinessMains;
}
