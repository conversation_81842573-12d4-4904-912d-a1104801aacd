<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.py.crm.customer.customerlaundry.mapper.CustomerLaundryMapper">


    <resultMap type="com.py.crm.customer.customerlaundry.domain.vo.CustomerLaundryListVO" id="CrmCustomerResult">
        <result property="id" column="id" />
        <result property="customerId" column="customer_id" />
        <result property="name" column="name" />
        <result property="lineBusiness" column="line_business" />
        <result property="cooperationStatus" column="cooperation_status" />
<!--        <result property="changeStatusReason" column="change_status_reason" />-->
<!--        <result property="customerSource" column="customer_source" />-->
<!--        <result property="customerType" column="customer_type" />-->
<!--        <result property="brandStage" column="brand_stage" />-->
<!--        <result property="cooperativeSector" column="cooperative_sector" />-->
<!--        <result property="businessSource" column="business_source" />-->
<!--        <result property="serviceProduct" column="service_product"-->
<!--                typeHandler="com.py.common.typehandler.impl.StringSetTypeHandler" />-->
<!--        <result property="biddingStrategy" column="bidding_strategy" />-->
<!--        <result property="decisionLink" column="decision_link" />-->
<!--        <result property="remark" column="remark" />-->
<!--        <result property="version" column="version" />-->
<!--        <result property="auditStatus" column="audit_status" />-->
<!--        <result property="auditTime" column="audit_time" />-->
<!--        <result property="serviceUserId" column="service_user_id" />-->
        <result property="createId" column="create_id" />
        <result property="createBy" column="create_by" />
        <result property="createDept" column="create_dept" />
        <result property="createTime" column="create_time" />
        <result property="updateId" column="update_id" />
        <result property="updateBy" column="update_by" />
        <result property="updateDept" column="update_dept" />
        <result property="updateTime" column="update_time" />
<!--        <result property="delFlag" column="del_flag" />-->
    </resultMap>

    <!--  页客户管理-查看清单列表  -->
    <select id="pageCustomerLaundryList"
            resultMap="CrmCustomerResult">
        SELECT
        customer.id, customer.customer_id, customer.name, customer.line_business, customer.industry_category_id,
        customer.cooperation_status, customer.change_status_reason, customer.customer_source,
        customer.customer_type, customer.brand_stage, customer.cooperative_sector, customer.business_source,
        customer.service_product, customer.bidding_strategy, customer.decision_link, customer.remark,
        customer.version, customer.audit_status, customer.service_user_id, customer.create_id,
        customer.create_by, customer.create_dept, customer.create_time, customer.update_id,
        customer.update_by, customer.update_dept, customer.update_time,laundry.laundry_id
        FROM
            `py_crm_customer_laundry` laundry
                LEFT JOIN py_crm_customer customer ON laundry.biz_id = customer.customer_id
        where
            laundry.del_flag = 0
          and customer.del_flag = 0
          <if test="cooperationStatus != null">
              and customer.cooperation_status = #{cooperationStatus}
          </if>
          and customer.audit_status = #{auditStatus}
          and laundry.create_id = #{userId}
          <if test="bizType != null">
              and laundry.biz_type = #{bizType}
          </if>
          <if test="dataScope != null and dataScope != ''">
              and laundry.biz_id IN (select customer_id from py_crm_wander_about where del_flag = 0 and is_distribution = 1  ${dataScope} )
          </if>
          order by
        laundry.create_time desc
    </select>
<!--  查看清单头部统计  -->
    <select id="customerLaundryCount" resultType="com.py.crm.customer.domain.vo.CustomerCountVO">
        SELECT
        COUNT(Distinct customer.id) as total,
        count(Distinct IF (cooperation_status = 0 ,customer_id,null )) as cooperation,
        count(Distinct IF (cooperation_status = 1 ,customer_id,null )) as suspensionCooperation,
        count(Distinct IF (cooperation_status = 2 ,customer_id,null )) as intentionCooperate
        FROM
        `py_crm_customer_laundry` laundry
        LEFT JOIN py_crm_customer customer ON laundry.biz_id = customer.customer_id
        where
        laundry.del_flag = 0
        and customer.del_flag = 0
        <if test="cooperationStatus != null">
            and customer.cooperation_status = #{cooperationStatus}
        </if>
        and customer.audit_status = #{auditStatus}
        and laundry.create_id = #{userId}
        <if test="bizType != null">
            and laundry.biz_type = #{bizType}
        </if>
        <if test="dataScope != null and dataScope != ''">
            and laundry.biz_id IN (select Distinct customer_id from py_crm_wander_about where del_flag = 0 and is_distribution = 1  ${dataScope} )
        </if>
    </select>
<!--  移除清单  -->
    <update id="removeLaundry">
        update
            py_crm_customer_laundry
        set
            del_flag = 1,
            update_id = #{updateId},
            update_by = #{updateBy},
            update_time = #{updateTime}
        where
            del_flag = 0
          and create_id = #{createId}
            <if test="customerIdList != null and customerIdList.size() > 0">
                and laundry_id IN <foreach collection="customerIdList" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
            </if>
            <if test="cooperationStatus != null">
                and biz_id IN (select Distinct customer_id from py_crm_customer where del_flag = 0 and cooperation_status = #{cooperationStatus} )
            </if>
    </update>
</mapper>
