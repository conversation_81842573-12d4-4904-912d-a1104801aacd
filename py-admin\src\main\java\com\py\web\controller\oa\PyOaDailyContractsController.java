package com.py.web.controller.oa;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.file.FileInfoVO;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.oa.pyoacost.domain.vo.PyOaCostCheckBoxVO;
import com.py.oa.pyoadailycontracts.domain.dto.PyOaDailyContractsDTO;
import com.py.oa.pyoadailycontracts.domain.query.PyDailyContractCheckBoxQuery;
import com.py.oa.pyoadailycontracts.domain.query.PyDailyContractQuery;
import com.py.oa.pyoadailycontracts.domain.query.PyOaDailyContractCheckBoxQuery;
import com.py.oa.pyoadailycontracts.domain.query.PyOaDailyContractsQuery;
import com.py.oa.pyoadailycontracts.domain.vo.*;
import com.py.oa.pyoadailycontracts.service.IPyOaDailyContractsService;
import com.py.oa.pyoadailycontracts.service.impl.ContractsExport;
import com.py.oa.pyoadailycontracts.service.impl.PyOaDailyContractsServiceImpl;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;


/**
 * Oa日常合同Controller
 *
 * <AUTHOR>
 * @date 2023-07-21
 */
@Api(tags = "Oa日常合同")
@Validated
@RestController
@RequestMapping("/oa/pyOaDailyContracts")
public class PyOaDailyContractsController extends BaseController {

    /** Oa日常合同服务 */
    @Resource
    private IPyOaDailyContractsService pyOaDailyContractsService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 系统设置-主体参数设置服务 */
    @Resource
    private ISystemMainstayParamService systemMainstayParamService;

    /**
     * 分页查询Oa日常合同列表
     *
     * @param query Oa日常合同查询参数
     * @return Oa日常合同分页
     */
    @ApiOperation("分页查询询Oa日常合同列表")
    @PreAuthorize("@ss.hasPermi('oa:pyOaDailyContracts:list')")
    @PostMapping("/pagePyOaDailyContracts")
    public R<PageInfo<PyOaDailyContractsListVO>> pagePyOaDailyContracts(@Validated @RequestBody  PyOaDailyContractsQuery query) {
        PageInfo<PyOaDailyContractsListVO> voList = this.pyOaDailyContractsService.pagePyOaDailyContractsList(query);
        return R.success(voList);
    }

    /**
     * 获取Oa日常合同详细信息
     * @param id Oa日常合同主键
     * @return Oa日常合同视图模型
     */
    @ApiOperation("获取Oa日常合同详细信息")
    @PreAuthorize("@ss.hasPermi('oa:pyOaDailyContracts:query')")
    @GetMapping(value = "/getInfo/{id}")
    public R<PyOaDailyContractsVO> getInfo(@PathVariable("id") Long id) {
        return R.success(pyOaDailyContractsService.selectPyOaDailyContractsById(id));
    }

    /**
     * 新增Oa日常合同
     *
     * @param dto Oa日常合同修改参数
     * @return 是否成功
     */
    @ApiOperation("新增Oa日常合同")
    @PreAuthorize("@ss.hasPermi('oa:pyOaDailyContracts:add')")
    @Log(title = "Oa日常合同", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Long> add(@RequestBody @Valid PyOaDailyContractsDTO dto) {
        return R.success(pyOaDailyContractsService.insertPyOaDailyContracts(dto));
    }

    /**
     * 修改Oa日常合同
     *
     * @param dto Oa日常合同修改参数
     * @return 是否成功
     */
    @ApiOperation("修改Oa日常合同")
    @PreAuthorize("@ss.hasPermi('oa:pyOaDailyContracts:add')")
    @Log(title = "Oa日常合同", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R<Long> edit(@RequestBody @Valid PyOaDailyContractsDTO dto) {
        return R.success(pyOaDailyContractsService.updatePyOaDailyContracts(dto));
    }

    /**
     * 多选框查询
     *
     * @param checkBoxQuery 多选框查询
     * @return PyOaCostCheckBoxVO
     */
    @ApiOperation("多选框多选")
    @PreAuthorize("@ss.hasPermi('oa:pyOaDailyContracts:query')")
    @PostMapping("/checkbox")
    public R<PyOaCostCheckBoxVO> checkBox (@RequestBody PyOaDailyContractCheckBoxQuery checkBoxQuery) {
        return R.success(pyOaDailyContractsService.checkBoxQuery(checkBoxQuery));
    }

    /**
     * 导出Oa日常合同
     * @param query 导出查询参数
     */
    @ApiOperation("导出Oa日常合同")
    @PreAuthorize("@ss.hasPermi('oa:pyOaDailyContracts:export')")
    @Log(title = "Oa日常合同", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@Validated @RequestBody PyOaDailyContractsQuery query) {
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService
                .selectSystemMainstayParamById(ListUtil.first(query.getPyMainstayIdList()));
        query.setLoginUser(SecurityUtils.getLoginUser());
        String fileName = "日常合同列表数据-" + systemMainstayParamVO.getMainstayName() + "-"
                        + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        reusableAsyncTaskService.addTask("OA日常合同信息数据", TaskType.Export,query, PyOaDailyContractsServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 查询日常合同列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询日常合同列表上的创建部门下拉" )
    @GetMapping("/listOaDailyDept" )
    public R<List<String>> listOaDailyDept(PyOaDailyContractsQuery query){
        return R.success(pyOaDailyContractsService.listOaDailyDept(query));
    }

    /**
     * 审批查询-分页查询日常合同审批
     * @param query 查询条件
     * @return PyDailyContractListVO
     */
    @ApiOperation("审批查询-分页查询日常合同审批")
    @PostMapping("/PageDailyContractApproval")
    public R<PageInfo<PyDailyContractListVO>>pagePyController(@Validated @RequestBody PyDailyContractQuery query){
        PageInfo<PyDailyContractListVO> voList = this.pyOaDailyContractsService.listPyDailyContract(query);
        return R.success(voList);
    }

    /**
     * 审批查询-主体标签日常合同审批数量
     * @param pyMainstayIds 主体 id
     * @return PyDailyContractCountVO
     */
    @ApiOperation("审批查询-主体标签日常合同审批数量")
    @PostMapping("/DailyContractApprovalLabel")
    public R<List<PyDailyContractCountVO>> getContractsApprovalLabel(@RequestBody List<Long> pyMainstayIds){
        return R.success(pyOaDailyContractsService.getContractLabel(pyMainstayIds));
    }

    /**
     *  审批查询-日常合同审批查询合计
     * @param checkBoxQuery 是否全选
     * @return PyDailyContractCheckBoxVO
     */
    @ApiOperation("审批查询-日常合同审批查询合计")
    @PostMapping("/ContractCheckBox")
    public R<PyDailyContractCheckBoxVO> contractCheckBox(@RequestBody PyDailyContractCheckBoxQuery checkBoxQuery){
        return R.success(pyOaDailyContractsService.checkContractBoxAudit(checkBoxQuery));
    }

    /**
     * 审批查询-导出日常合同审批
     * @param query 查询条件
     */
    @ApiOperation("导出日常合同审批")
    @PostMapping("/exportContractsApproval")
    @PreAuthorize("@ss.hasPermi('oa:pyOaDailyContracts:export')")
    @Log(title = "日常合同", businessType = BusinessType.EXPORT)
    public R<String> exportContractApproval(@Validated @RequestBody PyDailyContractQuery query){
        reusableAsyncTaskService.addTask("日常合同审批信息数据",TaskType.Export, query , ContractsExport.class);
        return R.success("导出成功");
    }
    /**
     * 日常合同审批查询
     *
     * @param query 查询参数
     * @return 日常合同审批查询
     */
    @ApiOperation("日常合同审批查询")
    @PreAuthorize("@ss.hasPermi('oa:pyOaDailyContracts:list')")
    @PostMapping("/pagePyOaDailyApprove")
    public R<PageInfo<PyOaDailyApproveVO>> pagePyOaDailyApprove(@Validated @RequestBody  PyOaDailyContractsQuery query) {
        PageInfo<PyOaDailyApproveVO> voList = this.pyOaDailyContractsService.pagePyOaDailyApprove(query);
        return R.success(voList);
    }

    /**
     * 根据主体id查询合同类型
     * @param pyMainstayId 主体id
     * @return List
     */
    @ApiOperation("合同类型查询")
    @PostMapping("/selectContractType")
    public R<List<Integer>>getContractType(@RequestBody List<Long> pyMainstayId){
        return  R.success(pyOaDailyContractsService.getContractType(pyMainstayId));
    }
    /**
     * 日常合同审批查询统计
     *
     * @param query 查询参数
     * @return 统计
     */
    @ApiOperation("日常合同审批查询统计")
    @PreAuthorize("@ss.hasPermi('oa:pyOaDailyContracts:list')")
    @PostMapping("/countOaDailyApprove")
    public R<PyOaCostCheckBoxVO> countOaDailyApprove(@RequestBody PyOaDailyContractCheckBoxQuery query) {
        PyOaCostCheckBoxVO pyOaCostCheckBoxVO = this.pyOaDailyContractsService.countOaDailyApprove(query);
        return R.success(pyOaCostCheckBoxVO);
    }

    /**
     * 日常合同审批查询顶部统计
     *
     * @param query 查询参数
     * @return 统计
     */
    @ApiOperation("日常合同审批查询顶部统计")
    @PreAuthorize("@ss.hasPermi('oa:pyOaDailyContracts:list')")
    @PostMapping("/listOaDailyApproveTopCount")
    public R<List<OaDailyApproveCountMainstayVO>> listOaDailyApproveTopCount(@Validated @RequestBody PyOaDailyContractsQuery query) {
        List<OaDailyApproveCountMainstayVO> count =this.pyOaDailyContractsService.listOaDailyApproveTopCount(query);
        return R.success(count);
    }

    /**
     * 导出日常合同审批查询
     * @param query 导出查询参数
     */
    @ApiOperation("导出日常合同审批查询")
    @PreAuthorize("@ss.hasPermi('oa:pyOaDailyContracts:export')")
    @Log(title = "Oa日常合同", businessType = BusinessType.EXPORT)
    @PostMapping("/exportApprove")
    public R<String> exportApprove(@Validated @RequestBody PyOaDailyContractsQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPyMainstayId());
        String fileName = "日常合同审批查询列表数据-" + systemMainstayParamVO.getMainstayName() + "-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setIsApproval(true);
        reusableAsyncTaskService.addTask("日常合同审批查询", TaskType.Export,query, PyOaDailyContractsServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 查看盖章合同
     * @param id 日常合同信息主键
     * @return List<FileInfoVO> 盖章合同详情
     */
    @ApiOperation("查看盖章合同（OA费用）")
    @GetMapping(value = "/selectSealedOaContract/{id}")
    public R<List<FileInfoVO>>selectSealedOaContract(@PathVariable("id") Long id){
        return  R.success(pyOaDailyContractsService.selectDailyStampContractById(id));
    }

    /**
     * 上传盖章合同
     * @param pyOaDailyStampContractVO 主键id及盖章详情
     * @return 上传是否成功
     */
    @ApiOperation("上传盖章合同（OA费用）")
    @PostMapping(value = "/addSealedOaContract")
    public R<Boolean> addSealedOaContract(@RequestBody PyOaDailyStampContractVO pyOaDailyStampContractVO){
        return R.success(pyOaDailyContractsService.addDailyContractById(pyOaDailyStampContractVO));
    }

    /**
     * 删除OA管理 日常管理合同 盖章合同
     * @param pyOaDailyStampContractVO   主键id及盖章详情
     * @return  删除结果
     */
    @ApiOperation("删除盖章合同(OA费用)")
    @PostMapping(value = "/deleteSealedOaContract")
    public R<Boolean> deleteSealedOaContract(@RequestBody PyOaDailyStampContractVO pyOaDailyStampContractVO) {
        return R.success(pyOaDailyContractsService.deleteDailyContractById(pyOaDailyStampContractVO));
    }

    /**
     * 查询日常合同审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询日常合同审批列表上的创建部门下拉" )
    @GetMapping("/listOaDailyApproveDept" )
    public R<List<String>> listOaDailyApproveDept(PyDailyContractQuery query){
        return R.success(pyOaDailyContractsService.listOtherApproveDept(query));
    }
}
