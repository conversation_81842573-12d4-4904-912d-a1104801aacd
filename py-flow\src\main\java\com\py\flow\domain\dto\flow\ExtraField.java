package com.py.flow.domain.dto.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version ExtraField 2024/5/6 13:43
 */
@Data
public class ExtraField {
    /** 合同金额*/
    @ApiModelProperty("合同金额")
    private BigDecimal amount;

    /** 合同名*/
    @ApiModelProperty("合同名")
    private String contractName;

    /** 备注*/
    @ApiModelProperty("备注")
    private String notes;

    /** 派芽合作主体名*/
    @ApiModelProperty("派芽合作主体名")
    private String pyMainstayName;

    /** 流程版本ID */
    private Long flowVersionId;

    public ExtraField(BigDecimal amount, String contractName, String notes, String pyMainstayName) {
        this.amount = amount;
        this.contractName = contractName;
        this.notes = notes;
        this.pyMainstayName = pyMainstayName;
    }

    public ExtraField() {
    }
}
