package com.py.web.controller.system;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.framework.web.service.UserInfoCacheHelper;
import com.py.system.dept.domain.dto.DeptDTO;
import com.py.system.dept.domain.query.DeptInfoListQuery;
import com.py.system.dept.domain.query.DeptListQuery;
import com.py.system.dept.domain.query.DeptOptionQuery;
import com.py.system.dept.domain.vo.*;
import com.py.system.dept.service.ISysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 部门管理
 * <AUTHOR>
 */
@Api(tags = "组织权限管理 - 部门管理")
@RestController
@RequestMapping("/system/dept")
public class SysDeptController extends BaseController {

    /** 系统设置-部门管理服务 */
    @Resource
    private ISysDeptService deptService;


    /** 用户信息缓存帮助类 */
    @Resource
    private UserInfoCacheHelper userInfoCacheHelper;

    /***
     * 获取部门树
     * @return 部门树
     */
    @ApiOperation(value = "获取部门树")
    @PreAuthorize("@ss.hasPermi('system:dept:list')")
    @GetMapping("/list")
    public R<List<DeptTreeVO>> list(DeptListQuery query) {
        List<DeptTreeVO> deptTree = this.deptService.selectDeptList(query);
        return R.success(deptTree);
    }

    /**
     * 获取部门树 - 人员管理页面
     * @param deptName 部门名称
     * @return 部门树
     */
    @ApiOperation(value = "获取部门树 - 人员管理页面")
    @GetMapping("/listByUserView")
    public R<List<DeptUserTreeVO>> listByUserView(String deptName){
        List<DeptUserTreeVO> deptTree = this.deptService.selectDeptListByUserView(deptName);
        return R.success(deptTree);
    }

    /**
     * 获取部门下拉树
     * @return 部门下拉树
     */
    @ApiOperation(value = "获取部门下拉树")
    @GetMapping("/selectOptionTree")
    public R<List<DeptOptionVO>> selectOptionTree(){
        return R.success(this.deptService.selectOptionTree());
    }

    /**
     * 获取部门下拉框选项
     * @param query 查询条件
     * @return 应用下拉框选项列表
     */
    @ApiOperation("获取部门下拉框选项")
    @GetMapping("/selectOption")
    public R<List<DeptOptionVO>> selectOption(DeptOptionQuery query){
        return R.success(this.deptService.selectOption(query));
    }

    /**
     * 部门详情
     * @param deptId 部门ID
     */
    @ApiOperation("部门详情")
    @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @GetMapping(value = "/{deptId}")
    public R<DeptVO> getInfo(@PathVariable Long deptId) {
        return R.success(this.deptService.selectDeptById(deptId));
    }

    /**
     * 部门详情列表
     * @param query 查询条件
     */
    @ApiOperation("部门详情列表")
    @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @GetMapping(value = "/getInfoList")
    public R<List<DeptVO>> getInfoList(@Validated DeptInfoListQuery query) {
        return R.success(this.deptService.selectDeptById(query.getDeptIdList()));
    }

    /**
     * 新增部门
     * @param deptDto 新增DTO
     * @return 是否成功
     */
    @ApiOperation("新增部门")
    @PreAuthorize("@ss.hasPermi('system:dept:add')")
    @Log(title = "新增部门", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody DeptDTO deptDto) {
        this.deptService.insertDept(deptDto);
        return R.success();
    }

    /**
     * 修改部门
     * @param deptDto 修改DTO
     * @return 是否成功
     */
    @ApiOperation("修改部门")
    @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @Log(title = "修改部门", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody DeptDTO deptDto) {
        this.deptService.updateDept(deptDto);
        this.userInfoCacheHelper.refreshLoginUserCache(user -> user.getUser().getDeptIdList().contains(deptDto.getDeptId()));
        return R.success();
    }

    /**
     * 移动部门
     * @param moveDTO 部门移动DTO
     * @return 是否成功
     */
    @ApiOperation("移动部门")
    @Log(title = "移动部门", businessType = BusinessType.UPDATE)
    @PutMapping("/move")
    public R<Boolean> move(@Validated @RequestBody DeptMoveDTO moveDTO){
        this.deptService.move(moveDTO);
        return R.success();
    }

    /**
     * 删除部门
     */
    @ApiOperation("删除部门")
    @PreAuthorize("@ss.hasPermi('system:dept:remove')")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public R<Boolean> remove(@PathVariable Long deptId) {
        this.deptService.deleteDeptById(deptId);
        return R.success();
    }

    /**
     * 刷新部门祖级列表
     * @return 刷新结果
     */
    @ApiOperation("刷新部门祖级列表")
    @PutMapping("/refreshDeptAncestors")
    public R<Boolean> refreshDeptAncestors(){
        this.deptService.refreshDeptAncestors();
        return R.success();
    }

    /**
     * 刷新部门人数
     * @return 刷新结果
     */
    @ApiOperation("刷新部门人数")
    @PutMapping("/refreshDeptEmployeeNum")
    public R<Boolean> refreshDeptEmployeeNum(){
        this.deptService.refreshDeptEmployeeNum();
        return R.success();
    }

    /**
     * 同步历史部门
     * @return 是否成功
     */
    @ApiOperation("同步历史部门")
    @PutMapping("/syncHistoryDept")
    public R<Boolean> syncHistoryDept(){
        this.deptService.syncHistoryDept();
        return R.success();
    }

    /**
     * 查询列表上的更新部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询列表上的更新部门下拉" )
    @GetMapping("/listDropDownDept" )
    public R<PageInfo<DeptVO>> listDropDownDept(DeptListQuery query){
        return R.success(deptService.listDropDownDept(query));
    }

    /**
     * 获取部门带数据权限下拉框选项
     * @param query 查询条件
     * @return 应用下拉框选项列表
     */
    @ApiOperation("获取部门带数据权限下拉框选项")
    @GetMapping("/selectPermissionOption")
    public R<List<DeptVO>> selectPermissionOption(DeptOptionQuery query) {
        return R.success(this.deptService.selectPermissionOption(query));
    }
}
