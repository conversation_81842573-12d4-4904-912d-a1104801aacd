package com.py.common.utils;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.junit.Assert;
import org.junit.Test;

/**
 * 数据字典工具类 测试
 * <AUTHOR>
 */
public class EnumUtilsTest {

    @Test
    public void tpynum() {
        TestEnum result = EnumUtils.toEnum(0, TestEnum.class);
        Assert.assertEquals(TestEnum.A, result);

        result = EnumUtils.toEnum(2, TestEnum.class);
        Assert.assertEquals(TestEnum.C, result);

        result = EnumUtils.toEnum(null, TestEnum.class);
        Assert.assertNull(result);

        Assert.assertThrows(UnsupportedOperationException.class,
                () -> EnumUtils.toEnum(-1, TestEnum.class));
    }

    @Test
    public void tpynumNullable() {
        TestEnum result = EnumUtils.toEnumNullable(0, TestEnum.class);
        Assert.assertEquals(TestEnum.A, result);

        result = EnumUtils.toEnumNullable(2, TestEnum.class);
        Assert.assertEquals(TestEnum.C, result);

        result = EnumUtils.toEnumNullable(null, TestEnum.class);
        Assert.assertNull(result);

        result = EnumUtils.toEnumNullable(-1, TestEnum.class);
        Assert.assertNull(result);
    }

    @Test
    public void toDict() {
        TestEnum result = EnumUtils.toDict("A" , TestEnum.class);
        Assert.assertEquals(TestEnum.A, result);

        result = EnumUtils.toDict("B" , TestEnum.class);
        Assert.assertEquals(TestEnum.B, result);

        result = EnumUtils.toDict("" , TestEnum.class);
        Assert.assertNull(result);

        result = EnumUtils.toDict(null, TestEnum.class);
        Assert.assertNull(result);

        Assert.assertThrows(UnsupportedOperationException.class,
                () -> EnumUtils.toDict("unknown" , TestEnum.class));
    }

    @Test
    public void toDictNullable() {
        TestEnum result = EnumUtils.toDictNullable("A" , TestEnum.class);
        Assert.assertEquals(TestEnum.A, result);

        result = EnumUtils.toDictNullable("B" , TestEnum.class);
        Assert.assertEquals(TestEnum.B, result);

        result = EnumUtils.toDictNullable("" , TestEnum.class);
        Assert.assertNull(result);

        result = EnumUtils.toDictNullable(null, TestEnum.class);
        Assert.assertNull(result);

        result = EnumUtils.toDictNullable("unknown" , TestEnum.class);
        Assert.assertNull(result);
    }

    @AllArgsConstructor
    @Getter
    private enum TestEnum implements IDict<Integer> {

        A(0, "A"),
        B(1, "B"),
        C(2, "C");

        private final Integer value;

        private final String label;
    }
}
