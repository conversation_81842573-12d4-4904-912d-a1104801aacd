package com.py.common.tools.modifycomparator.view.datarelater;

import com.py.common.core.domain.entity.SysDictData;
import com.py.common.core.domain.service.IDictService;
import com.py.common.oss.IOssService;
import com.py.common.oss.model.OssObjectInfo;
import com.py.common.tools.modifycomparator.view.BaseModifyDiffVO;
import com.py.common.tools.modifycomparator.view.BaseModifyDiffVO.StringModifyDiff;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 修改差异信息关联器
 * <AUTHOR>
 */
@Component
public class ModifyDiffRelater {

    /** 对象存储服务 */
    @Resource
    private IOssService ossService;

    /** 字典服务 */
    @Resource
    private IDictService dictService;

    /**
     * 为修改差异设置信息关联
     * <p>将字典将转换为字典值</p>
     * <p>通过OssKey添加Oss对象</p>
     * @param modifyDiffVoList 修改差异视图模型列表
     */
    public void relatedInfo(List<BaseModifyDiffVO<?>> modifyDiffVoList) {
        if(ListUtil.isEmpty(modifyDiffVoList)) {
            return;
        }

        this.relatedDictInfo(modifyDiffVoList);
        this.relatedOssInfo(modifyDiffVoList);
    }

    /**
     * 关联字典信息
     * @param modifyDiffVoList 修改差异视图模型列表
     */
    private void relatedDictInfo(List<BaseModifyDiffVO<?>> modifyDiffVoList) {
        List<StringModifyDiff> dictModifyDiffList = modifyDiffVoList.stream()
                .filter(modifyDiff -> modifyDiff instanceof StringModifyDiff)
                .map(modifyDiff -> (StringModifyDiff) modifyDiff)
                .filter(StringModifyDiff::isAsDict)
                .collect(Collectors.toList());


        // 单字典的字段
        Map<String, List<StringModifyDiff>> singleDictModifyDiffGroup = dictModifyDiffList.stream()
                .filter(modifyDiff -> modifyDiff.isAsListDict() == false)
                .collect(Collectors.groupingBy(StringModifyDiff::getDictName));

        if(ListUtil.any(singleDictModifyDiffGroup)) {
            singleDictModifyDiffGroup.forEach((dictName, dictModifyList) -> {
                this.dictService.relatedDict(dictModifyList, dictName, StringModifyDiff::getAfter,
                        (modifyDiff, dict) -> modifyDiff.setAfter(dict.getDictLabel()));

                this.dictService.relatedDict(dictModifyList, dictName, StringModifyDiff::getBefore,
                        (modifyDiff, dict) -> modifyDiff.setBefore(dict.getDictLabel()));
            });
        }

        // 字典列表的字段
        Map<String, List<StringModifyDiff>> listDictModifyDiffGroup = dictModifyDiffList.stream()
                .filter(StringModifyDiff::isAsListDict)
                .collect(Collectors.groupingBy(StringModifyDiff::getDictName));

        if(ListUtil.any(listDictModifyDiffGroup)) {
            listDictModifyDiffGroup.forEach((dictName, dictModifyList) -> {

                this.dictService.relatedDictList(dictModifyList, dictName,
                        diff -> StringUtils.convertStringList(diff.getAfter()),
                        (StringModifyDiff modifyDiff, List<SysDictData> dictList) -> modifyDiff.setAfter(this.formatDictList(dictList)));

                this.dictService.relatedDictList(dictModifyList, dictName,
                        diff -> StringUtils.convertStringList(diff.getBefore()),
                        (StringModifyDiff modifyDiff, List<SysDictData> dictList) -> modifyDiff.setBefore(this.formatDictList(dictList)));
            });
        }
    }

    /**
     * 格式化日志中的字典列表
     * @param dictList 字典列表
     * @return 格式化后的字典
     */
    private String formatDictList(List<SysDictData> dictList) {
        return ListUtil.joining(dictList, ",", SysDictData::getDictLabel);
    }

    /**
     * 关联Oss信息
     * @param modifyDiffVoList 修改差异视图模型列表
     */
    private void relatedOssInfo(List<BaseModifyDiffVO<?>> modifyDiffVoList) {
        List<OssObjectInfo> singleOssObjectInfoList = modifyDiffVoList.stream()
                .filter(modifyDiff -> modifyDiff instanceof BaseModifyDiffVO.OssFileModifyDiff)
                .map(modifyDiff -> (BaseModifyDiffVO.OssFileModifyDiff) modifyDiff)
                .flatMap(modifyDiff -> Stream.of(modifyDiff.getAfter(), modifyDiff.getBefore()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<OssObjectInfo> listOssObjectInfoList = modifyDiffVoList.stream()
                .filter(modifyDiff -> modifyDiff instanceof BaseModifyDiffVO.ListOssFileModifyDiff)
                .map(modifyDiff -> (BaseModifyDiffVO.ListOssFileModifyDiff) modifyDiff)
                .flatMap(modifyDiff -> ListUtil.merge(modifyDiff.getAfter(), modifyDiff.getBefore()).stream())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<OssObjectInfo> ossObjectInfoList = ListUtil.merge(singleOssObjectInfoList, listOssObjectInfoList);

        this.ossService.relationOssUrl(ossObjectInfoList, OssObjectInfo::getKey,
                OssObjectInfo::setOssUrl);
    }
}
