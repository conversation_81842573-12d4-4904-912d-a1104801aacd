package com.py.crm.customer.customervisit.domian.vo;

import com.py.common.core.domain.vo.BaseUpdateInfoVO;
import com.py.common.file.FileAnnex;
import com.py.common.file.FileInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 *<AUTHOR>
 *
 */
@Data
@ApiModel("客户管理-客户拜访日志视图模型")
public class CustomerVisitVO extends BaseUpdateInfoVO {

    private static final long serialVersionUID=1L;
    /**自增id*/
    @ApiModelProperty("自增id")
    private Long id;

    /**客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /**访问日期*/
    @ApiModelProperty("访问日期")
    private LocalDate visitTime;

    /**访问日志id*/
    @ApiModelProperty("访问日志id")
    private Long visitId;

    /**随访人员*/
    @ApiModelProperty("随访人员")
    private String visitPersonnel;

    /**访问事项*/
    @ApiModelProperty("访问事项")
    private String visitMatters;

    /**事项结果*/
    @ApiModelProperty("事项结果")
    private String visitOutcome;

    /**附件*/
    @ApiModelProperty("附件")
    private List<FileAnnex> annex;

    @ApiModelProperty("附件信息")
    private List<FileInfoVO> annexList;

    /**备注*/
    @ApiModelProperty("备注")
    private String note;
    /**展示id*/
    @ApiModelProperty("展示id")
    private Integer showId;

}
