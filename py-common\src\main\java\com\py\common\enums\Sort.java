package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 排序枚举
 * <AUTHOR>
 */
@AllArgsConstructor
public enum Sort implements IDict<Integer>{
    /**
     * 正序
     */
    ASC(1,"正序"),
    /**
     * 倒序
     */
    DESC(2,"倒序")
    ;
    private final Integer value;
    private final String label;

    @Override
    public String getLabel() {
        return this.label;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
