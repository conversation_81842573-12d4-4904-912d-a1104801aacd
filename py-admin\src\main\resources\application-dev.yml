# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: *************************************************************************************************************************************************************************************************************************
                username: eloans
                password: eloans@123
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url:
                username:
                password:
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ruoyi
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
    # redis 配置
    redis:
        # 地址
        host:  r-bp1xpqo4w6ysomnyv1pd.redis.rds.aliyuncs.com
        # 端口，默认为6379
        port: 6379
        # 数据库索引
        database: 2
        # 密码
        password: Aa123456
        # 连接超时时间
        timeout: 10s
        lettuce:
            pool:
                # 连接池中的最小空闲连接
                min-idle: 0
                # 连接池中的最大空闲连接
                max-idle: 8
                # 连接池的最大数据库连接数
                max-active: 8
                # #连接池最大阻塞等待时间（使用负值表示没有限制）
                max-wait: -1ms

#socket.io 配置
socketio:
    port: 8088
    # 设置最大每帧处理数据的长度，防止他人利用大数据来攻击服务器
    maxFramePayloadLength: 1048576
    # 设置 http 交互最大内容长度
    maxHttpContentLength: 1048576
    # socket连接数大小（如只监听一个端口 boss 线程组为 1 即可）
    bossCount: 5
    workCount: 1000
    allowCustomRequests: true
    # 协议升级超时时间（毫秒），默认 10 秒。HTTP握手升级为 ws 协议超时时间
    upgradeTimeout: 20000
    # Ping 消息超时时间（毫秒），默认 60 秒，这个时间间隔内没有接收到心跳消息就会发送超时事件
    pingTimeout: 60000
    # Ping 消息间隔（毫秒），默认 25 秒。客户端向服务器发送一条心跳消息间隔
    pingInterval: 25000