package com.py.web.controller.crm;

import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.crm.customer.invoicing.domain.dto.InvoicingDTO;
import com.py.crm.customer.invoicing.domain.dto.InvoicingExportModel;
import com.py.crm.customer.invoicing.domain.vo.InvoicingVO;
import com.py.crm.customer.invoicing.domain.vo.InvoicingListVO;
import com.py.crm.customer.invoicing.domain.query.InvoicingQuery;
import com.py.crm.customer.invoicing.service.IInvoicingService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户管理-客户-开票信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Api(tags = "客户管理-客户-开票信息")
@RestController
@RequestMapping("/crm/invoicing")
public class InvoicingController extends BaseController {

    /** 客户管理-客户-开票信息服务 */
    @Resource
    private IInvoicingService invoicingService;

    /**
     * 分页查询客户管理-客户-开票信息列表
     *
     * @param query 客户管理-客户-开票信息查询参数
     * @return 客户管理-客户-开票信息分页
     */
    @ApiOperation("分页查询询客户管理-客户-开票信息列表")
    @PreAuthorize("@ss.hasPermi('com.py.crm:invoicing:list')")
    @GetMapping("/pageinvoicing")
    public R<PageInfo<InvoicingListVO>> pageinvoicing(InvoicingQuery query) {
        PageInfo<InvoicingListVO> voList = this.invoicingService.pageinvoicingList(query);
        return R.success(voList);
    }

    /**
     * 分页查询客户管理-客户-开票信息列表--下拉列表
     *
     * @param query 客户管理-客户-开票信息查询参数
     * @return 客户管理-客户-开票信息分页
     */
    @ApiOperation("分页查询询客户管理-客户-开票信息列表")
    @PreAuthorize("@ss.hasPermi('com.py.crm:invoicing:list')")
    @GetMapping("/pageInvoicingSelected")
    public R<PageInfo<InvoicingListVO>> pageInvoicingSelected(InvoicingQuery query) {
        PageInfo<InvoicingListVO> voList = this.invoicingService.pageInvoicingSelected(query);
        return R.success(voList);
    }

    /**
     * 获取客户管理-客户-开票信息详细信息
     * @param id 客户管理-客户-开票信息主键
     * @return 客户管理-客户-开票信息视图模型
     */
    @ApiOperation("获取客户管理-客户-开票信息详细信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:invoicing:query')")
    @GetMapping(value = "/{id}")
    public R<List<InvoicingVO>> getInfo(@PathVariable("id") Long id) {
        return R.success(invoicingService.selectinvoicingDetailById(id));
    }

    /**
     * 新增客户管理-客户-开票信息
     *
     * @param dto 客户管理-客户-开票信息修改参数
     * @return 是否成功
     */
    @ApiOperation("新增客户管理-客户-开票信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:invoicing:add')")
    @Log(title = "客户管理-客户-开票信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody InvoicingDTO dto) {
        return R.success(invoicingService.insertinvoicing(dto));
    }

    /**
     * 修改客户管理-客户-开票信息
     *
     * @param dto 客户管理-客户-开票信息修改参数
     * @return 是否成功
     */
    @ApiOperation("修改客户管理-客户-开票信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:invoicing:edit')")
    @Log(title = "客户管理-客户-开票信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody InvoicingDTO dto) {
        return R.success(invoicingService.updateinvoicing(dto));
    }

    /**
     * 删除客户管理-客户-开票信息
     * @param ids 需要删除的客户管理-客户-开票信息主键集合
     * @return 是否成功
     */
    @ApiOperation("删除客户管理-客户-开票信息" )
    @PreAuthorize("@ss.hasPermi('com.py.crm:invoicing:remove')")
    @Log(title = "客户管理-客户-开票信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(invoicingService.deleteinvoicingByIds(ids));
    }

    /**
     * 导出客户管理-客户-开票信息
     * @param response 请求响应
     * @param query 导出查询参数
     */
    @ApiOperation("导出客户管理-客户-开票信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:invoicing:export')")
    @Log(title = "客户管理-客户-开票信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InvoicingQuery query) {
        List<InvoicingExportModel> exportList = this.invoicingService.exportinvoicing(query);

        ExcelUtil<InvoicingExportModel> util = new ExcelUtil<>(InvoicingExportModel. class);
        util.exportExcel(response, exportList, "客户管理-客户-开票信息数据" );
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('com.py.crm:invoicing:import')" )
    @Log(title = "客户管理-客户-开票信息" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<InvoicingExportModel> util = new ExcelUtil<>(InvoicingExportModel.class);
        List<InvoicingExportModel> invoicingList = util.importExcel(file.getInputStream());
        String message = this.invoicingService.importinvoicing(invoicingList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('com.py.crm:invoicing:import')" )
    @Log(title = "客户管理-客户-开票信息" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<InvoicingExportModel> util = new ExcelUtil<>(InvoicingExportModel.class);
        util.importTemplateExcel(response, "客户管理-客户-开票信息数据" );
    }

}
