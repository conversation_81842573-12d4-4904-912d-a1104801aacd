package com.py.crm.customer.customervisit.domian.dto;

import com.py.common.core.domain.vo.BaseUpdateInfoVO;
import com.py.common.file.FileAnnex;
import com.py.common.tools.modifycomparator.annotation.CompareField;
import com.py.common.tools.multisheetexcelexporter.domain.HyperlinkPackage;
import com.py.common.utils.collection.ListUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version CustomerVisitExportModel 2024/1/3 16:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerVisitExportModel extends BaseUpdateInfoVO {
    private static final long serialVersionUID=1L;
    /**访问日志id*/
    @ApiModelProperty("访问日志id")
    private Long visitId;

    /** 客户id */
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 客户名称*/
    @ApiModelProperty("客户名称")
    private String name;

    /** 品牌/业务线*/
    @ApiModelProperty("品牌/业务线")
    private String lineBusiness;

    /** 行业类目id*/
    @ApiModelProperty("行业类目id" )
    private List<Long> industryCategoryIdList;
    private String industryCategoryStr;

    /** 合作状态(0.合作中 1.暂停合作 2.意向合作)*/
    @ApiModelProperty("合作状态(0.合作中 1.暂停合作 2.意向合作)")
    private Integer cooperationStatus;
    private String cooperationStatusStr;

    /**随访人员*/
    @ApiModelProperty("随访人员")
    @CompareField(value = "随访人员")
    private String visitPersonnel;

    /**访问事项*/
    @ApiModelProperty("访问事项")
    @CompareField(value = "访问事项")
    private String visitMatters;

    /**事项结果*/
    @ApiModelProperty("事项结果")
    @CompareField(value = "事项结果")
    private String visitOutcome;

    /**附件*/
    @ApiModelProperty("附件")
    private List<FileAnnex> annex;

    /**附件*/
    @ApiModelProperty("附件导出")
    private List<HyperlinkPackage> annexModel;

    /**备注*/
    @ApiModelProperty("备注")
    @CompareField(value = "备注")
    private String note;

    /**访问日期*/
    @ApiModelProperty("访问日期")
    @CompareField(value = "访问日期")
    private LocalDate visitTime;

    /**访问日期*/
    @ApiModelProperty("访问日期")
    private String visitTimeStr;

    /**更新日期*/
    @ApiModelProperty("更新日期")
    private String updateTimeStr;


    public void conversion(){
        List<FileAnnex> annexList = this.getAnnex();
        if(ListUtil.isEmpty(annexList)){
            return;
        }
        List<HyperlinkPackage> hyperlinkPackageList = new ArrayList<>();
        annexList.forEach(fileAnnex -> {
            HyperlinkPackage hyperlinkPackage = new HyperlinkPackage();
            hyperlinkPackage.setFileName(fileAnnex.getFileName());
            hyperlinkPackage.setOssUrl(fileAnnex.getKey());
            hyperlinkPackageList.add(hyperlinkPackage);
        });
        setAnnexModel(hyperlinkPackageList);
    }

    public HyperlinkPackage getCustomerVisitIndex(int index) {
        if(ListUtil.isEmpty(annexModel)){
            return new HyperlinkPackage();
        }
        if(index >= annexModel.size()){
            return new HyperlinkPackage();
        }
        return annexModel.get(index);
    }
}
