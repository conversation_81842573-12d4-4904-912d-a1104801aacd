package com.py.common.annotation;

import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @description 表情校验
 * @date Created in 2022/6/22
 * @version: $
 */
public class EmojiCheckAnnotationImpl implements ConstraintValidator<EmojiCheckAnnotation, String> {
    private EmojiCheckAnnotation emojiCheckAnnotation;

    @Override
    public void initialize(EmojiCheckAnnotation emojiCheckAnnotation) {
        this.emojiCheckAnnotation = emojiCheckAnnotation;
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        // 具体校验方法
        if (StringUtils.isBlank(s)) {
            return true;
        }
        int len = s.length();
        for (int i = 0; i < len; i++) {
            char codePoint = s.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认含有表情
                return false;
            }
        }
        return true;
    }

    /**
     * 判断是否为非Emoji字符
     *
     * @param codePoint 比较的单个字符
     * @return
     */
    private boolean isNotEmojiCharacter(char codePoint) {
        return (codePoint == 0x0)
                || (codePoint == 0x9)
                || (codePoint == 0xA)
                || (codePoint == 0xD)
                || ((codePoint >= 0x20)
                && (codePoint <= 0xD7FF))
                || ((codePoint >= 0xE000)
                && (codePoint <= 0xFFFD))
                || ((codePoint >= 0x10000)
                && (codePoint <= 0x10FFFF));
    }
}

