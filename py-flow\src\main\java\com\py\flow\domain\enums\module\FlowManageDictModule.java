package com.py.flow.domain.enums.module;

import com.py.common.config.dict.AbstractDictModule;
import com.py.flow.domain.enums.ApprovalBizType;
import com.py.flow.domain.enums.ApprovalKind;
import com.py.flow.domain.enums.ApprovalTabType;
import com.py.flow.domain.enums.FlowReApprovalStrategy;
import org.springframework.stereotype.Component;

/**
 * 流程管理字典模组
 * <AUTHOR>
 */
@Component
public class FlowManageDictModule extends AbstractDictModule {

    /** 初始化 */
    @Override
    protected void init() {
        // 注册字典
        registered(ApprovalBizType.class);
        registered(ApprovalKind.class);
        registered(ApprovalTabType.class);
        registered(FlowReApprovalStrategy.class);
    }
}
