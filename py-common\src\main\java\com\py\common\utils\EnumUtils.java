package com.py.common.utils;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.py.common.enums.IBitEnum;
import com.py.common.enums.IDict;
import com.py.common.utils.collection.ListUtil;
import lombok.NonNull;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 数据字典工具类
 * <AUTHOR>
 */
public final class EnumUtils {

    /** 枚举内容缓存 */
    private static final Map<Class<?>, List<?>> ENUM_CONSTANTS_CACHE = new ConcurrentHashMap<>();

    /**
     * 转换字典枚举, 转换值不正确时抛异常
     * @param value 枚举值
     * @param dictClass 字典枚举类型
     * @param <TDict> 字典枚举类型
     * @param <TValue> 枚举值
     * @return 字典枚举
     */
    public static <TDict extends IEnum<TValue>, TValue extends Serializable> TDict toEnum(TValue value, Class<TDict> dictClass) {
        verifyEnumType(dictClass);

        //noinspection AlibabaAvoidComplexCondition
        if(Objects.isNull(value)
                || (value instanceof String && StringUtils.isBlank((String) value))) {
            return null;
        }

        TDict dict = convertNotNullEnumValue(value, dictClass);
        if(dict != null) {
            return dict;
        }

        throw new UnsupportedOperationException(String.format("枚举 %s: 不存在值为 %s 的项目" , dictClass.getName(), value));
    }

    /**
     * 转换字典枚举, 无法转换时返回null
     * @param value 枚举值
     * @param dictClass 字典枚举类型
     * @param <TDict> 字典枚举类型
     * @param <TValue> 枚举值
     * @return 字典枚举
     */
    public static <TDict extends IEnum<TValue>, TValue extends Serializable> TDict toEnumNullable(TValue value, Class<TDict> dictClass) {
        verifyEnumType(dictClass);

        //noinspection AlibabaAvoidComplexCondition
        if(Objects.isNull(value)
        || (value instanceof String && StringUtils.isBlank((String) value))) {
            return null;
        }

        return convertNotNullEnumValue(value, dictClass);
    }

    /**
     * 转换字典枚举, 无法转换时返回空
     * @param valueList 枚举值列表
     * @param dictClass 字典枚举类型
     * @param <TDict> 字典枚举类型
     * @param <TValue> 枚举值
     * @return 字典枚举
     */
    public static <TDict extends IEnum<TValue>, TValue extends Serializable> List<TDict> toEnumListNullable(
            List<TValue> valueList, Class<TDict> dictClass) {
        if(ListUtil.isEmpty(valueList)) {
            return ListUtil.emptyList();
        }
        return valueList.stream()
                .map(value -> toEnumNullable(value, dictClass))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 验证枚举类型
     * @param enumClass 枚举
     */
    private static void verifyEnumType(Class<?> enumClass) {
        if(enumClass == null || enumClass.isEnum() == false) {
            throw new UnsupportedOperationException("仅支持枚举类型");
        }
    }

    /**
     * 通过字典标签转换字典枚举, 转换值不正确时抛异常
     * @param label 字典标签
     * @param dictClass 字典枚举类型
     * @param <TDict> 字典枚举类型
     * @return 字典枚举
     */
    public static <TDict extends IDict<?>> TDict toDict(String label, Class<TDict> dictClass) {
        verifyEnumType(dictClass);

        if(StringUtils.isBlank(label)) {
            return null;
        }

        TDict enumItem = convertNotNullDictValue(label, dictClass);
        if(enumItem != null) {
            return enumItem;
        }
        throw new UnsupportedOperationException(String.format("枚举 %s: 不存在值为 %s 的项目" , dictClass.getName(), label));
    }

    /**
     * 通过字典标签转换字典枚举, 转换值不正确时抛异常
     * @param label 字典标签
     * @param dictClass 字典枚举类型
     * @param <TDict> 字典枚举类型
     * @return 字典枚举
     */
    public static <TDict extends IDict<?>> TDict toDictNullable(String label, Class<TDict> dictClass) {
        verifyEnumType(dictClass);

        if(StringUtils.isBlank(label)) {
            return null;
        }

        return convertNotNullDictValue(label, dictClass);
    }

    /**
     * 获取指定标签值列表
     * @param dictClass 字典类型
     * @param <TDict> 字典类型
     * @return label1, label2, label3
     */
    public static <TDict extends IDict<?>> String getDictLabel(Class<TDict> dictClass) {
        verifyEnumType(dictClass);

        return getEnumConstants(dictClass).stream()
                .map(IDict::getLabel)
                .collect(Collectors.joining(", "));
    }


    /**
     * 序列化枚举集合, 以位标志模式编码
     * <p>基于枚举顺序, 按枚举排序将枚举集合的值转换为二进制位</p>
     * <p>例子: 定义枚举值 A, B, C, D</p>
     * <p>枚举集合为 [A, B] -> 0011</p>
     * <p>枚举集合为 [A, D] -> 1001</p>
     * <p>枚举集合为 [A, B, C, D] ->1111</p>
     * @param enumSet 需要序列化的枚举集合值
     * @param <TEnum> 枚举类型
     * @return 序列化结果
     */
    public static <TEnum extends Enum<TEnum> & IBitEnum> byte[] serializeEnumSet(EnumSet<TEnum> enumSet) {

        if(ListUtil.isEmpty(enumSet)) {
            return new byte[0];
        }
        int maxPosition = enumSet.stream().mapToInt(IBitEnum::getBitPosition).max().orElse(-1);
        Assert.isTrue(maxPosition >= 0,
                String.format("枚举 %s,位枚举设置错误, 位枚举位置必须大于等于0!", ListUtil.firstOrThrow(enumSet).getClass().getName()));
        byte[] result = new byte[(maxPosition / Byte.SIZE) + 1];

        int bitPosition;
        for(TEnum enumValue : enumSet) {
            bitPosition = enumValue.getBitPosition();
            Assert.isTrue(bitPosition >= 0, String.format("枚举 %s,位枚举设置错误, 位枚举位置必须大于等于0!", enumValue.getClass().getName()));
            // 倒序获取数据库存放的字节, 原因: 数据库获取时高位在前, 低位在后
            result[result.length - 1 - bitPosition / Byte.SIZE] |= (1 << (bitPosition % Byte.SIZE));
        }
        return result;
    }

    /**
     * 反序列化枚举集合
     * @param enumSetValue 枚举集合序列化值
     * @param enumClass 枚举类型参数
     * @param <TEnum> 枚举类型
     * @return 枚举集合
     */
    public static <TEnum extends Enum<TEnum> & IBitEnum> EnumSet<TEnum> deserializeEnumSet(
            byte[] enumSetValue,
            @NonNull Class<TEnum> enumClass) {
        verifyEnumType(enumClass);
        EnumSet<TEnum> result = EnumSet.noneOf(enumClass);

        if(enumSetValue == null || enumSetValue.length == 0) {
            return result;
        }

        List<TEnum> enumConstants = getEnumConstants(enumClass);
        Map<Integer, TEnum> enumMap = ListUtil.toMap(enumConstants, IBitEnum::getBitPosition);

        int maxPosition = enumConstants.stream().mapToInt(IBitEnum::getBitPosition).max().orElse(-1);
        Assert.isTrue(maxPosition >= 0,
                String.format("枚举 %s,位枚举设置错误, 位枚举位置必须大于等于0!", enumClass.getName()));
        int totalIndex;
        for(int byteIndex = 0; byteIndex < enumSetValue.length; byteIndex++) {
            // 倒序获取数据库存放的字节, 原因: 数据库获取时高位在前, 低位在后
            byte enumValue = enumSetValue[enumSetValue.length - byteIndex - 1];
            // 扫码获取的的字节的每一位
            for(int index = 0; index < Byte.SIZE; index++) {
                // 设置整体索引, 当[整体索引]超过[枚举长内容数组大小]时退出枚举位标志判断循环
                totalIndex = index + byteIndex * Byte.SIZE;
                if(totalIndex > maxPosition) {
                    break;
                }

                int offset = enumValue & (1 << index);
                if(offset != 0) {
                    result.add(enumMap.get(totalIndex));
                }
            }
        }

        return result;
    }

    /**
     * 将枚举集合为二进制字符串
     * @param enumSet 需要转换的字节数组
     * @param <TEnum> 枚举类型
     * @return 二进制字符串
     */
    public static <TEnum extends Enum<TEnum> & IBitEnum> String toBinString(EnumSet<TEnum> enumSet) {
        if(ListUtil.isEmpty(enumSet)) {
            return ByteUtils.toBinString(new byte[0]);
        }

        byte[] configEnumByteArray = EnumUtils.serializeEnumSet(EnumSet.copyOf(enumSet));
        return ByteUtils.toBinString(configEnumByteArray);
    }

    /**
     * 获取枚举内容
     * <p>不做合法性入参检查, 需检查后调用</p>
     * @param enumClass 枚举类型参数
     * @param <TEnum> 枚举类型
     * @return 枚举内容
     */
    @SuppressWarnings("unchecked")
    public static <TEnum> List<TEnum> getEnumConstants(Class<TEnum> enumClass) {
        if(ENUM_CONSTANTS_CACHE.containsKey(enumClass) == false) {
            ENUM_CONSTANTS_CACHE.put(enumClass, Arrays.asList(enumClass.getEnumConstants()));
        }
        return (List<TEnum>) ENUM_CONSTANTS_CACHE.get(enumClass);
    }

    /**
     * 获取随机枚举元素
     * @param enumClass 枚举类型参数
     * @param <TEnum> 枚举类型
     * @return 随机枚举元素
     */
    public static <TEnum> TEnum getRandomEnum(Class<TEnum> enumClass) {
        verifyEnumType(enumClass);

        List<TEnum> enumConstants = getEnumConstants(enumClass);
        return enumConstants.get(RandomUtil.randomInt(0, enumConstants.size() - 1));
    }

    /**
     * 转换内容不为空的转换字典枚举, 无法转换时返回null
     * @param value 枚举值
     * @param dictClass 字典枚举类型
     * @param <TDict> 字典枚举类型
     * @param <TValue> 枚举值
     * @return 字典枚举
     */
    private static <TDict extends IEnum<TValue>, TValue extends Serializable> TDict convertNotNullEnumValue(TValue value, Class<TDict> dictClass) {
        for(TDict enumItem : getEnumConstants(dictClass)) {
            if(enumItem.getValue().equals(value)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 转换内容不为空的转换字典标签, 无法转换时返回null
     * @param label 字典标签
     * @param dictClass 字典枚举类型
     * @param <TDict> 字典枚举类型
     * @return 字典枚举
     */
    private static <TDict extends IDict<?>> TDict convertNotNullDictValue(String label, Class<TDict> dictClass) {
        for(TDict enumItem : getEnumConstants(dictClass)) {
            if(enumItem.getLabel().equals(label)) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 获取枚举值对应的字典标签
     * @param value 字典值
     * @param dictClass 字典枚举类型
     * @param <TDict> 字典枚举类型
     * @return 字典标签
     */
    public static <TDict extends IDict<TValue>, TValue extends Serializable> String getLabelByValue(TValue value, Class<TDict> dictClass) {
        TDict dict = toEnumNullable(value, dictClass);
        return NullMergeUtils.nullMerge(dict, IDict::getLabel, StringUtils.EMPTY);
    }

    /**
     * 获取字典标签对应的枚举值
     * @param label 字典标签
     * @param dictClass 字典枚举类型
     * @param <TDict> 字典枚举类型
     * @return 字典值
     */
    public static <TDict extends IDict<TValue>, TValue extends Serializable> TValue getValueByLabel(String label, Class<TDict> dictClass) {
        TDict dict = toDictNullable(label, dictClass);
        return NullMergeUtils.nullMerge(dict, IDict::getValue);
    }
}
