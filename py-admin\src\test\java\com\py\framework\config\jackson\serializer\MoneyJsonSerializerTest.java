package com.py.framework.config.jackson.serializer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.junit.Assert;
import org.junit.Test;

import java.math.BigDecimal;

/**
 * 金额序列化器测试类
 * <AUTHOR>
 */

public class MoneyJsonSerializerTest {

    /** 对象映射器 */
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void serialize() throws JsonProcessingException {
        TestData testData = new TestData();
        testData.setValue(new BigDecimal("123456789.127456789"));

        String json = objectMapper.writeValueAsString(testData);
        Assert.assertEquals("{\"value\":\"123,456,789.13\"}", json);

        testData.setValue(new BigDecimal("89765"));
        json = objectMapper.writeValueAsString(testData);
        Assert.assertEquals("{\"value\":\"89,765\"}", json);

        testData.setValue(new BigDecimal("89765.02"));
        json = objectMapper.writeValueAsString(testData);
        Assert.assertEquals("{\"value\":\"89,765.02\"}", json);

        testData.setValue(new BigDecimal("89765.20"));
        json = objectMapper.writeValueAsString(testData);
        Assert.assertEquals("{\"value\":\"89,765.2\"}", json);

        testData.setValue(new BigDecimal("100.00"));
        json = objectMapper.writeValueAsString(testData);
        Assert.assertEquals("{\"value\":\"100\"}", json);
    }

    @Data
    public static class TestData{

        @JsonSerialize(using = MoneyJsonSerializer.class)
        BigDecimal value;
    }
}
