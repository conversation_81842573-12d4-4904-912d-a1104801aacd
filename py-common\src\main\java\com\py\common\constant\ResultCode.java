package com.py.common.constant;

/**
 * <AUTHOR> pyr
 * @date : 2022-03-16 16:20
 **/
public enum ResultCode {

    /** 请求成功 */
    WX_SUCCESS(0, "请求成功"),
    /**
     * 获取用户信息失败
     */
    CODE_ERROR(99, "授权无效"),
    /**
     * 参数为空
     */
    ILLEGAL_PARAM(400, "参数为空"),

    MISS(500, "操作失败"),

    /**
     * 系统异常,请稍后再试
     */
    FAILED(500, "系统异常,请稍后再试"),

    /**
     * 请输入楼宇信息
     */
    BUILDING_INFO_NULL(6001,"请输入楼宇信息"),

    /**
     * 请输入小区名称
     */
    VILLAGE_INFO_NULL(6002,"请输入小区名称"),

    /**
     * 楼层名称部分重复，请检查后重试！
     */
    FLOOR_NAME_REPEAT_FAILED(6003, "楼层名称部分重复，请检查后重试！"),

    VISSAGE_ID_NULL(6004, "小区名称不存在，请重新选择！"),

    EXIST_BUILDING(6005, "已存在重复的楼宇名称，请检查后重试"),

    /**
     * 数据出现异常或丢失，请检查后重试！
     */
    DATA_ABNORMAL_OR_MISSING(6006, "数据出现异常或丢失，请检查后重试！"),

    /**
     * 请输入房号信息
     */
    HOUSE_INFO_NULL(6007,"请输入房号信息"),

    /**
     * 请输入楼层信息
     */
    FLOOR_INFO_NULL(6008,"请输入楼层信息"),

    EXIST_HOUSE(6009, "已存在重复的房号，请检查后重试"),

    /**
     * 请输入房号
     */
    HOUSE_NOT_EXIST(6010, "请输入房号"),

    /**
     * 请输入用户信息
     */
    USER_NOT_EXIST(6011, "请输入用户信息"),

    /**
     * 请输入主账号信息
     */
    USER_ID_NOT_EXIST(6012, "请输入主账号信息"),

    /**
     * 请输入被合并账号信息
     */
    OLD_USER_ID_NOT_EXIST(6013, "请输入被合并账号信息"),

    /**
     * 用户不存在或已停用
     */
    USER_IS_NOT_EXIT(6014, "用户不存在或已停用"),

    /**
     * 被合并账号不能是来自微信注册的账号
     */
    USER_CHECK_ERROR(6015, "被合并账号不能是来自微信注册的账号"),
    /**
     * 房源性质不存在
     */
    HOUSE_NATURE_NOT_EXIST(6016, "房源性质不存在"),

    /**
     * 房源不存在，请重新选择
     */
    HOUSE_INFO_NOT_EXIST(6017, "房源不存在，请重新选择"),

    /**
     * 用户不存在，请重新选择
     */
    USER_INFO_NOT_EXIST(6018, "用户不存在，请重新选择"),

    /**
     * 房源已和该用户存在关联
     */
    USER_HOUSE_EXIST_RELATION(6019, "房源已和该用户存在关联"),

    /**
     * 请输入楼层信息
     */
    BUILDING_FLOOR_NULL(6020, "请输入楼层信息"),

    /**
     * 已存在重复的楼层名称，请检查后重试
     */
    EXIST_BUILDING_FLOOR(6021, "已存在重复的楼层名称，请检查后重试"),

    /**
     * 已存在重复的主体名称/编码，请检查后重试
     */
    SAME_NAME_ERROR(6024, "已存在重复的主体名称，请检查后重试"),
    /**
     * 已存在重复的编码，请检查后重试
     */
    SAME_CODE_ERROR(6024, "已存在重复的编码，请检查后重试"),

    /**
     * 楼层数已超过最大值，请检查后重试
     */
    BUILDING_FLOOR_TOO_MUCH(6022, "楼层数已超过最大值，请检查后重试！"),

    /**
     * 已存在重复的资源id,请检查后重试
     */
    SAME_RESOURCE_CODE(6023,"已存在重复的资源ID，请检查后重试"),

    /**
     * 长度超过了1000
     */
    FIXED_ASSETS_LENGTH_EXCEEDS_1000(6024,"固定资产不可以超过1000字")
    ;

    ;

    private Integer code;

    private String desc;

    ResultCode(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
