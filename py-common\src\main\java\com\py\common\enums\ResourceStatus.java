package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 资源状态枚举
 * <AUTHOR>
 */
@AllArgsConstructor
public enum ResourceStatus implements IDict<Integer>{
    /**
     * 使用中
     */
    ENABLE(0,"使用中"),

    /**
     * 已禁用
     */
    DISABLE(1,"已禁用")
    ;

    private final Integer value;
    private final String label;

    @Override
    public String getLabel() {
        return this.label;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
