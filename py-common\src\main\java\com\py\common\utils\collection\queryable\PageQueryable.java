package com.py.common.utils.collection.queryable;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableList;
import com.py.common.utils.collection.queryable.functional.PageQueryRequest;
import lombok.Getter;
import rx.functions.Action1;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

/**
 * 分页延迟查询对象
 * <AUTHOR>
 */
public class PageQueryable<T> implements Iterator<T>, Iterable<T> {

    /** 集合大小 */
    private int size;

    /** 当前页信息 */
    private PageInfo<T> current;

    /** 页大小 */
    @Getter
    private final int pageSize;

    /** 当前索引 */
    private int currentIndex = 0;
    /** 分页查询请求 */
    private final PageQueryRequest<T> pageQueryRequest;
    /** 当前页码 */
    private int currentPageNum = 1;

    public PageQueryable(PageQueryRequest<T> queryRequest){
        this(queryRequest, 10);
    }

    public PageQueryable(PageQueryRequest<T> queryRequest, int pageSize){
        this.pageQueryRequest = queryRequest;
        this.pageSize = pageSize;
    }

    /**
     * 分页For循环
     * @param action 回调
     */
    public void pageFor(Action1<ImmutableList<T>> action){
        List<T> temp = new ArrayList<>(this.pageSize);
        for(T t : this){
            temp.add(t);
            if(temp.size() == this.pageSize){
                action.call(ImmutableList.copyOf(temp));
                temp.clear();
            }else if(this.hasNext() == false){
                action.call(ImmutableList.copyOf(temp));
                temp.clear();
            }
        }
    }

    /** 初始化 */
    private void init(){
        if(this.currentIndex != 0){
            return;
        }
        // 初始化当前页信息
        this.current = this.pageQueryRequest.query(this.currentPageNum, this.pageSize);
        this.setSize(this.current.getTotal());
    }

    public int size() {
        if(this.current == null){
            this.init();
        }
        return this.size;
    }

    /**
     * 获取当前页信息
     * @return 当前页信息
     */
    public PageInfo<T> getCurrent() {
        if(this.current == null){
            this.init();
        }
        return current;
    }

    /**
     * 设置集合大小
     * @param total 集合大小
     */
    private void setSize(long total) {
        if(total > Integer.MAX_VALUE){
            throw new UnsupportedOperationException("集合大小超出最大值");
        }
        this.size = (int) total;
    }

    /**
     * 集合是否为空
     * @return true: 空, false: 非空
     */
    public boolean isEmpty() {
        return this.size == 0;
    }

    @Override
    public Iterator<T> iterator() {
        return this;
    }

    /**
     * 返回是否有下一个元素
     * @return true: 有, false: 无
     */
    @Override
    public boolean hasNext() {
        return this.currentIndex + 1 <= this.size();
    }

    /**
     * 返回迭代中的下一个元素。
     * @return 迭代中的下一个元素
     * @throws NoSuchElementException 无可迭代元素
     */
    @Override
    public T next() throws NoSuchElementException {
        this.currentIndex++;

        int precedingPagingSize = (this.currentPageNum - 1) * this.pageSize;
        int currentPageMaxIndex = precedingPagingSize + this.getCurrent().getSize();
        boolean needQuery = this.currentIndex > currentPageMaxIndex;
        if(needQuery){
            if(this.getCurrent().isHasNextPage() == false){
                throw new NoSuchElementException("无可迭代元素");
            }
            // 获取下一页分页数据
            this.currentPageNum++;
            this.current = this.pageQueryRequest.query(this.currentPageNum, this.pageSize);
            precedingPagingSize = (this.currentPageNum - 1) * this.pageSize;
        }
        int localIndex = this.currentIndex - precedingPagingSize - 1;

        if(localIndex >= this.getCurrent().getSize()){
            throw new NoSuchElementException("无可迭代元素");
        }
        return this.getCurrent().getList().get(localIndex);
    }

    /**
     * 转换为List
     * @return List
     */
    public List<T> toList(){
        return this.stream().collect(Collectors.toList());
    }

    /**
     * 转换为流
     * @return 流
     */
    public Stream<T> stream() {
        return StreamSupport.stream(spliterator(), false);
    }

    /**
     * 转换为并行流
     * @return 并行流
     */
    public Stream<T> parallelStream() {
        return StreamSupport.stream(spliterator(), true);
    }
}
