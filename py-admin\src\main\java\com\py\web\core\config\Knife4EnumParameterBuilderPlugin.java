package com.py.web.core.config;

import com.py.common.enums.IDict;
import com.py.common.utils.EnumUtils;
import com.py.common.utils.collection.ListUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import springfox.documentation.service.AllowableListValues;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.ExpandedParameterBuilderPlugin;
import springfox.documentation.spi.service.contexts.ParameterExpansionContext;

import java.util.List;

/**
 * Knife4j枚举属性构建插件
 * <AUTHOR>
 */
@Log4j2
@Component
@SuppressWarnings("all")
public class Knife4EnumParameterBuilderPlugin implements ExpandedParameterBuilderPlugin {

    @Override
    public void apply(ParameterExpansionContext context) {
        Class<?> erasedType = context.getFieldType().getErasedType();
        if(IDict.class.isAssignableFrom(erasedType) == false){
            return;
        }

        List<IDict<?>> enumList = (List<IDict<?>>) EnumUtils.getEnumConstants(erasedType);
        List<String> valueList = ListUtil.map(enumList, dict -> dict.getValue().toString());
        AllowableListValues allowableListValues = new AllowableListValues(valueList,"LIST");

        context.getParameterBuilder()
                .allowableValues(allowableListValues);
        context.getRequestParameterBuilder()
                .query(q -> q.enumerationFacet(f -> f.allowedValues(allowableListValues)));
    }

    @Override
    public boolean supports(DocumentationType documentationType) {
        return true;
    }
}
