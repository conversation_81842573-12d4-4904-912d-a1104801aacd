package com.py.flow.domain.dto.flow;

import com.py.flow.domain.enums.ApprovalBizType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 打印流程审批DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("打印流程审批DTO")
public class BatchPrintFlowApprovalParam {

    /** 审批业务类型 */
    @ApiModelProperty(value = "审批业务类型", required = true)
    @NotNull(message = "审批业务类型不能为空")
    private ApprovalBizType bizType;

    /** 业务ID */
    @ApiModelProperty(value = "业务ID", required = true)
    @NotNull(message = "业务ID不能为空")
    private Long bizId;

}
