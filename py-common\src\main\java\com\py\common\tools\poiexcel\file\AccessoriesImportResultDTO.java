package com.py.common.tools.poiexcel.file;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 导入结果
 * <AUTHOR>
 */
@ApiModel("导入返回结果")
@Data
public class AccessoriesImportResultDTO {

    /** 错误信息 */
    @ApiModelProperty("错误信息")
    private List<RowVerifyErrorVO> infoList;


    /** 是否成功 */
    @ApiModelProperty("是否成功")
    private Boolean isSuccess;

    /** 错误模板 */
    @ApiModelProperty("错误模板")
    private byte[] failTemplate;

    /** 文件地址 */
    @ApiModelProperty("文件地址")
    private String fileUrl;


    /**
     * 返回失败结果
     * @param errorList 错误信息
     * @param failTemplate 错误模板
     * @return 失败结果
     */
    public static AccessoriesImportResultDTO failure(List<RowVerifyErrorVO> errorList, byte[] failTemplate) {
        AccessoriesImportResultDTO importResult = new AccessoriesImportResultDTO();
        importResult.setIsSuccess(false);
        importResult.setFailTemplate(failTemplate);
        importResult.setInfoList(errorList);

        return importResult;
    }
}
