package com.py.common.tools.verify.verifyprovider.impl;


import com.py.common.tools.verify.VerifyTools;
import com.py.common.tools.verify.enums.VerifyType;
import com.py.common.tools.verify.verifypackage.LengthVerifyPackage;
import com.py.common.tools.verify.verifyprovider.VerifyProvider;
import com.py.common.utils.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 小数位数验证提供者
 * <AUTHOR>
 */
@Component
public class DecimalLengthVerifyProvider implements VerifyProvider {

    /**
     * 支持的验证类型
     * @return 支持的验证类型
     */
    @Override
    public VerifyType supportedVerifyType() {
        return VerifyType.DecimalLength;
    }

    /**
     * 验证
     * @param target 验证目标
     * @return 验证结果 true:验证通过  false:验证失败
     * @throws Exception 验证异常
     */
    @Override
    public boolean verify(Object target) throws Exception {
        if(target == null) {
            return false;
        }

        if(target instanceof LengthVerifyPackage == false) {
            throw new IllegalArgumentException("小数位数校验入参应使用 LabelVerifyPackage.valueOf 作为入参");
        }

        LengthVerifyPackage verifyPackage = (LengthVerifyPackage) target;
        if(verifyPackage.getValue() instanceof String == false) {
            throw new IllegalArgumentException("小数位数校验仅支持字符串");
        }

        String decimalPart = StringUtils.getDecimalPart((String) verifyPackage.getValue());

        if(decimalPart == null) {
            return verifyPackage.getMin() == null || verifyPackage.getMin() == 0;
        }

        return VerifyTools.lengthVerify(decimalPart.length(), verifyPackage.getMin(), verifyPackage.getMax());
    }
}
