package com.py.web.controller.finance;

import com.github.pagehelper.PageInfo;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.SecurityUtils;
import com.py.project.projectresource.domain.vo.ProjectResourceListVO;
import com.py.project.projectresource.service.IProjectResourceService;
import com.py.project.pyexecutiveflow.asynctask.BusinessFinancialServiceImpl;
import com.py.project.pyexecutiveflow.asynctask.FinancialInfoServiceImpl;
import com.py.project.pyexecutiveflow.asynctask.FinancialServiceImpl;
import com.py.project.pyexecutiveflow.domain.query.PyFinancialStatementQuery;
import com.py.project.pyexecutiveflow.domain.vo.PyFinancialInfoCheckBoxVO;
import com.py.project.pyexecutiveflow.domain.vo.PyFinancialPerformanceVO;
import com.py.project.pyexecutiveflow.service.IPyExecutiveFlowService;
import com.py.project.pysalemanagesourceflow.domain.query.ConfirmIncomeQuery;
import com.py.system.mainstayparam.domain.vo.ISystemMainstayVO;
import com.py.system.mainstayparam.domain.vo.MainstayParamListVO;
import com.py.web.controller.task.EnterpriseStatementsTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2023/8/2 17:12
 */
@Api(tags = "企业财报")
@RestController
@RequestMapping("/projectrebate/")
public class ProjectFinancialController extends BaseController {

    /** 执行人员业绩服务 */
    @Resource
    private IPyExecutiveFlowService pyExecutiveFlowService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 项目资源服务 */
    @Resource
    private IProjectResourceService  projectResourceService;

    /** 企业财报定时任务 */
    @Resource
    private EnterpriseStatementsTask enterpriseStatementsTask;

    /**
     * 分页查询业绩报表数据
     *
     * @param query 项目返点管理表查询参数
     * @return 项目返点管理表分页
     */
    @ApiOperation("财报分页")
    @PreAuthorize("@ss.hasPermi('projectrebate::list')")
    @PostMapping("/pageFinancial")
    public R<PyFinancialPerformanceVO> pageFinancial(@RequestBody PyFinancialStatementQuery query) {
        PyFinancialPerformanceVO pyFinancialPerformanceVO = this.pyExecutiveFlowService.selectFinancial(query);
        return R.success(pyFinancialPerformanceVO);
    }

    /**
     * 财报明细分页
     *
     * @param query
     * @return
     */
    @ApiOperation("财报明细分页")
    @PreAuthorize("@ss.hasPermi('projectrebate::list')")
    @PostMapping("/financialInfoPage")
    public R<PageInfo<ISystemMainstayVO>> pageFinancialInfo(@RequestBody PyFinancialStatementQuery query) {
        PageInfo<ISystemMainstayVO> systemVoList = this.pyExecutiveFlowService.selectFinancialInfoPage(query, true);
        return R.success(systemVoList);
    }

    /**
     * 财报明细多选
     *
     * @param query
     * @return 项目返点管理表分页
     */
    @ApiOperation("财务报表多选框")
    @PreAuthorize("@ss.hasPermi('projectrebate::list')")
    @PostMapping("/checkbox")
    public R<PyFinancialInfoCheckBoxVO> checkBox(@RequestBody PyFinancialStatementQuery query) {
        PyFinancialInfoCheckBoxVO pyFinancialInfoCheckBoxVO = this.pyExecutiveFlowService.checkBox(query);
        return R.success(pyFinancialInfoCheckBoxVO);
    }

    /**
     * 确认收入明细
     * @param query
     * @return
     */
    @ApiOperation("确认收入明细")
    @ApiModelProperty("确认收入明细(业务财报部门实时)")
    @PostMapping("/confirmIncome")
    public R<PageInfo<ProjectResourceListVO>> confirmIncome(@RequestBody ConfirmIncomeQuery query) {
        PageInfo<ProjectResourceListVO> projectResourceListVOList = this.projectResourceService.selectConfirmIncome(query);
        return R.success(projectResourceListVOList);
    }

    /**
     * 确认收入明细(业务财报部门快照)
     * @param query 业务财报时间,部门,项目id条件
     * @return 确认收入明细列表
     */
    @ApiOperation("确认收入明细")
    @ApiModelProperty("确认收入明细(业务财报部门快照)")
    @PostMapping("/confirmIncome/dept")
    public R<PageInfo<ProjectResourceListVO>> businessDeptconfirmIncome(@RequestBody ConfirmIncomeQuery query){
        PageInfo<ProjectResourceListVO> projectResourceListVOList = this.projectResourceService.businessDeptconfirmIncome(query);
        return R.success(projectResourceListVOList);
    }

    /**
     * 导出财务报表
     * @param
     * @param query 导出查询参数
     */
    @ApiOperation("导出财务报表")
    @PostMapping("/exportFinancialPage")
    public R<String> exportFinancialPage( @RequestBody PyFinancialStatementQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("财务报表数据", TaskType.Export, query, FinancialInfoServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 导出客户合同信息
     * @param
     * @param query 导出查询参数
     */
    @ApiOperation("导出财务明细报表")
    @PostMapping("/exportFinancial")
    public R<String> export( @RequestBody PyFinancialStatementQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("财务报表数据", TaskType.Export, query, FinancialServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 查询企业财报下有权限的主体列表
     *
     * @return 有权限的主体列表
     */
    @ApiOperation("查询企业财报下有权限的主体列表")
    @GetMapping("/listSystemMainstayParam")
    public R<List<MainstayParamListVO>> listSystemMainstayParam() {
        List<MainstayParamListVO> voList = this.pyExecutiveFlowService.listSystemMainstayParam();
        return R.success(voList);
    }

    /**
     * 发送业务财务
     */
    @ApiOperation("发送业务财务消息")
    @PostMapping("/sendFinancialMeg")
    public void sendFinancialMeg(){
        this.enterpriseStatementsTask.generateFinancial();
    }

    /**
     * 企业财报 - 确认收入明细
     * @param query
     * @return
     */
    @ApiOperation("企业财报 - 确认收入明细")
    @PostMapping("/confirmFinanceIncome")
    public R<PageInfo<ProjectResourceListVO>> confirmFinanceIncome(@RequestBody ConfirmIncomeQuery query) {
        PageInfo<ProjectResourceListVO> projectResourceListVOList = this.projectResourceService.selectConfirmFinanceIncome(query);
        return R.success(projectResourceListVOList);
    }


    /**
     * 业务财报列表 (实时部门)
     *
     * @param query 业务财报查询参数
     * @return 业务财报列表
     */
    @ApiOperation("业务财报列表")
    @PreAuthorize("@ss.hasPermi('projectrebate::list')")
    @PostMapping("/pageBusinessFinancial")
    public R<PyFinancialPerformanceVO> pageBusinessFinancial(@RequestBody PyFinancialStatementQuery query) {
        PyFinancialPerformanceVO pyFinancialPerformanceVO = this.pyExecutiveFlowService.pageBusinessFinancial(query);
        return R.success(pyFinancialPerformanceVO);
    }

    /**
     * 分页查询业务财报明细(实时部门)
     *
     * @param query 业务财报查询参数
     * @return 业务财报列表
     */
    @ApiOperation("分页查询业务财报明细")
    @PreAuthorize("@ss.hasPermi('projectrebate::list')")
    @PostMapping("/selectBusinessFinancialDetails")
    public R<PageInfo<ISystemMainstayVO>> selectBusinessFinancialDetails(@RequestBody PyFinancialStatementQuery query) {
        return R.success(this.pyExecutiveFlowService.selectBusinessFinancialDetails(query,true));
    }


    /**
     * 业务财报明细金额合计(实时部门)
     * @param query 业务财报查询参数
     * @return 业务财报明细勾选合计数据
     */
    @ApiOperation("查询业务财报明细金额合计")
    @PreAuthorize("@ss.hasPermi('projectrebate::list')")
    @PostMapping("/selectBusinessFinancial")
    public R<PyFinancialInfoCheckBoxVO> selectBusinessFinancial(@RequestBody PyFinancialStatementQuery query) {
        return R.success(this.pyExecutiveFlowService.selectBusinessFinancial(query));
    }


    /**
     * 导出业务财报(实时部门)
     *
     * @param query 查询勾选导出
     * @return 操作结果
     */
    @ApiOperation("导出业务财报")
    @PostMapping("/exportBusinessFinancial")
    public R<String> exportBusinessFinancial(@RequestBody PyFinancialStatementQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("财务报表数据", TaskType.Export, query, BusinessFinancialServiceImpl.class);
        return R.success("提交成功");
    }

}
