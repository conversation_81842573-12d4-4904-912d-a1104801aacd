package com.py.crm.customerdevote.domain.dto;

import com.py.common.tools.poiexcel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 收入坏账信息导出模型
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
@Data
@ApiModel("客户贡献收入坏账信息导出模型" )
public class CustomerDevoteBadDebtExportModel {
    private static final long serialVersionUID = 1L;



    /** 坏账金额（元）*/

    @ApiModelProperty("历史坏账金额（元）" )
    private BigDecimal totalBadDebtAmount;
    @Excel(name = "历史坏账金额（元）")
    private String totalBadDebtAmountStr;
    /** 项目名称*/
    @Excel(name = "项目名称")
    @ApiModelProperty("项目名称" )
    private String projectName;

    /** 派芽合作主体名称*/
    @Excel(name = "派芽合作主体")
    @ApiModelProperty("派芽合作主体名称" )
    private String pyMainstayName;

    /** 客户名称*/
    @Excel(name = "客户名称")
    @ApiModelProperty("客户名称" )
    private String customerName;

    /** 客户合作主体名称*/
    @Excel(name = "客户合作主体")
    @ApiModelProperty("客户合作主体名称" )
    private String customerMainstayName;
    /** 品牌线*/
    @Excel(name = "品牌/业务线")
    @ApiModelProperty("品牌线" )
    private String brandLine;
    /** 项目id*/
    @ApiModelProperty("项目id" )
    private Long projectId;

    /** 派芽主体Id*/
    @ApiModelProperty("派芽主体Id" )
    private Long pyMainstayId;


}
