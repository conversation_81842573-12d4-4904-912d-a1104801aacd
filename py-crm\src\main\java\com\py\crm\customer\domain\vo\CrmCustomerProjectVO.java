package com.py.crm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 客户比稿项目详情
 * <AUTHOR>
 */
@Data
public class CrmCustomerProjectVO {

    /** 客户id */
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 比稿项目id */
    @ApiModelProperty("比稿项目id")
    private Long comparedDraftId;

    /** 比稿项目id */
    private Long projectId;

    /** 比稿金额 */
    private BigDecimal comparedDraftMoney;

}
