package com.py.common.approve;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 付款列表视图模型
 */
@Data
@ApiModel("付款列表视图模型")
public class RefundInfoListDTO {
    private static final long serialVersionUID = 1L;

    /** 自增id */
    @ApiModelProperty("自增id")
    private Long id;

    /** 项目资源id */
    @ApiModelProperty("项目资源id")
    private Long projectResourceId;

    /** 项目id */
    @ApiModelProperty("项目id")
    private Long projectId;

    /** 资源id */
    @ApiModelProperty("资源id")
    private Long resourceId;

    /** 选资源类型 */
    @ApiModelProperty("选资源类型")
    private Integer type;

    /** 资源code */
    @ApiModelProperty("资源code")
    private String resourceCode;

    /** 资源名称 */
    @ApiModelProperty("资源名称")
    private String resourceName;

    /** 合作平台 */
    @ApiModelProperty("合作平台")
    private String cooperationPlatform;

    /** 付款渠道id */
    @ApiModelProperty("付款渠道id")
    private Long channelsId;

    /** 付款渠道 */
    @ApiModelProperty("付款渠道")
    private String channelsName;


    /** 资源类型 */
    @ApiModelProperty("资源类型")
    private Integer resourceType;
    private String resourceTypeName;

    /** 合作方式 */
    @ApiModelProperty("合作方式")
    private Integer cooperationMethod;
    private String cooperationMethodName;

    /** 资源退款账户名称*/
    @ApiModelProperty("资源退款账户名称")
    private String refundAccountName;

    /** 资源退款渠道id*/
    @ApiModelProperty("资源退款渠道id")
    private Long refundChannelsId;

    /** 资源退款渠道名称*/
    @ApiModelProperty("资源退款渠道名称")
    private String refundChannelsName;

    /** 退款总额*/
    @ApiModelProperty("退款总额")
    private BigDecimal refundAmount;

    /** 已退金额 */
    @ApiModelProperty("已退金额")
    private BigDecimal returnedAmount;

    /** 未退金额 */
    @ApiModelProperty("未退金额")
    private BigDecimal unReturnedAmount;

    /** 本次退款金额*/
    @ApiModelProperty("本次退款金额")
    private BigDecimal currentRefundAmount;

    /** 派芽收款账户*/
    @ApiModelProperty("派芽收款账户")
    private String collectionAccount;

}