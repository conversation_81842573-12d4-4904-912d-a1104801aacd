package com.py.crm.connection.domain.dto;

import com.py.crm.connectionemployment.domain.dto.ConnectionEmploymentDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 人脉管理表数据传输模型
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
@ApiModel("人脉管理表数据传输模型" )
public class ConnectionDTO {
    private static final long serialVersionUID = 1L;

    /** 自增id */
    @ApiModelProperty("自增id" )
    private Long id;

    /** 人脉Id */
    @ApiModelProperty("人脉Id" )
    private Long connectionId;

    /** 客户名称 */
    @ApiModelProperty("客户名称" )
    @NotBlank(message = "客户姓名不能为空")
    @Size(max = 100,message = "客户姓名长度不能超过100")
    private String connectionName;

    /** 状态（0无效；1有效） */
    @ApiModelProperty("状态（0无效；1有效）" )
    private Integer status;

    /** 电话 */
    @ApiModelProperty("电话" )
    @NotBlank(message = "电话不能为空")
    @Size(max = 100,message = "电话长度不能超过100")
    private String phone;

    /** 微信 */
    @ApiModelProperty("微信" )
    @Size(max = 100,message = "微信长度不能超过100")
    private String wechatNumber;

    /** 钉钉/飞书/其它 */
    @ApiModelProperty("钉钉/飞书/其它" )
    @Size(max = 100,message = "钉钉/飞书/其它长度不能超过100")
    private String otherNumber;

    /** 现任职企业id(客户id) */
    @ApiModelProperty("现任职企业id(客户id)" )
    private Long customerId;

    /** 现任职企业（手动输入） */
    @ApiModelProperty("现任职企业（手动输入）" )
    private String currentEmployer;

    /** 负责品牌/业务线 */
    @ApiModelProperty("负责品牌/业务线" )
    private List<String> responsibleBrand;

    /** 行业类目(来源于数据字典) */
    @ApiModelProperty("行业类目(来源于数据字典)" )
    private List<Long> industryCategory;

    /** 所在部门 */
    @ApiModelProperty("所在部门" )
    @Size(max = 100,message = "所在部门长度不能超过100")
    private String departmentName;

    /** 岗位名称 */
    @ApiModelProperty("岗位名称" )
    @Size(max = 100,message = "岗位长度不能超过100")
    private String postName;

    /** 对派芽信任度（数据字典） */
    @ApiModelProperty("对派芽信任度（数据字典）" )
    private String pyTrustLevel;

    /** 人脉地址 */
    @ApiModelProperty("人脉地址" )
    @Size(max = 100,message = "人脉地址长度不能超过100")
    private String connectionAddress;

    @ApiModelProperty("备注" )
    @Size(max = 1000,message = "备注长度不能超过1000")
    private String remark;

    /** 目标服务人员id */
    @ApiModelProperty("目标服务人员id" )
    private Long serviceUserId;

    /** 创建人部门 */
    @ApiModelProperty("创建人部门" )
    private String createDept;

    /** 更新人部门 */
    @ApiModelProperty("更新人部门" )
    private String updateDept;
    @ApiModelProperty("从业经历" )
    private List<ConnectionEmploymentDTO> connectionEmploymentDTOS;

    /** 是否是审批驳回修改 */
    @ApiModelProperty(hidden = true)
    public Boolean isApprovalEdit;

    /** 上次的审批ID */
    @ApiModelProperty("上次的审批ID" )
    private Long lastFlowInstanceId;
}
