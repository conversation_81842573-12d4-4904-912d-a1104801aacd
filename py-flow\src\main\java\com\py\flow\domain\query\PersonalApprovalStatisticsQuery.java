package com.py.flow.domain.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.py.common.utils.EnumUtils;
import com.py.flow.domain.enums.ApprovalKind;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 个人审批统计查询对象
 * <AUTHOR>
 */
@Data
@ApiModel("个人审批统计查询对象")
public class PersonalApprovalStatisticsQuery {

    /** 业务类型 */
    @ApiModelProperty(value = "业务类型", required = true)
    @NotNull(message = "业务类型不能为空")
    private ApprovalKind businessType;

    public void setBusinessType(Integer businessType) {
        this.businessType = EnumUtils.toEnumNullable(businessType, ApprovalKind.class);
    }


    /** 流程id */
    @ApiModelProperty("流程id")
    private List<String> processInstanceIdList;

    /** 第一条新版审批ID */
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Long firstNewVersionFlowInstanceId;
}
