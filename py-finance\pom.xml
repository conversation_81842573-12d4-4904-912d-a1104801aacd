<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>py</artifactId>
        <groupId>com.py</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>py-finance</artifactId>

    <description>
        财务模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.py</groupId>
            <artifactId>py-common</artifactId>
        </dependency>

        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.2</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.py</groupId>
            <artifactId>py-system</artifactId>
        </dependency>
        <dependency>
            <groupId>com.py</groupId>
            <artifactId>py-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>com.py</groupId>
            <artifactId>py-project</artifactId>
        </dependency>
        <dependency>
            <groupId>com.py</groupId>
            <artifactId>py-project</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>
