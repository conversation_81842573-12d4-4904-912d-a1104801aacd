package com.py.common.config.dict;

import com.google.common.collect.ImmutableList;
import com.py.common.enums.IDict;

import java.util.ArrayList;
import java.util.List;

/**
 * 字典模组
 * <AUTHOR>
 */
public abstract class AbstractDictModule {

    /** 注册的字典列表 */
    private final List<Class<IDict<?>>> dictList = new ArrayList<>();

    /** 初始化 */
    protected abstract void init();

    /**
     * 获取字典列表
     * @return 字典列表-不可变
     */
    public ImmutableList<Class<IDict<?>>> getDictList(){
        return ImmutableList.copyOf(this.dictList);
    }

    /**
     * 注册字典
     * @param dictClass 字典类
     */
    protected <T extends IDict<?>> void registered(Class<T> dictClass){
        //noinspection unchecked
        this.dictList.add((Class<IDict<?>>) dictClass);
    }

}
