package com.py.common.constant;

/**
 * 长度常量定义
 *
 * @<PERSON> zhao<PERSON><PERSON>
 * @Date 2021/8/3 15:33
 */
public class LengthConstant {

    /**
     * 10个字符
     */
    public static final int LENGTH_TEN = 10;

    /**
     * 20个字符
     */
    public static final int LENGTH_TWENTY = 20;

    /**
     * 100个字符
     */
    public static final int LENGTH_ONE_HUNDRED = 100;

    /**
     * 100个字符
     */
    public static final String LENGTH_ONE_HUNDRED_LONG = "100";

    /**
     * 2个字符
     */
    public static final int TWO = 2;

    /**
     * 15个字符
     */
    public static final int FIFTEEN = 15;

    /**
     * 18个字符
     */
    public static final int EIGHTEEN = 18;

    /**
     * 19个字符
     */
    public static final int NINETEEN = 19;

    /**
     * 50个字符
     */
    public static final int FIFTY = 50;

    /**
     * 30个字符
     */
    public static final int THIRTY = 30;

    /**
     * 300个字符
     */
    public static final int LENGTH_THREE_HUNDRED = 300;

    /**
     * 11个字符
     */
    public static final int ELEVEN = 11;

    /**
     * 0
     */
    public static final Long ZERO_LONG = 0L;

    /**
     * 100
     */
    public static final Long ONE_HUNDRED_LONG = 100L;

    /**
     * 0
     */
    public static final String ZERO_STRING = "0";

    /**
     * 0
     */
    public static final int ZERO = 0;


    /**
     * 0
     */
    public static final int ONE = 1;


}
