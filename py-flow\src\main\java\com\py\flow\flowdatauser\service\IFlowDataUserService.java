package com.py.flow.flowdatauser.service;

import com.py.flow.flowdatauser.domain.vo.ApproveUserVO;
import com.py.flow.flowinstance.domain.FlowInstance;
import com.py.flow.tools.flownotifier.model.FlowUserEvent;

import java.util.List;
import java.util.Map;

/**
 * 审批数据与人员关联Service接口
 *
 * <AUTHOR>
 * @date 2023-09-04
 */
public interface IFlowDataUserService {

    /**
     * 处理用户审批事件
     * @param event 用户审批事件
     * @return 是否成功
     */
    boolean handleFlowUserEvent(FlowUserEvent event);

    /**
     * 根据业务id查询审批人
     * @param bizIdList 业务id
     * @return 业务审批人Map (业务Id, 审批人)
     */
    Map<Long,String> listFlowDataUserByBizId(List<Long> bizIdList);

    /**
     * 赋值审批人
     * @param approveUserVOList 所要赋值的数据
     */
    void setApprovalUser(List<? extends ApproveUserVO> approveUserVOList);

    /**
     * 添加上一流程审批人到当前流程
     * @param lastFlowInstance 上一流程实例
     * @param newFlowInstance 当前流程实例
     */
    void addLastFlowHistoricalApproverToCurrentFlow(FlowInstance lastFlowInstance, FlowInstance newFlowInstance);
}
