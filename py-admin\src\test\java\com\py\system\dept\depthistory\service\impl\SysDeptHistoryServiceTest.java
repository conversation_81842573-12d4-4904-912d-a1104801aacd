package com.py.system.dept.depthistory.service.impl;

import com.py.common.utils.JsonUtil;
import com.py.system.dept.depthistory.domain.vo.DeptTimeLine;
import com.py.system.dept.depthistory.service.ISysDeptHistoryService;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * 历史部门服务测试类
 * <AUTHOR>
 */
//@SpringBootTest
//@RunWith(SpringRunner.class)
public class SysDeptHistoryServiceTest {

    /** 历史部门服务 */
    @Resource
    private ISysDeptHistoryService deptHistoryService;

    @Ignore
    @Test
    public void getHistoryDeptTree() {
        List<DeptTimeLine> historyDeptTree = this.deptHistoryService.getHistoryDeptTree(LocalDate.of(2022, 1, 1), LocalDate.of(2023, 8, 31));
        JsonUtil.obj2StringPretty(historyDeptTree);
    }
}
