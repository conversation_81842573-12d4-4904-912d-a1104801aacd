package com.py.common.utils.file;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

/**
 * 自动删除文件的输出流
 * <AUTHOR>
 */
public class AutoDeleteFileOutputStream extends FileInputStream {

    /** 关闭锁 */
    private final Object closeLock = new Object();
    /** 流打开的文件 */
    private final File file;
    /** 是否已关闭 */
    private volatile boolean closed = false;

    public AutoDeleteFileOutputStream(File file) throws FileNotFoundException {
        super(file);
        this.file = file;
    }

    @Override
    public void close() throws IOException {
        synchronized (closeLock) {
            if (closed) {
                return;
            }
            closed = true;
        }
        super.close();
        if(this.file != null && this.file.exists()){
            this.file.delete();
        }
    }
}
