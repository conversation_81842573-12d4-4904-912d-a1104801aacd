package com.py.flow.flowinstance.domain.dto;

import com.py.common.oss.model.OssUploadResult;

import java.util.List;

/**
 * 通用审批操作信息
 * <AUTHOR>
 */
public interface ICommonApprovalOperateInfo {

    /**
     * 获取流程实例ID
     * @return 流程实例ID
     */
    Long getFlowInstanceId();

    /**
     * 获取任务ID
     * @return 任务ID
     */
    String getTaskId();

    /**
     * 获取审批意见
     * @return 审批意见
     */
    String getComment();

    /**
     * 获取附件列表
     * @return 附件列表
     */
    List<OssUploadResult> getFileAnnexList();
}
