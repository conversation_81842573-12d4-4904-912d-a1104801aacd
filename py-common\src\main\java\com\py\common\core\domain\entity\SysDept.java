package com.py.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.py.common.core.domain.BaseEntity;
import com.py.common.enums.Enable;
import com.py.common.typehandler.impl.LongSetTypeHandler;
import com.py.common.utils.tree.IAncestors;
import com.py.common.utils.tree.ITreeNode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 部门表 sys_dept
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_dept", autoResultMap = true)
public class SysDept
        extends BaseEntity
        implements ITreeNode<SysDept>, IAncestors<SysDept> {

    /** 部门ID */
    @TableId
    private Long deptId;

    /** 父部门ID */
    private Long parentId;

    /** 祖级列表 */
    @TableField(typeHandler = LongSetTypeHandler.class)
    private List<Long> ancestors;

    /** 部门名称 */
    private String deptName;

    /** 员工人数 */
    private Integer employeeNum;

    /** 显示顺序 */
    private Integer orderNum;

    /** 部门状态  */
    private Enable status;

    /** 子部门 */
    @TableField(exist = false)
    private List<SysDept> children = new ArrayList<>();

    /**
     * 获取树Id
     * @return 树Id
     */
    @Override
    public Long getTreeId() {
        return this.getDeptId();
    }

    /**
     * 获取节点名称
     * @return 节点名称
     */
    @Override
    public String getNodeName() {
        return this.getDeptName();
    }
}
