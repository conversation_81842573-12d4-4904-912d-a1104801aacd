package com.py.common.tools.excelhelper;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.py.common.constant.Constants;
import com.py.common.exception.ServiceException;
import com.py.common.tools.excelhelper.functions.SameDataRowVerifyErrorGenerator;
import com.py.common.tools.excelhelper.functions.SameDataSerialNumberErrorGenerator;
import com.py.common.tools.multisheetexcelexporter.IMultiSheetExcelExporter;
import com.py.common.tools.multisheetexcelexporter.MultiSheetExcelExporter;
import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;
import com.py.common.tools.verify.domain.ISerialNumber;
import com.py.common.tools.verify.domain.RowVerifyError;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import lombok.NonNull;
import lombok.var;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import rx.functions.Action2;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 导出报表帮助类
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class ExcelHelper {

    /**
     * 设置导出的序列号
     * @param exportModelList 导出模型列表
     * @param serialNumberSetter 序列号设置器
     * @param startNo 起始序列号
     * @param <T> 需要设置序列号的列表元素类型
     */
    public static <T> void setSerialNumber(List<T> exportModelList, Action2<T, Integer> serialNumberSetter, int startNo) {
        if(ListUtil.isEmpty(exportModelList)) {
            return;
        }

        int index = startNo;
        for(T item : exportModelList) {
            serialNumberSetter.call(item, index++);
        }
    }

    /**
     * 设置导出的序列号, 从 1 开始
     * @param exportModelList 导出模型列表
     * @param serialNumberSetter 序列号设置器
     * @param <T> 需要设置序列号的列表元素类型
     */
    public static <T> void setSerialNumber(List<T> exportModelList, Action2<T, Integer> serialNumberSetter) {
        setSerialNumber(exportModelList, serialNumberSetter, 1);
    }

    /**
     * 设置导出的序列号
     * @param exportModelList 导出模型列表
     * @param startNo 起始序列号
     * @param <T> 需要设置序列号的列表元素类型
     */
    public static <T extends ISerialNumber> void setSerialNumber(List<T> exportModelList, int startNo) {
        setSerialNumber(exportModelList, ISerialNumber::setSerialNumber, startNo);
    }

    /**
     * 设置导出的序列号, 从 1 开始
     * @param exportModelList 导出模型列表
     * @param <T> 需要设置序列号的列表元素类型
     */
    public static <T extends ISerialNumber> void setSerialNumber(List<T> exportModelList) {
        setSerialNumber(exportModelList, ISerialNumber::setSerialNumber, 1);
    }

    /**
     * 导入表格文件格式校验
     * @param importExcelFile 导入的表格文件
     */
    public static void importExcelSchemeVerify(MultipartFile importExcelFile) {
        if(importExcelFile == null
                || importExcelFile.isEmpty()
                || StringUtils.isBlank(importExcelFile.getOriginalFilename())) {
            throw new ServiceException("上传文件不能为空");
        }

        String fileSuffix = "." + FileUtil.getSuffix(importExcelFile.getOriginalFilename().trim());
        if(Constants.XLSX.equals(fileSuffix) == false
                && Constants.XLS.equals(fileSuffix) == false) {
            throw new ServiceException("文件类型错误: 请上传 .xlsx 或 .xls 文件");
        }
    }

    /**
     * 将导入文件流读取读取为字节数组
     * @param importFile 导入文件
     * @return 读取出的字节流
     */
    public static byte[] readImportByteData(MultipartFile importFile) throws IOException {
        try(InputStream fileStream = importFile.getInputStream();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()
        ) {
            IoUtil.copy(fileStream, byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        }
    }

    /** 错误信息版 */
    public static class ErrorMsg {

        /**
         * 验证是否存在重复项目
         * <p>重复的空元素也被认为是重复项目</p>
         * @param list 需要验证重复项的列表
         * @param sameVerifyItemSelector 重复项选择器
         * @param serialNumberSelector 导出元素序列号选择器
         * @param errorMsgBuilder 错误信息生成器
         * @param <T> 导入元素类型
         * @param <TSameVerifyItem> 重复项目类型
         * @return 重复内容错误消息, 没有错误内容返回null
         */
        public static <T, TSameVerifyItem> String verifySameItem(
                List<T> list,
                @NonNull Function<T, TSameVerifyItem> sameVerifyItemSelector,
                @NonNull Function<T, Integer> serialNumberSelector,
                @NonNull SameDataSerialNumberErrorGenerator errorMsgBuilder) {
            if(ListUtil.isEmpty(list)) {
                return null;
            }

            var sameItemList = ListUtil.sameItemList(list, sameVerifyItemSelector);
            if(ListUtil.isEmpty(sameItemList)) {
                return null;
            }
            return generateSameErrorMsg(sameItemList, serialNumberSelector, errorMsgBuilder);
        }

        /**
         * 验证是否存在重复项目
         * <p>重复的空元素也被认为是重复项目</p>
         * @param list 需要验证重复项的列表
         * @param sameVerifyItemSelector 重复项选择器
         * @param errorMsgBuilder 错误信息生成器
         * @param <T> 导入元素类型
         * @param <TSameVerifyItem> 重复项目类型
         * @return 重复内容错误消息, 没有错误内容返回null
         */
        public static <T extends ISerialNumber, TSameVerifyItem> String verifySameItem(
                List<T> list,
                @NonNull Function<T, TSameVerifyItem> sameVerifyItemSelector,
                @NonNull SameDataSerialNumberErrorGenerator errorMsgBuilder) {
            return verifySameItem(list, sameVerifyItemSelector, ISerialNumber::getSerialNumber, errorMsgBuilder);
        }

        /**
         * 生成重复内容错误消息
         * @param sameItemList 重复项目列表
         * @param serialNumberSelector 序列号选择器
         * @param errorGenerator 错误信息生成器
         * @param <TSameItem> 重复项目类型
         * @param <T> 导入元素类型
         * @return 重复内容错误消息
         */
        public static <TSameItem, T> String generateSameErrorMsg(
                List<Map.Entry<TSameItem, List<T>>> sameItemList,
                Function<T, Integer> serialNumberSelector,
                SameDataSerialNumberErrorGenerator errorGenerator) {
            StringJoiner joiner = new StringJoiner(StringUtils.NEW_LINE);
            for(Map.Entry<TSameItem, List<T>> item : sameItemList) {
                List<String> sameItemSerialNumberList = ListUtil.map(item.getValue(), x -> serialNumberSelector.apply(x).toString());
                String errorMsg = errorGenerator.create(String.join("," , sameItemSerialNumberList), item.getKey().toString());
                joiner.add(errorMsg);
            }
            return joiner.toString();
        }

        /**
         * 生成重复内容错误消息
         * @param sameItemList 重复项目列表
         * @param errorMsgBuilder 错误信息生成器
         * @param <TSameItem> 重复项目类型
         * @param <T> 导入元素类型
         * @return 重复内容错误消息
         */
        public static <TSameItem, T extends ISerialNumber> String generateSameErrorMsg(
                List<Map.Entry<TSameItem, List<T>>> sameItemList,
                SameDataSerialNumberErrorGenerator errorMsgBuilder) {
            return generateSameErrorMsg(sameItemList, ISerialNumber::getSerialNumber, errorMsgBuilder);
        }

    }

    /** 行错误版 */
    public static class RowError {
        /**
         * 验证是否存在重复项目
         * <p>重复的空元素也被认为是重复项目</p>
         * @param list 需要验证重复项的列表
         * @param sameVerifyItemSelector 重复项选择器
         * @param errorGenerator 错误信息生成器
         * @param <T> 导入元素类型
         * @param <TSameVerifyItem> 重复项目类型
         * @return 重复内容错误消息, 没有错误内容返回空集合
         */
        public static <T extends ISerialNumber, TSameVerifyItem> List<RowVerifyError<T>> verifySameItem(
                List<T> list,
                @NonNull Function<T, TSameVerifyItem> sameVerifyItemSelector,
                @NonNull SameDataRowVerifyErrorGenerator<T> errorGenerator) {
            return verifySameItem(list, sameVerifyItemSelector, ISerialNumber::getSerialNumber, errorGenerator);
        }

        /**
         * 验证是否存在重复项目
         * <p>重复的空元素也被认为是重复项目</p>
         * @param list 需要验证重复项的列表
         * @param sameVerifyItemSelector 重复项选择器
         * @param serialNumberSelector 导出元素序列号选择器
         * @param errorGenerator 错误信息生成器
         * @param <T> 导入元素类型
         * @param <TSameVerifyItem> 重复项目类型
         * @return 重复内容错误消息, 没有错误内容返回空集合
         */
        public static <T, TSameVerifyItem> List<RowVerifyError<T>> verifySameItem(
                List<T> list,
                @NonNull Function<T, TSameVerifyItem> sameVerifyItemSelector,
                @NonNull Function<T, Integer> serialNumberSelector,
                @NonNull SameDataRowVerifyErrorGenerator<T> errorGenerator) {
            if(ListUtil.isEmpty(list)) {
                return null;
            }

            var sameItemList = ListUtil.sameItemList(list, sameVerifyItemSelector);
            if(ListUtil.isEmpty(sameItemList)) {
                return null;
            }
            return generateSameErrorMsg(sameItemList, serialNumberSelector, errorGenerator);
        }

        /**
         * 生成重复内容错误消息
         * @param sameItemList 重复项目列表
         * @param serialNumberSelector 序列号选择器
         * @param errorGenerator 错误信息生成器
         * @param <TSameItem> 重复项目类型
         * @param <T> 导入元素类型
         * @return 重复内容错误消息
         */
        public static <TSameItem, T> List<RowVerifyError<T>> generateSameErrorMsg(
                List<Map.Entry<TSameItem, List<T>>> sameItemList,
                Function<T, Integer> serialNumberSelector,
                @NonNull SameDataRowVerifyErrorGenerator<T> errorGenerator) {

            return sameItemList.stream().flatMap(sameEntry -> {
                List<T> sameList = sameEntry.getValue();

                List<String> sameSerialNumberList = ListUtil.map(sameList, item -> String.valueOf(serialNumberSelector.apply(item)));
                String sameRowsStr = StringUtils.convertStringList(sameSerialNumberList);
                return sameList.stream().map(sameItem ->
                        errorGenerator.create(serialNumberSelector.apply(sameItem),
                                sameItem,
                                sameRowsStr));
            }).collect(Collectors.toList());
        }

        /**
         * 生成重复内容错误消息
         * @param sameItemList 重复项目列表
         * @param errorGenerator 错误信息生成器
         * @param <TSameItem> 重复项目类型
         * @param <T> 导入元素类型
         * @return 重复内容错误消息
         */
        public static <TSameItem, T extends ISerialNumber> List<RowVerifyError<T>> generateSameErrorMsg(
                List<Map.Entry<TSameItem, List<T>>> sameItemList,
                @NonNull SameDataRowVerifyErrorGenerator<T> errorGenerator) {
            return generateSameErrorMsg(sameItemList, ISerialNumber::getSerialNumber, errorGenerator);
        }
    }

    /**
     * 生成导入失败报表
     * <p>在原报表前两行添加错误信息</p>
     * @param importByteData 导入数据字节数组
     * @param rowVerifyErrorList 验证错误列表
     * @param sheetName 需要填充错误的Sheet名称
     * @param originalRowNum 原始数据Sheet行号, 用于对齐行号
     * @return 包含错误信息的报表字节数组
     */
    public static <T> byte[] generateFailureExcel(byte[] importByteData, List<RowVerifyError<T>> rowVerifyErrorList, String sheetName, int originalRowNum) throws IOException {
        IMultiSheetExcelExporter excelExporter = new MultiSheetExcelExporter();

        ExcelSheetConfig<RowVerifyError> errorMsgExportConfig = new ExcelSheetConfig<>();

        List<RowVerifyError> errorList = fillBlankRow(rowVerifyErrorList, originalRowNum);
        errorMsgExportConfig
                .addConfig(rowVerifyError -> rowVerifyError.getRowNo() + 1, "行号")
                .addConfig(RowVerifyError::getErrorInfo, "错误信息")
                .appendStyleModifier(style -> style.setAlignment(HorizontalAlignment.LEFT));

        excelExporter.addSheetConfig(errorList, errorMsgExportConfig, sheetName);

        try(InputStream inputStream = new ByteArrayInputStream(importByteData)) {
            return excelExporter.exportExcel(inputStream);
        }
    }

    /**
     * 为了导入美观, 填充空白的错误消息
     * @param rowVerifyErrorList 原始行错误列表, 不连续(1, 3, 7)
     * @param originalRowNum 原始数据Sheet行号, 用于对齐行号
     * @return 在不连续的地方填充默认信息的错误列表
     */
    private static  <T> List<RowVerifyError> fillBlankRow(List<RowVerifyError<T>> rowVerifyErrorList, int originalRowNum) {
        Assert.notEmpty(rowVerifyErrorList, "原始错误信息列表不能为空");

        int maxRow = ListUtil.maxInt(rowVerifyErrorList, RowVerifyError::getRowNo);

        List<RowVerifyError> result = new ArrayList<>(maxRow);
        Map<Integer, RowVerifyError<T>> errorMap = ListUtil.toMap(rowVerifyErrorList, RowVerifyError<T>::getRowNo);

        for(int i = originalRowNum; i <= maxRow; i++) {
            RowVerifyError rowVerifyError = errorMap.get(i);
            if(rowVerifyError != null){
                result.add(rowVerifyError);
                continue;
            }
            rowVerifyError = new RowVerifyError(i,rowVerifyError);
            result.add(rowVerifyError);
        }
        return result;
    }
}
