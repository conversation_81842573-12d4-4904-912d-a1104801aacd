package com.py.common.datascope;

import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.StringUtils;

/**
 * 数据权限参数
 * <AUTHOR>
 */
public interface IDataScopeArgs {

    /**
     * 设置数据权限Sql
     * @param sql 数据权限Sql
     */
    void setDataScopeSql(String sql);

    /**
     * 获取数据权限Sql
     * @return 数据权限Sql
     */
    String getDataScopeSql();

    /**
     * 获取数据权限Sql
     * @return 数据权限Sql
     */
    default String getDataScope() {
        return NullMergeUtils.blankMerge(this.getDataScopeSql(), StringUtils.EMPTY);
    }
}
