package com.py.common.utils;

import com.py.common.exception.base.BaseException;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.Assert;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 时间工具类
 * <AUTHOR>
 */
@Log4j2
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    public final static String YYYY = "yyyy";

    public final static String YYYY_MM = "yyyy-MM";

    public final static String YYYY_MM_STR = "yyyy年MM月";

    public final static String YYYY_MM_DD = "yyyy-MM-dd";

    public final static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public final static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public final static String YYYY_MM_DD_COLON_HH_MM_SS = "yyyy-MM-dd-HH：mm：ss";

    public final static String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";
    public final static String yyyyMMdd = "yyyyMMdd";

    private static final String[] PARSE_PATTERNS = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /** 日期区间匹配正则表达式 */
    private static final Pattern DATE_RANG_PATTERN = Pattern.compile("^((?<year1>\\d{4})[.\\-/](?<month1>\\d{1,2})[.\\-/](?<day1>\\d{1,2}))?[-/]{1,2}(?<year2>\\d{4})[.\\-/](?<month2>\\d{1,2})[.\\-/](?<day2>\\d{1,2})$");

    /** 日期匹配正则表达式 */
    private static final Pattern DATE_PATTERN = Pattern.compile("^(?<year>\\d{4})[.\\-/](?<month>\\d{1,2})([.\\-/](?<day>\\d{1,2}))?$");

    /** 日期匹配正则表达式 */
    private static final Pattern DATE_PATTERN_ALL = Pattern.compile("^(\\d{4})[.\\-/](\\d{1,2})[.\\-/](\\d{1,2})$");


    public static  final DateTimeFormatter yyyyMMdd_formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static  final DateTimeFormatter YYYY_MM_DD_HH_MM_SS_formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);

    /**
     * 获取当前Date型日期
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 获取当前日期, 格式为yyyy-MM-dd-HH：mm：ss
     * @return 获取当前时间
     */
    public static String getTimeCn() {
        return dateTimeNow(YYYY_MM_DD_COLON_HH_MM_SS);
    }

    public static String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static String getDateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD_HH_MM, date);
    }

    public static String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch(ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if(str == null) {
            return null;
        }
        try {
            String string = str.toString();
            string = string.replaceAll("\r\n|\r|\n", "");
            return parseDate(string.trim(), PARSE_PATTERNS);
        } catch(ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算相差天数
     * @param date2 开始时间
     * @param date1 结束时间
     * @return 时间差（天/小时/分钟）
     */
    public static int differentDaysByMillisecond(Date date1, Date date2)
    {
        return Math.abs((int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24)));
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        if(temporalAccessor == null){
            return null;
        }
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        if(temporalAccessor == null){
            return null;
        }

        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * date => LocalDateTime
     * @param date date
     * @return LocalDateTime
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        if(date == null){
            return null;
        }
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * 格式化LocalDateTime
     * @param date 日期
     * @param pattern 格式化字符串
     * @return 格式化结果
     */
    public static String format(LocalDateTime date, String pattern) {
        if(date == null || org.apache.commons.lang3.StringUtils.isBlank(pattern)) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern, Locale.SIMPLIFIED_CHINESE);
        return formatter.format(date);
    }

    /**
     * 格式化LocalDate
     * @param date 日期
     * @param pattern 格式化字符串
     * @return 格式化结果
     */
    public static String format(LocalDate date, String pattern) {
        if(date == null || StringUtils.isBlank(pattern)) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern, Locale.SIMPLIFIED_CHINESE);
        return formatter.format(date);
    }

    /**
     * Date
     * @param date 日期
     * @param pattern 格式化字符串
     * @return 格式化结果
     */
    public static String format(final Date date, final String pattern) {
        return new SimpleDateFormat(pattern).format(date);
    }

    /**
     * 格式化星期
     * @param dayOfWeek 星期
     * @return 格式化结果
     */
    public static String format(DayOfWeek dayOfWeek) {
        switch(dayOfWeek) {
            case MONDAY:
                return "周一";
            case TUESDAY:
                return "周二";
            case WEDNESDAY:
                return "周三";
            case THURSDAY:
                return "周四";
            case FRIDAY:
                return "周五";
            case SATURDAY:
                return "周六";
            case SUNDAY:
                return "周日";
            default:
                throw new UnsupportedOperationException(String.format("枚举: %s对应操作未实现", dayOfWeek));
        }
    }

    /**
     * 匹配日期区间
     * @param dateRangStr 日期区间字符串
     * @return 匹配到的日期 (key:日期1, value:日期2)
     */
    public static Pair<LocalDate, LocalDate> matchDateRang(String dateRangStr) {
        if(StringUtils.isEmpty(dateRangStr)) {
            log.error("日期区间匹配失败-内容为空");
            return null;
        }

        // 移除空白占位符
        dateRangStr = dateRangStr.replaceAll("\\s", StringUtils.EMPTY);

        Matcher matcher = DATE_RANG_PATTERN.matcher(dateRangStr);
        if(matcher.find() == false) {
            log.error("日期区间匹配失败-日期格式错误, 匹配内容->{}", dateRangStr);
            return null;
        }
        LocalDate date1 = null;
        if(matcher.group("year1") != null) {
            int year1 = Integer.parseInt(matcher.group("year1"));
            int month1 = Integer.parseInt(matcher.group("month1"));
            int day1 = Integer.parseInt(matcher.group("day1"));

            try {
                date1 = LocalDate.of(year1, month1, day1);
            } catch(DateTimeException e) {
                log.error("日期区间匹配失败{}-日期1错误, 匹配内容->{}", e.getMessage(), dateRangStr);
                return null;
            }
        }

        int year2 = Integer.parseInt(matcher.group("year2"));
        int month2 = Integer.parseInt(matcher.group("month2"));
        int day2 = Integer.parseInt(matcher.group("day2"));

        LocalDate date2;
        try {
            date2 = LocalDate.of(year2, month2, day2);
        } catch(DateTimeException e) {
            log.error("日期区间匹配失败{}-日期2错误, 匹配内容->{}", e.getMessage(), dateRangStr);
            return null;
        }

        return Pair.of(date1, date2);
    }

    /**
     * 匹配日期
     * @param dateStr 日期字符串
     * @return 日期
     */
    public static LocalDate matchDate(String dateStr) {
        if(StringUtils.isEmpty(dateStr)) {
            log.error("日期匹配失败-内容为空");
            return null;
        }

        // 移除空白占位符
        dateStr = dateStr.replaceAll("\\s", StringUtils.EMPTY);
        Matcher matcher = DATE_PATTERN.matcher(dateStr);
        if(matcher.find() == false) {
            try {
                Date date = new Date(dateStr);
                return toLocalDate(date);
            } catch(Exception ignored) {
            }
            log.error("日期匹配失败-日期格式错误, 匹配内容->{}", dateStr);
            return null;
        }

        int year = Integer.parseInt(matcher.group("year"));
        int month = Integer.parseInt(matcher.group("month"));
        int day = NullMergeUtils.nullMerge(matcher.group("day"), x -> Integer.parseInt(x), 1);
        try {
            return LocalDate.of(year, month, day);
        } catch(DateTimeException e) {
            log.error("日期匹配失败{}-日期错误, 匹配内容->{}", e.getMessage(), dateStr);
            return null;
        }
    }

    /**
     * 匹配日期,完全匹配
     * @param dateStr 日期字符串
     * @return 日期
     */
    public static Date matchDateByStr(String dateStr) {
        if(StringUtils.isEmpty(dateStr)) {
            log.error("日期匹配失败-内容为空");
            return null;
        }
        // 移除空白占位符
        dateStr = dateStr.replaceAll("\\s", StringUtils.EMPTY);
        Matcher matcher = DATE_PATTERN_ALL.matcher(dateStr);
        if(matcher.matches()) {
            return parseDate(dateStr);
        }
        return null;
    }

    /**
     * 将 Date 转换为 LocalDate
     * @param date Date
     * @return LocalDate
     */
    public static LocalDate toLocalDate(Date date) {
        if(date == null) {
            return null;
        }
        return LocalDate.of(date.getYear() + 1900, date.getMonth() + 1, date.getDate());
    }

    /**
     * 将字符串转换为 LocalDateTime
     * <p>格式: yyyy-MM-dd HH:mm:ss</p>
     * @param dateStr 时间字符串
     * @return LocalDateTime实例
     */
    public static LocalDateTime toLocalDateTime(String dateStr) {
        Assert.hasText(dateStr, "时间字符串不能为空");
        return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS));
    }

    /**
     * 将字符串转换为 LocalDate
     * <p>格式: yyyy-MM-dd</p>
     * @param dateStr 时间字符串
     * @return LocalDateTime实例
     */
    public static LocalDate toLocalDate(String dateStr) {
        Assert.hasText(dateStr, "时间字符串不能为空");
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(YYYY_MM_DD));
    }


    /**
     * 获取当前时间与指定时间的毫秒值
     * @param specifiedTime 指定时间
     * @return 时间差
     */
    public static long timeDifference(Date specifiedTime) {
        //当前时间的毫秒值
        long time1 = System.currentTimeMillis();
        //指定时间的毫秒值
        long time2 = specifiedTime.getTime();
        //计算毫秒值
        long time = time2 - time1;
        if(time < 0) {
            throw new BaseException("指定日期晚于当前日期，无法计算");
        }
        return time;
    }

    /**
     * 获取次日00:00
     */
    public static Date getNextDay() {
        Date now = new Date();
        Date nextDay = getAgoDayByDate(now, 1);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return format.parse(format.format(nextDay));
        } catch(ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 指定日期往前走num天
     * @param date 领取
     * @param num 天数
     */
    public static Date getAgoDayByDate(Date date, Integer num) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_YEAR, num);
        return calendar.getTime();
    }

    /**
     * 获取输入当天第一秒的时间
     * @param localDateTime 日期
     * @return 当天第一秒的时间
     */
    public static LocalDateTime getDayFirstTime(LocalDateTime localDateTime) {
        if(localDateTime == null) {
            return null;
        }
        return LocalDateTime.of(localDateTime.toLocalDate(), LocalTime.MIN);
    }

    /**
     * 获取输入当天最后一秒的时间
     * @param localDateTime 日期
     * @return 当天最后一秒的时间
     */
    public static LocalDateTime getDayLastTime(LocalDateTime localDateTime) {
        if(localDateTime == null) {
            return null;
        }
        return LocalDateTime.of(localDateTime.toLocalDate(), LocalTime.MAX);
    }

    /**
     * 获取输入当天第一秒的时间
     * @param localDate 日期
     * @return 当天第一秒的时间
     */
    public static LocalDateTime getDayFirstTime(LocalDate localDate) {
        if(localDate == null) {
            return null;
        }
        return LocalDateTime.of(localDate, LocalTime.MIN);
    }

    /**
     * 获取输入当天最后一秒的时间
     * @param localDate 日期
     * @return 当天最后一秒的时间
     */
    public static LocalDateTime getDayLastTime(LocalDate localDate) {
        if(localDate == null) {
            return null;
        }
        // 取LocalTime.MAX的纳秒值 -> 999999999 时将导致MySQL日期查询判断错误, 会查出下一天
        return LocalDateTime.of(localDate, LocalTime.MAX).withNano(0);
    }

    /**
     * 拼接时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 拼接时间字符串
     */
    public static String spliceTime(Date startTime, Date endTime) {
        return DateUtils.dateTime(startTime) + " ~ " + DateUtils.dateTime(endTime);
    }

    /**
     * 获取今天的剩余时间
     * @return 剩余时间（分钟）
     */
    public static Integer getTodayRemainderTime(){
        LocalDateTime tomorrow = LocalDateTime.of(LocalDate.now().plusDays(1), LocalTime.MIN);
        return Long.valueOf(Duration.between(LocalDateTime.now(), tomorrow).toMinutes()).intValue();
    }

    /**
     * 获取今天到明天的剩余时间
     * @return 剩余时间（分钟）
     */
    public static Integer getTodayToTomorrowTime(){
        LocalDateTime tomorrow = LocalDateTime.of(LocalDate.now().plusDays(2), LocalTime.MIN);
        return Long.valueOf(Duration.between(LocalDateTime.now(), tomorrow).toMinutes()).intValue();
    }

    /**
     * 获取当前时间距离指定时间的时间差
     * @param time 指定时间
     * @return 时间差（分钟）
     */
    public static Integer getDistanceTime(Date time) {
        long nm = 1000 * 60;
        Date date = new Date();
        // 获得两个时间的毫秒时间差异
        long diff = time.getTime() - date.getTime();
        // 计算差多少分钟
        return Math.toIntExact(diff / nm);
    }

    public static String parseDate(String format, LocalDateTime date) {
        if(StringUtils.isBlank(format) || Objects.isNull(date)) {
            return null;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(format);
        return date.format(dateTimeFormatter);
    }

    /**
     * string转LocalDate
     * @param dateStr
     * @param formatter
     * @return
     */
    public static LocalDate format(String dateStr, String formatter) {
        if(StringUtils.isBlank(dateStr) || StringUtils.isBlank(formatter)) {
            return null;
        }
        // Excel数值格式日期特殊处理
        if(StringUtils.isNumeric(dateStr)){
            return LocalDate.of(1900, 1, 1).plusDays(Integer.parseInt(dateStr));
        }

        DateTimeFormatter formatterDate = DateTimeFormatter.ofPattern(formatter);
        try {
            return LocalDate.parse(dateStr, formatterDate);
        }catch(DateTimeParseException parseException){
            log.warn("日期格式转换异常, dateStr: {}, formatter: {}", dateStr, formatter);
            return null;
        }
    }

    /**
     * 获取当月最后一秒
     * @param localDate 时间
     * @return
     */
    public static LocalDateTime getMonthLastTime(LocalDate localDate){
        if(localDate == null) {
            return null;
        }
        LocalDate date = localDate.with(TemporalAdjusters.lastDayOfMonth());
        return LocalDateTime.of(date, LocalTime.MAX).withNano(0);
    }

    /**
     * 获取当月最后一秒
     * @param localDate 时间
     * @return
     */
    public static LocalDate getMonthLastDate(LocalDate localDate) {
        if(localDate == null) {
            return null;
        }
        return localDate.with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 获取上个月的第一天
     * @return
     */
    public static String getLastMonthFirstDay() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD);
        Calendar calendar=Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return simpleDateFormat.format(calendar.getTime());
    }

    /**
     * 获取上个月的最后一天
     * @return
     */
    public static String getLastMonthLastDay() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return simpleDateFormat.format(calendar.getTime());
    }

    private static final long ONE_MINUTE = 60000L;
    private static final long ONE_HOUR = 3600000L;
    private static final long ONE_DAY = 86400000L;
    private static final long ONE_WEEK = 604800000L;

    private static final String ONE_SECOND_AGO = "秒前";
    private static final String ONE_MINUTE_AGO = "分钟前";
    private static final String ONE_HOUR_AGO = "小时前";
    private static final String ONE_DAY_AGO = "天前";
    private static final String ONE_MONTH_AGO = "月前";
    private static final String ONE_YEAR_AGO = "年前";

    public static String format(Date date) {
        long delta = new Date().getTime() - date.getTime();
        if (delta < 1L * ONE_MINUTE) {
            long seconds = toSeconds(delta);
            return (seconds <= 0 ? 1 : seconds) + ONE_SECOND_AGO;
        }
        if (delta < 45L * ONE_MINUTE) {
            long minutes = toMinutes(delta);
            return (minutes <= 0 ? 1 : minutes) + ONE_MINUTE_AGO;
        }
        if (delta < 24L * ONE_HOUR) {
            long hours = toHours(delta);
            return (hours <= 0 ? 1 : hours) + ONE_HOUR_AGO;
        }
        if (delta < 48L * ONE_HOUR) {
            return "1天前";
        }
        if (delta < 30L * ONE_DAY) {
            long days = toDays(delta);
            return (days <= 0 ? 1 : days) + ONE_DAY_AGO;
        }
        if (delta < 12L * 4L * ONE_WEEK) {
            long months = toMonths(delta);
            return (months <= 0 ? 1 : months) + ONE_MONTH_AGO;
        } else {
            long years = toYears(delta);
            return (years <= 0 ? 1 : years) + ONE_YEAR_AGO;
        }
    }

    private static long toSeconds(long date) {
        return date / 1000L;
    }

    private static long toMinutes(long date) {
        return toSeconds(date) / 60L;
    }

    private static long toHours(long date) {
        return toMinutes(date) / 60L;
    }

    private static long toDays(long date) {
        return toHours(date) / 24L;
    }

    private static long toMonths(long date) {
        return toDays(date) / 30L;
    }

    private static long toYears(long date) {
        return toMonths(date) / 365L;
    }

    /**
     * 获取Date格式最小日期1970-01-01
     * @return 日期
     */
    public static Date getMinDateTime() {
        Date date = new Date(0);
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        String formattedDate = sdf.format(date);
        return parseDate(formattedDate);
    }

    /**
     * 获取日期9999-12-31 23:59:59
     * @return 日期
     */
    public static Date getMaxDateTime() {
        // 创建一个 Calendar 实例，并设置为 9999-12-31 23:59:59
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, 9999);
        calendar.set(Calendar.MONTH, Calendar.DECEMBER);
        calendar.set(Calendar.DAY_OF_MONTH, 31);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);

        return calendar.getTime();
    }

}
