package com.py.web.controller.tool;

import com.py.common.core.domain.R;
import com.py.system.tools.email.IEmailService;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 测试邮件发送
 * @date 2023/10/9 10:18
 */
@RestController
@RequestMapping("/tool/email")
public class EmailController {

    /**
     * 邮件服务
     */
    @Resource
    private IEmailService emailService;

    /**
     * 邮件发送
     * @param to  发送给谁
     */
    @GetMapping("/sendEmail")
    @ApiOperation("邮件发送")
    public R<Boolean> sendEmail(@RequestParam("emailAccount") String to) {
        return R.success(this.emailService.sendSimpleMail(to));
    }
}
