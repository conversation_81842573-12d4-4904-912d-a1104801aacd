package com.py.common.core.domain.query;

import java.time.LocalDate;

/**
 * 创建信息查询接口
 * <AUTHOR>
 */
public interface ICreateQuery {

    /**
     * 获取创建者名字
     * @return 创建者名字
     */
    String getCreateUser();

    /**
     * 获取创建者部门
     * @return 创建者部门
     */
    String getCreateDept();

    /**
     * 获取创建时间-开始
     * @return 创建时间-开始
     */
    LocalDate getStartCreateDate();

    /**
     * 获取创建时间-结束
     * @return 创建时间-结束
     */
    LocalDate getEndCreateDate();
}
