package com.py.crm.customer.customervisit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Sequence;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.py.common.enums.DelFlag;
import com.py.common.file.FileInfoVO;
import com.py.common.mybatisplus.SuperServiceImpl;
import com.py.common.oss.IOssService;
import com.py.common.tools.modifycomparator.IObjectComparator;
import com.py.common.tools.modifycomparator.model.BaseModifyDiff;
import com.py.common.utils.DateUtils;
import com.py.common.utils.PageUtils;
import com.py.common.utils.SqlHelper;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.crmupdaterecord.domain.dto.CrmUpdateRecordDTO;
import com.py.crm.crmupdaterecord.domain.enums.RecordBizType;
import com.py.crm.crmupdaterecord.domain.enums.RecordMenuType;
import com.py.crm.crmupdaterecord.service.ICrmUpdateRecordService;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryExportModel;
import com.py.crm.customer.customervisit.converter.CustomerVisitConverter;
import com.py.crm.customer.customervisit.domian.CustomerVisit;
import com.py.crm.customer.customervisit.domian.dto.CustomerVisitDTO;
import com.py.crm.customer.customervisit.domian.dto.CustomerVisitExportModel;
import com.py.crm.customer.customervisit.domian.vo.CustomerVisitVO;
import com.py.crm.customer.customervisit.mapper.CustomerVisitMapper;
import com.py.crm.customer.customervisit.service.ICustomerVisitService;
import com.py.crm.customer.service.ICustomerService;
import com.py.system.recyclebin.domain.enums.RecycleBizType;
import com.py.system.recyclebin.service.IRecycleBinService;
import com.py.system.user.service.ISysUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *
 */
@Service
public class CustomerVisitServiceImpl extends SuperServiceImpl<CustomerVisitMapper,CustomerVisit> implements ICustomerVisitService {
    /** 用户 业务层 */
    @Resource
    private ISysUserService userService;
    /**客户管理-客户拜访日志模型转换器 */
    @Resource
    private CustomerVisitConverter customerVisitConverter;
    /** 对象比较器 */
    @Resource
    private IObjectComparator objectComparator;
    /** 客户/人脉(状态)更新记录表服务 */
    @Resource
    private ICrmUpdateRecordService crmUpdateRecordService;
    @Resource
    private CustomerVisitMapper customerVisitMapper;
    /** 回收站服务 */
    @Resource
    private final IRecycleBinService recycleBinService;
    /** 主键生成器 */
    @Resource
    private Sequence sequence;
    /** oss文件接口*/
    @Resource
    private IOssService ossService;

    @Resource
    private ICustomerService customerService;

    public CustomerVisitServiceImpl(
            IRecycleBinService recycleBinService) {
        recycleBinService.registeredRestoreCallback(RecycleBizType.CUSTOMER_VISIT, this::restore);
        this.recycleBinService = recycleBinService;
    }

    /**
     * 新增访客日志
     * @param dtoList 客户管理-客户拜访日志数据传输模型
     * @param customerId 客户id
     * @param isApprovalEdit 是否是审批驳回修改
     */
    @Override
    public void insertBatchCustomerVisit(List<CustomerVisitDTO> dtoList, Long customerId, Boolean isApprovalEdit) {
        //是否为撤回修改，为修改则删除原有的数据
        if(isApprovalEdit){
            this.remove(Wrappers.<CustomerVisit>lambdaQuery().eq(CustomerVisit::getCustomerId,customerId));
            dtoList.forEach(item->{
                item.setId(null);
            });
        }
        if(ListUtil.isEmpty(dtoList)){
            return;
        }
        List<CustomerVisit> customerVisitList = customerVisitConverter.toEntityByDto(dtoList);
        this.joinVisitPersonnel(customerVisitList,dtoList);
        int id = 1;
        //关联客户id和展示id
        for(CustomerVisit customerVisit : customerVisitList){
            customerVisit.setCustomerId(customerId);
            customerVisit.setShowId(id);
            id++;
        }
        //新增更新记录
        List<CrmUpdateRecordDTO> crmUpdateRecordDTOList = new ArrayList<>();
        this.addUpdateRecord(crmUpdateRecordDTOList,customerVisitList);
        crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordDTOList);
        customerVisitList.forEach(customerVisit -> customerVisit.setVisitId(sequence.nextId()));
        SqlHelper.appendAddUpdateInfo(customerVisitList);
        this.saveBatch(customerVisitList);
    }

    /**
     * 随访人员处理
     * @param customerVisitList 随访日志集合
     * @param dtoList 客户管理-客户拜访日志数据传输模型
     */
    public void joinVisitPersonnel(List<CustomerVisit> customerVisitList , List<CustomerVisitDTO> dtoList){
        customerVisitList.forEach(item->
                item.setVisitPersonnel(String.join(",",item.getVisitPersonnelList())));
    }

    /**
     * 修改客户拜访日志
     * @param dtoList 客户管理-客户拜访日志数据传输模型
     * @param customerId 客户id
     * @param name 客户 名称
     */
    @Override
    public void updateBatchCustomerVisit(List<CustomerVisitDTO> dtoList, Long customerId, String name) {
        //修改的访问日志id
        List<Long> visitIdList = dtoList.stream().map(CustomerVisitDTO::getVisitId).filter(visitId->visitId != null).collect(Collectors.toList());
        //修改记录
        List<CrmUpdateRecordDTO> crmUpdateRecordDTOList = new ArrayList<>();
        if(ListUtil.isNotEmpty(visitIdList)){
            //查询未修改前数据
            List<CustomerVisit> customerVisitList = this.list(Wrappers.<CustomerVisit>lambdaQuery()
                .in(CustomerVisit::getVisitId,visitIdList));
            //转换修改后端数据
            List<CustomerVisitDTO> customerVisitDTOList = dtoList.stream().filter(customerVisitDTO -> customerVisitDTO.getVisitId() != null)
                    .collect(Collectors.toList());
            List<CustomerVisit> editCustomerVisit = customerVisitConverter.toEntityByDto(customerVisitDTOList);
            this.joinVisitPersonnel(editCustomerVisit,customerVisitDTOList);
            //对比数据修改部分并生成记录
            List<CustomerVisit> customerVisits = this.editVisit(crmUpdateRecordDTOList,customerVisitList,editCustomerVisit);
            if(ListUtil.isNotEmpty(editCustomerVisit)){
                this.updateBatchById(editCustomerVisit);
            }
            //删除拜访日志并添加更新记录
            this.removeVisit(customerId,visitIdList,crmUpdateRecordDTOList,name);
            //获取新增的拜访日志
            List<CustomerVisitDTO> visitDTOList = dtoList.stream().filter(visitDTO->visitDTO.getVisitId() == null ).collect(Collectors.toList());
            //赋值客户id和展示id
            if(ListUtil.isNotEmpty(visitDTOList)){
                List<CustomerVisit> addCustomerVisits = customerVisitConverter.toEntityByDto(visitDTOList);
                this.joinVisitPersonnel(addCustomerVisits,visitDTOList);
                //查询最新的展示id
                Integer showId = customerVisitMapper.getShowId(customerId);
                showId = showId ==null?1:showId;
                for(CustomerVisit customerVisit : addCustomerVisits){
                    customerVisit.setCustomerId(customerId);
                    customerVisit.setShowId(showId);
                    showId++;
                }
                //新增记录
                this.addUpdateRecord(crmUpdateRecordDTOList,addCustomerVisits);
                SqlHelper.appendAddUpdateInfo(addCustomerVisits);
                addCustomerVisits.forEach(customerVisit -> customerVisit.setVisitId(sequence.nextId()));
                this.saveBatch(addCustomerVisits);
            }
            crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordDTOList);
        }else{
            //获取新增的拜访日志
            List<CustomerVisitDTO> visitDTOList = dtoList.stream().filter(visitDTO->visitDTO.getVisitId() == null ).collect(Collectors.toList());
            //赋值客户id和展示id
            if(ListUtil.isNotEmpty(visitDTOList)){
                List<CustomerVisit> addCustomerVisits = customerVisitConverter.toEntityByDto(visitDTOList);
                this.joinVisitPersonnel(addCustomerVisits,visitDTOList);
                //查询最新的展示id
                Integer showId = customerVisitMapper.getShowId(customerId);
                showId = showId ==null?1:showId;
                for(CustomerVisit customerVisit : addCustomerVisits){
                    customerVisit.setCustomerId(customerId);
                    customerVisit.setShowId(showId);
                    showId++;
                }
                //新增记录
                this.addUpdateRecord(crmUpdateRecordDTOList,addCustomerVisits);
                SqlHelper.appendAddUpdateInfo(addCustomerVisits);
                addCustomerVisits.forEach(customerVisit -> customerVisit.setVisitId(sequence.nextId()));
                this.saveBatch(addCustomerVisits);
            }
            crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordDTOList);
        }

    }

    /**
     * 根据客户id分页查询客户拜访日志
     * @param customerId 客户id
     * @return  PageInfo<CustomerVisitVO> 分页视图
     */
    @Override
    public PageInfo<CustomerVisitVO> pageCustomerVisitList(Long customerId) {
        PageUtils.startPage();
        LambdaQueryWrapper<CustomerVisit> wrapper = Wrappers.lambdaQuery(CustomerVisit.class);
        wrapper.eq(CustomerVisit::getCustomerId,customerId)
                .eq(CustomerVisit::getDelFlag, DelFlag.NOT_DELETED);
        List<CustomerVisit> customerVisitList = this.list(wrapper);
        List<CustomerVisitVO> customerVisitVOList = this.customerVisitConverter.toVoByEntity(customerVisitList);
        this.userService.relatedUpdateInfo(customerVisitVOList);
        this.getAnnex(customerVisitVOList);
        return ListUtil.pageConvert(customerVisitList,customerVisitVOList);
    }

    /**
     * 编辑客户时，如果未传拜访日志数据，则删除旧的拜访日志数据
     * @param customerId 客户id
     * @param name 客户名称
     */
    @Override
    public void deleteBatchCustomerVisit(Long customerId, String name) {
        //删除客户拜访日志
        List<CrmUpdateRecordDTO> crmUpdateRecordList = new ArrayList<>();
        removeVisit(customerId,null,crmUpdateRecordList,name);
        //新增更新记录
        if(ListUtil.isNotEmpty(crmUpdateRecordList)){
            crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordList);
        }
    }

    /**
     * 根据客户id获取客户拜访日志列表
     * @param customerId 客户id
     * @return  CustomerVisitVO 客户拜访日志视图列表
     */
    @Override
    public List<CustomerVisitVO> selectCustomerVisitByCustomerId(Long customerId) {
        List<CustomerVisit> customerVisitList = this.list(Wrappers.<CustomerVisit>lambdaQuery()
                .eq(CustomerVisit::getCustomerId,customerId)
                .orderByAsc(CustomerVisit::getShowId));
        List<CustomerVisitVO> customerVisitVOList = this.customerVisitConverter.toVoByEntity(customerVisitList);
        this.userService.relatedUpdateInfo(customerVisitVOList);
        this.getAnnex(customerVisitVOList);
        return customerVisitVOList;
    }

    /**
     * 根据客户ID查询客户拜访日志列表
     * @param customerIdList 客户ID
     * @return 客户拜访日志列表
     */
    @Override
    public List<CustomerVisitExportModel> listVisitByCustomerIds(List<Long> customerIdList,Map<Long, CustomerLaundryExportModel> customerMap) {
        if(ListUtil.isEmpty(customerIdList)){
            return ListUtil.emptyList();
        }
        List<CustomerVisit> customerVisitList = this.list(Wrappers.<CustomerVisit>lambdaQuery()
                .in(CustomerVisit::getCustomerId,customerIdList)
                .orderByAsc(CustomerVisit::getVisitId));
        if(ListUtil.isEmpty(customerVisitList)){
            return ListUtil.emptyList();
        }

        List<CustomerVisitExportModel> customerVisitExportModelList = customerVisitConverter.toExportModel(customerVisitList);
        this.userService.relatedUpdateInfo(customerVisitExportModelList);
        customerVisitExportModelList.forEach(export -> {
            export.conversion();
            export.setVisitTimeStr(DateUtils.format(export.getVisitTime(),"yyyy-MM-dd"));
            export.setUpdateTimeStr(DateUtils.format(export.getUpdateTime(),"yyyy-MM-dd HH:mm:ss"));
            if(ListUtil.isNotEmpty(export.getAnnexModel())){
                export.getAnnexModel().forEach(hyperlinkPackage -> {
                    String urlByKey = ossService.getUrlByKey(hyperlinkPackage.getOssUrl());
                    hyperlinkPackage.setOssUrl(urlByKey);
                });
            }
            if (customerMap != null) {
                CustomerLaundryExportModel model = customerMap.get(export.getCustomerId());
                if (model != null) {
                    export.setName(model.getName());
                    export.setCooperationStatusStr(model.getCooperationStatusStr());
                    export.setLineBusiness(model.getLineBusiness());
                    export.setIndustryCategoryStr(model.getIndustryCategoryStr());
                }
            }
        });
        return customerVisitExportModelList;
    }

    /**
     * 文件处理
     * @param customerVisitVOList 视图模型list
     */
    public void getAnnex(List<CustomerVisitVO> customerVisitVOList){
        //附件处理
        for(CustomerVisitVO customerVisitVO : customerVisitVOList){
            if(ListUtil.isNotEmpty(customerVisitVO.getAnnex())){
                List<FileInfoVO> fileInfoByKey = ossService.getFileInfoByKey(customerVisitVO.getAnnex());
                customerVisitVO.setAnnexList(fileInfoByKey);
            }
        }
    }
    /**
     * 删除客户拜访日志
     * @param customerId 客户id
     * @param editVisitIdList 修改访问日志id
     * @param crmUpdateRecordDTOList 更新记录
     * @param name 客户名称
     */
    private void removeVisit(Long customerId,List<Long> editVisitIdList,List<CrmUpdateRecordDTO>  crmUpdateRecordDTOList , String name ){
        //获取被删除的数据
        LambdaQueryWrapper<CustomerVisit> wrapper = Wrappers.<CustomerVisit>lambdaQuery()
                .in(CustomerVisit::getCustomerId, customerId)
                .notIn(ListUtil.isNotEmpty(editVisitIdList),CustomerVisit::getVisitId,editVisitIdList);
        List<CustomerVisit> customerVisitList = this.list(wrapper);
        //添加删除记录
        if(ListUtil.isNotEmpty(customerVisitList)){
            customerVisitList.forEach(customerVisit -> {
                CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                String content = "客户拜访日志-删除ID：" + customerVisit.getShowId() +"【客户拜访事项】" +"“"+customerVisit.getVisitMatters()+"”";
                crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(), customerVisit.getCustomerId(),
                        content,RecordMenuType.UPDATE_RECORD.getValue(), null,null);
                crmUpdateRecordDTO.setBizId(customerId);
                crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
            });
            this.remove(wrapper);
//            //回收站添加删除记录
//            List<DeleteAddDTO> deleteAddDTOList = new ArrayList<>();
//            customerVisitList.forEach(customerVisit -> {
//                DeleteAddDTO deleteAddDTO = new DeleteAddDTO();
//                deleteAddDTO.setBizId(customerVisit.getVisitId());
//                deleteAddDTO.setContent(name+"-客户拜访日志："+customerVisit.getVisitMatters());
//                deleteAddDTOList.add(deleteAddDTO);
//            });
//            recycleBinService.addDeleteRecord(RecycleBizType.CUSTOMER_VISIT,deleteAddDTOList);
        }
    }
    /**
     * 修改字段对比
     * @param crmUpdateRecordDTOList 修改记录
     * @param customerVisitList 修改前记录
     * @param editCustomerVisit 修改后的数据
     * @return customerVisit 对比后的修改数据
     */
    public List<CustomerVisit>  editVisit(List<CrmUpdateRecordDTO> crmUpdateRecordDTOList,List<CustomerVisit> customerVisitList ,List<CustomerVisit> editCustomerVisit){
        Map<Long ,CustomerVisit> visitMap = ListUtil.toMap(editCustomerVisit,CustomerVisit::getVisitId);
        List<CustomerVisit> customerVisits = new ArrayList<>();
        customerVisitList.forEach(customerVisit -> {
            CustomerVisit visit = visitMap.get(customerVisit.getVisitId());
            List<BaseModifyDiff<?>> baseModifyDiffs = objectComparator.compareObject(customerVisit,visit,CustomerVisit.class);
            if(ListUtil.isNotEmpty(baseModifyDiffs)){
                baseModifyDiffs.forEach(baseModifyDiff -> {
                    CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                    Object source = baseModifyDiff.getBefore() != null ? baseModifyDiff.getBefore():"--";
                    Object after = baseModifyDiff.getAfter() != null ? baseModifyDiff.getAfter():"--";
                    String content = "客户拜访日志-更新ID："+ customerVisit.getShowId() +"【"+ListUtil.last(baseModifyDiff.getFieldPath())+"】从"+"”"+source+"“"+"更新为”"+after+ "”";
                    crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(), customerVisit.getVisitId(),content,
                                    RecordMenuType.UPDATE_RECORD.getValue(),null,null );
                    crmUpdateRecordDTO.setBizId(visit.getCustomerId());
                    crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
                });
                customerVisits.add(visit);
            }
        });
        return  customerVisits;
     }

    /**
     * 新增更新记录
     * @param addCustomerVisit 更新访客日志
     * @param crmUpdateRecordDTOList 更新记录
     */
    public void  addUpdateRecord( List<CrmUpdateRecordDTO> crmUpdateRecordDTOList,List<CustomerVisit> addCustomerVisit){

        addCustomerVisit.forEach(customerVisit -> {
            List<BaseModifyDiff<?>> baseModifyDiffs = objectComparator.compareObject(new CustomerVisit(),customerVisit ,CustomerVisit.class);
            baseModifyDiffs.forEach(baseModifyDiff -> {
                CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();

                String content = "客户拜访日志-新增ID："+customerVisit.getShowId()+"【"+ListUtil.last(baseModifyDiff.getFieldPath())+"】"+"”"+baseModifyDiff.getAfter()+"“";

                crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(),customerVisit.getCustomerId(),
                        content, RecordMenuType.UPDATE_RECORD.getValue(),null,null);

                crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
            });
        });
    }
}
