package com.py.web.controller.crm;

import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.crm.customer.customeraddress.domain.vo.CustomerAddressVO;
import com.py.crm.customer.customeraddress.domain.vo.CustomerAddressListVO;
import com.py.crm.customer.customeraddress.domain.query.CustomerAddressQuery;
import com.py.crm.customer.customeraddress.domain.dto.CustomerAddressDTO;
import com.py.crm.customer.customeraddress.domain.dto.CustomerAddressExportModel;
import com.py.crm.customer.customeraddress.service.ICustomerAddressService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户管理-客户-客户地址Controller
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Api(tags = "客户管理-客户-客户地址")
@RestController
@RequestMapping("/crm/customerAddress")
public class CustomerAddressController extends BaseController {

    /** 客户管理-客户-客户地址服务 */
    @Resource
    private ICustomerAddressService customerAddressService;

    /**
     * 分页查询客户管理-客户-客户地址列表
     *
     * @param query 客户管理-客户-客户地址查询参数
     * @return 客户管理-客户-客户地址分页
     */
    @ApiOperation("分页查询询客户管理-客户-客户地址列表")
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAddress:list')")
    @GetMapping("/pageCustomerAddress")
    public R<PageInfo<CustomerAddressListVO>> pageCustomerAddress(CustomerAddressQuery query) {
        PageInfo<CustomerAddressListVO> voList = this.customerAddressService.pageCustomerAddressList(query);
        return R.success(voList);
    }

    /**
     * 获取客户管理-客户-客户地址详细信息
     * @param id 客户管理-客户-客户地址主键
     * @return 客户管理-客户-客户地址视图模型
     */
    @ApiOperation("获取客户管理-客户-客户地址详细信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAddress:query')")
    @GetMapping(value = "/{id}")
    public R<List<CustomerAddressVO>> getInfo(@PathVariable("id") Long id) {
        return R.success(customerAddressService.selectCustomerAddressDetailById(id));
    }

    /**
     * 新增客户管理-客户-客户地址
     *
     * @param dto 客户管理-客户-客户地址修改参数
     * @return 是否成功
     */
    @ApiOperation("新增客户管理-客户-客户地址")
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAddress:add')")
    @Log(title = "客户管理-客户-客户地址", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody CustomerAddressDTO dto) {
        return R.success(customerAddressService.insertCustomerAddress(dto));
    }

    /**
     * 修改客户管理-客户-客户地址
     *
     * @param dto 客户管理-客户-客户地址修改参数
     * @return 是否成功
     */
    @ApiOperation("修改客户管理-客户-客户地址")
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAddress:edit')")
    @Log(title = "客户管理-客户-客户地址", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody CustomerAddressDTO dto) {
        return R.success(customerAddressService.updateCustomerAddress(dto));
    }

    /**
     * 删除客户管理-客户-客户地址
     * @param ids 需要删除的客户管理-客户-客户地址主键集合
     * @return 是否成功
     */
    @ApiOperation("删除客户管理-客户-客户地址" )
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAddress:remove')")
    @Log(title = "客户管理-客户-客户地址", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(customerAddressService.deleteCustomerAddressByIds(ids));
    }

    /**
     * 导出客户管理-客户-客户地址
     * @param response 请求响应
     * @param query 导出查询参数
     */
    @ApiOperation("导出客户管理-客户-客户地址")
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAddress:export')")
    @Log(title = "客户管理-客户-客户地址", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CustomerAddressQuery query) {
        List<CustomerAddressExportModel> exportList = this.customerAddressService.exportCustomerAddress(query);

        ExcelUtil<CustomerAddressExportModel> util = new ExcelUtil<>(CustomerAddressExportModel. class);
        util.exportExcel(response, exportList, "客户管理-客户-客户地址数据" );
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAddress:import')" )
    @Log(title = "客户管理-客户-客户地址" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<CustomerAddressExportModel> util = new ExcelUtil<>(CustomerAddressExportModel.class);
        List<CustomerAddressExportModel> customerAddressList = util.importExcel(file.getInputStream());
        String message = this.customerAddressService.importCustomerAddress(customerAddressList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('com.py.crm:customerAddress:import')" )
    @Log(title = "客户管理-客户-客户地址" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<CustomerAddressExportModel> util = new ExcelUtil<>(CustomerAddressExportModel.class);
        util.importTemplateExcel(response, "客户管理-客户-客户地址数据" );
    }

}
