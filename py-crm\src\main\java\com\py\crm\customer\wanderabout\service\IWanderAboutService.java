package com.py.crm.customer.wanderabout.service;

import com.py.crm.customer.wanderabout.domain.WanderAbout;
import com.py.crm.customer.wanderabout.domain.dto.WanderAboutDTO;
import com.py.crm.customer.wanderabout.domain.vo.WanderAboutVO;

import java.util.List;

/**
 * 客户管理-客户流转Service接口
 *
 * <AUTHOR>
 * @date 2023-08-16
 */
public interface IWanderAboutService {

    /**
     * 根据客户ID查询被分配的人员
     * @param customerId 客户管理-客户id
     * @return 人员id
     */
     List<Long> selectWanderAboutById(Long customerId);

    /**
     * 新增客户管理-客户流转
     * @param dto 客户管理-客户流转修改参数
     * @return 是否成功
     */
    boolean insertWanderAbout(WanderAboutDTO dto);

    /**
     * 批量新增客户管理-客户流转
     * @param dtoList 客户管理-客户流转修改参数
     * @return 是否成功
     */
    boolean insertBatchWanderAbout(List<WanderAboutDTO> dtoList);

    /**
     * 根据客户ID查询被分配的人员
     * @param customerIdList 客户id
     * @return 人员id
     */
    List<WanderAbout> listWanderAboutByCustomerId(List<Long> customerIdList);

}
