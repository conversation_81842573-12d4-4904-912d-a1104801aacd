package com.py.web.controller.system;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.system.dept.domain.vo.DeptVO;
import com.py.system.sysnotice.domain.dto.SysNoticeDTO;
import com.py.system.sysnotice.domain.query.SysNoticeQuery;
import com.py.system.sysnotice.domain.vo.SysNoticeListVO;
import com.py.system.sysnotice.domain.vo.SysNoticeVO;
import com.py.system.sysnotice.service.ISysNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 通知公告Controller
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Api(tags = "通知公告")
@RestController
@RequestMapping("/sysnotice/sysNotice")
@Validated
public class SysNoticeController extends BaseController {

    /** 通知公告服务 */
    @Resource
    private ISysNoticeService sysNoticeService;

    /**
     * 分页查询通知公告列表
     *
     * @param query 通知公告查询参数
     * @return 通知公告分页
     */
    @ApiOperation("分页查询询通知公告列表")
    @PreAuthorize("@ss.hasPermi('sysnotice:sysNotice:list')")
    @PostMapping("/pageSysNotice")
    public R<PageInfo<SysNoticeListVO>> pageSysNotice(@RequestBody SysNoticeQuery query) {
        PageInfo<SysNoticeListVO> voList = this.sysNoticeService.pageSysNoticeList(query);
        return R.success(voList);
    }


    /**
     * 公告下线或者上线
     * @param id
     * @return
     */
    @ApiOperation("公告上线或者下线")
    @PreAuthorize("@ss.hasPermi('sysnotice:sysNotice:launchOr')")
    @GetMapping("/OfflineOrLaunch/{id}")
    public  R<Boolean> OfflineOrLaunch(@PathVariable Long id) {
        return R.success(sysNoticeService.offlineOrLaunch(id));
    }



    /**
     * 获取通知公告详细信息
     * @param id 通知公告主键
     * @return 通知公告视图模型
     */
    @ApiOperation("获取通知公告详细信息")
    @PreAuthorize("@ss.hasPermi('sysnotice:sysNotice:query')")
    @GetMapping(value = "getInfo/{id}")
    public R<SysNoticeVO> getInfo(@PathVariable("id") Long id) {
        return R.success(sysNoticeService.selectSysNoticeById(id));
    }


    /**
     * 新增通知公告
     *
     * @param dto 通知公告修改参数
     * @return 是否成功
     */
    @ApiOperation("新增通知公告")
    @PreAuthorize("@ss.hasPermi('sysnotice:sysNotice:add')")
    @Log(title = "通知公告", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Boolean> add(@Valid @RequestBody SysNoticeDTO dto) {
        return R.success(sysNoticeService.insertSysNotice(dto));
    }

    /**
     * 修改通知公告
     *
     * @param dto 通知公告修改参数
     * @return 是否成功
     */
    @ApiOperation("修改通知公告")
    @PreAuthorize("@ss.hasPermi('sysnotice:sysNotice:edit')")
    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody SysNoticeDTO dto) {
        return R.success(sysNoticeService.updateSysNotice(dto));
    }

    /**
     * 删除通知公告
     * @param 　
     * @return 是否成功
     */
    @ApiOperation("删除通知公告" )
    @PreAuthorize("@ss.hasPermi('sysnotice:sysNotice:remove')")
    @Log(title = "通知公告", businessType = BusinessType.DELETE)
    @PostMapping("/delete" )
    public R<Boolean> remove(@RequestBody List<Long> idList) {
        return R.success(sysNoticeService.deleteSysNoticeByIds(idList));
    }

    /**
     * 查询列表上的更新部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询列表上的更新部门下拉" )
    @GetMapping("/listNoticeDept" )
    public R<PageInfo<DeptVO>> listNoticeDept(SysNoticeQuery query){
        return R.success(sysNoticeService.listNoticeDept(query));
    }

}
