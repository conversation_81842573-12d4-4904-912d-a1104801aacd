package com.py.common.utils;


import com.py.common.constant.LengthConstant;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2020/04/27 14:41
 * @Description 输入框校验工具类
 */
public class InputVerifyUtils {

    /**
     * 字符串N常量
     */
    private final static char N = 'N';

    /**
     * 特殊字符@常量
     */
    private final static String SPECIAL_CHAR_1 = "@";

    /**
     * 手机号校验正则
     * 第1位必须为1，总数是11位
     */
    public final static String MOBILE_REGEX = "^1[\\d]{10}";

    /**
     * 手机号校验正则
     * 第1位必须为1，总数是11位
     */
    private final static String PHONE_REGEX = "^[1][0-9][0-9]{9}$";

    private final static String NUM_REGEX = "^[0-9]*$";

    /**
     * 身份证号校验正则
     * 必须是18位，前17位为数字，最后一位是校验位，可能为数字或字符X。
     */
    private final static String IDENTITY_REGEX = "^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";

    /**
     * 密码校验正则
     * 必须同时包含大小写字母、数字、符号四种
     */
    private final static String PASSWORD1_REGEX = "^(?=.*[0-9]+)(?=.*[a-z]+)(?=.*[A-Z]+)(?=.*[-:!@#$/%^&*()<>,.?_+=`~·！@#￥%……&*（）——\\+={}|《》？：“”\"【】\\]\\[『』「」、；‘'，。、]+)[-:!@/#$%^&*()<>,.?_+=`~·！@#￥%……&*（）——\\+={}\\]\\[|《》？：“”\"【】『』「」、；‘'，。、0-9a-zA-Z]+$";
    /**
     * 密码校验正则
     * 必须同时包含字母、数字两种
     */
    private final static String PASSWORD2_REGEX = "^(\\d+[A-Za-z]+[A-Za-z0-9]*)|([A-Za-z]+\\d+[A-Za-z0-9]*)$";
    /**
     * 密码校验正则
     * 必须同时包含字母、符号两种
     */
    private final static String PASSWORD3_REGEX = "^(?=.*[a-zA-Z]+)(?=.*[-:!@#$/%^&*()<>,.?_+=`~·！@#￥%……&*（）——\\+={}|《》？：“”\"【】\\]\\[『』「」、；‘'，。、]+)[-:!@#$%/^&*()<>,.?_+=`~·！@#￥%……&*（）——\\+={}|《》？：“”\"【】\\]\\[『』「」、；‘'，。、a-zA-Z]+$";

    /**
     * 密码校验正则
     * 必须同时包含数字、符号两种
     */
    private final static String PASSWORD4_REGEX = "^(?=.*[0-9]+)(?=.*[-:!@#$/%^&*()<>,.?_+=`~·！@#￥%……&*（）——\\+={}|《》？：“”\"【】\\]\\[『』「」、；‘'，。、]+)[-:!@/#$%^&*()<>,.?_+=`~·！@#￥%……&*（）——\\+={}|《》？：“”\"【】\\]\\[『』「」、；‘'，。、0-9&&[^A-Z]]+$";

    /**
     * 密码校验正则
     * 必须只能是数字
     */
    private final static String PASSWORD5_REGEX = "[0-9]+";

    /**
     * 密码校验正则
     * 必须只能是字母
     */
    private final static String PASSWORD6_REGEX = "[a-zA-Z]+";

    /**
     * 密码校验正则
     * 必须只能是符号
     */
    private final static String PASSWORD7_REGEX = "^[-:!@/#$%^&*()<>,.?_+=`~·！@#￥%……&*（）——\\+={}|《》？：“”\"【】\\]\\[『』「」、；‘'，。、&&[^A-Z]]+$";
    /**
     * 密码校验正则
     * 必须包含字母或者数字两种
     */
    private final static String PASSWORD8_REGEX = "^(\\d|[A-Za-z]+[A-Za-z0-9]*)|([A-Za-z]|\\d|[A-Za-z0-9]*)$";
    /**
     * 账号校验正则
     * 账号能输入英文或者数字或者混合
     */
    private final static String ACCOUNT_REGEX = "^(\\d|[A-Za-z]+[A-Za-z0-9]*)|([A-Za-z]+\\d|[A-Za-z0-9]*)$";
    /**
     * 车牌号校验正则
     */
    private final static String TICKETNO_REGEX = "([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1})";

    /**
     * 网址校验正则
     * 网址格式以http(s)://开头，网址内容中间必须包含
     */
    private final static String URL_REGEX = "http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%#&=]*)?";

    /**
     * 文本校验正则
     * 只能输入中文、字母、空格
     */
    private final static String BASEINPUT6_REGEX = "^[a-zA-Z\\u4e00-\\u9fa5 ]+$";

    /**
     * 文本校验正则
     * 中文，字母，数字
     */
    private final static String BASEINPUT1_REGEX = "^[\\u4E00-\\u9FA5A-Za-z0-9_]+$";
    /**
     * 文本校验正则
     * 只能输入中文
     */
    private final static String BASEINPUT2_REGEX = "[\u4e00-\u9fa5]+";
    /**
     * 文本校验正则
     * 只能输入纯正整数
     */
    private final static String BASEINPUT3_REGEX = "^[1-9]\\d*$";

    /**
     * 文本校验正则
     * 只能输入正浮点数且最多保留2位小数
     */
    private final static String BASEINPUT4_REGEX = "^(([1-9]{1}\\d*)|(0{1}))(\\.\\d{0,2})?$";
    /**
     * 文本校验正则
     * 只能输入纯字母，不用区分大小写
     */
    private final static String BASEINPUT5_REGEX = "[A-Za-z]+";

    /**
     * 文本校验正则
     * 只能输入字母（不区分大小写）和数字
     */
    private final static String BASEINPUT7_REGEX = "^[a-zA-Z\\d]+$";

    /**
     * 银行卡校验
     */
    private final static String BANK_CARD_D = "\\d+";

    /**
     * 银行卡校验-魔法值N
     */
    private final static char BANK_CARD_N = 'N';

    /**
     * 银行卡校验-魔法值0
     */
    private final static char BANK_CARD_0 = '0';

    /**
     * 项目编号校验
     */
    private final static String PROJECT_NUMBER_REGEX = "^[A-Za-z0-9]{6,30}$";

    /**
     * 项目名称校验
     */
    private final static String PROJECT_NAME_REGEX = "^[\\s\\S]{2,30}$";

    /**
     * 角色名称校验
     */
    private final static String ROLE_NAME_REGEX = "^[\\s\\S]{1,20}$";

    /**
     * 基础校验规则
     */
    private final static String BASE_REGEX = "^[\\s\\S]*$";

    /**
     * 项目金额检验
     */
    public final static String PROJECT_AMOUNT_REGEX = "^(0{1}|[1-9]\\d{0,7})(\\.\\d{1,2})?$";

    /**
     * 金额检验（最多两位小数）
     */
    public final static String NUMBER_REGEX = "^\\d+(\\.\\d{1,2})?$";

    /**
     * TicketNoInput 车牌号输入框
     * 1、校验输入不能为空
     * 2、正常车牌校验输入只能是7位字符
     * 3、新能源车牌校验输入只能是8位字符
     * 4、新能源中的小型车，第一位：只能用字母D或字母F，第二位：字母或者数字，后四位：必须使用数字
     * 5、新能源中大型车，前五位：必须使用数字，第六位：只能用字母D或字母F
     *
     * @param ticketNo
     * @return
     */
    public static String verifyTicketNo(String ticketNo) {
        if (StringUtils.isBlank(ticketNo)) {
            return "请输入车牌号";
        }
        if (!ticketNo.matches(TICKETNO_REGEX)) {
            return "请输入正确的车牌号";
        }
        return null;
    }

    /**
     * BankCardInput 银行卡号输入框
     * 1、校验输入不能为空
     * 2、银行卡号长度必须在15位-19位之间
     * 3、校验银行卡格式是否正确
     *
     * @param bankCard
     * @return
     */
    public static String verifyBankCard(String bankCard) {
        if (StringUtils.isBlank(bankCard)) {
            return "请输入银行卡号";
        }
        if (bankCard.length() < LengthConstant.FIFTEEN || bankCard.length() > LengthConstant.NINETEEN) {
            return "请输入15到19位的长度";
        }
        char bit = getBankCardCheckCode(bankCard.substring(0, bankCard.length() - 1));
        Boolean flag = bankCard.charAt(bankCard.length() - 1) == bit;
        if (bit == N || !flag) {
            return "请输入正确的银行卡号";
        }
        return null;
    }

    /**
     * 从不含校验位的银行卡卡号采用 Luhm 校验算法获得校验位
     * 校验过程：
     * 1、从卡号最后一位数字开始，逆向将奇数位(1、3、5等等)相加。
     * 2、从卡号最后一位数字开始，逆向将偶数位数字，先乘以2（如果乘积为两位数，将个位十位数字相加，即将其减去9），再求和。
     * 3、将奇数位总和加上偶数位总和，结果应该可以被10整除。
     *
     * @param nonCheckCodeBankCard
     * @return
     */
    private static char getBankCardCheckCode(String nonCheckCodeBankCard) {
        if (nonCheckCodeBankCard == null || nonCheckCodeBankCard.trim().length() == 0
                || !nonCheckCodeBankCard.matches(BANK_CARD_D)) {
            //如果传的不是数据返回N
            return BANK_CARD_N;
        }
        char[] chs = nonCheckCodeBankCard.trim().toCharArray();
        int luhmSum = 0;
        for (int i = chs.length - 1, j = 0; i >= 0; i--, j++) {
            int k = chs[i] - BANK_CARD_0;
            if (j % 2 == 0) {
                k *= 2;
                k = k / 10 + k % 10;
            }
            luhmSum += k;
        }
        return (luhmSum % 10 == 0) ? BANK_CARD_0 : (char) ((10 - luhmSum % 10) + BANK_CARD_0);
    }

    /**
     * UrlInput 网址输入框
     * 1、校验输入不能为空
     * 2、校验输入不能是非法字符
     *
     * @param url
     * @return
     */
    public static String verifyUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return "请输入网址";
        }
        if (!url.matches(URL_REGEX)) {
            return "请输入正确的网址";
        }
        return null;
    }

    /**
     * TextFieldInput 文本域输入框
     * 1、校验输入不能为空
     * 2、字符长度最小值为min位，最大长度为max位（可以根据实际情况更改）
     *
     * @param txtField
     * @param min
     * @param max
     * @return
     */
    public static String verifyTextField(String txtField, String min, String max) {
        if (StringUtils.isBlank(txtField)) {
            return "请输入内容";
        }
        if (txtField.length() < Long.valueOf(min)) {
            return "内容长度不得小于" + min + "位字符";
        }
        if (txtField.length() > Long.valueOf(max)) {
            return "内容长度不得超过" + max + "位字符";
        }
        return null;
    }

    /**
     * MobileNoInput 手机号输入框校验
     * 1、校验输入不能为空
     * 2、只能输入数字。校验手机格式，第1位必须为1，总数是11位
     *
     * @param mobile
     * @return
     */
    public static String mobileCheck(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return "请您填写手机号码";
        }

        Matcher mobileCheck = Pattern.compile(MOBILE_REGEX).matcher(mobile);
        if (!mobileCheck.matches()) {
            return "请输入正确的手机号";
        }

        return null;
    }

    public static String phoneCheck(String phone) {
        if (phone.length() != LengthConstant.ELEVEN) {
            return "手机号应为11位数";
        } else {
            Pattern p = Pattern.compile(PHONE_REGEX);
            Matcher m = p.matcher(phone);
            boolean isMatch = m.matches();
            if (!isMatch) {
                return "您的手机号格式不正确";
            }
        }
        return null;
    }

    public static String codeCheck(String s) {
        if (s.length() > LengthConstant.ELEVEN) {
            return "邀请码不得超过11位字符";
        }
        if (!"".equals(s.trim())) {
            boolean matches = s.matches(NUM_REGEX);
            if (!matches) {
                return "邀请码必须为数字";
            }
        }
        return null;
    }

    /**
     * EmailInput 邮箱输入框校验
     * 1、校验输入不能为空
     * 2、校验字符串中是否有@
     *
     * @param email
     * @return
     */
    public static String emailCheck(String email) {
        if (StringUtils.isBlank(email)) {
            return "请输入邮箱";
        }

        if (email.length() > LengthConstant.FIFTY) {
            return "输入不得超过50位字符";
        }

        if (!email.contains(SPECIAL_CHAR_1)) {
            return "请输入正确的邮箱";
        }

        return null;
    }

    /**
     * IdentityInput 身份证号输入框校验
     * 1、校验输入不能为空
     * 2、校验输入一定是18位，前17位为数字，最后一位是校验位，可能为数字或字符X。
     *
     * @param identityNum
     * @return
     */
    public static String identityCheck(String identityNum) {
        if (StringUtils.isBlank(identityNum)) {
            return "请输入身份证号";
        }

        Matcher mobileCheck = Pattern.compile(IDENTITY_REGEX).matcher(identityNum);
        if (!mobileCheck.matches()) {
            return "请输入正确的身份证号";
        }
        if (identityNum.length() != LengthConstant.EIGHTEEN){
            return "身份证号应为18位";
        }

        return null;
    }

    /**
     * PwdInput1 密码输入框校验
     * 1、校验输入不能为空
     * 2、字符长度最小值为min位，最大长度为max位（可以根据实际情况更改），超过字数限制不能再输入
     * 3、必须包含字母（大小写）、数字、符号
     * 4、校验密码的隐藏和显示操作
     */
    public static String pwd1Check(String inputPwd, String min, String max) {
        String msg = checkParam(inputPwd, min, max, "必须同时包含大小写字母、数字、符号四种且长度为");
        if (msg != null) {
            return msg;
        }
        Matcher pwdCheck = Pattern.compile(PASSWORD1_REGEX).matcher(inputPwd);
        if (!pwdCheck.matches()) {
            return "必须同时包含大小写字母、数字、符号四种且长度为" + min + "到" + max + "位";
        }

        return null;
    }

    private static String checkParam(String inputPwd, String min, String max, String msg) {
        if (StringUtils.isBlank(inputPwd)) {
            return "请输入密码";
        }
        if (inputPwd.length() < Long.valueOf(min) || inputPwd.length() > Long.valueOf(max)) {
            return msg + min + "到" + max + "位";
        }
        return null;
    }

    /**
     * PwdInput2 密码输入框校验
     * 1、校验输入不能为空
     * 2、字符长度最小值为min位，最大长度为max位（可以根据实际情况更改），超过字数限制不能再输入
     * 3、必须包含字母、数字
     * 4、校验密码的隐藏和显示操作
     */
    public static String pwd2Check(String inputPwd, String min, String max) {
        String msg = checkParam(inputPwd, min, max, "必须包含字母或者数字两种且长度为");
        if (msg != null) {
            return msg;
        }
        Matcher pwdCheck = Pattern.compile(PASSWORD8_REGEX).matcher(inputPwd);
        if (!pwdCheck.matches()) {
            return "必须包含字母或者数字两种且长度为" + min + "到" + max + "位";
        }

        return null;
    }

    /**
     * PwdInput3 密码输入框校验
     * 1、校验输入不能为空
     * 2、字符长度最小值为min位，最大长度为max位（可以根据实际情况更改），超过字数限制不能再输入
     * 3、必须包含字母、符号
     * 4、校验密码的隐藏和显示操作
     */
    public static String pwd3Check(String inputPwd, String min, String max) {
        String msg = checkParam(inputPwd, min, max, "必须同时包含字母、符号两种且长度为");

        if (msg != null) {
            return msg;
        }
        Matcher pwdCheck = Pattern.compile(PASSWORD3_REGEX).matcher(inputPwd);
        if (!pwdCheck.matches()) {
            return "必须同时包含字母、符号两种且长度为" + min + "到" + max + "位";

        }

        return null;
    }

    /**
     * PwdInput4 密码输入框校验
     * 1、校验输入不能为空
     * 2、字符长度最小值为min位，最大长度为max位（可以根据实际情况更改），超过字数限制不能再输入
     * 3、必须包含字数字、符号
     * 4、校验密码的隐藏和显示操作
     */
    public static String pwd4Check(String inputPwd, String min, String max) {
        String msg = checkParam(inputPwd, min, max, "必须同时包含数字、符号两种且长度为");

        if (msg != null) {
            return msg;
        }
        Matcher pwdCheck = Pattern.compile(PASSWORD4_REGEX).matcher(inputPwd);
        if (!pwdCheck.matches()) {
            return "必须同时包含数字、符号两种且长度为" + min + "到" + max + "位";

        }

        return null;
    }

    /**
     * PwdInput5 密码输入框校验
     * 1、校验输入不能为空
     * 2、字符长度最小值为min位，最大长度为max位（可以根据实际情况更改），超过字数限制不能再输入
     * 3、必须是纯数字
     * 4、校验密码的隐藏和显示操作
     */
    public static String pwd5Check(String inputPwd, String min, String max) {
        String msg = checkParam(inputPwd, min, max, "必须只能是数字且长度为");

        if (msg != null) {
            return msg;
        }
        Matcher pwdCheck = Pattern.compile(PASSWORD5_REGEX).matcher(inputPwd);
        if (!pwdCheck.matches()) {
            return "必须只能是数字且长度为" + min + "到" + max + "位";

        }

        return null;
    }

    /**
     * PwdInput6 密码输入框校验
     * 1、校验输入不能为空
     * 2、字符长度最小值为min位，最大长度为max位（可以根据实际情况更改），超过字数限制不能再输入
     * 3、必须是纯字母
     * 4、校验密码的隐藏和显示操作
     */
    public static String pwd6Check(String inputPwd, String min, String max) {
        String msg = checkParam(inputPwd, min, max, "必须只能是字母且长度为");

        if (msg != null) {
            return msg;
        }
        Matcher pwdCheck = Pattern.compile(PASSWORD6_REGEX).matcher(inputPwd);
        if (!pwdCheck.matches()) {
            return "必须只能是字母且长度为" + min + "到" + max + "位";

        }

        return null;
    }

    /**
     * PwdInput7 密码输入框校验
     * 1、校验输入不能为空
     * 2、字符长度最小值为min位，最大长度为max位（可以根据实际情况更改），超过字数限制不能再输入
     * 3、必须是纯符号
     * 4、校验密码的隐藏和显示操作
     */
    public static String pwd7Check(String inputPwd, String min, String max) {
        String msg = checkParam(inputPwd, min, max, "必须只能包含符号且长度为");

        if (msg != null) {
            return msg;
        }
        Matcher pwdCheck = Pattern.compile(PASSWORD7_REGEX).matcher(inputPwd);
        if (!pwdCheck.matches()) {
            return "必须只能包含符号且长度为" + min + "到" + max + "位";

        }

        return null;
    }

    /**
     * BaseInput6 中文+字母+空格（允许中间有空格）
     * 1、校验输入不能为空
     * 2、长度不超过max位字符（可以根据实际业务更改）
     * 3、只能输入中文、字母、空格
     *
     * @param baseInput
     * @param max
     * @return
     */
    public static String verifybaseInput6(String baseInput, String max) {
        if (StringUtils.isBlank(baseInput)) {
            return "请输入";
        }
        if (baseInput.length() > Long.valueOf(max)) {
            return "输入不得超过" + max + "位字符";
        }
        Matcher pwdCheck = Pattern.compile(BASEINPUT6_REGEX).matcher(baseInput);
        if (!pwdCheck.matches()) {
            return "请填写正确格式：只能输入中文、字母、空格";
        }
        return null;
    }

    /**
     * BaseInput7 任意字符输入框
     * 1、校验输入不能为空
     * 2、长度不超过max位字符（可以根据实际业务更改）
     * 3、不能输入表情符号
     *
     * @param baseInput
     * @param max
     * @return
     */
    public static String verifyBaseInput7(String baseInput, String max) {
        if (StringUtils.isBlank(baseInput)) {
            return "请输入";
        }
        if (baseInput.length() > Long.valueOf(max)) {
            return "输入不得超过" + max + "位字符";
        }
        int len = baseInput.length();
        for (int i = 0; i < len; i++) {
            char codePoint = baseInput.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认有表情字符
                return "请填写正确格式：不能输入表情符号";
            }
        }
        return null;
    }

    /**
     * BaseInput8 任意字符输入框
     * 1、校验输入不能为空
     * 2、长度不超过max位字符（可以根据实际业务更改）
     * 3、不能输入表情符号
     *
     * @param baseInput
     * @param max
     * @return
     */
    public static boolean verifyBaseInput8(String baseInput, String max) {
        if (baseInput.length() > Long.valueOf(max)) {
            return false;
        }
        int len = baseInput.length();
        for (int i = 0; i < len; i++) {
            char codePoint = baseInput.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认有表情字符
                return false;
            }
        }
        return true;
    }

    /**
     * BaseInput1 基本输入框：中文+字母+数字
     * 1、校验输入不能为空
     * 2、长度不超过max位字符（可以根据实际业务更改）
     * 3、可以输入中文、字母、数字
     *
     * @param inputValue
     * @param max
     * @return
     */
    public static String baseInput1Check(String inputValue, String max) {
        if (StringUtils.isBlank(inputValue)) {
            return "请输入";
        }
        if (inputValue.length() > Long.valueOf(max)) {
            return "输入不得超过" + max + "位字符";
        }
        Matcher pwdCheck = Pattern.compile(BASEINPUT1_REGEX).matcher(inputValue);
        if (!pwdCheck.matches()) {
            return "请填写正确格式：中文、字母或数字";
        }
        return null;
    }

    /**
     * BaseInput2 基本输入框：纯中文
     * 1、校验输入不能为空
     * 2、长度不超过max位字符（可以根据实际业务更改）
     * 3、只能输入中文
     *
     * @param inputValue
     * @param max
     * @return
     */
    public static String baseInput2Check(String inputValue, String max) {
        if (StringUtils.isBlank(inputValue)) {
            return "请输入";
        }
        if (inputValue.length() > Long.valueOf(max)) {
            return "输入不得超过" + max + "位字符";
        }
        Matcher pwdCheck = Pattern.compile(BASEINPUT2_REGEX).matcher(inputValue);
        if (!pwdCheck.matches()) {
            return "请填写正确格式：只能输入中文";
        }
        return null;
    }


    /**
     * BaseInput3 基本输入框：纯正整数
     * 1、校验输入不能为空
     * 2、最小值为min，最大值为max
     * 3、只能输入纯正整数
     *
     * @param inputValue
     * @param min
     * @param max
     * @return
     */
    public static String baseInput3Check(String inputValue, String min, String max) {
        if (StringUtils.isBlank(inputValue)) {
            return "请输入";
        }

        Matcher numberCheck = Pattern.compile(BASEINPUT3_REGEX).matcher(inputValue);
        if (!numberCheck.matches()) {
            return "请填写正确格式：只能输入正整数";

        }

        if(new BigDecimal(inputValue).scale() > 0) {
            return "请填写正确格式：只能输入正整数";
        }

        if(new BigDecimal(inputValue).compareTo(new BigDecimal(max)) > 0) {
            return "输入不得超过最大值" + max;
        }
        if(null != min && new BigDecimal(inputValue).compareTo(new BigDecimal(min)) < 0) {
            return "输入不得小于最小值" + min;
        }

        return null;
    }

    /**
     * priceInputCheck 基本输入框：纯正整数
     * 1、校验输入不能为空
     * 2、最小值为min，最大值为max
     * 3、只能输入纯正整数
     *
     * @param inputValue
     * @param min
     * @param max
     * @return
     */
    public static String priceInputCheck(String inputValue, String min, String max) {
        if (StringUtils.isBlank(inputValue)) {
            return "请输入";
        }

        Matcher numberCheck = Pattern.compile(BASEINPUT3_REGEX).matcher(inputValue);
        if (!numberCheck.matches()) {
            return "请填写正确格式：只能输入正浮点数且最多保留2位小数";

        }

        if(new BigDecimal(inputValue).scale() > 0) {
            return "请填写正确格式：只能输入正浮点数且最多保留2位小数";
        }

        if(new BigDecimal(inputValue).compareTo(new BigDecimal(max)) > 0) {
            return "输入不得超过最大值" + max;
        }
        if(null != min && new BigDecimal(inputValue).compareTo(new BigDecimal(min)) < 0) {
            return "输入不得小于最小值" + min;
        }

        return null;
    }

    /**
     * BaseInput4 基本输入框：正浮点数
     * 1、校验输入不能为空
     * 2、最小值为min，最大值为max
     * 3、只能输入浮点数，保留小数点后2位
     *
     * @param inputValue
     * @param min
     * @param max
     * @return
     */
    public static String baseInput4Check(String inputValue, String min, String max) {
        if (StringUtils.isBlank(inputValue)) {
            return "请输入";
        }
        Matcher pwdCheck = Pattern.compile(BASEINPUT4_REGEX).matcher(inputValue);
        if (!pwdCheck.matches()) {
            return "请填写正确格式：只能输入正浮点数且最多保留2位小数";
        }

        if (new BigDecimal(inputValue).scale() < LengthConstant.TWO) {
            return "请填写正确格式：只能输入正浮点数且最多保留2位小数";
        }

        if (new BigDecimal(inputValue).compareTo(new BigDecimal(max)) == 1) {
            return "输入不得超过最大值" + max;
        }
        if (null != min && new BigDecimal(inputValue).compareTo(new BigDecimal(min)) == -1) {
            return "输入不得小于最小值" + min;
        }

        return null;
    }

    /**
     * BaseInput5 基本输入框：纯字母，不用区分大小写
     * 1、校验输入不能为空
     * 2、长度不超过max位字符（可以根据实际业务更改）
     * 3、只能输入字母
     *
     * @param inputValue
     * @param max
     * @return
     */
    public static String baseInput5Check(String inputValue, String max) {
        if (StringUtils.isBlank(inputValue)) {
            return "请输入";
        }
        if (inputValue.length() > Long.valueOf(max)) {
            return "输入不得超过" + max + "位字符";
        }
        Matcher pwdCheck = Pattern.compile(BASEINPUT5_REGEX).matcher(inputValue);
        if (!pwdCheck.matches()) {
            return "请填写正确格式：只能输入纯字母";
        }
        return null;
    }

    /**
     * BaseInput7 基本输入框：字母+数字
     * 1、校验输入不能为空
     * 2、长度不超过max位字符（可以根据实际业务更改）
     * 3、可以输入字母、数字
     * 4、字母不区分大小写
     *
     * @param inputValue
     * @param max
     * @return
     */
    public static String baseInput7Check(String inputValue, String max) {
        Matcher pwdCheck = Pattern.compile(BASEINPUT7_REGEX).matcher(inputValue);
        if (!pwdCheck.matches()) {
            return "请填写正确格式：字母和数字";
        }
        if (inputValue.length() != Long.valueOf(max)) {
            return "输入值必须为" + max + "位字符";
        }
        return null;
    }

    /**
     * BaseInput3 基本输入框：纯正整数
     * 1、校验输入不能为空
     * 2、最小值为min，最大值为max
     * 3、只能输入纯正整数
     *
     * @param inputValue
     * @param max
     * @return
     */
    public static String baseInput8Check(String inputValue, String max) {
        Matcher numberCheck = Pattern.compile(BASEINPUT3_REGEX).matcher(inputValue);
        if (!numberCheck.matches()) {
            return "请填写正确格式：只能输入正整数";
        }
        if (inputValue.length() != Long.valueOf(max)) {
            return "输入值必须为" + max + "位字符";
        }
        return null;
    }

    /**
     * 判断是否为非Emoji字符
     *
     * @param codePoint 比较的单个字符
     * @return
     */
    public static boolean isNotEmojiCharacter(char codePoint) {
        return (codePoint == 0x0)
                || (codePoint == 0x9)
                || (codePoint == 0xA)
                || (codePoint == 0xD)
                || ((codePoint >= 0x20)
                && (codePoint <= 0xD7FF))
                || ((codePoint >= 0xE000)
                && (codePoint <= 0xFFFD))
                || ((codePoint >= 0x10000)
                && (codePoint <= 0x10FFFF));
    }

    /**
     * 校验是否含有Emoji字符
     *
     * @param baseInput
     * @return
     */
    public static boolean checkIsEmoji(String baseInput) {
        int len = baseInput.length();
        for (int i = 0; i < len; i++) {
            char codePoint = baseInput.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认含有表情
                return true;
            }
        }
        return false;
    }

    /**
     * codePhotoInput 图片验证码输入框校验
     * 1、校验输入不能为空
     *
     * @param codePhoto
     * @return
     */
    public static String codePhotoCheck(String codePhoto) {
        if(StringUtils.isBlank(codePhoto)) {
            return "请您填写图片验证码";
        }
        if(codePhoto.length() != 4) {
            return "图片验证码必须为4位";
        }
        return null;
    }

    /**
     * codePhoneInput 手机验证码输入框校验
     * 1、校验输入不能为空
     * 2、只能输入纯数字
     * @param codePhone
     * @return
     */
    public static String codePhoneCheck(String codePhone) {
        if (StringUtils.isBlank(codePhone)) {
            return "请您填写手机验证码";
        }
        Matcher codePhoneCheck = Pattern.compile(PASSWORD5_REGEX).matcher(codePhone);
        if(!codePhoneCheck.matches()) {
            return "请输入正确格式：只能输入整数";
        }
        if(codePhone.length() != 6) {
            return "手机验证码必须为6位";
        }
        return null;
    }
    /**
     * passwordInput 密码输入框校验
     * 1、校验输入不能为空
     *
     * @param password
     * @return
     */
    public static String passwordCheck(String password) {
        if (StringUtils.isBlank(password)) {
            return "请您填写密码";
        }
        return null;
    }

    /**
     * account 账号输入框校验
     * 1、校验输入不能为空
     *
     * @param account
     * @return
     */
    public static String accountCheck(String account, String min, String max) {
        Matcher numberCheck = Pattern.compile(ACCOUNT_REGEX).matcher(account);
        if(StringUtils.isBlank(account)) {
            return "请您填写账号";
        }
        if(account.length() > Integer.parseInt(max)) {
            return "账号长度不得大于" + max + "位字符";
        }
        if(account.length() < Integer.parseInt(min)) {
            return "账号长度不得小于" + min + "位字符";
        }
        if(!numberCheck.matches()) {
            return "请填写正确格式";
        }
        return null;
    }


    /**
     * menuName 菜单名称校验
     * 1. 上线可输入30个字符
     * 2. B7任意字符
     * 3.不支持表情
     *
     * @param menuName 菜单名称
     * @return
     */
    public static String menuNameCheck(String menuName) {

        if (menuName.length() > LengthConstant.THIRTY) {
            return "菜单名称不得超过30个字符";
        }
        Matcher proNameCheck = Pattern.compile(BASE_REGEX).matcher(menuName);
        if (!proNameCheck.matches()) {
            return "请填写正确格式：B7任意字符等";
        }
        int len = menuName.length();
        for (int i = 0; i < len; i++) {
            char codePoint = menuName.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认有表情字符
                return "请填写正确格式：不能输入表情符号";
            }
        }
        return null;
    }

    /**
     * 通用url 路径校验
     * 1. 上线可输入100个字符
     * 2. B7任意字符
     * 3.不支持表情
     *
     * @param url 路径
     * @return
     */
    public static String urlCheck(String url) {
        if(StringUtils.isBlank(url)){
            return null;
        }
        if (url.length() > LengthConstant.LENGTH_ONE_HUNDRED) {
            return "接口路径不得超过100个字符";
        }
        Matcher proNameCheck = Pattern.compile(BASE_REGEX).matcher(url);
        if (!proNameCheck.matches()) {
            return "请填写正确格式：B7任意字符等";
        }
        int len = url.length();
        for (int i = 0; i < len; i++) {
            char codePoint = url.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认有表情字符
                return "请填写正确格式：不能输入表情符号";
            }
        }
        return null;
    }

    /**
     * roleName 角色名称校验
     * 1. 上线可输入20个字符
     * 2. B7任意字符
     * 3.不支持表情
     *
     * @param roleName 角色名称
     * @return
     */
    public static String roleNameCheck(String roleName) {
        if (roleName.length() > LengthConstant.LENGTH_TWENTY) {
            return "角色名称不得超过20个字符";
        }
        Matcher proNameCheck = Pattern.compile(ROLE_NAME_REGEX).matcher(roleName);
        if (!proNameCheck.matches()) {
            return "请填写正确格式：B7任意字符等";
        }
        int len = roleName.length();
        for (int i = 0; i < len; i++) {
            char codePoint = roleName.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认有表情字符
                return "请填写正确格式：不能输入表情符号";
            }
        }
        return null;
    }

    public static String roleDescCheck(String roleDesc) {
        if (roleDesc.length() > LengthConstant.LENGTH_ONE_HUNDRED) {
            return "角色描述不得超过100个字符";
        }
        return null;
    }

    /**
     * operateName 权限名称校验
     * 1. 上线可输入30个字符
     * 2. B7任意字符
     * 3.不支持表情
     *
     * @param operateName 权限名称
     * @return
     */
    public static String operateNameCheck(String operateName) {
        if (operateName.length() > LengthConstant.LENGTH_TWENTY) {
            return "权限名称不得超过30个字符";
        }
        Matcher proNameCheck = Pattern.compile(BASE_REGEX).matcher(operateName);
        if (!proNameCheck.matches()) {
            return "请填写正确格式：B7任意字符等";
        }
        int len = operateName.length();
        for (int i = 0; i < len; i++) {
            char codePoint = operateName.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认有表情字符
                return "请填写正确格式：不能输入表情符号";
            }
        }
        return null;
    }

    /**
     * operateCode 权限标识校验
     * 1. 上线可输入100个字符
     * 2. B7任意字符
     * 3.不支持表情
     *
     * @param operateCode 权限标识
     * @return
     */
    public static String operateCodeCheck(String operateCode) {
        if (operateCode.length() > LengthConstant.LENGTH_ONE_HUNDRED) {
            return "授权标识不得超过100个字符";
        }
        Matcher proNameCheck = Pattern.compile(BASE_REGEX).matcher(operateCode);
        if (!proNameCheck.matches()) {
            return "请填写正确格式：B7任意字符等";
        }
        int len = operateCode.length();
        for (int i = 0; i < len; i++) {
            char codePoint = operateCode.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认有表情字符
                return "请填写正确格式：不能输入表情符号";
            }
        }
        return null;
    }

    /**
     * projectNumber 项目编号校验
     * 1、必填
     * 2.上线可输入30字符
     * 3.仅支持字母或数字
     *
     * @param projectNumber
     * @return
     */
    public static String projectNumberCheck(String projectNumber) {
        if(StringUtils.isBlank(projectNumber)){
            return "请输入项目编号";
        }
        if (projectNumber.length() > LengthConstant.THIRTY) {
            return "项目编号不得超过30个字符";
        }
        Matcher projectNumberCheck = Pattern.compile(PASSWORD8_REGEX).matcher(projectNumber);
        if (!projectNumberCheck.matches()) {
            return "请填写正确格式：项目编号仅支持字母或数字";
        }
        return null;
    }

    /**
     * projectName 项目名称校验
     * 1.必填
     * 1.上线可输入30个字符
     * 2.任意字符
     * 3.不支持表情
     *
     * @param projectName
     * @return
     */
    public static String projectNameCheck(String projectName) {
        if(StringUtils.isBlank(projectName)){
            return "请输入项目名称";
        }
        if (projectName.length() > LengthConstant.THIRTY) {
            return "项目名称不得超过30个字符";
        }
        int len = projectName.length();
        for (int i = 0; i < len; i++) {
            char codePoint = projectName.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认有表情字符
                return "项目名称请填写正确格式：不能输入表情符号";
            }
        }
        return null;
    }

    /**
     * projectAddress 项目地址校验
     * 1.必填
     * @param projectAddress
     * @return
     */
    public static String projectAddressCheck(String projectAddress) {
        if(StringUtils.isBlank(projectAddress)){
            return "请输入项目地址";
        }
        return null;
    }

    /**
     * projectLeaderId 项目负责人校验
     * 1.必填
     * @param projectLeaderId
     * @return
     */
    public static String projectLeaderIdCheck(String projectLeaderId) {
        if(Objects.isNull(projectLeaderId)){
            return "项目负责人不能为空";
        }
        return null;
    }

    /**
     * startTime 开始时间
     * 1.必填
     * @param startTime
     * @return
     */
    public static String startTimeCheck(String startTime) {
        if(Objects.isNull(startTime)){
            return "开始时间不能为空";
        }
        return null;
    }


    /**
     * projectDescription 项目描述校验
     *
     * 1.上限可输入300个字符
     * 2.B7任意字符
     * 3.不支持表情
     *
     * @param projectDescription
     * @return
     */
    public static String projectDescriptionCheck(String projectDescription) {
        if(StringUtils.isBlank(projectDescription)){
            return null;
        }
        if (projectDescription.length() > LengthConstant.LENGTH_THREE_HUNDRED) {
            return "项目描述不得超过300个字符";
        }
        int len = projectDescription.length();
        for (int i = 0; i < len; i++) {
            char codePoint = projectDescription.charAt(i);
            if (!isNotEmojiCharacter(codePoint)) {
                //判断到了这里表明，确认有表情字符
                return "项目描述请填写正确格式：不能输入表情符号";
            }
        }
        return null;
    }

    /**
     * serviceType 服务类型
     * 1.必填
     * @param serviceType
     * @return
     */
    public static String serviceTypeCheck(String serviceType) {
        if(Objects.isNull(serviceType)){
            return "服务类型不能为空";
        }
        return null;
    }

    /**
     * province 省
     * 1.必填
     * @param province
     * @return
     */
    public static String provinceNameCheck(String province) {
        if(Objects.isNull(province)){
            return "省不能为空";
        }
        return null;
    }

    /**
     * provinceCode 省CODE码
     * 1.必填
     * @param provinceCode
     * @return
     */
    public static String provinceCodeCheck(String provinceCode) {
        if(Objects.isNull(provinceCode)){
            return "省CODE码不能为空";
        }
        return null;
    }

    /**
     * city 市
     * 1.必填
     * @param city
     * @return
     */
    public static String cityNameCheck(String city) {
        if(Objects.isNull(city)){
            return "市不能为空";
        }
        return null;
    }

    /**
     * cityCode 市CODE码
     * 1.必填
     * @param cityCode
     * @return
     */
    public static String cityCodeCheck(String cityCode) {
        if (Objects.isNull(cityCode)) {
            return "市CODE码不能为空";
        }
        return null;
    }

    /**
     * area 区
     * 1.必填
     * @param area
     * @return
     */
    public static String areaNameCheck(String area) {
        if (Objects.isNull(area)) {
            return "区不能为空";
        }
        return null;
    }

    /**
     * areaCode 区CODE码
     * 1.必填
     * @param areaCode
     * @return
     */
    public static String areaCodeCheck(String areaCode) {
        if (Objects.isNull(areaCode)) {
            return "区CODE码不能为空";
        }
        return null;
    }

}
