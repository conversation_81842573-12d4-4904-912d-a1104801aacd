package com.py.crm.customer.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 客户管理-客户/业务线模糊下拉搜索对象
 * <AUTHOR>
 */
@Data
@ApiModel("客户管理-客户/业务线模糊下拉搜索对象" )
public class CustomerLineBusinessQuery implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 品牌/业务线*/
    @ApiModelProperty("品牌/业务线" )
    private String lineBusiness;

    /** 客户名称*/
    @ApiModelProperty("客户名称" )
    private String name;

    /** 合作状态(0.合作中 1.暂停合作 2.意向合作)*/
    @ApiModelProperty("合作状态(0.合作中 1.暂停合作 2.意向合作)" )
    private Integer cooperationStatus;

}
