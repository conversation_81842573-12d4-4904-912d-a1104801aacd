package com.py.common.tools.verify.verifyprovider.impl;

import com.py.common.tools.verify.enums.VerifyType;
import com.py.common.tools.verify.verifyprovider.VerifyProvider;
import com.py.common.utils.DateUtils;
import com.py.common.utils.StringUtils;
import org.springframework.context.annotation.Configuration;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 时间范围校验
 * @date 2024/2/20 11:13
 */
@Configuration
public class DateRangeVerifyProvider implements VerifyProvider {
    @Override
    public VerifyType supportedVerifyType() {
        return VerifyType.DateRange;
    }

    @Override
    public boolean verify(Object target) throws Exception {
        if(target == null) {
            return true;
        }

        if(target instanceof String == false) {
            return false;
        }
        if(StringUtils.isBlank(target.toString())){
            return true;
        }
        Date date = DateUtils.parseDate(target);
        if (date == null){
            return true;
        }
        // 获取最小日期和最大日期
        Date minDate = DateUtils.getMinDateTime();
        Date maxDate = DateUtils.getMaxDateTime();

        // 检查日期是否在范围内
        return date.compareTo(minDate) >= 0 && date.compareTo(maxDate) <= 0;
    }

}
