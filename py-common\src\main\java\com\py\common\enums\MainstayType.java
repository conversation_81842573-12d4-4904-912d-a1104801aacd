package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 主体类型枚举
 * <AUTHOR>
 */
@AllArgsConstructor
public enum MainstayType implements IDict<String>{
    /**
     * 项目主体
     */
    PROJECT_MAINSTAY("0","项目主体"),
    /**
     * OA主体
     */
    OA_MAINSTAY("1","OA主体")
    ;

    private final String value;
    private final String label;
    @Override
    public String getLabel() {
        return this.label;
    }

    @Override
    public String getValue() {
        return this.value;
    }
}
