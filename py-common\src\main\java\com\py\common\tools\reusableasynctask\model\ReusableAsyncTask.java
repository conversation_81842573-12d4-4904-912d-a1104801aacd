package com.py.common.tools.reusableasynctask.model;

import com.py.common.utils.JsonUtil;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * 可重用异步任务
 * @param <TaskArgs> 任务参数
 * <AUTHOR>
 */
public interface ReusableAsyncTask<TaskArgs  extends ReusableAsyncTaskArgs> {

    /**
     * 执行任务
     * @param args 任务参数
     */
    void execute(TaskArgs args);

    /**
     * 获取任务参数类型
     * @return 任务参数类型
     */
    Class<TaskArgs> argsClass();

    /**
     * 执行任务
     * @param argsJson 任务参数json
     */
    default void execute(String argsJson, UserDetails userDetails){
        TaskArgs args = JsonUtil.string2Obj(argsJson,this.argsClass());
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
        this.execute(args);
    }
}
