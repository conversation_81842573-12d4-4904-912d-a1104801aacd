package com.py.common.tools.verify.enums;

/**
 * 验证类型
 * <AUTHOR>
 */
public enum VerifyType {
    /** 对象不为Null */
    NotNull,
    /** 字符串不为空 */
    NotBlank,
    /** 输入为True */
    IsTrue,
    /** 输入为False */
    IsFalse,
    /** 枚举标签值合法 */
    EnumLabelLegal,
    /** 长度校验 */
    Length,
    /** 特殊符号 */
    SpecialSymbols,
    /** 输入为数值(包含整数和小数) */
    IsNumber,
    /** 输入为正整数 */
    IsInteger,
    /** 小数位数 */
    DecimalLength,
    /** 时间格式 */
    DateFormat,
    /** 手机号校验 */
    Phone,
    /** 在数据数据范围内 */
    InDataRange,
    /** 不在数据数据范围内 */
    NotInDataRange,
    /** 日期范围校验 */
    DateRange,
}
