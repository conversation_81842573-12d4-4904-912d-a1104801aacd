package com.py.web.controller.crm;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.CustomerDevoteMenuTypeStatus;
import com.py.common.enums.ProjectStatus;
import com.py.common.enums.TaskType;
import com.py.common.oss.IOssService;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.SecurityUtils;
import com.py.crm.compareddraft.domain.vo.ComparedDraftListVO;
import com.py.crm.customerdevote.domain.query.CustomerDevoteQuery;
import com.py.crm.customerdevote.domain.vo.CrmProjectListVO;
import com.py.crm.customerdevote.domain.vo.CustomerDevoteBadDebtListVO;
import com.py.crm.customerdevote.domain.vo.CustomerDevoteListVO;
import com.py.crm.customerdevote.domain.vo.CustomerDevoteTotal;
import com.py.crm.customerdevote.service.ICrmProjectService;
import com.py.crm.customerdevote.service.ICustomerDevoteBadDebtService;
import com.py.crm.customerdevote.service.ICustomerDevoteService;
import com.py.crm.customerdevote.service.impl.CustomerDevoteServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 客户贡献管理
 *
 * <AUTHOR>
 * @version CustomerDevoteController 2023/8/9 11:13
 */
@Api(tags = "客户贡献管理")
@RestController
@RequestMapping("/crm/devote")
public class CustomerDevoteController {

    /**
     * 客户贡献管理
     */
    @Resource
    private ICustomerDevoteService customerDevoteService;

    @Resource
    private ICrmProjectService projectService;

    /**
     * 收入坏账信息服务
     */
    @Resource
    private ICustomerDevoteBadDebtService badDebtService;
    @Value("${ruoyi.profile}")
    private String path;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 查询客户贡献管理列表
     *
     * @param query 查询
     * @return 客户贡献管理列表
     */
    @ApiOperation("查询客户贡献管理列表")
    @PreAuthorize("@ss.hasPermi('crm:devote:list')")
    @GetMapping("/listCustomerDevote")
    public R<PageInfo<CustomerDevoteListVO>> listCustomerDevote(CustomerDevoteQuery query) {
        return R.success(customerDevoteService.pageCustomerDevote(query));
    }

    /**
     * 导出客户贡献管理
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出客户贡献管理")
    @PreAuthorize("@ss.hasPermi('devote:project:export')")
    @Log(title = "导出客户贡献管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCustomerDevote")
    public R<String> exportCustomerDevote(@RequestBody CustomerDevoteQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("客户贡献管理", TaskType.Export, query, CustomerDevoteServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 勾选数据合计
      * @param query
     * @return
     */
    @ApiOperation("勾选数据合计")
    @PreAuthorize("@ss.hasPermi('crm:devote:list')")
    @GetMapping("/selectTotal")
    public R<CustomerDevoteTotal> getCustomerDevoteTotal(CustomerDevoteQuery query) {
        return R.success(customerDevoteService.getCustomerDevoteTotal(query));
    }
    /**
     * 比稿列表
     *
     * @param query 查询
     * @return 客户贡献管理列表
     */
    @ApiOperation("比稿列表")
    @PreAuthorize("@ss.hasPermi('crm:devote:list')")
    @GetMapping("/listComparedDraft")
    public R<PageInfo<ComparedDraftListVO>> listComparedDraft(CustomerDevoteQuery query) {
        return R.success(customerDevoteService.listComparedDraft(query));

    }
    /**
     * 导出比稿明细
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出比稿明细")
    @PreAuthorize("@ss.hasPermi('devote:project:export')")
    @Log(title = "导出比稿明细", businessType = BusinessType.EXPORT)
    @PostMapping("/exportComparedDraft")
    public R<String> exportComparedDraft(@RequestBody CustomerDevoteQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setMenuType(CustomerDevoteMenuTypeStatus.COMPARED_DRAFT);
        reusableAsyncTaskService.addTask("比稿明细", TaskType.Export, query, CustomerDevoteServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }
    /**
     * 项目列表
     *
     * @param query 查询
     * @return 客户贡献管理列表
     */
    @ApiOperation("项目列表")
    @PreAuthorize("@ss.hasPermi('crm:devote:list')")
    @GetMapping("/listProject")
    public R<PageInfo<CrmProjectListVO>> listProject(CustomerDevoteQuery query) {
        return R.success(projectService.pageProjectList(query));

    }
    /**
     * 导出项目明细
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目明细")
    @PreAuthorize("@ss.hasPermi('devote:project:export')")
    @Log(title = "导出项目明细", businessType = BusinessType.EXPORT)
    @PostMapping("/exportProject")
    public R<String> exportProject(@RequestBody CustomerDevoteQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setMenuType(CustomerDevoteMenuTypeStatus.PROJECT);
        reusableAsyncTaskService.addTask("项目明细", TaskType.Export, query, CustomerDevoteServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }
    /**
     * 结案项目列表
     *
     * @param query 查询
     * @return 客户贡献管理结案列表
     */
    @ApiOperation("结案项目列表")
    @PreAuthorize("@ss.hasPermi('crm:devote:list')")
    @GetMapping("/lisCloseCaseProject")
    public R<PageInfo<CrmProjectListVO>> lisCloseCaseProject(CustomerDevoteQuery query) {
        query.setProjectStatus(ProjectStatus.CLOSED_CASE.getValue());
        return R.success(projectService.pageProjectList(query));

    }

    /**
     * 导出结案明细
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出结案明细")
    @PreAuthorize("@ss.hasPermi('devote:closecase:export')")
    @Log(title = "导出结案明细", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCloseCase")
    public R<String> exportCloseCase(@RequestBody CustomerDevoteQuery query) {
        query.setProjectStatus(ProjectStatus.CLOSED_CASE.getValue());
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setMenuType(CustomerDevoteMenuTypeStatus.CLOSE_CASE_PROJECT);
        reusableAsyncTaskService.addTask("结案明细", TaskType.Export, query, CustomerDevoteServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 坏账列表
     *
     * @param query 查询
     * @return 客户贡献管理列表
     */
    @ApiOperation("坏账列表")
    @PreAuthorize("@ss.hasPermi('crm:devote:list')")
    @GetMapping("/listBadDebt")
    public R<PageInfo<CustomerDevoteBadDebtListVO>> listBadDebt(CustomerDevoteQuery query) {
        return R.success(this.badDebtService.pagePyIncomeBadDebtList(query));
    }

    /**
     * 导出坏账明细
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出坏账明细")
    @PreAuthorize("@ss.hasPermi('devote:baddebt:export')")
    @Log(title = "导出坏账明细", businessType = BusinessType.EXPORT)
    @PostMapping("/exportBadDebt")
    public R<String> exportBadDebt(@RequestBody CustomerDevoteQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setMenuType(CustomerDevoteMenuTypeStatus.BAD_DEBT);
        reusableAsyncTaskService.addTask("坏账明细", TaskType.Export, query, CustomerDevoteServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }
}
