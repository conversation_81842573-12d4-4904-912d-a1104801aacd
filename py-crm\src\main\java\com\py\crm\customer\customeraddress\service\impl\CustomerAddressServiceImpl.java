package com.py.crm.customer.customeraddress.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.py.common.mybatisplus.SuperServiceImpl;
import com.py.common.tools.modifycomparator.IObjectComparator;
import com.py.common.tools.modifycomparator.model.BaseModifyDiff;
import com.py.common.utils.PageUtils;
import com.py.common.utils.SqlHelper;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.crmupdaterecord.domain.dto.CrmUpdateRecordDTO;
import com.py.crm.crmupdaterecord.domain.enums.RecordBizType;
import com.py.crm.crmupdaterecord.domain.enums.RecordMenuType;
import com.py.crm.crmupdaterecord.service.ICrmUpdateRecordService;
import com.py.crm.customer.cooperatemainstay.domain.CooperateMainstay;
import com.py.crm.customer.customeraddress.converter.CustomerAddressConverter;
import com.py.crm.customer.customeraddress.domain.CustomerAddress;
import com.py.crm.customer.customeraddress.domain.dto.CustomerAddressDTO;
import com.py.crm.customer.customeraddress.domain.dto.CustomerAddressExportModel;
import com.py.crm.customer.customeraddress.domain.query.CustomerAddressQuery;
import com.py.crm.customer.customeraddress.domain.vo.CustomerAddressListVO;
import com.py.crm.customer.customeraddress.domain.vo.CustomerAddressVO;
import com.py.crm.customer.customeraddress.mapper.CustomerAddressMapper;
import com.py.crm.customer.customeraddress.service.ICustomerAddressService;
import com.py.system.recyclebin.domain.dto.DeleteAddDTO;
import com.py.system.recyclebin.domain.enums.RecycleBizType;
import com.py.system.recyclebin.service.IRecycleBinService;
import com.py.system.user.service.ISysUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 客户管理-客户-客户地址Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Service
public class CustomerAddressServiceImpl
        extends SuperServiceImpl<CustomerAddressMapper, CustomerAddress>
        implements ICustomerAddressService{

    /** 客户管理-客户-客户地址模型转换器 */
    @Resource
    private CustomerAddressConverter customerAddressConverter;

    /** 客户管理-客户-客户地址Mapper接口 */
    @Resource
    private CustomerAddressMapper customerAddressMapper;

    /** 对象比较器 */
    @Resource
    private IObjectComparator objectComparator;

    /** 客户/人脉(状态)更新记录表服务 */
    @Resource
    private ICrmUpdateRecordService crmUpdateRecordService;

    /** 用户 业务层 */
    @Resource
    private ISysUserService userService;

    /** 回收站服务 */
    @Resource
    private final IRecycleBinService recycleBinService;

    public CustomerAddressServiceImpl(
            IRecycleBinService recycleBinService) {
        recycleBinService.registeredRestoreCallback(RecycleBizType.CUSTOMER_CUSTOMER_ADDRESS, this::restore);
        this.recycleBinService = recycleBinService;
    }

    /**
     * 分页客户管理-客户-客户地址列表
     * @param query 客户管理-客户-客户地址
     * @return 客户管理-客户-客户地址分页
     */
    @Override
    public PageInfo<CustomerAddressListVO> pageCustomerAddressList(CustomerAddressQuery query) {
        PageUtils.startPage();
        List<CustomerAddress> customerAddressList = this.list();
        List<CustomerAddressListVO> customerAddressVoList = this.customerAddressConverter.toListVoByEntity(customerAddressList);
        return ListUtil.pageConvert(customerAddressList, customerAddressVoList);

    }

    /**
     * 查询客户管理-客户-客户地址
     * @param id 客户管理-客户-客户地址主键
     * @return 客户管理-客户-客户地址视图模型
     */
    @Override
    public CustomerAddressVO selectCustomerAddressById(Long id) {
        CustomerAddress customerAddress = this.getById(id);
        return this.customerAddressConverter.toVoByEntity(customerAddress);
    }

    /**
     * 查询客户管理-客户-客户地址详细信息
     * @param id 客户管理-客户-客户地址主键
     * @return 客户管理-客户-客户地址视图模型
     */
    @Override
    public List<CustomerAddressVO> selectCustomerAddressDetailById(Long id) {
        List<CustomerAddress> customerAddressList = this.list(Wrappers.<CustomerAddress>lambdaQuery()
                .eq(CustomerAddress::getCustomerId, id)
                .orderByAsc(CustomerAddress::getShowId));
        List<CustomerAddressVO> customerAddressVoS = this.customerAddressConverter.toVoByEntity(customerAddressList);
        this.userService.relatedUpdateInfo(customerAddressVoS);
        return customerAddressVoS;
    }

    /**
     * 新增客户管理-客户-客户地址
     * @param dto 客户管理-客户-客户地址修改参数
     * @return 是否成功
     */
    @Override
    public boolean insertCustomerAddress(CustomerAddressDTO dto) {
        CustomerAddress customerAddress = this.customerAddressConverter.toEntityByDto(dto);
        return this.save(customerAddress);
    }

    /**
     * 修改客户管理-客户-客户地址
     * @param dto 客户管理-客户-客户地址修改参数
     * @return 是否成功
     */
    @Override
    public boolean updateCustomerAddress(CustomerAddressDTO dto) {
        CustomerAddress customerAddress = this.customerAddressConverter.toEntityByDto(dto);
        return this.updateById(customerAddress);
    }

    /**
     * 批量删除客户管理-客户-客户地址
     * @param idList 需要删除的客户管理-客户-客户地址主键
     * @return 是否成功
     */
    @Override
    public boolean deleteCustomerAddressByIds(List<Long> idList) {
        return this.removeByIds(idList);
    }

    /**
     * 导出客户管理-客户-客户地址
     * @param query 导出查询参数
     * @return 导出数据
     */
    @Override
    public List<CustomerAddressExportModel> exportCustomerAddress(CustomerAddressQuery query) {
        List<CustomerAddress> customerAddressList = this.list();
        return this.customerAddressConverter.toExportModel(customerAddressList);
    }

    /**
     * 导入客户管理-客户-客户地址
     * @param exportList 导入数据
     * @return 导入结果
     */
    @Override
    public String importCustomerAddress(List<CustomerAddressExportModel> exportList) {
        List<CustomerAddress> customerAddressList = this.customerAddressConverter.toEntityByExportModel(exportList);
        this.saveBatch(customerAddressList);
        return "导入成功!";
    }

    /**
     * 批量新增客户管理-客户-客户地址
     * @param dtoList 客户管理-客户-客户地址数据传输模型
     * @param customerId 客户ID
     * @param isApprovalEdit 是否是审批驳回修改
     */
    @Override
    public void insertBatchCustomerAddress(List<CustomerAddressDTO> dtoList, Long customerId, Boolean isApprovalEdit) {
        if(isApprovalEdit){
            this.remove(Wrappers.<CustomerAddress>lambdaQuery().eq(CustomerAddress::getCustomerId,customerId));
        }
        if(ListUtil.isEmpty(dtoList)){
            return;
        }
        List<CustomerAddress> customerAddresses = customerAddressConverter.toEntityByDto(dtoList);
        int id = 1;
        for(CustomerAddress customerAddress : customerAddresses) {
            customerAddress.setCustomerId(customerId);
            customerAddress.setShowId(id);
            id++;
        }
        // 新增的更新记录
        List<CrmUpdateRecordDTO> crmUpdateRecordDTOList = new ArrayList<>();
        this.addUpdateRecord(crmUpdateRecordDTOList, customerAddresses);
        crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordDTOList);
        SqlHelper.appendAddUpdateInfo(customerAddresses);
        this.saveBatch(customerAddresses);
    }

    /**
     * 批量修改客户管理-客户-客户地址
     * @param dtoList 客户管理-客户-客户地址修改参数
     * @param customerId 客户id
     * @param name 客户名称
     */
    @Override
    public void updateBatchCustomerAddress(List<CustomerAddressDTO> dtoList, Long customerId, String name) {
        List<Long> editAddressIdList = dtoList.stream().map(CustomerAddressDTO::getAddressId).filter(addressId -> addressId != null)
                .collect(Collectors.toList());
        List<CrmUpdateRecordDTO> crmUpdateRecordDTOList = new ArrayList<>();
        if(ListUtil.isNotEmpty(editAddressIdList)){
            List<CustomerAddress> customerAddressList = this.list(Wrappers.<CustomerAddress>lambdaQuery()
                    .in(CustomerAddress::getAddressId, editAddressIdList));
            List<CustomerAddressDTO> customerAddressDTOList = dtoList.stream().filter(customerAddressDTO -> customerAddressDTO.getAddressId() != null).collect(Collectors.toList());
            List<CustomerAddress> editCustomerAddresses = customerAddressConverter.toEntityByDto(customerAddressDTOList);
            List<CustomerAddress> customerAddresses = new ArrayList<>();
            this.editCustomerAddress(crmUpdateRecordDTOList, customerAddressList, editCustomerAddresses,customerAddresses);
            if(ListUtil.isNotEmpty(customerAddresses)){
                this.updateBatchById(customerAddresses);
            }
        }
        this.removeAddress(customerId, editAddressIdList, crmUpdateRecordDTOList,name);
        List<CustomerAddressDTO> addressDTOList = dtoList.stream()
                .filter(contact -> contact.getAddressId() == null).collect(Collectors.toList());
        if(ListUtil.isNotEmpty(addressDTOList)){
            List<CustomerAddress> addCustomerAddresses = customerAddressConverter.toEntityByDto(addressDTOList);
            Integer showId = customerAddressMapper.getShowId(customerId);
            showId = showId == null ? 1 : showId;
            for(CustomerAddress customerAddress : addCustomerAddresses) {
                customerAddress.setCustomerId(customerId);
                customerAddress.setShowId(showId);
                showId++;
            }
            this.addUpdateRecord(crmUpdateRecordDTOList, addCustomerAddresses);
            SqlHelper.appendAddUpdateInfo(addCustomerAddresses);
            this.saveBatch(addCustomerAddresses);
        }
        crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordDTOList);
    }

    /**
     * 根据客户id获取客户地址
     * @param customerIdList 客户id
     * @return 客户地址
     */
    @Override
    public List<CustomerAddressVO> listCustomerAddressByCustomerId(List<Long> customerIdList) {
        if(ListUtil.isEmpty(customerIdList)){
            return ListUtil.emptyList();
        }
        List<CustomerAddress> customerAddressList = this.list(Wrappers.<CustomerAddress>lambdaQuery()
                .in(CustomerAddress::getCustomerId, customerIdList)
                .orderByAsc(CustomerAddress::getShowId));
        return customerAddressConverter.toVoByEntity(customerAddressList);
    }

    /**
     * 编辑客户时，如果未传客户地址数据，则需删除旧的客户地址数据
     * @param customerId
     * @param name
     */
    @Override
    public void deleteBatchCustomerAddress(Long customerId, String name) {
        List<CrmUpdateRecordDTO> crmUpdateRecordList = new ArrayList();

        //删除客户地址
        removeAddress(customerId, null, crmUpdateRecordList, name);

        //批量新增更新记录
        if(ListUtil.isNotEmpty(crmUpdateRecordList)) {
            crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordList);
        }
    }

    /**
     * 根据客户ID删除
     * @param bizIds 客户ID
     */
    @Override
    public void deleteByCustomerIds(List<Long> bizIds) {
        this.remove(Wrappers.<CustomerAddress>lambdaQuery()
                .in(CustomerAddress::getCustomerId,bizIds));
    }

    /**
     * 新增更新记录
     * @param crmUpdateRecordDTOList 更新记录
     * @param addCustomerAddresses 新增的地址
     */
    private void addUpdateRecord(List<CrmUpdateRecordDTO> crmUpdateRecordDTOList, List<CustomerAddress> addCustomerAddresses) {
        addCustomerAddresses.forEach(customerAddress -> {
            List<BaseModifyDiff<?>> baseModifyDiffs = objectComparator.compareObject(new CustomerAddress(), customerAddress, CustomerAddress.class);
            baseModifyDiffs.forEach(baseModifyDiff -> {
                CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                String content = "企业地址信息-新增ID:" + customerAddress.getShowId() + "【" + ListUtil.last(baseModifyDiff.getFieldPath()) + "】" + "“" + baseModifyDiff.getAfter() + "”";
                crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(),customerAddress.getCustomerId()
                        ,content, RecordMenuType.UPDATE_RECORD.getValue(),null,null);
                crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
            });
        });
    }

    /**
     * 删除
     * @param customerId 客户id
     * @param editAddressIdList 修改的地址id
     * @param crmUpdateRecordDTOList 更新记录
     * @param name 客户名称
     */
    private void removeAddress(Long customerId, List<Long> editAddressIdList, List<CrmUpdateRecordDTO> crmUpdateRecordDTOList, String name) {
        LambdaQueryWrapper<CustomerAddress> wrapper = Wrappers.<CustomerAddress>lambdaQuery()
                .in(CustomerAddress::getCustomerId, customerId)
                .notIn(ListUtil.isNotEmpty(editAddressIdList),CustomerAddress::getAddressId, editAddressIdList);
        List<CustomerAddress> customerAddressList = list(wrapper);
        if(ListUtil.isNotEmpty(customerAddressList)){
            customerAddressList.forEach(customerAddress -> {
                CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                String content = "企业地址信息-删除ID: " + customerAddress.getShowId() + "【客户地址】" + "“" + customerAddress.getCustomerAddress() + "”";
                crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(),customerAddress.getCustomerId()
                        ,content, RecordMenuType.UPDATE_RECORD.getValue(),null,null);
                crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
            });
            this.remove(wrapper);
            List<DeleteAddDTO> deleteAddDTOList = new ArrayList<>();
            customerAddressList.forEach(customerAddress -> {
                DeleteAddDTO deleteAddDTO = new DeleteAddDTO();
                deleteAddDTO.setBizId(customerAddress.getAddressId());
                deleteAddDTO.setContent(name + "-企业地址信息:" + customerAddress.getCustomerAddress() );
                deleteAddDTOList.add(deleteAddDTO);
            });
            recycleBinService.addDeleteRecord(RecycleBizType.CUSTOMER_CUSTOMER_ADDRESS,deleteAddDTOList);
        }
    }

    /**
     * 修改字段对比
     * @param crmUpdateRecordDTOList 更新记录
     * @param customerAddressList 修改的原记录
     * @param editCustomerAddresses 修改的数据
     */
    private void editCustomerAddress(List<CrmUpdateRecordDTO> crmUpdateRecordDTOList, List<CustomerAddress> customerAddressList,
                                     List<CustomerAddress> editCustomerAddresses,List<CustomerAddress> customerAddresses) {
        Map<Long, CustomerAddress> addressIdMap = ListUtil.toMap(editCustomerAddresses, CustomerAddress::getAddressId);
        customerAddressList.forEach(customerAddress -> {
            CustomerAddress address = addressIdMap.get(customerAddress.getAddressId());
            List<BaseModifyDiff<?>> baseModifyDiffs = objectComparator.compareObject(customerAddress, address, CustomerAddress.class);
            if(ListUtil.isNotEmpty(baseModifyDiffs)){
                baseModifyDiffs.forEach(baseModifyDiff -> {
                    CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                    Object source = baseModifyDiff.getBefore() != null ? baseModifyDiff.getBefore() : "--";
                    Object after = baseModifyDiff.getAfter() != null ? baseModifyDiff.getAfter() : "--";
                    String content = "企业地址信息-ID:" + customerAddress.getShowId() + "【" + ListUtil.last(baseModifyDiff.getFieldPath()) + "】从" + "“" + source + "”更新为“" + after + "”";
                    crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(),customerAddress.getCustomerId()
                            ,content, RecordMenuType.UPDATE_RECORD.getValue(),null,null);
                    crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
                });
                customerAddresses.add(address);
            }
        });
    }
}
