package com.py.crm.customerdevote.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收入坏账信息列表视图模型
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
@Data
@ApiModel("收入坏账信息列表视图模型")
public class CustomerDevoteBadDebtListVO {
    private static final long serialVersionUID = 1L;

    /** 坏账金额（元）*/
    @ApiModelProperty("坏账金额（元）")
    private BigDecimal totalBadDebtAmount;

    /** 品牌线*/
    @ApiModelProperty("品牌线")
    private String brandLine;


    /** 客户合作主体名称*/
    @ApiModelProperty("客户合作主体名称")
    private String customerMainstayName;

    /** 客户名称*/
    @ApiModelProperty("客户名称")
    private String customerName;

    /** 项目id*/
    @ApiModelProperty("项目id")
    private Long projectId;

    /** 项目名称*/
    @ApiModelProperty("项目名称")
    private String projectName;

    /** 派芽主体id*/
    @ApiModelProperty("派芽合作主体id")
    private Long pyMainstayId;

    /** 派芽合作主体*/
    @ApiModelProperty("派芽合作主体")
    private String pyMainstayName;
    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建人部门")
    private String createDept;

    @ApiModelProperty("创建时间")
    private LocalDate startCreatTime;

    @ApiModelProperty("创建时间")
    private LocalDate endCreatTime;



}
