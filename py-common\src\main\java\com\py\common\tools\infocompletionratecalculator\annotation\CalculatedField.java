package com.py.common.tools.infocompletionratecalculator.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 信息完整度计算字段标记注解
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CalculatedField {

    /** 字段是否必填 */
    boolean required() default false;

    /** 是否为子模型 */
    boolean isSubModel() default false;
}
