package com.py.web.controller.system.reusableasynctask;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.exception.ServiceException;
import com.py.common.oss.IOssService;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.StringUtils;
import com.py.system.tools.reusableasynctask.domain.dto.TaskRecodeDTO;
import com.py.system.tools.reusableasynctask.domain.query.TaskRecodeQuery;
import com.py.system.tools.reusableasynctask.domain.vo.TaskRecodeListVO;
import com.py.system.tools.reusableasynctask.service.ITaskRecodeService;
import com.py.system.tools.reusableasynctask.taskrecodeuser.domain.vo.TaskRecodeUserVO;
import com.py.system.tools.reusableasynctask.taskrecodeuser.service.ITaskRecodeUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 异步任务执行记录Controller
 *
 * <AUTHOR>
 * @date 2023-08-19
 */
@Api(tags = "异步任务执行记录")
@RestController
@RequestMapping("/system/taskRecode")
public class TaskRecodeController extends BaseController {

    /** 异步任务执行记录服务 */
    @Resource
    private ITaskRecodeService taskRecodeService;


    /** 对象存储服务 */
    @Resource
    private IOssService ossService;

    /** 下载任务列表-用户关联Service接口 */
    @Resource
    private ITaskRecodeUserService taskRecodeUserService;

    /**
     * 分页查询异步任务执行记录列表
     *
     * @param query 异步任务执行记录查询参数
     * @return 异步任务执行记录分页
     */
    @ApiOperation("分页查询询异步任务执行记录列表")
    @PreAuthorize("@ss.hasPermi('system:taskRecode:list')")
    @GetMapping("/pageTaskRecode")
    public R<PageInfo<TaskRecodeListVO>> pageTaskRecode(TaskRecodeQuery query) {
        PageInfo<TaskRecodeListVO> voList = this.taskRecodeService.pageTaskRecodeList(query);
        return R.success(voList);
    }


    /**
     * 删除异步任务执行记录
     * @param ids 需要删除的异步任务执行记录主键集合
     * @return 是否成功
     */
    @ApiOperation("删除异步任务执行记录" )
    @PreAuthorize("@ss.hasPermi('system:taskRecode:remove')")
    @Log(title = "异步任务执行记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(taskRecodeService.deleteTaskRecodeByIds(ids));
    }

    /**
     * 获取url
     * @param query 文件key
     * @return url
     */
    @ApiOperation("获取url")
    @GetMapping("/getUrl")
    public R<String> getUrl(TaskRecodeQuery query){
        if(StringUtils.isBlank(query.getKey())){
            throw new ServiceException("文件下载失败");
        }
        if(query.getTaskId() != null){
            TaskRecodeDTO taskRecodeDTO = new TaskRecodeDTO();
            taskRecodeDTO.setTaskId(query.getTaskId());
            taskRecodeService.updateTaskRecode(taskRecodeDTO);
        }
        return R.success(ossService.getUrlByKey(query.getKey()));
    }

    /**
     * 查询用户是否进入异步任务列表
     * @return 是否成功
     */
    @ApiOperation("查询用户是否进入异步任务列表" )
    @GetMapping("/listTaskRecodeUserbByUserId" )
    public R<TaskRecodeUserVO> listTaskRecodeUserbByUserId() {
        return R.success(taskRecodeUserService.listTaskRecodeUserbByUserId(SecurityUtils.getUserId()));
    }
}
