package com.py.common.config;

import com.py.common.utils.NullMergeUtils;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 钉钉配置
 * <AUTHOR>
 */
@Component
@ConfigurationProperties("ding-talk")
@Data
public class DingTalkConfig {

    /** AppKey */
    private String appKey;

    /** App密钥 */
    private String appSecret;

    /** 是否启用钉钉通知 */
    private Boolean enableDingTalkNotice;

    /**
     * 获取是否启用钉钉通知, 默认为true
     * @return 是否启用钉钉通知
     */
    public Boolean getEnableDingTalkNotice() {
        return NullMergeUtils.nullMerge(enableDingTalkNotice, true);
    }
}
