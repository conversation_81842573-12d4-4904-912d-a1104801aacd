package com.py.common.enums.flow;

import com.py.common.enums.IDict;
import lombok.AllArgsConstructor;

/**
 * 流程表单变量枚举
 * <AUTHOR>
 * @date 2023/7/27 17:05
 */
@AllArgsConstructor
public enum FlowFormVariablesEnum implements IDict<String> {

    /** 金额 */
    MONEY("money", "金额"),

    /** 发起人id */
    LAUNCHER_ID("launcherId","发起人id"),

    /** 发起人部门id */
    LAUNCHER_DEPT_ID("launcherDeptId","发起人部门id"),

    /** 直属主管 */
    DIRECT_MANAGER("directManager","直属主管"),

    /** 部门主管 */
    DEPT_MANAGER("deptManager","部门主管"),

    /** 项目成员职位 */
    PROJECT_USER_POST("projectUserPost","项目成员职位");

    private final String value;

    private final String label;

    @Override
    public String getLabel() {
        return label;
    }

    @Override
    public String getValue() {
        return value;
    }
}
