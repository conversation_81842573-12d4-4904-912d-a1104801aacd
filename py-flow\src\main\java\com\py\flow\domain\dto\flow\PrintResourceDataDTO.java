package com.py.flow.domain.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 审批执行表单信息DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("审批执行表单信息DTO")
public class PrintResourceDataDTO {

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "派芽合作主体")
    private String mainstayName;

    @ApiModelProperty(value = "资源ID")
    private String resourceCode;

    @ApiModelProperty(value = "资源名称")

    private String resourceName;
    @ApiModelProperty(value = "合作平台")
    private String cooperationPlatform;

    // 批量付款
    @ApiModelProperty(value = "付款渠道")
    private String channelsName;

    @ApiModelProperty(value = "付款账户")
    private String paymentAccount;

    @ApiModelProperty(value = "执行未付金额")
    private BigDecimal unpaidAmount;

    @ApiModelProperty(value = "本次付款金额")
    private BigDecimal currentPaymentAmount;

    // 批量退款
    @ApiModelProperty(value = "退款渠道")
    private String refundChannelsName;

    @ApiModelProperty(value = "退款账户")
    private String refundAccountName;

    @ApiModelProperty(value = "未退金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "本次退款金额")
    private BigDecimal currentRefundAmount;

}
