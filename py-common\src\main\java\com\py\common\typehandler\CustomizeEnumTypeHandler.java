package com.py.common.typehandler;

import com.baomidou.mybatisplus.annotation.IEnum;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;

/**
 * 自定义枚举属性转换器
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class CustomizeEnumTypeHandler<TEnum extends Enum<TEnum> & IEnum<?>> extends BaseTypeHandler<TEnum> {

    /** 转换的枚举类型 */
    private final Class<TEnum> enumClass;

    public CustomizeEnumTypeHandler(Class<TEnum> enumClass) {
        this.enumClass = enumClass;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, TEnum parameter, JdbcType jdbcType) throws SQLException {
        if(jdbcType == null) {
            ps.setObject(i, parameter.getValue());
        } else {
            ps.setObject(i, parameter.getValue(), jdbcType.TYPE_CODE);
        }
    }

    @Override
    public TEnum getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object value = rs.getObject(columnName);
        if(value == null && rs.wasNull()) {
            return null;
        }
        return this.valueOf(value);
    }

    @Override
    public TEnum getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object value = rs.getObject(columnIndex);
        if(value == null && rs.wasNull()) {
            return null;
        }
        return this.valueOf(value);
    }

    @Override
    public TEnum getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object value = cs.getObject(columnIndex);
        if(value == null && cs.wasNull()) {
            return null;
        }
        return this.valueOf(value);
    }

    /**
     * 枚举转换
     * @param value 枚举值
     * @return 枚举
     */
    private TEnum valueOf(Object value) {
        if(IEnum.class.isAssignableFrom(this.enumClass) == false) {
            return Enum.valueOf(this.enumClass, String.valueOf(value).trim());
        }

        for(TEnum enumItem : this.enumClass.getEnumConstants()) {
            if(this.equalsValue(value, ((IEnum<?>) enumItem).getValue())) {
                return enumItem;
            }
        }
        return null;
    }

    /**
     * 值比较
     * @param sourceValue 数据库字段值
     * @param targetValue 当前枚举属性值
     * @return 是否匹配
     */
    protected boolean equalsValue(Object sourceValue, Object targetValue) {
        String sValue = String.valueOf(sourceValue).trim();
        String tValue = String.valueOf(targetValue).trim();
        if(sourceValue instanceof Number && targetValue instanceof Number
                && new BigDecimal(sValue).compareTo(new BigDecimal(tValue)) == 0) {
            return true;
        }
        return Objects.equals(sValue, tValue);
    }

}
