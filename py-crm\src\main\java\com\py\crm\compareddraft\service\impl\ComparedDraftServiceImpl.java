package com.py.crm.compareddraft.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.py.common.constant.DictConstant;
import com.py.common.core.domain.entity.SysDictData;
import com.py.common.datascope.DataScopePageType;
import com.py.common.datascope.DataScopeUtils;
import com.py.common.enums.DelFlag;
import com.py.common.exception.ServiceException;
import com.py.common.mybatisplus.ShowTableNameLambdaQueryWrapper;
import com.py.common.mybatisplus.SuperServiceImpl;
import com.py.common.oss.IOssService;
import com.py.common.oss.model.OssObjectInfo;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.tools.multisheetexcelexporter.MultiSheetExcelExporter;
import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;
import com.py.common.tools.multisheetexcelexporter.domain.HyperlinkPackage;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTask;
import com.py.common.utils.*;
import com.py.common.utils.collection.JoinUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.compareddraft.converter.ComparedDraftConverter;
import com.py.crm.compareddraft.domain.ComparedDraft;
import com.py.crm.compareddraft.domain.dto.ComparedDraftDTO;
import com.py.crm.compareddraft.domain.dto.ComparedDraftExportModel;
import com.py.crm.compareddraft.domain.query.ComparedDraftQuery;
import com.py.crm.compareddraft.domain.vo.ComparedDraftAmountVO;
import com.py.crm.compareddraft.domain.vo.ComparedDraftListVO;
import com.py.crm.compareddraft.domain.vo.ComparedDraftVO;
import com.py.crm.compareddraft.mapper.ComparedDraftMapper;
import com.py.crm.compareddraft.service.IComparedDraftService;
import com.py.crm.customer.customercontribute.domain.ComparedDraftUser;
import com.py.crm.customer.customercontribute.service.IComparedDraftUserService;
import com.py.crm.customer.domain.SupCustomer;
import com.py.crm.customer.projectInfo.domain.CrmProjectVO;
import com.py.crm.customerdevote.service.ICrmProjectService;
import com.py.system.dict.service.ISysDictDataService;
import com.py.system.recyclebin.domain.dto.DeleteAddDTO;
import com.py.system.recyclebin.domain.enums.RecycleBizType;
import com.py.system.recyclebin.service.IRecycleBinService;
import com.py.system.tools.reusableasynctask.domain.ExportTaskResult;
import com.py.system.tools.reusableasynctask.service.ITaskRecodeService;
import com.py.system.user.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户管理-比稿管理服务实现
 *
 * <AUTHOR>
 * @date 2023/07/17 09:36
 */
@Slf4j
@Service
public class ComparedDraftServiceImpl extends
        SuperServiceImpl<ComparedDraftMapper, ComparedDraft>
        implements IComparedDraftService, ReusableAsyncTask<ComparedDraftQuery> {

    /** 客户管理-比稿管理Mapper */
    @Resource
    private ComparedDraftMapper comparedDraftMapper;

    /** 客户管理-比稿关联模型转换器 */
    @Resource
    private ComparedDraftConverter comparedDraftConverter;

    /** 对象存储服务 */
    @Resource
    private IOssService ossService;

    /** 客户管理-比稿策划人关联Service接口 */
    @Resource
    private IComparedDraftUserService comparedDraftUserService;

    /** 字典服务 */
    @Resource
    private ISysDictDataService sysDictDataService;

    /** 异步任务执行记录服务 */
    @Resource
    private ITaskRecodeService taskRecodeService;

    /** 用户 业务层 */
    @Resource
    private ISysUserService userService;

    /** 回收站服务 */
    @Resource
    private final IRecycleBinService recycleBinService;
    /**客户项目服务*/
    @Resource
    private ICrmProjectService crmProjectService;

    public ComparedDraftServiceImpl(
            IRecycleBinService recycleBinService) {
        recycleBinService.registeredRestoreCallback(RecycleBizType.COMPARED_DRAFT, this::restore);
        this.recycleBinService = recycleBinService;
    }

    /**
    * 分页查询客户管理-比稿管理
    *
    * @param comparedDraftQuery 客户管理-比稿管理查询条件
    * @return 客户管理-比稿管理查询内容
    */
    @Override
    public PageInfo<ComparedDraftListVO> listComparedDraft(ComparedDraftQuery comparedDraftQuery){
        LambdaQueryWrapper<ComparedDraft> queryWrapper = this.getQueryStatement(comparedDraftQuery);

        LambdaQueryWrapper<SupCustomer> wrapper = Wrappers.lambdaQuery(SupCustomer.class);
//                .eq(SupCustomer::getDelFlag, DelFlag.NOT_DELETED)
        DataScopeUtils.setDateScope(wrapper,DataScopePageType.CRM_CustomerManage,dataScopeContext -> {
            dataScopeContext.setIgnoreUserPermission(true);
            dataScopeContext.setDateAlias("audit_time");
        });


        PageUtils.startPage();
        List<ComparedDraft> comparedDraftList = comparedDraftMapper.listComparedDraft(comparedDraftQuery, queryWrapper,wrapper);
        List<ComparedDraftListVO> comparedDraftVoList = comparedDraftConverter.toListVoByEntity(comparedDraftList);
        if(ListUtil.isEmpty(comparedDraftVoList)){
            return ListUtil.emptyPage();
        }
        List<Long> comparedDraftIdList = ListUtil.distinctMap(comparedDraftVoList, ComparedDraftListVO::getComparedDraftId);
        List<ComparedDraftUser> comparedDraftUserList = comparedDraftUserService.listComparedDraftIds(comparedDraftIdList);
        // 将策划人的名加入出参
        JoinUtils.oneByMany(comparedDraftVoList,comparedDraftUserList
                ,ComparedDraftListVO::getComparedDraftId,ComparedDraftUser::getComparedDraftId
                , (comparedDraftVO,comparedDraftUser) -> {
                    List<String> plotterNameList = ListUtil.distinctMap(comparedDraftUser, ComparedDraftUser::getUserName);
                    comparedDraftVO.setPlotterNameList(plotterNameList);
                });

        userService.relatedUpdateInfo(comparedDraftVoList);
        return ListUtil.pageConvert(comparedDraftList,comparedDraftVoList);
    }

    /**
     * 获取查询条件
     * @param comparedDraftQuery 查询条件
     * @return Wrapper
     */
    private LambdaQueryWrapper<ComparedDraft> getQueryStatement(ComparedDraftQuery comparedDraftQuery) {
        ShowTableNameLambdaQueryWrapper<ComparedDraft> queryWrapper = new ShowTableNameLambdaQueryWrapper<>(ComparedDraft.class);
        queryWrapper.setTableAlias("draft");

        // 比稿id集合
        queryWrapper.in(ListUtil.isNotEmpty(comparedDraftQuery.getComparedDraftIdList())
                ,ComparedDraft::getComparedDraftId,comparedDraftQuery.getComparedDraftIdList());
        queryWrapper.like(StringUtils.isNotEmpty(comparedDraftQuery.getCustomerName())
                ,ComparedDraft::getCustomerName, comparedDraftQuery.getCustomerName());
        queryWrapper.like(StringUtils.isNotEmpty(comparedDraftQuery.getCustomerCooperateName())
                ,ComparedDraft::getCustomerCooperateName, comparedDraftQuery.getCustomerCooperateName());
        queryWrapper.like(StringUtils.isNotEmpty(comparedDraftQuery.getBrandName())
                ,ComparedDraft::getBrandName, comparedDraftQuery.getBrandName());
        queryWrapper.like(StringUtils.isNotEmpty(comparedDraftQuery.getComparedDraftName())
                ,ComparedDraft::getComparedDraftName, comparedDraftQuery.getComparedDraftName());
        queryWrapper.eq(StringUtils.isNotNull(comparedDraftQuery.getFinalFruits())
                ,ComparedDraft::getFinalFruits, comparedDraftQuery.getFinalFruits());
        queryWrapper.eq(StringUtils.isNotNull(comparedDraftQuery.getCommercialBidFruits())
                ,ComparedDraft::getCommercialBidFruits, comparedDraftQuery.getCommercialBidFruits());
        queryWrapper.eq(StringUtils.isNotNull(comparedDraftQuery.getTechniqueBidFruits())
                ,ComparedDraft::getTechniqueBidFruits, comparedDraftQuery.getTechniqueBidFruits());
        queryWrapper.in(ListUtil.isNotEmpty(comparedDraftQuery.getUserIdList())
                ,ComparedDraft::getUpdateId, comparedDraftQuery.getUserIdList());
        queryWrapper.ge(StringUtils.isNotNull(comparedDraftQuery.getEnteringBeginTime())
                ,ComparedDraft::getEnteringTime, DateUtils.getDayFirstTime(comparedDraftQuery.getEnteringBeginTime()));
        queryWrapper.le(StringUtils.isNotNull(comparedDraftQuery.getEnteringEndTime())
                ,ComparedDraft::getEnteringTime, DateUtils.getDayLastTime(comparedDraftQuery.getEnteringEndTime()));
        queryWrapper.ge(StringUtils.isNotNull(comparedDraftQuery.getComparedBeginDraftTime())
                ,ComparedDraft::getComparedDraftTime, DateUtils.getDayFirstTime(comparedDraftQuery.getComparedBeginDraftTime()));
        queryWrapper.le(StringUtils.isNotNull(comparedDraftQuery.getComparedEndDraftTime())
                ,ComparedDraft::getComparedDraftTime, DateUtils.getDayLastTime(comparedDraftQuery.getComparedEndDraftTime()));
        queryWrapper.eq(Objects.nonNull(comparedDraftQuery.getCustomerId()),ComparedDraft::getCustomerId,comparedDraftQuery.getCustomerId());
        String customerDraftUserScope = DataScopeUtils.getDateScope(DataScopePageType.CRM_ComparedManage, null,
                context -> {
                    context.setUserTableAlias("draft");
                    context.setIgnoreDataPermission(true);
        });

        String customerUserScope = DataScopeUtils.getDateScope(DataScopePageType.CRM_ComparedManage, null, context -> {
            context.setUserIdAlias("user_id");
            context.setUserTableAlias("wanderAbout");
            context.setIgnoreDataPermission(true);
        });
        if(StringUtils.isNotBlank(customerUserScope) && StringUtils.isNotBlank(customerDraftUserScope)){
            queryWrapper.apply("("+customerUserScope + " or " + customerDraftUserScope+")" );
        }

        DataScopeUtils.setDateScope(queryWrapper,DataScopePageType.CRM_ComparedManage, null, context -> {
            context.setIgnoreUserPermission(true);
            context.setDateTableAlias("draft");
        });
        DataScopeUtils.setDateScope(queryWrapper,DataScopePageType.CRM_CustomerManage, null, context -> {
            context.setIgnoreDataPermission(true);
            context.setIgnoreUserPermission(true);
//            context.setSplicedSql("wanderAbout.is_distribution = 1");
        });
        queryWrapper.orderByDesc(ComparedDraft::getComparedDraftTime).orderByDesc(ComparedDraft::getId);
        return queryWrapper;
    }

    /**
     * 查询客户管理-比稿管理
     *
     * @param id 客户管理-比稿管理查询条件
     * @return 客户管理-比稿管理实例
     */
    @Override
    public ComparedDraftVO queryComparedDraft(Long id){
        ComparedDraft byId = this.getById(id);
        if(ListUtil.isNotEmpty(byId.getComparedDraftPlan())){
            byId.getComparedDraftPlan().forEach(ossObjectInfo -> {
                String urlByKey = ossService.getUrlByKey(ossObjectInfo.getKey());
                ossObjectInfo.setOssUrl(urlByKey);
            });
        }
        ComparedDraftVO comparedDraftVO = comparedDraftConverter.toVoByEntity(byId);
        List<ComparedDraftUser> comparedDraftUsers = comparedDraftUserService.listComparedDraftIds(Collections.singletonList(id));
        List<Long> plotterIdList = ListUtil.distinctMap(comparedDraftUsers, ComparedDraftUser::getUserId);
        comparedDraftVO.setPlotterIdList(plotterIdList);
        List<String> plotterNameList = ListUtil.distinctMap(comparedDraftUsers, ComparedDraftUser::getUserName);
        comparedDraftVO.setPlotterNameList(plotterNameList);
        if(ListUtil.isNotEmpty(comparedDraftVO.getCategoryId())){
            List<String> categoryName = sysDictDataService.selectByDictValueList(DictConstant.INDUSTRY_CATEGORY, comparedDraftVO.getCategoryId());
            comparedDraftVO.setCategoryName(categoryName);
        }
        userService.relatedUpdateInfo(CollectionUtil.toList(comparedDraftVO));
        return comparedDraftVO;
    }

    /**
     * 查询客户管理-比稿管理详细信息
     *
     * @param id 客户管理-比稿管理查询条件
     * @param customerCooperateId
     * @param projectId 项目ID
     * @return 客户管理-比稿管理查询内容
     */

    @Override
    public PageInfo<ComparedDraftVO> queryComparedDraftDetailById(Long id, Long customerCooperateId, Long projectId) {
        //获取客户下的项目
        List<CrmProjectVO> projectVOList = crmProjectService.getProjectByCustomerId(id,projectId);
        Set<Long> exitesComparedDraftIdSet = ListUtil.toSet(projectVOList, CrmProjectVO::getComparedDraftId);
        //过滤比稿项目
        List<ComparedDraftVO> comparedDraftVOList = this.queryComparedDraftDetailByIds(Collections.singletonList(id), customerCooperateId);
        comparedDraftVOList = comparedDraftVOList.stream()
                .filter(item -> exitesComparedDraftIdSet.contains(item.getComparedDraftId()) == false)
                .collect(Collectors.toList());
        return ListUtil.pageConvert(comparedDraftVOList);

    }

    /**
     * 添加客户管理-比稿管理
     *
     * @param draftDTO 客户管理-比稿管理Param
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addComparedDraft(ComparedDraftDTO draftDTO){
        ComparedDraft comparedDraft = comparedDraftConverter.toEntityByDto(draftDTO);
        if(ListUtil.isNotEmpty(comparedDraft.getCategoryId())){
            List<String> categoryName = sysDictDataService.selectByDictValueList(DictConstant.INDUSTRY_CATEGORY, comparedDraft.getCategoryId());
            comparedDraft.setCategoryName(categoryName);
        }
        comparedDraft.setEnteringTime(LocalDateTime.now());
        SqlHelper.appendAddUpdateInfo(comparedDraft);
        boolean save = this.save(comparedDraft);
        if(!save){
            throw new ServiceException("新增比稿失败");
        }
        if(ListUtil.isNotEmpty(draftDTO.getUserIdList())){
            comparedDraftUserService.insertComparedDraftUser(comparedDraft.getComparedDraftId(),draftDTO.getUserIdList());
        }
        return true;
    }

    /**
     * 修改客户管理-比稿管理
     *
     * @param draftDTO 客户管理-比稿管理Param
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateComparedDraft(ComparedDraftDTO draftDTO){
        ComparedDraft comparedDraft = comparedDraftConverter.toEntityByDto(draftDTO);
        if(ListUtil.isNotEmpty(comparedDraft.getCategoryId())){
            List<String> categoryName = sysDictDataService.selectByDictValueList(DictConstant.INDUSTRY_CATEGORY, comparedDraft.getCategoryId());
            comparedDraft.setCategoryName(categoryName);
        }
        if(ListUtil.isNotEmpty(draftDTO.getUserIdList())){
            comparedDraftUserService.updateComparedDraftUserByIds(comparedDraft.getComparedDraftId(),draftDTO.getUserIdList());
        } else {
            comparedDraftUserService.removeComparedDraftId(draftDTO.getComparedDraftId());
        }
        return this.updateById(comparedDraft);
    }

    /**
     * 删除客户管理-比稿管理
     *
     * @param draftDTO 要删除的客户管理-比稿管理Id
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteComparedDraft(ComparedDraftDTO draftDTO){
        ComparedDraft comparedDraft = getById(draftDTO.getComparedDraftId());
        DeleteAddDTO deleteAddDTO = new DeleteAddDTO();
        deleteAddDTO.setContent("比稿项目名称:" + comparedDraft.getComparedDraftName());
        deleteAddDTO.setBizId(draftDTO.getComparedDraftId());
        boolean record = recycleBinService.addDeleteRecord(RecycleBizType.COMPARED_DRAFT, deleteAddDTO);
        if(!record){
            throw new ServiceException("比稿删除失败");
        }
        return removeById(draftDTO.getComparedDraftId());
    }

    /**
     * 客户管理-比稿管理合计
     * @param query 要删除的客户管理-比稿管理查询内容
     * @return 是否成功
     */
    @Override
    public ComparedDraftAmountVO amount(ComparedDraftQuery query) {
        if(!query.getIsAll() && ListUtil.isEmpty(query.getComparedDraftIdList())){
            return new ComparedDraftAmountVO();
        }
        LambdaQueryWrapper<SupCustomer> wrapper = Wrappers.lambdaQuery(SupCustomer.class);
//                .eq(SupCustomer::getDelFlag, DelFlag.NOT_DELETED)
        DataScopeUtils.setDateScope(wrapper,DataScopePageType.CRM_CustomerManage,dataScopeContext -> {
            dataScopeContext.setIgnoreUserPermission(true);
            dataScopeContext.setDateAlias("audit_time");
        });
        LambdaQueryWrapper<ComparedDraft> queryWrapper = this.getQueryStatement(query);
        return comparedDraftMapper.amount(query,queryWrapper,wrapper);
    }

    /**
     * 下载比稿
     * @param query 查询条件
     */
    @Override
    public void download(ComparedDraftQuery query) {
        taskRecodeService.editTaskBegin(query.getTaskId());
        ExportTaskResult exportTaskResult = new ExportTaskResult();
        // zip的名称里的时间的格式化
        String pathName = query.getFileName();
        exportTaskResult.setFileName(pathName);
        try {
            LambdaQueryWrapper<ComparedDraft> queryWrapper = this.getQueryStatement(query);

            LambdaQueryWrapper<SupCustomer> wrapper = Wrappers.lambdaQuery(SupCustomer.class).select(SupCustomer::getCustomerId);
            DataScopeUtils.setDateScope(wrapper,DataScopePageType.CRM_CustomerManage,dataScopeContext -> {
                dataScopeContext.setIgnoreUserPermission(true);
                dataScopeContext.setDateAlias("audit_time");
            });


            List<ComparedDraftExportModel> exportModelList = new ArrayList<>();
            if(query.getIsAll() != null || ListUtil.isNotEmpty(query.getComparedDraftIdList())){
                List<ComparedDraft> comparedDraftList = comparedDraftMapper.listComparedDraft(query,queryWrapper,wrapper);
                exportModelList = comparedDraftConverter.toExportModel(comparedDraftList);
            }

            userService.relatedUpdateInfo(exportModelList);
            // 设置策划人名称
            this.setPlotterName(exportModelList);
            // 设置比稿的字典标签
            this.setComparedDraftDictLabel(exportModelList);
            // 导出附件字段赋值
            this.fieldAssignment(exportModelList);

            MultiSheetExcelExporter excelExporter = new MultiSheetExcelExporter();
            ExcelSheetConfig<ComparedDraftExportModel> excelConfig = this.getComparedDraftExportConfig(exportModelList);
            // 下载当前所有数据
            excelExporter.addSheetConfig(exportModelList, excelConfig, "比稿明细");
            byte[] bytes = excelExporter.exportExcelToByte(pathName);
            OssUploadResult upload;
            InputStream inputStream = new ByteArrayInputStream(bytes);
            // 上传oss
            upload = ossService.upload(inputStream, pathName, true);
            inputStream.close();
            exportTaskResult.setIsSuccess(true);
            exportTaskResult.setKey(upload.getOssKey());
            taskRecodeService.taskExportCompleted(query.getTaskId(),exportTaskResult);
            return;
        } catch(Exception e){
            log.error("比稿管理下载错误：" + e.getMessage());
            exportTaskResult.setIsSuccess(false);
        }
        taskRecodeService.taskExportFailed(query.getTaskId(),exportTaskResult);
    }

    /**
     * 导出附件字段赋值
     * @param exportModelList 比稿数组
     */
    private void fieldAssignment(List<ComparedDraftExportModel> exportModelList) {
        if(ListUtil.isEmpty(exportModelList)){
            return;
        }
        for(ComparedDraftExportModel draftExportModel : exportModelList) {
            List<HyperlinkPackage> hyperlinkPackageList = new ArrayList<>();
            ossService.relationOssUrl(draftExportModel.getComparedDraftPlan(), OssObjectInfo::getKey,
                    (plan,file) -> hyperlinkPackageList.add(new HyperlinkPackage(plan.getFile(), file)));
            draftExportModel.setComparedDraftPlanPackage(hyperlinkPackageList);
        }
    }

    /**
     * 设置策划人名称
     * @param exportModelList 比稿数组
     */
    private void setPlotterName(List<ComparedDraftExportModel> exportModelList) {
        if(ListUtil.isEmpty(exportModelList)){
            return;
        }
        List<Long> comparedDraftIdList = ListUtil.distinctMap(exportModelList, ComparedDraftExportModel::getComparedDraftId);
        List<ComparedDraftUser> comparedDraftUserList = comparedDraftUserService.listComparedDraftIds(comparedDraftIdList);
        if(ListUtil.isNotEmpty(comparedDraftUserList)){
            Map<Long, List<ComparedDraftUser>> userIdListMap = ListUtil.toGroup(comparedDraftUserList, ComparedDraftUser::getComparedDraftId);
            exportModelList.forEach(comparedDraftExportModel -> {
                List<ComparedDraftUser> comparedDraftUsers = userIdListMap.get(comparedDraftExportModel.getComparedDraftId());
                if(ListUtil.isNotEmpty(comparedDraftUsers)){
                    String name = ListUtil.joining(comparedDraftUsers, ",", ComparedDraftUser::getUserName);
                    comparedDraftExportModel.setUserName(name);
                }
            });
        }
    }

    /**
     * 设置比稿的字典标签
     * @param exportModelList 比稿数组
     */
    private void setComparedDraftDictLabel(List<ComparedDraftExportModel> exportModelList) {
        if(ListUtil.isEmpty(exportModelList)){
            return;
        }
        List<SysDictData> planTypeList = sysDictDataService.selectAllByType(DictConstant.PLAN_TYPE);
        JoinUtils.oneByOne(exportModelList,planTypeList,
                ComparedDraftExportModel::getPlanTypeDict,SysDictData::getDictValue
                ,(exportModel,dict) -> exportModel.setPlanTypeName(dict.getDictLabel()));
        List<SysDictData> pyTypeList = sysDictDataService.selectAllByType(DictConstant.PY_TYPE);
        JoinUtils.oneByOne(exportModelList,pyTypeList,
                ComparedDraftExportModel::getPyTypeDict,SysDictData::getDictValue
                ,(exportModel,dict) -> exportModel.setPyTypeName(dict.getDictLabel()));
        List<SysDictData> industryCategoryList = sysDictDataService.selectAllByType(DictConstant.INDUSTRY_CATEGORY);
        JoinUtils.manyByMany(exportModelList,industryCategoryList,
                ComparedDraftExportModel::getCategoryId,SysDictData::getDictValue
                ,(exportModel,dict) -> exportModel.setCategoryNameStr(ListUtil.joining(dict,",",SysDictData::getDictLabel)));
        List<SysDictData> brandStageList = sysDictDataService.selectAllByType(DictConstant.BRAND_STAGE);
        JoinUtils.oneByOne(exportModelList,brandStageList,
                ComparedDraftExportModel::getBrandStageDict,SysDictData::getDictValue
                ,(exportModel,dict) -> exportModel.setBrandStageStr(dict.getDictLabel()));
    }

    /**
     * 导出模版
     * @param exportModelList 导出信息
     * @return 模版
     */
    @Override
    public ExcelSheetConfig<ComparedDraftExportModel> getComparedDraftExportConfig(List<ComparedDraftExportModel> exportModelList) {
        ExcelSheetConfig<ComparedDraftExportModel> config = new ExcelSheetConfig<>();
        config.setNeedSerialNumber(false)
                .addConfig(ComparedDraftExportModel::getCustomerName,"客户名称")
                .addConfig(ComparedDraftExportModel::getCustomerCooperateName,"客户合作主体")
                .addConfig(ComparedDraftExportModel::getBrandName,"品牌/业务线")
                .addConfig(ComparedDraftExportModel::getComparedDraftName,"比稿项目名称")
                .addConfig(ComparedDraftExportModel::getCommercialBidFruitsStr,"商务标比稿结果")
                .addConfig(ComparedDraftExportModel::getTechniqueBidFruitsStr,"技术标比稿结果")
                .addConfig(ComparedDraftExportModel::getFinalFruitsStr,"最终比稿结果")
                .addConfig(ComparedDraftExportModel::getComparedDraftMoneyStr,"比稿金额(元)")
                .addConfig(ComparedDraftExportModel::getTurnoverMoneyStr,"成交金额(元)")
                .addConfig(ComparedDraftExportModel::getPyTypeName,"派芽业务类型")
                .addConfig(ComparedDraftExportModel::getPlanTypeName,"方案类型")
                .addConfig(ComparedDraftExportModel::getUpdateBy,"比稿人")
                .addConfig(ComparedDraftExportModel::getUserName,"策划人")
                .addConfig(ComparedDraftExportModel::getComparedDraftTimeStr,"比稿时间")
                .addConfig(ComparedDraftExportModel::getEnteringTimeStr,"录入时间")
                .addConfig(ComparedDraftExportModel::getCategoryNameStr,"行业类目")
                .addConfig(ComparedDraftExportModel::getBrandStageStr,"品牌阶段")
                .addConfig(ComparedDraftExportModel::getCollaborateDapt,"合作部门")
                .addConfig(ComparedDraftExportModel::getUpdateDept,"比稿人部门")
                .addConfig(ComparedDraftExportModel::getStrategyReview,"比稿策略复盘")
                .addConfig(ComparedDraftExportModel::getRemarks,"备注");
        if(ListUtil.isNotEmpty(exportModelList)){
            List<ComparedDraftExportModel> modelList = exportModelList.stream().filter(item -> ListUtil.isNotEmpty(item.getComparedDraftPlan())).collect(Collectors.toList());
            int max = 1;
            if(ListUtil.isNotEmpty(modelList)){
                max = modelList.stream().mapToInt(item -> item.getComparedDraftPlan().size()).max().getAsInt();
            }
            for(int i = 0; i < max; i++) {
                int finalI = i;
                config.addFileCellConfig(item -> item.getComparedDraftPlanIndex(finalI), "比稿方案");
            }
        }
        return config;
    }

    /**
     * 查询客户管理-比稿管理详细信息
     * @param idList 客户id
     * @param customerCooperateId 客户合作主题
     * @return 客户管理-比稿管理查询内容
     */
    @Override
    public List<ComparedDraftVO> queryComparedDraftDetailByIds(List<Long> idList, Long customerCooperateId) {
        if(ListUtil.isEmpty(idList)){
            return ListUtil.emptyList();
        }
        List<ComparedDraft> comparedDraftList = this.list(Wrappers.<ComparedDraft>lambdaQuery()
                .in(ListUtil.isNotEmpty(idList),ComparedDraft::getCustomerId, idList)
                .eq(customerCooperateId != null,ComparedDraft::getCustomerCooperateId, customerCooperateId)
                .orderByDesc(ComparedDraft::getComparedDraftTime));
        List<ComparedDraftVO> comparedDraftVOList = this.comparedDraftConverter.toVoByEntity(comparedDraftList);
        if(comparedDraftVOList == null) {
            return ListUtil.emptyList();
        }
        List<Long> comparedDraftIdList = ListUtil.distinctMap(comparedDraftVOList, ComparedDraftVO::getComparedDraftId);
        List<ComparedDraftUser> comparedDraftUserList = comparedDraftUserService.listComparedDraftIds(comparedDraftIdList);
        // 将策划人的名加入出参
        JoinUtils.oneByMany(comparedDraftVOList,comparedDraftUserList
                ,ComparedDraftVO::getComparedDraftId,ComparedDraftUser::getComparedDraftId
                , (comparedDraftVO,comparedDraftUser) -> {
                    List<String> plotterNameList = ListUtil.distinctMap(comparedDraftUser, ComparedDraftUser::getUserName);
                    comparedDraftVO.setPlotterNameList(plotterNameList);
                });
        return comparedDraftVOList;
    }

    /**
     * 获取所有的客户有比稿明细的客户id列表
     * @param customerIds
     * @return
     */
    @Override
    public List<Long> listExistedCustomerIds(List<Long> customerIds) {
        List<Long> existedCustomerIds = comparedDraftMapper.listExistedCustomerIds(customerIds);
        if(ListUtil.isEmpty(existedCustomerIds)) {
            return ListUtil.emptyList();
        }
        return existedCustomerIds;
    }

    /**
     * 执行任务
     * @param comparedDraftQuery 任务参数
     */
    @Override
    public void execute(ComparedDraftQuery comparedDraftQuery) {
        this.download(comparedDraftQuery);
    }

    /**
     * 获取任务参数类型
     * @return 任务参数类型
     */
    @Override
    public Class<ComparedDraftQuery> argsClass() {
        return ComparedDraftQuery.class;
    }


    /**
     * 根据客户id查询比搞数据
     * @param customerId 客户id
     * @return 比搞详情
     */
    @Override
    public List<ComparedDraft> queryComparedDraftByCustomerId(Long customerId) {
        return  this.list(new LambdaQueryWrapper<>(ComparedDraft.class)
                        .in(ComparedDraft::getCustomerId, customerId));
    }


    /**
     * 批量更新客户的比稿信息
     * @param comparedDrafts 客户比稿详情集合
     * @return 批量更新结果
     */
    @Override
    public Boolean updateComparedDraftByCustomerId(List<ComparedDraft> comparedDrafts) {
       return this.updateBatchById(comparedDrafts);
    }


}
