package com.py.crm.customer.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("客户名称模糊搜索下拉参数")
public class CustomerNameDownQuery implements Serializable {
    private static final long serialVersionUID = 1L;


    /** 客户名称*/
    @ApiModelProperty("客户名称" )
    private String custerName;
}
