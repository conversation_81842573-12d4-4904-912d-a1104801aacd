package com.py.common.utils.ip;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020/12/10 15:36
 * @description 将接收的前端字符串类型“ip”转换成Long类型
 **/
@Slf4j
public class IpJsonDeserializer extends JsonDeserializer<Long> {

    @Override
    public Long deserialize(JsonParser p, DeserializationContext ctxt) throws IOException{
        String value = p.getText();
        try {
            return StringUtils.isBlank(value) ? null : IpUtils.ipToInt(value);
        } catch (NumberFormatException e) {
            log.error("ip转换异常，value:{}", value, e);
            return null;
        }
    }
}
