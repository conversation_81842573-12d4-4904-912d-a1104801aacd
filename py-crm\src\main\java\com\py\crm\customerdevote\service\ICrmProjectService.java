package com.py.crm.customerdevote.service;

import com.github.pagehelper.PageInfo;
import com.py.crm.customer.projectInfo.domain.CrmProjectVO;
import com.py.crm.customerdevote.domain.dto.CrmProjectListExportVO;
import com.py.crm.customerdevote.domain.query.CustomerDevoteQuery;
import com.py.crm.customerdevote.domain.vo.CrmProjectListVO;

import java.util.List;

/**
 * 项目管理-项目表Service接口
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
public interface ICrmProjectService{

    /**
     * 分页查询项目管理-项目表列表
     *
     * @param query 项目管理-项目表查询参数
     * @return 项目管理-项目表分页
     */
    PageInfo<CrmProjectListVO> pageProjectList(CustomerDevoteQuery query);

    /**
     * 导出客户贡献管理结案明细
     * @param query
     * @return List<CustomerDevoteBadDebtExportModel> 结案明显
     */
    List<CrmProjectListExportVO> exportCustomerDevoteCloseCase(CustomerDevoteQuery query);

    /**
     * 导出客户贡献管理结案明细sheet
     * @param query
     * @return List<CustomerDevoteBadDebtExportModel> 结案明显
     */
    List<CrmProjectListExportVO> exportCustomerDevoteProject(CustomerDevoteQuery query);

    /**
     * 根据客户id查询项目
     * @param customerIdList 客户id查
     * @return 项目
     */
    List<CrmProjectListExportVO> listProjectByCustomerId(List<Long> customerIdList);

    /**
     * 获取已经结案的项目信息列表
     * @param query 客户贡献查询条件-用于过滤项目合作/结案时间
     * @param customerIds 客户id列表
     * @return 已经结案的项目id列表
     */
    List<Long> selectClosedCustomerIds(CustomerDevoteQuery query, List<Long> customerIds);

    /**
     * 根据客户id查询项目
     * @param customerId 客户id
     * @return CrmProjectVO
     */
    List<CrmProjectVO> getProjectByCustomerId(Long customerId , Long projectId);
}
