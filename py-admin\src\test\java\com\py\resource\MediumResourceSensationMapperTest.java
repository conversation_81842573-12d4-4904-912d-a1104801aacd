package com.py.resource;

import com.py.common.utils.collection.ListUtil;
import com.py.resources.mediumresourcesensation.domain.MediumResourceSensation;
import com.py.resources.mediumresourcesensation.service.IMediumResourceSensationService;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;
import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class MediumResourceSensationMapperTest {

    @Autowired
    private IMediumResourceSensationService iMediumResourceSensationService;

    @Test
    @Ignore
    public void show() {

        List<MediumResourceSensation> list = ListUtil.emptyList();
        for (int i = 0; i < 30000; i++) {
            MediumResourceSensation mediumResourceSensation = new MediumResourceSensation();
            mediumResourceSensation.setResourceCode(String.valueOf(30000 + i));
            mediumResourceSensation.setResourceName("测试导出"+i);
            mediumResourceSensation.setType(0);
            mediumResourceSensation.setCategoryId(Collections.singletonList(1712657102282002434L));
            mediumResourceSensation.setCategoryName("美食");
            mediumResourceSensation.setStatus(0);
            list.add(mediumResourceSensation);
        }
        iMediumResourceSensationService.saveBatch(list);
    }
}
