package com.py.common.utils;

import java.util.concurrent.ThreadLocalRandom;

/**
 * 验证码工具类
 * <AUTHOR>
 */
public class VerifyCodeUtil {

    /** 验证码内容数组 */
    private static final String[] VERIFY_CODE_ARRAY = {
            "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"
    };

    /**
     * 生成验证码
     * @param number 生成验证码位数
     * @return 验证码
     */
    public static String generateVerifyCode(int number) {
        ThreadLocalRandom random = ThreadLocalRandom.current();
        StringBuilder result = new StringBuilder(StringUtils.EMPTY);
        for(int i = 0; i < number; i++) {
            result.append(VERIFY_CODE_ARRAY[Math.abs(random.nextInt()) % VERIFY_CODE_ARRAY.length]);
        }
        return result.toString();
    }
}
