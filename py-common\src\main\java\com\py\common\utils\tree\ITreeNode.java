package com.py.common.utils.tree;

import java.util.List;

/**
 * 树接口
 * <AUTHOR>
 */
public interface ITreeNode<T> {

    /**
     * 获取树Id
     * @return 树Id
     */
    Long getTreeId();

    /**
     * 获取父级Id
     * @return 父级Id
     */
    Long getParentId();

    /**
     * 获取节点名称
     * @return 节点名称
     */
    String getNodeName();

    /**
     * 获取子级列表
     * @return 子级列表
     */
    List<T> getChildren();

    /**
     * 设置子级列表
     * @param children 子级列表
     */
    void setChildren(List<T> children);
}
