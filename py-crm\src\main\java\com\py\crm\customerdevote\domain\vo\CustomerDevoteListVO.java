package com.py.crm.customerdevote.domain.vo;

import com.py.common.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 客户贡献管理
 * <AUTHOR>
 * @version CustomerDevoteListVO 2023/8/9 11:40
 */
@Data
public class CustomerDevoteListVO {

    /** 客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 客户名称*/
    @ApiModelProperty("客户名称")
    private String name;

    /** 品牌/业务线*/
    @ApiModelProperty("品牌/业务线")
    private String lineBusiness;

    /** 行业类目id*/
    @ApiModelProperty("行业类目id")
    private String industryCategoryId;

    /** 行业类目*/
    @ApiModelProperty("行业类目")
    private String industryCategoryName;

    /** 比稿金额(元) */
    @ApiModelProperty("比稿金额(元)")
    private BigDecimal comparedDraftMoney;
    private String comparedDraftMoneyStr;

    /** 成交金额 */
    @ApiModelProperty("成交金额")
    private BigDecimal transactionMoney;

    /** 结案金额 */
    @ApiModelProperty("结案金额")
    private BigDecimal closeCaseMoney;

    /** 收入坏账金额 */
    @ApiModelProperty("收入坏账金额")
    private BigDecimal badDebtMoney;

    /** 利润率 */
    @ApiModelProperty("利润率")
    private BigDecimal profitMargin;

    /**
     * 是否有比稿明细的权限
     */
    @ApiModelProperty("是否有比稿明细的权限")
    private Boolean hasComparedDraft;

    /**
     * 是否有项目明细的权限
     */
    @ApiModelProperty("是否有项目明细的权限")
    private Boolean hasProject;

    /**
     * 是否有结案明细的权限
     */
    @ApiModelProperty("是否有结案明细的权限")
    private Boolean hasCase;

    /**
     * 是否有坏账明细的权限
     */
    @ApiModelProperty("是否有坏账明细的权限")
    private Boolean hasDebt;

    /** 行业类目id*/
    @ApiModelProperty("行业类目id")
    private List<Long> industryCategoryIdList;

    public String getComparedDraftMoneyStr(){
        if(comparedDraftMoney == null){
            return  null;
        }
        return StringUtils.fmtMicrometer(comparedDraftMoney.toString());
    }

}

