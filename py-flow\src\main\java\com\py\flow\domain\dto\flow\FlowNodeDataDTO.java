package com.py.flow.domain.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 审批执行表单信息DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("审批执行表单信息DTO")
public class FlowNodeDataDTO {

    @ApiModelProperty(value = "节点序号")
    private Integer nodeIndex;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "节点状态")
    private String nodeStatus;

    @ApiModelProperty(value = "节点操作")
    private String nodeOperate;

    @ApiModelProperty(value = "节点id")
    private String nodeId;

    @ApiModelProperty(value = "节点人员信息列表")
    private List<FlowNodeUserDataDTO> userList;


    @ApiModelProperty(value = "左线条是否展示")
    private boolean pSl;
    @ApiModelProperty(value = "右侧连接是否展示")
    private boolean pSr;
    @ApiModelProperty(value = "连接线是否展示")
    private boolean showPLine;

    @ApiModelProperty(value = "节点图标样式")
    private String pIcon;
    @ApiModelProperty(value = "节点名称样式")
    private String pName;

    @ApiModelProperty(value = "是否替换序号")
    private boolean replace;

    @ApiModelProperty(value = "颜色")
    private String pColor;
    @ApiModelProperty(value = "连接线样式")
    private String pLine;
    @ApiModelProperty(value = "用户结点执行标识")
    private String pSpot;//操作未执行是p-spot2,已执行是空
}
