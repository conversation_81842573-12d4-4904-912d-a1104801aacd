package com.py.flow.domain.dto.flow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 审批节点用户信息DTO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("审批节点用户信息DTO")
public class FlowNodeUserDataDTO {
    @ApiModelProperty(value = "序号")
    private int userIndex;

    @ApiModelProperty(value = "附件列表")
    private String annexList;

    @ApiModelProperty(value = "")
    private String comment;

    @ApiModelProperty(value = "节点状态")
    private String nodeStatus;

    @ApiModelProperty(value = "节点操作")
    private String nodeOperate;

    @ApiModelProperty(value = "操作时间")
    private String operateTime;

    @ApiModelProperty(value = "回退节点至")
    private String returnNode;

    @ApiModelProperty(value = "")
    private Long userId;

    @ApiModelProperty(value = "")
    private String userName;

    @ApiModelProperty(value = "颜色")
    private String pColor;

    @ApiModelProperty(value = "节点状态颜色")
    private String nodeColor;

}
