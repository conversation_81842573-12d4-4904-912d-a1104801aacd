package com.py.crm.connection.domain.dto;

import com.py.common.tools.poiexcel.annotation.Excel;
import com.py.common.tools.verify.domain.ISerialNumber;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 人脉管理-批量分配错误信息表导出模型
 *
 * <AUTHOR>
 * @date 2023-09-28
 */
@Data
@ApiModel("人脉管理-批量分配错误信息表导出模型" )
public class ConnectionExportErrorModel implements ISerialNumber {
    private static final long serialVersionUID = 1L;

    /** 数据行号 */
    @Excel(setRowIndex = true)
    private Integer serialNumber;

    /** 客户名称 */
    @Excel(name = "*客户姓名")
    @ApiModelProperty("客户姓名" )
    private String connectionName;

    /** 电话 */
    @Excel(name = "电话")
    @ApiModelProperty("电话" )
    private String phone;

    /** *服务人员 */
    @Excel(name = "*服务人员")
    @ApiModelProperty("*服务人员" )
    private String serviceUser;

}
