package com.py.flow.api.flow;

import com.py.flow.tools.aftersign.model.AfterSignDTO;
import com.py.flow.tools.aftersign.model.AfterSignNodeInfo;
import org.flowable.engine.delegate.event.FlowableActivityEvent;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;

/**
 * 流程任务服务
 * <AUTHOR>
 * @date 2023/7/14 14:50
 */
public interface IFlowTaskApi {

    /**
     * 同意审批流程任务
     * @param userId 审批用户
     * @param taskId 任务ID
     */
    void passApproval(Long userId, String taskId);

    /**
     * 拒绝审批流程任务
     * @param userId 审批用户
     * @param taskId 任务ID
     */
    void rejectApproval(Long userId, String taskId);

    /**
     * 流程撤回
     * @param processInstanceId 流程实例ID
     */
    void cancelApproval(String processInstanceId);

    /**
     * 流程作废
     * @param processInstanceId 流程实例ID
     */
    void invalidatedApproval(String processInstanceId);

    /**
     * 流程转交
     * @param taskId 需转交的任务
     * @param userId 转交人
     * @param forwardUserId 被转交人
     */
    void forwardApproval(String taskId, Long userId, Long forwardUserId);

    /**
     * 查询指定ID的任务
     * @param taskId 任务ID
     * @return 任务
     */
    Task selectTask(String taskId);

    /**
     * 向后加签
     * @param afterSignDto 向后加签DTO
     * @param userId 操作用户ID
     * @return 加签成功的节点信息
     */
    AfterSignNodeInfo afterSign(AfterSignDTO afterSignDto, Long userId);

    /**
     * 查询Flowable流程实例
     * @param processInstanceId 流程实例ID
     * @return Flowable流程实例
     */
    ProcessInstance selectProcessInstance(String processInstanceId);

    /**
     * 设置流程实例变量
     * @param processInstanceId 流程实例ID
     * @param variableName 变量名
     * @param value 变量值
     */
    void setProcessInstanceVariable(String processInstanceId, String variableName, Object value);

    /**
     * 设置流程实例变量
     * @param processInstance 流程实例
     * @param variableName 变量名
     * @param value 变量值
     */
    void setProcessInstanceVariable(ProcessInstance processInstance, String variableName, Object value);

    /**
     * 通知子流程完成
     * @param activityCompletedEvent 子流程完成事件
     */
    void notifySubProcessCompleted(FlowableActivityEvent activityCompletedEvent);
}
