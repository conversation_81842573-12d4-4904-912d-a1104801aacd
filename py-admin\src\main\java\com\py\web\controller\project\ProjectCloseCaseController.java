package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.project.enums.ExportType;
import com.py.project.project.domain.query.ProjectQuery;
import com.py.project.project.domain.vo.ProjectDetailsVO;
import com.py.project.projectclosecase.domain.dto.ProjectCloseCaseDTO;
import com.py.project.projectclosecase.domain.query.ProjectCloseCaseAuditQuery;
import com.py.project.projectclosecase.domain.query.ProjectCloseCaseQuery;
import com.py.project.projectclosecase.domain.vo.ProjectCloseCaseAuditCountVO;
import com.py.project.projectclosecase.domain.vo.ProjectCloseCaseAuditListVO;
import com.py.project.projectclosecase.domain.vo.ProjectCloseCaseCommitInfoVO;
import com.py.project.projectclosecase.domain.vo.ProjectCloseCaseListVO;
import com.py.project.projectclosecase.service.IProjectCloseCaseService;
import com.py.project.projectclosecase.service.impl.ProjectCloseCaseDownloadServiceImpl;
import com.py.project.projectclosecase.service.impl.ProjectCloseCaseServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 项目管理-项目结案信息Controller
 *
 * <AUTHOR>
 * @date 2023-08-01
 */
@Api(tags = "项目管理-项目结案信息")
@RestController
@RequestMapping("/project/projectCloseCase")
public class ProjectCloseCaseController extends BaseController {

    /** 项目管理-项目结案信息服务 */
    @Resource
    private IProjectCloseCaseService projectCloseCaseService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;


    /**
     * 查询项目管理-项目结案信息列表
     *
     * @param query 项目管理-项目结案信息查询参数
     * @return 项目管理-项目结案信息列表
     */
    @ApiOperation("查询项目管理-项目结案信息列表")
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:list')")
    @GetMapping("/listProjectCloseCase")
    public R<List<ProjectCloseCaseListVO>> listProjectCloseCase(ProjectCloseCaseQuery query) {
        List<ProjectCloseCaseListVO> voList = this.projectCloseCaseService.listProjectCloseCase(query);
        return R.success(voList);
    }

    /**
     * 分页查询项目管理-项目结案信息列表
     *
     * @param query 项目管理-项目结案信息查询参数
     * @return 项目管理-项目结案信息分页
     */
    @ApiOperation("分页查询询项目管理-项目结案信息列表")
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:list')")
    @GetMapping("/pageProjectCloseCase")
    public R<PageInfo<ProjectCloseCaseListVO>> pageProjectCloseCase(ProjectCloseCaseQuery query) {
        PageInfo<ProjectCloseCaseListVO> voList = this.projectCloseCaseService.pageProjectCloseCaseList(query);
        return R.success(voList);
    }

    /**
     * 获取项目管理-项目结案信息详细信息
     * @param query 项目管理-项目结案信息主键
     * @return 项目管理-项目结案信息视图模型
     */
    @ApiOperation("获取项目管理-项目结案信息详细信息")
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:query')")
    @GetMapping(value = "/getInfo")
    public R<ProjectDetailsVO> getInfo(ProjectQuery query) {
        return R.success(projectCloseCaseService.selectProjectCloseCaseById(query));
    }

    /**
     * 获取项目管理-项目结案信息详细信息-打印数据
     * @param query 项目管理-项目结案信息主键
     * @return 项目管理-项目结案信息视图模型
     */
    @ApiOperation("获取项目管理-项目结案信息详细信息-打印数据")
    @GetMapping(value = "/print/getInfo")
    public R<ProjectDetailsVO> printGetInfo(ProjectQuery query) {
        return R.success(projectCloseCaseService.selectProjectCloseCaseById(query));
    }

    /**
     * 批量结案信息列表
     * @param query 项目管理-项目结案信息主键
     * @return 项目管理-项目结案信息视图模型
     */
    @ApiOperation("批量结案信息列表")
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:query')")
    @GetMapping(value = "/getCloseCaseList")
    public R<ProjectCloseCaseCommitInfoVO> getCloseCaseList(ProjectQuery query) {
        return R.success(projectCloseCaseService.getCloseCaseList(query));
    }

    /**
     * 新增项目管理-项目结案信息
     *
     * @param dto 项目管理-项目结案信息修改参数
     * @return 是否成功
     */
    @ApiOperation("新增项目管理-项目结案信息")
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:add')")
    @Log(title = "项目管理-项目结案信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Long> add(@RequestBody ProjectCloseCaseDTO dto) {
        return R.success(projectCloseCaseService.insertProjectCloseCase(dto));
    }

    /**
     * 校验提交结案
     *
     * @param dto 项目管理-项目结案信息修改参数
     */
    @ApiOperation("校验提交结案")
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:add')")
    @GetMapping("/checkProjectCloseCase")
    public R<Boolean> checkProjectCloseCase(ProjectCloseCaseDTO dto) {
        return R.success(projectCloseCaseService.checkProjectCloseCase(dto));
    }

    /**
     * 修改项目管理-项目结案信息
     *
     * @param dto 项目管理-项目结案信息修改参数
     * @return 是否成功
     */
    @ApiOperation("修改项目管理-项目结案信息")
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:edit')")
    @Log(title = "项目管理-项目结案信息", businessType = BusinessType.UPDATE)
    @PostMapping("/approveProjectCloseCase")
    public R<Long> approveProjectCloseCase(@RequestBody ProjectCloseCaseDTO dto) {
        return R.success(projectCloseCaseService.approveProjectCloseCase(dto));
    }

    /**
     * 删除项目管理-项目结案信息
     * @param ids 需要删除的项目管理-项目结案信息主键集合
     * @return 是否成功
     */
    @ApiOperation("删除项目管理-项目结案信息" )
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:remove')")
    @Log(title = "项目管理-项目结案信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(projectCloseCaseService.deleteProjectCloseCaseByIds(ids));
    }

    /**
     * 导出项目管理-项目结案信息
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目管理-项目结案信息")
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:export')")
    @Log(title = "项目管理-项目结案信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@RequestBody ProjectCloseCaseQuery query) throws IOException {

        reusableAsyncTaskService.addTask("项目详情结案信息下载", TaskType.Export,query, ProjectCloseCaseServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);

    }

    /**
     * 分页查询结案审批查询列表
     *
     * @param query 项目管理-项目结案信息查询参数
     * @return 项目管理-项目结案信息分页
     */
    @ApiOperation("分页查询结案审批查询列表")
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:list')")
    @GetMapping("/pageProjectCloseCaseAudit")
    public R<PageInfo<ProjectCloseCaseAuditListVO>> pageProjectCloseCaseAudit(ProjectCloseCaseAuditQuery query) {

        PageInfo<ProjectCloseCaseAuditListVO> voList = this.projectCloseCaseService.pageProjectCloseCaseAudit(query);
        return R.success(voList);
    }

    /**
     * 结案查询合计
     *
     * @param query 项目管理-项目结案信息查询参数
     * @return 项目管理-项目结案信息分页
     */
    @ApiOperation("结案查询合计")
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:list')")
    @GetMapping("/closeCaseTotalCount")
    public R<ProjectCloseCaseAuditCountVO> closeCaseTotalCount(ProjectCloseCaseAuditQuery query) {
        ProjectCloseCaseAuditCountVO caseTotalCount = this.projectCloseCaseService.closeCaseTotalCount(query);
        return R.success(caseTotalCount);
    }

    /**
     * 审批查询-结案查询-主体统计
     * @return 合计数据信息
     */
    @ApiOperation("审批查询-结案查询-主体统计")
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:approval:sumlist')")
    @GetMapping("/sumProjectFinishMainstayId")
    public R<List<ProjectCloseCaseAuditCountVO>> sumProjectFinishMainstayId() {
        List<ProjectCloseCaseAuditCountVO> list = this.projectCloseCaseService.sumProjectFinishMainstayId();
        return R.success(list);
    }

    /**
     * 审批查询-结案查询-下载
     * @param query 导出查询参数
     */
    @ApiOperation("审批查询-结案查询-下载")
    @PreAuthorize("@ss.hasPermi('project:projectCloseCase:approval:export')")
    @Log(title = "项目管理-项目结案信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportProjectCloseCase")
    public R<String> exportProjectCloseCase(@RequestBody ProjectCloseCaseAuditQuery query) throws IOException {
        query.setExportType(ExportType.PROJECT_CLOSE_CASE_APPROVAL_LIST);
        reusableAsyncTaskService.addTask("项目结案审批查询",
                TaskType.Export,
                query,
                ProjectCloseCaseDownloadServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 查询结案审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询结案审批列表上的创建部门下拉" )
    @GetMapping("/listProjectCloseCaseAuditDept" )
    public R<List<String>> listProjectCloseCaseAuditDept(ProjectCloseCaseAuditQuery query){
        return R.success(projectCloseCaseService.listProjectCloseCaseAuditDept(query));
    }
}
