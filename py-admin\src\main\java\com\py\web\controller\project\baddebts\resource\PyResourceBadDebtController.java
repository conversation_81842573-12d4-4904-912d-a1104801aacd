package com.py.web.controller.project.baddebts.resource;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.project.projectresource.domain.vo.ResourceInfoVO;
import com.py.project.pyincomebaddebt.domain.vo.PyBadDeptLabelCountVO;
import com.py.project.pyresourcebaddebt.domain.dto.PyResourceBadDebtDTO;
import com.py.project.pyresourcebaddebt.domain.query.BadDebtBusinessFinancialQuery;
import com.py.project.pyresourcebaddebt.domain.query.PyResourceBadDebtCheckBoxQuery;
import com.py.project.pyresourcebaddebt.domain.query.PyResourceBadDebtQuery;
import com.py.project.pyresourcebaddebt.domain.query.PyResourceQuery;
import com.py.project.pyresourcebaddebt.domain.vo.PyResourceBadDebtInfoVO;
import com.py.project.pyresourcebaddebt.domain.vo.PyResourceCheckboxVO;
import com.py.project.pyresourcebaddebt.service.IPyResourceBadDebtService;
import com.py.project.pyresourcebaddebt.service.impl.PyResourceBadDebtServiceImpl;
import com.py.project.pyresourcebaddebtflow.domain.vo.PyResourceBadDebtFlowListVO;
import com.py.project.pyresourcebaddebtflow.domain.vo.ResourceBadDebtFlowApprovalVO;
import com.py.system.mainstayparam.domain.SystemMainstayParam;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 资源返点坏账审批Controller
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
@Api(tags = "资源返点坏账审批")
@RestController
@RequestMapping("/project/pyResourceBadDebt")
public class PyResourceBadDebtController extends BaseController {

    /** 资源返点坏账审批服务 */
    @Resource
    private IPyResourceBadDebtService pyResourceBadDebtService;


    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 系统设置-主体参数设置服务 */
    @Resource
    private ISystemMainstayParamService systemMainstayParamService;

    /**
     * 分页查询资源返点坏账审批列表
     *
     * @param query 资源返点坏账审批查询参数
     * @return 资源返点坏账审批分页
     */
    @ApiOperation("分页查询资源返点坏账审批列表")
    @PreAuthorize("@ss.hasPermi('project:pyResourceBadDebt:list')")
    @PostMapping("/pagePyResourceBadDebt")
    public R<PageInfo<PyResourceBadDebtFlowListVO>> pagePyResourceBadDebt(@Validated @RequestBody PyResourceBadDebtQuery query) {
        PageInfo<PyResourceBadDebtFlowListVO> voList = this.pyResourceBadDebtService.pagePyResourceBadDebtList(query);
        return R.success(voList);
    }


    /**
     * 获取资源返点坏账审批详细信息
     * @param id 资源返点坏账审批主键
     * @return 资源返点坏账审批视图模型
     */
    @ApiOperation("获取资源返点坏账审批详细信息")
    @PreAuthorize("@ss.hasPermi('project:pyResourceBadDebt:query')")
    @GetMapping(value = "getInfo/{id}")
    public R<PyResourceBadDebtInfoVO> getInfo(@PathVariable("id") Long id) {
        return R.success(pyResourceBadDebtService.selectPyResourceBadDebtById(id));
    }

    /**
     * 获取资源返点坏账审批详细信息
     * @param id 资源返点坏账审批主键
     * @return 资源返点坏账审批视图模型
     */
    @ApiOperation("获取资源返点坏账审批详细信息打印数据")
    @GetMapping(value = "print/getInfo/{id}")
    public R<PyResourceBadDebtInfoVO> printGetInfo(@PathVariable("id") Long id) {
        return R.success(pyResourceBadDebtService.selectPyResourceBadDebtById(id));
    }

    /**
     * 新增资源返点坏账审批
     *
     * @param dto 资源返点坏账审批修改参数
     * @return 是否成功
     */
    @ApiOperation("新增资源返点坏账审批")
    @PreAuthorize("@ss.hasPermi('project:pyResourceBadDebt:add')")
    @Log(title = "资源返点坏账审批", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Long> add(@RequestBody @Validated PyResourceBadDebtDTO dto) {
        return R.success(pyResourceBadDebtService.insertPyResourceBadDebt(dto));
    }

    /**
     * 更新资源返点坏账审批
     *
     * @param dto 资源返点坏账审批修改参数
     * @return 是否成功
     */
    @ApiOperation("更新资源返点坏账审批")
    @PreAuthorize("@ss.hasPermi('project:pyResourceBadDebt:edit')")
    @Log(title = "资源返点坏账审批", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R<Long> edit(@RequestBody PyResourceBadDebtDTO dto) {
        return R.success(pyResourceBadDebtService.updatePyResourceBadDebt(dto));
    }

    /**
     * 查询项目资源
     * @param pyResourceQuery
     * @return
     */
    @ApiOperation("查询项目资源")
    @PostMapping("/selectResource")
    public R<PageInfo<ResourceInfoVO>> selectResource(@RequestBody PyResourceQuery pyResourceQuery){
       return R.success(pyResourceBadDebtService.selectResource(pyResourceQuery));
    }

    /**
     * 财务合同的标签页数量
     * @param pyMainstayIds 派芽合作主体id集合
     * @return 客户合同信息视图模型
     */
    @ApiOperation("财务合同的资源坏账标签页数量")
    @PreAuthorize("@ss.hasPermi('crm:pyCustomerContract:query')")
    @PostMapping(value = "/getLabel")
    public R<List<PyBadDeptLabelCountVO>> getLabel(@RequestBody List<Long> pyMainstayIds) {
        return R.success(pyResourceBadDebtService.getBadDeptLabel(pyMainstayIds));
    }

    /**
     * 多选框传参
     * @param query
     * @return
             */
    @ApiOperation("多选框统计")
    @PreAuthorize("@ss.hasPermi('project:pyResourceBadDebt:add')")
    @PostMapping("/checkbox")
    public R<PyResourceCheckboxVO> checkBox(@RequestBody PyResourceBadDebtCheckBoxQuery query) {
        return R.success(pyResourceBadDebtService.selectCheckBox(query));
    }

    /**
     * 导出资源返点坏账审批
     * @param query 导出查询参数
     */
    @ApiOperation("导出资源返点坏账审批")
    @PreAuthorize("@ss.hasPermi('project:pyResourceBadDebt:export')")
    @Log(title = "资源返点坏账审批", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export( @Validated @RequestBody  PyResourceBadDebtQuery query) {
        String fileName;
        if(ListUtil.any(query.getPyMainstayIdList())) {
            //查询主体名称
            List<SystemMainstayParam> mainstayParamList = this.systemMainstayParamService.listByIds(query.getPyMainstayIdList());

            fileName = "返点坏账管理列表数据-"+mainstayParamList.get(0).getMainstayName()+"-"
                    + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        } else {
            fileName = "返点坏账管理列表数据-"+ DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        }

        query.setFileName(fileName);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("返点坏账管理列表数据", TaskType.Export, query, PyResourceBadDebtServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 查询返点坏账列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询返点坏账列表上的创建部门下拉" )
    @GetMapping("/listResourceBadDebtDept" )
    public R<List<String>> listResourceBadDebtDept(PyResourceBadDebtQuery query){
        return R.success(pyResourceBadDebtService.listResourceBadDebtDept(query));
    }

    /**
     * 分页查询资源返点坏账审批列表
     *
     * @param query 资源返点坏账审批查询参数
     * @return 资源返点坏账审批分页
     */
    @ApiOperation("分页查询资源返点坏账审批列表")
    @PreAuthorize("@ss.hasPermi('project:pyResourceBadDebt:list')")
    @PostMapping("/listApprovalPyResourceBadDebt")
    public R<PageInfo<ResourceBadDebtFlowApprovalVO>> listApprovalPyResourceBadDebt(@Validated @RequestBody PyResourceBadDebtQuery query) {
        PageInfo<ResourceBadDebtFlowApprovalVO> voList = this.pyResourceBadDebtService.listApprovalPyResourceBadDebt(query);
        return R.success(voList);
    }

    /**
     * 审批列表合计
     * @param query 查询条件
     * @return 统计
     */
    @ApiOperation("审批列表合计")
    @PreAuthorize("@ss.hasPermi('project:pyResourceBadDebt:add')")
    @PostMapping("/countApprovalBadDebt")
    public R<PyResourceCheckboxVO> countApprovalBadDebt(@RequestBody PyResourceBadDebtCheckBoxQuery query) {
        return R.success(pyResourceBadDebtService.countApprovalBadDebt(query));
    }

    /**
     * 审批列表顶部统计
     * @param pyMainstayIds 派芽合作主体id集合
     * @return 审批列表顶部统计
     */
    @ApiOperation("审批列表顶部统计")
    @PreAuthorize("@ss.hasPermi('crm:pyCustomerContract:query')")
    @PostMapping(value = "/countApprovalLabel")
    public R<List<PyBadDeptLabelCountVO>> countApprovalLabel(@RequestBody List<Long> pyMainstayIds) {
        return R.success(pyResourceBadDebtService.countApprovalLabel(pyMainstayIds));
    }

    /**
     * 导出资源返点坏账审批列表
     * @param query 导出查询参数
     */
    @ApiOperation("导出资源返点坏账审批列表")
    @PreAuthorize("@ss.hasPermi('project:pyResourceBadDebt:export')")
    @Log(title = "资源返点坏账审批", businessType = BusinessType.EXPORT)
    @PostMapping("/exportApproval")
    public R<String> exportApproval( @Validated @RequestBody  PyResourceBadDebtQuery query) {
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPyMainstayId());
        String fileName = "返点坏账审批查询列表数据-" + systemMainstayParamVO.getMainstayName() + "-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setIsApproval(true);
        reusableAsyncTaskService.addTask("资源返点坏账审批列表", TaskType.Export,query, PyResourceBadDebtServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 企业财报 - 分页查询资源返点坏账审批列表
     *
     * @param query 资源返点坏账审批查询参数
     * @return 资源返点坏账审批分页
     */
    @ApiOperation("企业财报 - 分页查询资源返点坏账审批列表")
    @PostMapping("/pagePyResourceBadDebtFinance")
    public R<PageInfo<PyResourceBadDebtFlowListVO>> pagePyResourceBadDebtFinance(@Validated @RequestBody PyResourceBadDebtQuery query) {
        PageInfo<PyResourceBadDebtFlowListVO> voList = this.pyResourceBadDebtService.pagePyResourceBadDebtFinance(query);
        return R.success(voList);
    }


    /**
     * 业务财报 - 分页查询资源返点坏账审批列表
     *
     * @param query 资源返点坏账审批查询参数
     * @return 资源返点坏账审批分页
     */
    @ApiOperation("业务财报 - 分页查询资源返点坏账审批列表")
    @PostMapping("/pagePyResourceBadDebtBusinessFinancial")
    public R<PageInfo<PyResourceBadDebtFlowListVO>> pagePyResourceBadDebtBusinessFinancial(@RequestBody BadDebtBusinessFinancialQuery query) {
        PageInfo<PyResourceBadDebtFlowListVO> voList = this.pyResourceBadDebtService.pagePyResourceBadDebtBusinessFinancial(query);
        return R.success(voList);
    }

    /**
     * 业务财报 - 分页查询资源返点坏账审批列表
     *  部门快照版本
     * @param query 资源返点坏账审批查询参数
     * @return 资源返点坏账审批分页
     */
    @ApiOperation("业务财报 - 分页查询资源返点坏账审批列表(部门快照版本)")
    @PostMapping("/pagePyResourceBadDebtBusinessFinancial/dept")
    public R<PageInfo<PyResourceBadDebtFlowListVO>> pagePyResourceBadDebtBusinessFinancialDept(@RequestBody BadDebtBusinessFinancialQuery query) {
        PageInfo<PyResourceBadDebtFlowListVO> voList = this.pyResourceBadDebtService.pagePyResourceBadDebtBusinessFinancialDept(query);
        return R.success(voList);
    }


    /**
     * 查询返点坏账审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询返点坏账审批列表上的创建部门下拉" )
    @GetMapping("/listApprovalResourceBadDebtDept" )
    public R<List<String>> listApprovalResourceBadDebtDept(PyResourceBadDebtQuery query){
        return R.success(pyResourceBadDebtService.listApprovalResourceBadDebtDept(query));
    }
}
