package com.py.crm.customer.customerlaundry.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.py.common.core.domain.R;
import com.py.crm.customer.customerlaundry.domain.CustomerLaundry;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryListDTO;
import com.py.crm.customer.customerlaundry.domain.vo.CustomerLaundryVO;
import com.py.crm.customer.customerlaundry.domain.vo.CustomerLaundryListVO;
import com.py.crm.customer.customerlaundry.domain.query.CustomerLaundryQuery;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryDTO;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryExportModel;
import com.github.pagehelper.PageInfo;
import com.py.crm.customer.domain.vo.CustomerCountVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;

import java.util.List;

/**
 * 客户管理-查看清单Service接口
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
public interface ICustomerLaundryService {

    /**
     * 分页查询客户管理-查看清单列表
     * @param query 客户管理-查看清单查询参数
     * @return 客户管理-查看清单分页
     */
    PageInfo<CustomerLaundryListVO>pageCustomerLaundryList(CustomerLaundryQuery query);

    /**
     * 批量新增客户管理-查看清单
     * @param dto 客户管理-查看清单修改参数
     * @return 是否成功
     */
    boolean insertBatchCustomerLaundry(CustomerLaundryListDTO dto);

    /**
     * 批量删除客户管理-查看清单
     * @param idList 需要删除的客户管理-查看清单主键集合
     * @return 是否成功
     */
    boolean deleteCustomerLaundryByIds(List<Long> idList);

    /**
     * 导出客户管理-查看清单
     * @param query 导出查询参数
     * @return 导出数据
     */
    void exportCustomerLaundry(CustomerLaundryQuery query);

    /**
     * 删除下载的客户
     * @param query 查询条件
     * @return 返回结果
     */
    List<CustomerLaundryListVO> removeCustomerLaundry(CustomerLaundryQuery query);

    /**
     * 导入客户管理-查看清单
     * @param exportList 导入数据
     * @return 导入结果
     */
    String importCustomerLaundry(List<CustomerLaundryExportModel> exportList);

    /**
     * 客户管理-查看清单头部统计
     * @return 是否成功
     */
    CustomerCountVO customerLaundryCount();

    /**
     * 客户管理-移除清单
     * @param dto 客户ID
     * @return 是否成功
     */
    Boolean removeLaundry(CustomerLaundryListDTO dto);
}
