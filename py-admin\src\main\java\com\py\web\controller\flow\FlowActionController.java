package com.py.web.controller.flow;

import com.github.pagehelper.PageInfo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.PageUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.ServletUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.flow.api.flow.IFlowDefinitionApi;
import com.py.flow.domain.dto.flow.ApprovalSubmitDTO;
import com.py.flow.domain.dto.flow.ReApprovalSubmitDTO;
import com.py.flow.domain.enums.ApprovalBizType;
import com.py.flow.domain.query.ApprovalListQuery;
import com.py.flow.domain.query.BatchPrintFlowApprovalQuery;
import com.py.flow.domain.query.PersonalApprovalStatisticsQuery;
import com.py.flow.domain.vo.ApprovalListVO;
import com.py.flow.domain.vo.PersonalApprovalStatisticsVO;
import com.py.flow.event.ApprovalEventManager;
import com.py.flow.event.FlowApprovalStatusChangeEvent;
import com.py.flow.flowinstance.approvalsnapshot.service.IApprovalSnapshotService;
import com.py.flow.flowinstance.domain.FlowInstance;
import com.py.flow.flowinstance.domain.dto.FlowApprovalCancelDTO;
import com.py.flow.flowinstance.domain.dto.FlowApprovalCounterSignDTO;
import com.py.flow.flowinstance.domain.dto.FlowApprovalForwardDTO;
import com.py.flow.flowinstance.domain.dto.FlowApprovalInvalidatedDTO;
import com.py.flow.flowinstance.domain.dto.FlowApprovalPassDTO;
import com.py.flow.flowinstance.domain.dto.FlowApprovalRejectDTO;
import com.py.flow.flowinstance.domain.dto.FlowApprovalReturnDTO;
import com.py.flow.flowinstance.domain.dto.FlowCommentDTO;
import com.py.flow.flowinstance.domain.dto.FlowRecipientAppendDTO;
import com.py.flow.flowinstance.domain.query.ApprovalBizInfoQuery;
import com.py.flow.flowinstance.domain.query.ApprovalInfoQuery;
import com.py.flow.flowinstance.domain.query.ReturnableNodeQuery;
import com.py.flow.flowinstance.domain.vo.ApprovalFlowChartVO;
import com.py.flow.flowinstance.domain.vo.FlowReturnableNodeVO;
import com.py.flow.flowinstance.service.IFlowInstanceService;
import com.py.flow.flowinstance.service.impl.FlowInstancePrintServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 流程管理 - 流程操作控制器
 * <AUTHOR>
 * @date 2023/7/14 14:21
 */
@Api(tags = "流程管理 - 流程操作")
@RestController
@RequestMapping("/flow-action")
@Log4j2
public class FlowActionController extends BaseController {

    /** 流程定义API */
    @Resource
    private IFlowDefinitionApi flowDefinitionService;

    /** 审批实例服务 */
    @Resource
    private IFlowInstanceService flowInstanceService;

    /** 审批快照服务接口 */
    @Resource
    private IApprovalSnapshotService approvalSnapshotService;

    /** 审批事件管理器 */
    @Resource
    private ApprovalEventManager approvalEventManager;

    /**
     * 可视化异步任务服务
     */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 获取审批列表
     * @param query 查询参数
     * @return 审批列表
     */
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "获取审批列表")
    @GetMapping("/pageApprovalList")
    public R<PageInfo<ApprovalListVO>> pageApprovalList(@Validated ApprovalListQuery query) {
        Long userId = SecurityUtils.getUserId();
        return R.success(this.flowInstanceService.pageApprovalList(query, userId));
    }

    /**
     * 获取审批列表下部门
     * @param query 查询参数
     * @return 部门集合
     */
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "获取审批列表下部门")
    @PostMapping("/pageApprovalDeptList")
    public R<List<String>> pageApprovalDeptList(@Validated @RequestBody ApprovalListQuery query) {
        Long userId = SecurityUtils.getUserId();
        return R.success(this.flowInstanceService.pageApprovalDeptList(query, userId));
    }

    /**
     * 获取业务审批的业务类型
     * @return 业务类型列表
     */
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "获取业务审批的业务类型")
    @GetMapping("/getByBizTypeList")
    public R<List<FlowBizTypeVO>> getByBizTypeList(Integer kind) {

        List<ApprovalBizType> approvalBizTypeList = Arrays.asList(ApprovalBizType.values());

        List<ApprovalBizType> bizTypeList = approvalBizTypeList.stream()
                .filter(item -> item.getKind().getValue().equals(kind))
                .sorted(Comparator.comparing(ApprovalBizType::isHasSupportPrint).reversed())
                .collect(Collectors.toList());

        List<FlowBizTypeVO> flowBizTypeVOList = bizTypeList.stream().map(item -> {
            FlowBizTypeVO flowBizTypeVO = new FlowBizTypeVO();
            flowBizTypeVO.setValue(item.getValue());
            flowBizTypeVO.setLabel(item.getLabel());
            return flowBizTypeVO;
        }).collect(Collectors.toList());

        return R.success(flowBizTypeVOList);
    }


    /**
     * 获取下一条待我审批的id与审批类型
     * @param query 查询参数
     * @return 下一条审批id与类型
     */
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "获取下一条待我审批的id与审批类型")
    @PostMapping("/nextWaitApproval")
    public R<ApprovalListVO> nextWaitApproval(@RequestBody ApprovalListQuery query) {
        Long userId = SecurityUtils.getUserId();
        return R.success( this.flowInstanceService.nextWaitApproval(query,userId));
    }

    /**
     * 获取个人审批统计
     * @param query 查询参数
     * @return 个人审批统计
     */
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "获取个人审批统计")
    @GetMapping("/personalApprovalStatistics")
    public R<PersonalApprovalStatisticsVO> getPersonalApprovalStatistics(@Validated PersonalApprovalStatisticsQuery query) {
        Long userId = SecurityUtils.getUserId();
        return R.success(this.flowInstanceService.getPersonalApprovalStatistics(query, userId));
    }

    /**
     * 提交审批流程
     * @param submitDto 提交审批流程DTO
     * @return 是否成功
     */
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "提交审批流程")
    @Log(title = "流程管理 - 流程操作- 提交审批流程", businessType = BusinessType.INSERT)
    @PostMapping("/submitFlow")
    public R<Boolean> submitFlow(@RequestBody ApprovalSubmitDTO submitDto){
        this.flowInstanceService.submitFlow(submitDto);
        return R.success();
    }

    /**
     * 再次提交审批流程
     * @param reSubmitDTO 提交审批流程DTO
     * @return 是否成功
     */
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "再次提交审批流程")
    @Log(title = "流程管理 - 流程操作- 再次提交审批流程", businessType = BusinessType.INSERT)
    @PostMapping("/reSubmitFlow")
    public R<Boolean> reSubmitFlow(@RequestBody ReApprovalSubmitDTO reSubmitDTO){
        this.flowInstanceService.reSubmitFlow(reSubmitDTO);
        return R.success();
    }

    /**
     * 获取审批详情
     * @param query 查询参数
     * @return 流程图
     */
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "获取审批详情")
    @GetMapping("/approvalFlowChart")
    public R<ApprovalFlowChartVO> getApprovalFlowChart(@Validated ApprovalInfoQuery query) {
        return R.success(this.flowInstanceService.getApprovalFlowChart(query));
    }

    /**
     * 获取审批业务详情
     * @param query 查询参数
     * @return 审批业务的详情VO
     */
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "获取审批业务详情")
    @GetMapping("/approvalBizInfo")
    public R<Object> getApprovalBizInfo(@Validated ApprovalBizInfoQuery query){
        FlowInstance flowInstance;
        if(query.getFlowInstanceId() != null){
            flowInstance = this.flowInstanceService.selectFlowInstance(query.getFlowInstanceId());
        }else {
            flowInstance = this.flowInstanceService.selectFlowInstance(query.getBizType(), query.getBizId());
        }
        return R.success(this.approvalSnapshotService.getApprovalBizInfo(flowInstance));
    }


    /**
     * 获取审批业务详情打印数据
     * @param query 查询参数
     * @return 审批业务的详情VO
     */
    @ApiOperation(value = "获取审批业务详情打印数据")
    @GetMapping("/print/approvalBizInfo")
    public R<Object> printGetApprovalBizInfo(@Validated ApprovalBizInfoQuery query){
        FlowInstance flowInstance;
        if(query.getFlowInstanceId() != null){
            flowInstance = this.flowInstanceService.selectFlowInstance(query.getFlowInstanceId());
        }else {
            flowInstance = this.flowInstanceService.selectFlowInstance(query.getBizType(), query.getBizId());
        }
        return R.success(this.approvalSnapshotService.getApprovalBizInfo(flowInstance));
    }

    /**
     * 同意审批流程
     * @param passDto 审批通过DTO
     * @return 是否成功
     */
    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "同意审批流程任务")
    @Log(title = "流程管理 - 流程操作- 同意审批流程任务", businessType = BusinessType.UPDATE)
    @PostMapping("/passApproval")
    public R<Boolean> passApproval(@Validated @RequestBody FlowApprovalPassDTO passDto) {
        this.flowInstanceService.passApproval(passDto, SecurityUtils.getUserId());
        return R.success();
    }

    /**
     * 虚拟同意审批流程任务
     * @param passDto 审批通过DTO
     * @return 是否成功
     */
    @SuppressWarnings("ConstantConditions")
    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "虚拟同意审批流程任务")
    @Log(title = "流程管理 - 流程操作- 虚拟同意审批流程任务", businessType = BusinessType.UPDATE)
    @PostMapping("/mockPassApproval")
    public R<Boolean> mockPassApproval(@Validated @RequestBody FlowApprovalPassDTO passDto) {
        PageUtils.startPage();
        this.flowInstanceService.passApproval(passDto, Long.valueOf(ServletUtils.getParameter("userId")));
        return R.success();
    }

    /**
     * 拒绝审批流程
     * @param rejectDto 审批拒绝DTO
     * @return 是否成功
     */
    @ApiOperationSupport(order = 14)
    @ApiOperation("拒绝流程任务")
    @Log(title = "流程管理 - 流程操作- 拒绝流程任务", businessType = BusinessType.UPDATE)
    @PostMapping("/rejectApproval")
    public R<Boolean> rejectApproval(@Validated @RequestBody FlowApprovalRejectDTO rejectDto) {
        this.flowInstanceService.rejectApproval(rejectDto);
        return R.success();
    }

    /**
     * 流程任务评论
     * <p>流程审批过了审批人也可以提交评论，评论会出现在审批人所在的节点位置不会出现在其它的节点</p>
     * @param commentDto 流程评论DTO
     * @return 是否成功
     */
    @ApiOperationSupport(order = 15)
    @ApiOperation(value = "流程任务评论")
    @Log(title = "流程管理 - 流程操作- 流程任务评论", businessType = BusinessType.UPDATE)
    @PostMapping("/approvalComment")
    public R<Boolean> addApprovalComment(@Validated @RequestBody FlowCommentDTO commentDto) {
        this.flowInstanceService.addApprovalComment(commentDto, SecurityUtils.getUserId());
        return R.success();
    }

    /**
     * 添加审批流程抄送人
     * @param recipientAppendDto 流程抄送人追加DTO
     * @return 是否成功
     */
    @ApiOperationSupport(order = 16)
    @ApiOperation("添加审批流程抄送人")
    @Log(title = "流程管理 - 流程操作- 添加审批流程抄送人", businessType = BusinessType.UPDATE)
    @PostMapping("/appendFlowRecipients")
    public R<Boolean> appendFlowRecipients(@Validated @RequestBody FlowRecipientAppendDTO recipientAppendDto) {
        this.flowInstanceService.appendFlowRecipients(recipientAppendDto, SecurityUtils.getUserId());
        return R.success();
    }

    /**
     * 撤回审批
     * @param cancelDto 撤回审批DTO
     * @return 是否成功
     */
    @ApiOperationSupport(order = 17)
    @ApiOperation("流程撤回")
    @Log(title = "流程管理 - 流程操作- 流程撤回", businessType = BusinessType.UPDATE)
    @PostMapping("/cancelApproval")
    public R<Boolean> cancelApproval(@Validated @RequestBody FlowApprovalCancelDTO cancelDto) {
        this.flowInstanceService.cancelApproval(cancelDto, SecurityUtils.getUserId());
        return R.success();
    }

    /**
     * 作废审批
     * @param invalidatedDto 作废审批DTO
     * @return 是否成功
     */
    @ApiOperationSupport(order = 18)
    @ApiOperation("作废审批")
    @Log(title = "流程管理 - 流程操作- 作废审批", businessType = BusinessType.UPDATE)
    @PostMapping("/invalidatedApproval")
    public R<Boolean> invalidatedApproval(@RequestBody FlowApprovalInvalidatedDTO invalidatedDto) {
        this.flowInstanceService.invalidatedApproval(invalidatedDto, SecurityUtils.getUserId());
        return R.success();
    }

    /**
     * 流程转交
     * @param forwardDto 流程转交DTO
     * @return 是否成功
     */
    @ApiOperationSupport(order = 19)
    @ApiOperation("流程转交")
    @Log(title = "流程管理 - 流程操作- 流程转交", businessType = BusinessType.UPDATE)
    @PostMapping("/forwardApproval")
    public R<Boolean> forwardApproval(@RequestBody FlowApprovalForwardDTO forwardDto) {
        this.flowInstanceService.forwardApproval(forwardDto, SecurityUtils.getUserId());
        return R.success();
    }

    /**
     * 获取可退回的流程节点列表
     * @param query 查询参数
     * @return 可退回的流程节点列表
     */
    @ApiOperationSupport(order = 20)
    @ApiOperation("获取可退回的流程节点列表")
    @GetMapping("/returnableNode")
    public R<List<FlowReturnableNodeVO>> getFlowReturnableNode(@Validated ReturnableNodeQuery query) {
        return R.success(this.flowInstanceService.getFlowReturnableNode(query));
    }

    /**
     * 流程退回
     * @param returnDto 流程退回DTO
     * @return 是否成功
     */
    @ApiOperationSupport(order = 20)
    @ApiOperation("流程退回")
    @Log(title = "流程管理 - 流程操作- 流程退回", businessType = BusinessType.UPDATE)
    @PostMapping("/returnApproval")
    public R<Boolean> returnApproval(@Validated @RequestBody FlowApprovalReturnDTO returnDto) {
        this.flowInstanceService.returnApproval(returnDto, SecurityUtils.getUserId());
        return R.success();
    }

    /**
     * 流程加签
     * @param counterSignDto 加签DTO
     * @return 是否成功
     */
    @ApiOperationSupport(order = 21)
    @ApiOperation("流程加签")
    @Log(title = "流程管理 - 流程操作- 流程加签", businessType = BusinessType.UPDATE)
    @PostMapping("/counterSignApproval")
    public R<Boolean> counterSignApproval(@Validated @RequestBody FlowApprovalCounterSignDTO counterSignDto) {
        this.flowInstanceService.counterSignApproval(counterSignDto, SecurityUtils.getUserId());
        return R.success();
    }

    /**
     * 获取流程版本流程设计图片
     * @param flowManageVersionId 流程版本id
     */
    @ApiOperation("获取流程版本流程设计图片")
    @GetMapping("/getFlowVersionFlowImage")
    public void getFlowVersionFlowImage(@RequestParam("flowManageVersionId") Long flowManageVersionId,
                                        HttpServletResponse response) {
        OutputStream os = null;
        BufferedImage image;
        try {
            image = ImageIO.read(flowDefinitionService.getFlowVersionFlowImage(flowManageVersionId));
            response.setContentType("image/png");
            os = response.getOutputStream();
            if (image != null) {
                ImageIO.write(image, "png", os);
            }
        } catch (Exception e) {
            log.error("getFlowVersionFlowDesign:", e);
        } finally {
            try {
                if (os != null) {
                    os.flush();
                    os.close();
                }
            } catch (IOException e) {
                log.error("getFlowVersionFlowDesign:", e);
            }
        }
    }

    /**
     * 模拟审批流程状态变更事件
     * @param event 审批流程状态变更事件
     * @return 是否成功
     */
    @ApiOperation("模拟审批流程状态变更事件")
    @PostMapping("/mockFlowStatusChangeEvent")
    public R<Boolean> mockFlowStatusChangeEvent(@RequestBody FlowApprovalStatusChangeEvent event) {
        approvalEventManager.publishEvent(event);
        return R.success();
    }

    @ApiOperation("作废删除")
    @PostMapping ("/deleteFlow")
    public void deleteFlow(int day) {
        //30天  作废的数据
        List<FlowInstance> list = this.flowInstanceService.selectDeleteFlow(day);
        if (ListUtil.isNotEmpty(list)){
            this.flowInstanceService.deleteFlowData(list);
        }
    }

    @ApiOperation("流程批量打印")
    @PostMapping ("/batchPrintFlowApproval")
    @Log(title = "流程管理 - 批量打印", businessType = BusinessType.EXPORT)
    public R<String> batchPrintFlowApproval(@RequestBody BatchPrintFlowApprovalQuery query) {
        String fileName = "流程指打印-" + DateUtils.format(LocalDateTime.now(), "yyyyMMddHHmmss") + ".zip";
        query.setFileName(fileName);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("流程批量打印", TaskType.Export,query, FlowInstancePrintServiceImpl.class);
        return R.success("提交成功");
    }

}
