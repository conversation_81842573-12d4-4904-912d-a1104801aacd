package com.py.framework.config.jackson.serializer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.py.common.utils.MathUtils;
import com.py.framework.config.jackson.JacksonConfig;
import lombok.Data;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.math.BigDecimal;

/**
 * BigDecimal反序列化器测试类
 * <AUTHOR>
 */
public class BigDecimalJsonDeserializerTest {

    /** 对象映射器 */
    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        JacksonConfig jacksonConfig = new JacksonConfig();
        Jackson2ObjectMapperBuilder builder = new Jackson2ObjectMapperBuilder();
        jacksonConfig.jackson2ObjectMapperBuilderCustomizer().customize(builder);
        this.objectMapper = builder.build();
    }

    @Test
    public void deserializer() throws JsonProcessingException {
        String json = "{\"value\":\"123,456,789.13\"}";
        TestData testData = objectMapper.readValue(json, TestData.class);

        Assert.assertTrue(MathUtils.equal(new BigDecimal("123456789.13"), testData.getValue()));
    }

    @Data
    public static class TestData{

        BigDecimal value;
    }
}
