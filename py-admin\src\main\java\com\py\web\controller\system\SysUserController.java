package com.py.web.controller.system;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.core.domain.entity.SysUser;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.exception.ServiceException;
import com.py.common.file.FileInfoVO;
import com.py.common.oss.IOssService;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.NullMergeUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.common.utils.tree.TreeUtil;
import com.py.framework.web.service.UserInfoCacheHelper;
import com.py.system.dept.domain.vo.DeptVO;
import com.py.system.user.domain.dto.UserDTO;
import com.py.system.user.domain.dto.UserImportModel;
import com.py.system.user.domain.dto.UserResetPwdEmail;
import com.py.system.user.domain.query.UserInfoListQuery;
import com.py.system.user.domain.query.UserListQuery;
import com.py.system.user.domain.query.UserOptionQuery;
import com.py.system.user.domain.vo.*;
import com.py.system.user.service.ISysUserService;
import com.py.system.user.service.impl.SysUserServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 用户管理
 * <AUTHOR>
 */
@RestController
@Api(tags = "组织权限管理 - 人员管理")
@RequestMapping("/system/user")
public class SysUserController extends BaseController {

    /** 用户服务 */
    @Resource
    private ISysUserService userService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 对象存储服务 */
    @Resource
    private IOssService ossService;

    /** 用户信息缓存帮助类 */
    @Resource
    private UserInfoCacheHelper userInfoCacheHelper;

    /**
     * 获取用户列表
     * @param query 查询条件
     * @return 用户列表
     */
    @ApiOperation("获取用户列表")
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public R<PageInfo<UserListVO>> list(UserListQuery query) {
        PageInfo<UserListVO> pageVo = this.userService.selectPageUserList(query);
        return R.success(pageVo);
    }

    /**
     * 获取用户下拉列表
     * @param query 查询条件
     * @return 用户下拉列表
     */
    @ApiOperation("用户下拉列表")
    @GetMapping("/selectOption")
    public R<List<UserOptionVO>> selectOption(UserOptionQuery query) {
        return R.success(userService.selectOption(query));
    }

    /**
     * 获取用户组织下拉树
     * @param deptId 部门ID
     * @return 用户组织下拉树
     */
    @ApiOperation("获取用户组织下拉树")
    @GetMapping("/getOrgTreeOption")
    public R<List<OrgTreeOptionVO>> selectOrgTreeOption(Long deptId) {
        // 不传部门ID时, 默认查询根节点
        deptId = NullMergeUtils.nullMerge(deptId, TreeUtil.TREE_ROOT);
        return R.success(userService.selectOrgTreeOption(deptId));
    }

    /**
     * 获取用户组织下拉树 - 根据名字模糊查询
     * @param userName 用户名
     * @param showTerminated 是否展示已离职数据,true:展示,false:不展示,默认不展示
     * @return 用户组织下拉树
     */
    @ApiOperation("获取用户组织下拉树 - 根据名字模糊查询")
    @GetMapping("/getOrgTreeOptionByName")
    public R<List<OrgTreeOptionVO>> selectOrgTreeOptionByName(String userName,Boolean showTerminated) {
        return R.success(userService.selectOrgTreeOptionByName(userName,showTerminated));
    }

    /**
     * 获取项目管理的初始化成员树
     * @param projectInitiatorId 立项人ID
     * @param showTerminated 是否展示已离职数据,true:展示,false:不展示,默认不展示
     * @return 项目管理的初始化成员树
     */
    @ApiOperation("获取项目管理的初始化成员树")
    @GetMapping("/getProjectOrgTree")
    public R<ProjectMemberInitVO> selectProjectOrgTree(Long projectInitiatorId,Boolean showTerminated) {
        return R.success(userService.selectProjectOrgTree(projectInitiatorId,showTerminated));
    }

    /**
     * 获取人脉人员组织树
     * @param enteredUserId 录入人
     * @param deptId 部门ID
     * @return 人脉人员组织树
     */
    @ApiOperation("获取人脉人员组织树")
    @GetMapping("/getOrgTreeOptionByDeptId")
    public R<List<OrgTreeOptionVO>> selectConnectionOrgTree(Long enteredUserId ,Long deptId){
        Assert.notNull(enteredUserId, "录入人ID不能为空");
        // 不传部门ID时, 默认查询根节点
        deptId = NullMergeUtils.nullMerge(deptId, TreeUtil.TREE_ROOT);
        return R.success(userService.selectConnectionOrgTree(enteredUserId, deptId));
    }

    /**
     * 获取用户详情
     * @param userId 用户ID
     * @return 用户信息
     */
    @ApiOperation("获取用户详情")
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/{userId}")
    public R<UserVO> getInfo(@PathVariable("userId") Long userId) {
        UserVO user = this.userService.getInfo(userId);
        return R.success(user);
    }

    /**
     * 获取用户详情列表
     * @param query 查询条件
     * @return 用户详情列表
     */
    @ApiOperation("获取用户详情列表")
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/getInfoList")
    public R<List<SysUser>> getInfoList(@Validated UserInfoListQuery query){
        return R.success(this.userService.selectUserInfo(query.getUserIdList()));
    }


    /**
     * 新增用户
     * @param dto 用户信息
     * @return 是否成功
     */
    @ApiOperation("新增用户")
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "新增用户", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody UserDTO dto) {
        String user = this.userService.insertUser(dto);
        if(StringUtils.isBlank(user)){
            return R.success();
        }
        return R.failed(8004,user,null);
    }

    /**
     * 修改用户
     * @param dto 用户信息
     * @return 是否成功
     */
    @ApiOperation("修改用户")
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "修改用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody UserDTO dto) {

        String update = this.userService.updateUser(dto);
        if(StringUtils.isBlank(update)){
            this.userInfoCacheHelper.refreshLoginUserCache(user -> user.getUserId().equals(dto.getUserId()));
            return R.success();
        }
        return R.failed(8004,update,null);
    }

    /**
     * 重置密码
     * @param userId 用户ID
     * @return 是否成功
     */
    @ApiOperation("重置密码")
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public R<Boolean> resetPwd(@RequestBody Long userId) {
        this.userService.resetPwd(userId);
        return R.success();
    }

    /**
     * 删除用户
     * @param userIds 用户ID集合
     * @return 是否成功
     */
    @ApiOperation("删除用户")
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public R<Boolean> remove(@PathVariable List<Long> userIds) {
        if(userIds.contains(getUserId())) {
            return R.failed("当前用户不能删除");
        }
        this.userService.deleteUserByIds(userIds);
        this.userInfoCacheHelper.removeUserCache(user -> userIds.contains(user.getUserId()));
        return R.success();
    }

    /**
     * 批量新增用户
     * @param file 文件
     * @return 是否成功
     */
    @ApiOperation("批量新增用户")
    @PreAuthorize("@ss.hasPermi('system:user:importUser')")
    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importUser")
    public R<Boolean> importUser(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xls"))) {
            throw new ServiceException("请上传正确的文件格式");
        }
        String originalFilename = file.getOriginalFilename();
        ExcelUtil<UserImportModel> util = new ExcelUtil<>(UserImportModel.class);
        List<UserImportModel> userImportModelList = util.importExcel(file.getInputStream(),0);
        if(ListUtil.isEmpty(userImportModelList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }

        OssUploadResult ossUploadResult = ossService.upload(file.getInputStream(), originalFilename, false);
        UserListQuery query = new UserListQuery();
        query.setFileKey(ossUploadResult.getOssKey());
        query.setFileName(originalFilename);
        query.setTaskType(TaskType.Import);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("组织权限管理-人员管理", TaskType.Import,query, SysUserServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 批量新增用户模板
     * @return 文件url
     */
    @ApiOperation("批量新增用户模板")
    @PreAuthorize("@ss.hasPermi('system:user:importTemplate')")
    @PostMapping("/importTemplate")
    public R<FileInfoVO> importTemplate() {
        FileInfoVO fileInfoVO = userService.importTemplate();
        return R.success(fileInfoVO);
    }

    /**
     * 修改密码
     * @param resetPwdEmail 重置密码参数
     * @return 是否成功
     */
    @ApiOperation("修改密码")
    @PostMapping("/resetPwdByEmail")
    public R<Boolean> resetPwdByEmail(@RequestBody UserResetPwdEmail resetPwdEmail){
       return R.success(this.userService.resetPwdByEmail(resetPwdEmail));
    }

    /**
     * 修改密码
     * @param newPassword 新密码
     * @return 是否成功
     */
    @ApiOperation("修改初始密码")
    @GetMapping("/resetDefaultPwd")
    @Log(title = "修改初始密码", businessType = BusinessType.UPDATE)
    public R<Boolean> resetDefaultPwd(@RequestParam String newPassword){
        return R.success(this.userService.resetDefaultPwd(newPassword));
    }

    /**
     * 初始化用户历史记录
     * @return 结果
     */
    @ApiOperation("初始化用户历史记录")
    @GetMapping("/initializeUserHistory")
    @Log(title = "初始化用户历史记录", businessType = BusinessType.UPDATE)
    public R<Boolean> initializeUserHistory(){
        return R.success(this.userService.initializeUserHistory());
    }

    /**
     * 查询列表上的更新部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询列表上的更新部门下拉" )
    @GetMapping("/listUserDept" )
    public R<PageInfo<DeptVO>> listUserDept(UserListQuery query){
        return R.success(userService.listUserDept(query));
    }


    /**
     * 用户离职
     * @param userId 用户id
     * @return 是否成功
     */
    @ApiOperation("用户离职")
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户离职", businessType = BusinessType.UPDATE)
    @PostMapping("/leave")
    public R<Boolean> leave(@RequestParam Long userId) {
        String update = this.userService.leave(userId);
        if(StringUtils.isBlank(update)){
            this.userInfoCacheHelper.refreshLoginUserCache(user -> user.getUserId().equals(userId));
            return R.success();
        }
        return R.failed(8004,update,null);
    }
}
