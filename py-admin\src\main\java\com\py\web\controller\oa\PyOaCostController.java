package com.py.web.controller.oa;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.oa.pyoacost.domain.dto.PyOaCostDTO;
import com.py.oa.pyoacost.domain.query.*;
import com.py.oa.pyoacost.domain.vo.*;
import com.py.oa.pyoacost.service.IPyOaCostService;
import com.py.oa.pyoacost.service.impl.CostApprovalExport;
import com.py.oa.pyoacost.service.impl.PyOaCostServiceImpl;
import com.py.oa.pyoadailycontracts.domain.vo.PyOaLabelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * OA费用管理Controller
 *
 * <AUTHOR>
 * @date 2023-07-21
 */
@Api(tags = "OA费用管理")
@RestController
@Validated
@RequestMapping("/oa/pyOaCost")
public class PyOaCostController extends BaseController {

    /** OA费用管理服务 */
    @Resource
    private IPyOaCostService pyOaCostService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;


    /**
     * 分页查询OA费用管理列表
     *
     * @param query OA费用管理查询参数
     * @return OA费用管理分页
     */
    @ApiOperation("分页查询询OA费用管理列表")
    @PreAuthorize("@ss.hasPermi('oa:pyOaCost:list')")
    @PostMapping("/pagePyOaCost")
    public R<PageInfo<PyOaCostPageVO>> pagePyOaCost(@Validated @RequestBody PyOaCostQuery query) {
        PageInfo<PyOaCostPageVO> voList = this.pyOaCostService.pagePyOaCostList(query);
        return R.success(voList);
    }

    /**
     * 查询财务报表-费用明细列表
     * @param query 费用明细查询条件
     * @return 费用明细列表
     */
    @ApiOperation("查询财务报表-费用明细列表")
    @PreAuthorize("@ss.hasPermi('oa:pyOaCost:list')")
    @GetMapping("/pageFinancialStatementCostDetailList")
    public R<PageInfo<FinancialStatementCostDetailsVO>> pageFinancialStatementCostDetailList(@Validated FinancialStatementCostDetailsQuery query) {
        PageInfo<FinancialStatementCostDetailsVO> voList = this.pyOaCostService.pageFinancialStatementCostDetailList(query);
        return R.success(voList);
    }

    /**
     * 获取OA费用管理详细信息
     * @param id OA费用管理主键
     * @return OA费用管理视图模型
     */
    @ApiOperation("获取OA费用管理详细信息")
    @PreAuthorize("@ss.hasPermi('oa:pyOaCost:query')")
    @GetMapping(value = "getInfo/{id}")
    public R<PyOaCostVO> getInfo(@PathVariable("id") Long id) {
        return R.success(pyOaCostService.selectPyOaCostById(id));
    }

    /**
     * 修改OA费用管理
     *
     * @param dto 新增OA费用管理
     * @return 是否成功
     */
    @ApiOperation("修改OA费用管理")
    @PreAuthorize("@ss.hasPermi('oa:pyOaCost:add')")
    @Log(title = "OA费用管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R<Long> edit(@Valid @RequestBody  PyOaCostDTO dto) {
        return R.success(pyOaCostService.updatePyOaCost(dto));
    }

    /**
     * 新增OA费用管理
     *
     * @param dto 新增OA费用管理
     * @return 是否成功
     */
    @ApiOperation("新增OA费用管理")
    @PreAuthorize("@ss.hasPermi('oa:pyOaCost:add')")
    @Log(title = "OA费用管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Long> add(@Valid @RequestBody  PyOaCostDTO dto) {
        return R.success(pyOaCostService.insertPyOaCost(dto));
    }

    /**
     * 多选框查询
     *
     * @param checkBoxQuery 多选框查询
     * @return PyOaCostCheckBoxVO
     */
    @ApiOperation("多选框多选")
    @PreAuthorize("@ss.hasPermi('oa:pyOaCost:query')")
    @PostMapping("/checkbox")
    public R<PyOaCostCheckBoxVO> checkBox(@RequestBody PyOaCheckBoxQuery checkBoxQuery) {
        return R.success(pyOaCostService.checkBoxQuery(checkBoxQuery));
    }

    /**
     * OA二级标签栏
     * @param pyMainstayId 主体id
     * @return pyOaCountVO
     */
    @ApiOperation("OA二级标签栏")
    @PreAuthorize("@ss.hasPermi('oa:pyOaCost:query')")
    @PostMapping("/secondLabel")
    public R<PyOaCountVO> oaCount (@RequestParam(value = "pyMainstayId",required = false) Long pyMainstayId) {
        return R.success(pyOaCostService.oaCount(pyMainstayId));
    }


    /**
     * OA一级标签页
     * @param pyMainstayIds 主体id
     * @return pyOaLabelVOList
     */
    @ApiOperation("OA一级标签栏")
    @PreAuthorize("@ss.hasPermi('oa:pyOaCost:query')")
    @PostMapping("firstLabel")
    public R<List<PyOaLabelVO>> getLabel (@RequestBody List<Long> pyMainstayIds) {
        return R.success(pyOaCostService.getOaLabel(pyMainstayIds));
    }

    /**
     * 导出OA费用管理
     * @param query 导出查询参数
     */
    @ApiOperation("导出OA费用管理")
    @PreAuthorize("@ss.hasPermi('oa:pyOaCost:export')")
    @Log(title = "OA费用管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@Validated @RequestBody PyOaCostQuery query) {
        query.setIsFinance(false);
        reusableAsyncTaskService.addTask("OA费用管理信息数据", TaskType.Export, query, PyOaCostServiceImpl.class);
        return R.success("导出成功");
    }

    /**
     * 企业财报-导出OA费用管理
     * @param query 导出查询参数
     */
    @ApiOperation("企业财报-导出OA费用管理")
    @PreAuthorize("@ss.hasPermi('oa:pyOaCost:export')")
    @Log(title = "OA费用管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportFinance")
    public R<String> exportFinance(@Validated @RequestBody PyOaCostQuery query) {
        query.setIsFinance(true);
        reusableAsyncTaskService.addTask("OA费用管理信息数据", TaskType.Export, query, PyOaCostServiceImpl.class);
        return R.success("导出成功");
    }


    /**
     * 查询费用列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询费用列表上的创建部门下拉" )
    @GetMapping("/listOaCostDept" )
    public R<List<String>> listOaCostDept(PyOaCostQuery query){
        return R.success(pyOaCostService.listOaCostDept(query));
    }

    /**
     * 导出费用审批
     * @param query 导出查询参数
     */
    @ApiOperation("导出费用审批")
    @PreAuthorize("@ss.hasPermi('oa:pyOaCost:export')")
    @Log(title = "费用审批", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCostApproval")
    public R<String> exportCostApproval(@Validated @RequestBody PyCostApprovalQuery query) {
        reusableAsyncTaskService.addTask("费用审批信息数据", TaskType.Export,query, CostApprovalExport.class);
        return R.success("导出成功");
    }

    /**
     * 审批查询-分页查询费用审批
     * @param query 查询条件
     * @return pyCostApprovalList
     */
    @ApiOperation("分页查询费用审批")
    @PostMapping("/PageCostApproval")
    public R<PageInfo<PyCostApprovalListVO>>pagePyCostApproval(@Validated @RequestBody PyCostApprovalQuery query){
        PageInfo<PyCostApprovalListVO> voList = this.pyOaCostService.listPyCostApproval(query);
        return R.success(voList);
    }

    /**
     * 审批查询-标签费用审批数量
     * @param pyMainstayIds 主体id
     * @return pyCostLabelVOList
     */
    @ApiOperation("审批查询-标签费用审批数量")
    @PostMapping("/CostApprovalLabel")
    public R<List<PyCostApprovalCountVO>> getCostApprovalLabel(@RequestBody List<Long> pyMainstayIds){
        return R.success(pyOaCostService.getCostLabel(pyMainstayIds));
    }

    /**
     * 审批查询-费用审批多选框多选----
     * @param checkBoxQuery 是否全选 id 请传费用详情-id
     * @return pyCostApprovalCheckBoxVO
     */
    @ApiOperation("费用审批多选")
    @PostMapping("/CostApprovalCheckBox")
    public R<PyCostApprovalCheckBoxVO> costApprovalCheckBox(@RequestBody PyCheckBoxQuery checkBoxQuery){
        return R.success(pyOaCostService.checkCostBoxAudit(checkBoxQuery));
    }

    /**
     * 获取费用事项
     * @return PyCostMatterVO
     */
    @ApiOperation("费用事项查询")
    @PostMapping("/CostMatterType")
    public R<PyCostMatterVO> getCostMatter(@RequestBody List<Long> pyMainstayIds){
        return R.success(pyOaCostService.getMatter(pyMainstayIds));
    }

    /**
     * 查询费用审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询费用审批列表上的创建部门下拉" )
    @GetMapping("/listOaCostApproveDept" )
    public R<List<String>> listOaCostApproveDept(PyCostApprovalQuery query){
        return R.success(pyOaCostService.listOaCostApproveDept(query));
    }

    @ApiOperation("非业务审批老数据处理")
    @PostMapping ("/oldDispose")
    public  R<Boolean> oldflowInstancesDispose() {
        Boolean success = this.pyOaCostService.oldDispose();
        return R.success(success);
    }
}
