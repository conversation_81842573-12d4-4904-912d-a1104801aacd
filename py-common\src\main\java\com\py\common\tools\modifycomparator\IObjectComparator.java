package com.py.common.tools.modifycomparator;

import com.py.common.tools.modifycomparator.model.BaseModifyDiff;
import lombok.NonNull;

import java.util.List;

/**
 * 对象比较器 - 接口
 * <AUTHOR>
 */
public interface IObjectComparator {

    /**
     * 比较对象
     * @param beforeObject 修改前的对象
     * @param afterObject 修改后的对象
     * @param clazz 需比较的类实例
     * @return 比较结果
     */
    List<BaseModifyDiff<?>> compareObject(
             Object beforeObject,
             Object afterObject,
            @NonNull Class<?> clazz);
}
