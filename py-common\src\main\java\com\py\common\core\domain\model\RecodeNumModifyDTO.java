package com.py.common.core.domain.model;

import com.py.common.utils.collection.ListUtil;
import lombok.Data;

import java.util.List;
import java.util.function.Function;

/**
 * 记录数量修改DTO
 * <AUTHOR>
 */
@Data
public class RecodeNumModifyDTO {

    /** 修改目标ID */
    private Long targetId;

    /** 修改数量 */
    private Integer modifyNum;

    /**
     * 创建添加记录数量修改DTO
     * @param list 集合
     * @param idSelector id选择器
     * @param <T> 集合元素类型
     * @return 记录数量修改DTO列表
     */
    public static  <T> List<RecodeNumModifyDTO> createPlusModifyDto(List<T> list, Function<T, Long> idSelector) {
        return ListUtil.map(list, item -> valueOf(idSelector.apply(item), 1));
    }

    /**
     * 创建减少记录数量修改DTO
     * @param list 集合
     * @param idSelector id选择器
     * @param <T> 集合元素类型
     * @return 记录数量修改DTO列表
     */
    public static  <T> List<RecodeNumModifyDTO> createMinusModifyDto(List<T> list, Function<T, Long> idSelector) {
        return ListUtil.map(list, item -> valueOf(idSelector.apply(item), -1));
    }

    /**
     * 构造记录数量修改DTO
     * @param targetId 修改目标ID
     * @param modifyValue 修改数量
     * @return 记录数量修改DTO
     */
    public static RecodeNumModifyDTO valueOf(Long targetId, int modifyValue) {
        RecodeNumModifyDTO dto = new RecodeNumModifyDTO();
        dto.setTargetId(targetId);
        dto.setModifyNum(modifyValue);
        return dto;
    }
}
