package com.py.web.controller.resources;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.exception.ServiceException;
import com.py.common.oss.IOssService;
import com.py.common.oss.model.OssUploadResult;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.EnumUtils;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.resources.downloadcontrol.service.IDownloadControlService;
import com.py.resources.enums.LaundryListType;
import com.py.resources.enums.ResourceSensationType;
import com.py.resources.mediumlaundrylist.domain.dto.LaundryListRemoveDTO;
import com.py.resources.mediumlaundrylist.service.IMediumLaundryListService;
import com.py.resources.mediumresourcesensation.converter.MediumResourceSensationConverter;
import com.py.resources.mediumresourcesensation.domain.dto.*;
import com.py.resources.mediumresourcesensation.domain.eunm.ResourceImportType;
import com.py.resources.mediumresourcesensation.domain.query.MediumResourceSensationQuery;
import com.py.resources.mediumresourcesensation.domain.query.SensationTypeQuery;
import com.py.resources.mediumresourcesensation.domain.vo.*;
import com.py.resources.mediumresourcesensation.excel.search.MediumResourceSensationSearchImporter;
import com.py.resources.mediumresourcesensation.excel.search.model.ImportSelectModel;
import com.py.resources.mediumresourcesensation.excel.search.model.ImportSelectQuery;
import com.py.resources.mediumresourcesensation.excel.search.model.ImportSelectResultVO;
import com.py.resources.mediumresourcesensation.service.IMediumResourceSensationHandleService;
import com.py.resources.mediumresourcesensation.service.IMediumResourceSensationService;
import com.py.resources.mediumresourcesensation.service.impl.MediumResourceSensationServiceImpl;
import com.py.resources.mediumresourcesensation.sync.CityModel;
import com.py.resources.mediumresourceservice.domain.dto.ResourceUpdateDTO;
import com.py.resources.mediumresourceservice.domain.query.LaundryListServiceQuery;
import com.py.resources.mediumupdaterecord.domain.query.PyMediumUpdateRecordQuery;
import com.py.resources.mediumupdaterecord.domain.vo.PyMediumUpdateRecordVO;
import com.py.resources.mediumupdaterecord.service.IPyMediumUpdateRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 媒介资源管理-媒介资源红人Controller
 *
 * <AUTHOR>
 * @date 2023-07-18
 */
@Api(tags = "媒介资源管理-媒介资源红人")
@RestController
@RequestMapping("/resources/mediumResourceSensation")
public class MediumResourceSensationController extends BaseController {

    /** 媒介资源管理-媒介资源红人服务 */
    @Resource
    private IMediumResourceSensationService sensationService;

    @Resource
    private IOssService ossService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    @Resource
    private IMediumLaundryListService laundryListService;

    @Resource
    private MediumResourceSensationConverter sensationConverter;

    @Resource
    private IDownloadControlService downloadControlService;

    /** 媒体资源红人导入查询 */
    @Resource
    private MediumResourceSensationSearchImporter resourceSensationSearchImporter;

    /** 资源更新记录服务层 */
    @Resource
    private IPyMediumUpdateRecordService pyMediumUpdateRecordService;

    @Resource
    private IMediumResourceSensationHandleService mediumResourceSensationHandleService;

    /**
     * 媒介资源(红人)-查看清单
     *
     * @param query 资源清单查询参数
     * @return 媒介资源管理-媒介资源红人清单列表
     */
    @ApiOperation("媒介资源(红人)-查看清单")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:list')")
    @GetMapping("/pageServiceLaundryList")
    public R<PageInfo<MediumResourceSensationListVO>> pageSensationLaundryList(LaundryListServiceQuery query) {
        PageInfo<MediumResourceSensationListVO> voList = this.sensationService.pageSensationLaundryList(query);
        return R.success(voList);
    }

    /**
     * 媒介资源(红人)-选资源合计
     * @param query 菜单类型
     * @return 红人合计
     */
    @ApiOperation("媒介资源(红人)-选资源合计")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:list')")
    @GetMapping("/querySelectResourceCount")
    public R<SensationCountVO> querySelectResourceCount(LaundryListServiceQuery query) {
        return R.success(this.sensationService.querySelectResourceCount(query));
    }

    /**
     * 媒介资源(红人)-查看清单合计
     *
     * @return 查看清单合计
     */
    @ApiOperation("媒介资源(红人)-查看清单合计")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:list')")
    @GetMapping("/queryLaundryListCount")
    public R<Long> queryLaundryListCount(LaundryListServiceQuery query) {
        return R.success(this.sensationService.queryLaundryListCount(query));
    }


    /**
     * 分页查询媒介资源管理-媒介资源红人列表
     *
     * @param query 媒介资源管理-媒介资源红人查询参数
     * @return 媒介资源管理-媒介资源红人分页
     */
    @ApiOperation("分页查询询媒介资源管理-媒介资源红人列表")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:list')")
    @PostMapping("/pageMediumResourceSensation")
    public R<MediumDisableInfoVO<PageInfo<MediumResourceSensationListVO>>> pageMediumResourceSensation(@RequestBody MediumResourceSensationQuery query) {
        MediumDisableInfoVO<PageInfo<MediumResourceSensationListVO>> voList = this.sensationService.pageMediumResourceSensationList(query);
        return R.success(voList);
    }


    /**
     * 查询媒介资源管理-媒介资源红人列表,更新人部门
     *
     * @param query 媒介资源管理-媒介资源红人查询参数
     * @return 媒介资源管理-媒介资源红人,更新人部门
     */
    @ApiOperation("分页查询询媒介资源管理-媒介资源红人列表")
    @PostMapping("/findMediumResourceSensationUpdateDept")
    public R<List<MediumResourceSensationDeptVO>> findMediumResourceSensationUpdateDept(@RequestBody MediumResourceSensationQuery query) {
        List<MediumResourceSensationDeptVO> voList = this.sensationService.findMediumResourceSensationUpdateDept(query);
        return R.success(voList);
    }

    /**
     * 获取媒介资源管理-媒介资源红人详细信息
     * @param id 媒介资源管理-媒介资源红人主键
     * @return 媒介资源管理-媒介资源红人视图模型
     */
    @ApiOperation("获取媒介资源管理-媒介资源红人详细信息")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:query')")
    @GetMapping(value = "/{id}")
    public R<MediumResourceSensationVO> getInfo(@PathVariable("id") Long id) {
        return R.success(sensationService.selectMediumResourceSensationById(id));
    }

    /**
     * 新增媒介资源管理-媒介资源红人
     *
     * @param dto 媒介资源管理-媒介资源红人修改参数
     * @return 是否成功
     */
    @ApiOperation("新增媒介资源管理-媒介资源红人")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:add')")
    @Log(title = "媒介资源管理-媒介资源红人", businessType = BusinessType.INSERT)
    @PostMapping
    public R<String> add(@RequestBody MediumResourceSensationDTO dto) {
        String isSuccess = sensationService.insertMediumResourceSensation(dto);
        if(StringUtils.isBlank(isSuccess)){
            return R.success();
        }
        return R.failed(8004,isSuccess,null);
    }

    /**
     * 修改媒介资源管理-媒介资源红人
     *
     * @param dto 媒介资源管理-媒介资源红人修改参数
     * @return 是否成功
     */
    @ApiOperation("修改媒介资源管理-媒介资源红人")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:edit')")
    @Log(title = "媒介资源管理-媒介资源红人", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<ErrorInfoVO> edit(@RequestBody MediumResourceSensationDTO dto) {
        ErrorInfoVO errorInfoVO = sensationService.updateMediumResourceSensation(dto);
        if(Objects.isNull(errorInfoVO)){
            return R.success();
        }
        if (StringUtils.isNotBlank(errorInfoVO.getErrorInfo())){
            return R.success(errorInfoVO);
        }
        return R.failed(8004,errorInfoVO.getErrorResourceName(),null);
    }

    /**
     * 删除列表
     * @param query 查询列表实体
     * @return 列表
     */
    @ApiOperation("删除列表" )
    @PostMapping("/queryRemoveList" )
    public R<PageInfo<MediumResourceSensationListVO>> queryRemoveList(@RequestBody MediumResourceSensationQuery query) {
        return R.success(sensationService.queryRemoveList(query));
    }

    /**
     * 机构列表
     * @return 列表
     */
    @ApiOperation("机构列表" )
    @GetMapping("/queryInstitutionList" )
    public R<List<String>> queryInstitutionList(String name,Integer type) {
        return R.success(sensationService.queryInstitutionList(name,type));
    }

    /**
     * 删除媒介资源管理-媒介资源红人
     * @param query 需要删除的媒介资源管理-媒介资源红人主键集合
     * @return 是否成功
     */
    @ApiOperation("删除媒介资源管理-媒介资源红人" )
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:remove')")
    @Log(title = "媒介资源管理-媒介资源红人", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove" )
    public R<Boolean> remove(@RequestBody MediumResourceSensationQuery query) {
        return R.success(sensationService.deleteMediumResourceSensationByIds(query));
    }

    /**
     * 导出媒介资源管理-媒介资源红人
     * @param response 请求响应
     * @param query 导出查询参数
     */
    @ApiOperation("导出媒介资源管理-媒介资源红人")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:export')")
    @Log(title = "媒介资源管理-媒介资源红人", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MediumResourceSensationQuery query) {
        List<TaoBaoMediumResourceImportModel> exportList = this.sensationService.exportMediumResourceSensation(query);

        ExcelUtil<TaoBaoMediumResourceImportModel> util = new ExcelUtil<>(TaoBaoMediumResourceImportModel. class);
        util.exportExcel(response, exportList, "媒介资源管理-媒介资源红人数据" );
    }

    /**
     * 小红书新增导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:importRedBookData')" )
    @Log(title = "媒介资源管理-媒介资源红人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importRedBookData" )
    public R<ErrorMediumResourceModelVO> importRedBookData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }
        // 资源信息(小红书)
        ExcelUtil<RedBookMediumResourceImportModel> util = new ExcelUtil<>(RedBookMediumResourceImportModel.class);
        List<RedBookMediumResourceImportModel> mediumResourceSensationList = util.importExcel("资源信息(小红书)",file.getInputStream(),0);
        this.verifyAddTemplate(file);
        if(ListUtil.isEmpty(mediumResourceSensationList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        MediumResourceSensationQuery query = new MediumResourceSensationQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setResourceImportType(ResourceImportType.LITTLE_RED_BOOK_ADD);
        query.setResourceSensationType(ResourceSensationType.LITTLE_RED_BOOK);
        reusableAsyncTaskService.addTask("资源管理-小红书", TaskType.Import, query, MediumResourceSensationServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 抖音新增导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:importTrillData')" )
    @Log(title = "媒介资源管理-媒介资源红人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTrillData" )
    public R<String> importTrillData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }
        ExcelUtil<TrillMediumResourceImportModel> util = new ExcelUtil<>(TrillMediumResourceImportModel.class);
        List<TrillMediumResourceImportModel> mediumResourceSensationList = util.importExcel("资源信息(抖音)",file.getInputStream(),0);
        if(ListUtil.isEmpty(mediumResourceSensationList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        this.verifyAddTemplate(file);
        MediumResourceSensationQuery query = new MediumResourceSensationQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setResourceImportType(ResourceImportType.TIK_TOK_ADD);
        query.setResourceSensationType(ResourceSensationType.TIKTOK);
        reusableAsyncTaskService.addTask("资源管理-抖音", TaskType.Import,query, MediumResourceSensationServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 校验模板
     * @param file
     * @throws Exception
     */
    private void verifyAddTemplate (MultipartFile file) throws Exception {
        // 账户信息
        ExcelUtil<AccountResourceModel> accountExcelUtil = new ExcelUtil<>(AccountResourceModel.class);
        accountExcelUtil.importExcel("账户信息", file.getInputStream(),0);
        // 开票信息
        ExcelUtil<InvoicingResourceModel> invoicingUtil = new ExcelUtil<>(InvoicingResourceModel.class);
        invoicingUtil.importExcel("开票信息", file.getInputStream(),0);
    }

    /**
     * b站新增导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:importBiliBiliData')" )
    @Log(title = "媒介资源管理-媒介资源红人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importBiliBiliData" )
    public R<String> importBiliBiliData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xls"))) {
            throw new ServiceException("请上传正确的文件格式");
        }
        ExcelUtil<BiliBiliMediumResourceImportModel> util = new ExcelUtil<>(BiliBiliMediumResourceImportModel.class);
        List<BiliBiliMediumResourceImportModel> mediumResourceSensationList = util.importExcel("资源信息(B站)",file.getInputStream(),0);
        if(ListUtil.isEmpty(mediumResourceSensationList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        this.verifyAddTemplate(file);
        MediumResourceSensationQuery query = new MediumResourceSensationQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setResourceImportType(ResourceImportType.BILI_BILI_ADD);
        query.setResourceSensationType(ResourceSensationType.B_STATION);
        reusableAsyncTaskService.addTask("资源管理-B站", TaskType.Import,query, MediumResourceSensationServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 微博新增导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:importWeiboData')" )
    @Log(title = "媒介资源管理-媒介资源红人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importWeiboData" )
    public R<String> importWeiboData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xls"))) {
            throw new ServiceException("请上传正确的文件格式");
        }
        ExcelUtil<WeiBoMediumResourceImportModel> util = new ExcelUtil<>(WeiBoMediumResourceImportModel.class);
        List<WeiBoMediumResourceImportModel> mediumResourceSensationList = util.importExcel("资源信息(微博)",file.getInputStream(),0);
        if(ListUtil.isEmpty(mediumResourceSensationList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        this.verifyAddTemplate(file);
        MediumResourceSensationQuery query = new MediumResourceSensationQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setResourceImportType(ResourceImportType.WEIBO_ADD);
        query.setResourceSensationType(ResourceSensationType.MICRO_BLOG);
        reusableAsyncTaskService.addTask("资源管理-微博", TaskType.Import,query, MediumResourceSensationServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 淘宝新增导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:importTaoBaoData')" )
    @Log(title = "媒介资源管理-媒介资源红人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTaoBaoData" )
    public R<String> importTaoBaoData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xls"))) {
            throw new ServiceException("请上传正确的文件格式");
        }
        ExcelUtil<TaoBaoMediumResourceImportModel> util = new ExcelUtil<>(TaoBaoMediumResourceImportModel.class);
        List<TaoBaoMediumResourceImportModel> mediumResourceSensationList = util.importExcel("资源信息(淘宝)",file.getInputStream(),0);
        if(ListUtil.isEmpty(mediumResourceSensationList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        this.verifyAddTemplate(file);
        MediumResourceSensationQuery query = new MediumResourceSensationQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setResourceImportType(ResourceImportType.TAO_BAO_ADD);
        query.setResourceSensationType(ResourceSensationType.TAO_BAO);
        reusableAsyncTaskService.addTask("资源管理-淘宝", TaskType.Import,query, MediumResourceSensationServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 小红书修改导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:importRedBookUpdateData')" )
    @Log(title = "媒介资源管理-媒介资源红人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importRedBookUpdateData" )
    public R<String> importRedBookUpdateData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }
        ExcelUtil<RedBookMediumResourceImportUpdateModel> util = new ExcelUtil<>(RedBookMediumResourceImportUpdateModel.class);
        Boolean exist = util.isSheetName("资源信息(小红书)", file.getInputStream());
        List<RedBookMediumResourceImportUpdateModel> mediumResourceSensationList = new ArrayList<>();
        if(!exist){
            mediumResourceSensationList = util.importExcel("资源信息(小红书)",file.getInputStream(),0);
        }
        Boolean aBoolean = this.verifyEditTemplate(file);
        if(aBoolean && ListUtil.isEmpty(mediumResourceSensationList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        MediumResourceSensationQuery query = new MediumResourceSensationQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        // 去除行业类目必填校验
        query.setIsUpdate(true);
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setResourceImportType(ResourceImportType.LITTLE_RED_BOOK_EDIT);
        query.setResourceSensationType(ResourceSensationType.LITTLE_RED_BOOK);
        reusableAsyncTaskService.addTask("资源管理-小红书", TaskType.Import,query, MediumResourceSensationServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 校验模板
     * @param file
     * @throws Exception
     * return true 都为空，反之不为空
     */
    private Boolean verifyEditTemplate(MultipartFile file) throws Exception {
        // 账户信息
        ExcelUtil<AccountResourceModel> accountExcelUtil = new ExcelUtil<>(AccountResourceModel.class);
        Boolean account = accountExcelUtil.isSheetName("账户信息", file.getInputStream());
        List<AccountResourceModel> accountResourceModels = new ArrayList<>();
        if(!account){
            accountResourceModels = accountExcelUtil.importExcel("账户信息", file.getInputStream(), 0);
        }
        // 开票信息
        ExcelUtil<InvoicingResourceModel> invoicingUtil = new ExcelUtil<>(InvoicingResourceModel.class);
        Boolean invoicing = invoicingUtil.isSheetName("开票信息", file.getInputStream());
        List<InvoicingResourceModel> invoicingResourceModelList = new ArrayList<>();
        if(!invoicing){
            invoicingResourceModelList = invoicingUtil.importExcel("开票信息", file.getInputStream(), 0);
        }
        return ListUtil.isEmpty(accountResourceModels) && ListUtil.isEmpty(invoicingResourceModelList);
    }

    /**
     * 抖音修改导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:importTrillUpdateData')" )
    @Log(title = "媒介资源管理-媒介资源红人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTrillUpdateData" )
    public R<ErrorMediumResourceModelVO> importTrillUpdateData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        ExcelUtil<TrillMediumResourceImportUpdateModel> util = new ExcelUtil<>(TrillMediumResourceImportUpdateModel.class);
        Boolean exist = util.isSheetName("资源信息(抖音)", file.getInputStream());
        List<TrillMediumResourceImportUpdateModel> mediumResourceSensationList = new ArrayList<>();
        if(!exist){
            mediumResourceSensationList = util.importExcel("资源信息(抖音)",file.getInputStream(),0);
        }
        Boolean aBoolean = this.verifyEditTemplate(file);
        if(aBoolean && ListUtil.isEmpty(mediumResourceSensationList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        MediumResourceSensationQuery query = new MediumResourceSensationQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        // 去除行业类目必填校验
        query.setIsUpdate(true);
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setResourceImportType(ResourceImportType.TIK_TOK_EDIT);
        query.setResourceSensationType(ResourceSensationType.TIKTOK);
        reusableAsyncTaskService.addTask("资源管理-抖音", TaskType.Import,query, MediumResourceSensationServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * b站修改导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:importBiliBiliUpdateData')" )
    @Log(title = "媒介资源管理-媒介资源红人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importBiliBiliUpdateData" )
    public R<String> importBiliBiliUpdateData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xls"))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        ExcelUtil<BiliBiliMediumResourceImportUpdateModel> util = new ExcelUtil<>(BiliBiliMediumResourceImportUpdateModel.class);
        Boolean exist = util.isSheetName("资源信息(B站)", file.getInputStream());
        List<BiliBiliMediumResourceImportUpdateModel> mediumResourceSensationList = new ArrayList<>();
        if(!exist){
            mediumResourceSensationList = util.importExcel("资源信息(B站)",file.getInputStream(),0);
        }
        Boolean aBoolean = this.verifyEditTemplate(file);
        if(aBoolean && ListUtil.isEmpty(mediumResourceSensationList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        MediumResourceSensationQuery query = new MediumResourceSensationQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        // 去除行业类目必填校验
        query.setIsUpdate(true);
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setResourceImportType(ResourceImportType.BILI_BILI_EDIT);
        query.setResourceSensationType(ResourceSensationType.B_STATION);
        reusableAsyncTaskService.addTask("资源管理-B站", TaskType.Import,query, MediumResourceSensationServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 微博修改导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:importWeiboUpdateData')" )
    @Log(title = "媒介资源管理-媒介资源红人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importWeiboUpdateData" )
    public R<String> importWeiboUpdateData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xls"))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        ExcelUtil<WeiBoMediumResourceImportUpdateModel> util = new ExcelUtil<>(WeiBoMediumResourceImportUpdateModel.class);
        Boolean exist = util.isSheetName("资源信息(微博)", file.getInputStream());
        List<WeiBoMediumResourceImportUpdateModel> mediumResourceSensationList = new ArrayList<>();
        if(!exist){
            mediumResourceSensationList = util.importExcel("资源信息(微博)",file.getInputStream(),0);
        }
        Boolean aBoolean = this.verifyEditTemplate(file);
        if(aBoolean && ListUtil.isEmpty(mediumResourceSensationList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        MediumResourceSensationQuery query = new MediumResourceSensationQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        // 去除行业类目必填校验
        query.setIsUpdate(true);
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setResourceImportType(ResourceImportType.WEIBO_EDIT);
        query.setResourceSensationType(ResourceSensationType.MICRO_BLOG);
        reusableAsyncTaskService.addTask("资源管理-微博", TaskType.Import,query, MediumResourceSensationServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 淘宝修改导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:importTaoBaoUpdateData')" )
    @Log(title = "媒介资源管理-媒介资源红人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTaoBaoUpdateData" )
    public R<String> importTaoBaoUpdateData(MultipartFile file) throws Exception {
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(".xls"))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        ExcelUtil<TaoBaoMediumResourceImportUpdateModel> util = new ExcelUtil<>(TaoBaoMediumResourceImportUpdateModel.class);
        Boolean exist = util.isSheetName("资源信息(淘宝)", file.getInputStream());
        List<TaoBaoMediumResourceImportUpdateModel> mediumResourceSensationList = new ArrayList<>();
        if(!exist){
            mediumResourceSensationList = util.importExcel("资源信息(淘宝)",file.getInputStream(),0);
        }
        Boolean aBoolean = this.verifyEditTemplate(file);
        if(aBoolean && ListUtil.isEmpty(mediumResourceSensationList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        MediumResourceSensationQuery query = new MediumResourceSensationQuery();
        OssUploadResult upload = ossService.upload(file.getInputStream(), file.getOriginalFilename(),false);
        query.setFileKey(upload.getOssKey());
        query.setFileName(file.getOriginalFilename());
        // 去除行业类目必填校验
        query.setIsUpdate(true);
        query.setLoginUser(SecurityUtils.getLoginUser());
        query.setResourceImportType(ResourceImportType.TAO_BAO_EDIT);
        query.setResourceSensationType(ResourceSensationType.TAO_BAO);
        reusableAsyncTaskService.addTask("资源管理-淘宝", TaskType.Import,query, MediumResourceSensationServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @ApiOperation("获取导入模板")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:import')" )
    @Log(title = "媒介资源管理-媒介资源红人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response,@RequestBody SensationTypeQuery query) {

        try {
            InputStream inputStream = null;
            switch (query.getType()){
                case LITTLE_RED_BOOK:
                    inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_little_red_book.xlsx");
                    break;
                case TAO_BAO:
                    inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_tao_bao.xlsx");
                    break;
                case TIKTOK:
                    inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_tiktok.xlsx");
                    break;
                case B_STATION:
                    inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_b_station.xlsx");
                    break;
                case MICRO_BLOG:
                    inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_micro_blog.xlsx");
                    break;
                default:
            }
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("资源管理(红人)-下载模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = null;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 使用/禁用
     *
     * @param dto 修改状态参数
     * @return 是否成功
     */
    @ApiOperation("使用/禁用")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:edit')")
    @Log(title = "禁用", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    public R<Boolean> updateStatus(@RequestBody ResourceUpdateDTO dto) {
        return R.success(sensationService.updateStatus(dto));
    }

    /**
     * 清单页面-下载资源
     * @param query 导出查询参数
     */
    @ApiOperation("清单页面-下载资源")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceSensation:export')")
    @Log(title = "清单页面-下载资源", businessType = BusinessType.EXPORT)
    @PostMapping("/exportResource")
    public R<String> exportResource(@RequestBody MediumResourceSensationQuery query) {

        Integer residueNum = this.downloadControlService.residueNum(query.getUserId());
        if (residueNum != null && residueNum == 0){
            throw new ServiceException("本人下载条数已达最大限制,请联系管理员");
        }

        String label = EnumUtils.getLabelByValue(query.getMenuType(), LaundryListType.class);

        String fileName = label+"(红人) " + DateUtils.getTimeCn() + ".xlsx";
        query.setFileName(fileName);

        //获取登录人员对应的下载条数
        List<Long> removeSensationModelIdList = ListUtil.emptyList();

        List<Long>resourceIdList;
        if (query.getMenuType().equals(LaundryListType.SELECT_RESOURCE.getValue()) || query.getMenuType().equals(LaundryListType.PROJECT.getValue())) {
            for (ResourceSensationType sensationType : ResourceSensationType.values()) {
                query.setType(sensationType);
                resourceIdList = this.sensationService.exportResourceIdList(query,residueNum);
                removeSensationModelIdList.addAll(resourceIdList);
                if(residueNum == null){
                    removeSensationModelIdList.addAll(resourceIdList);
                    continue;
                }
                if (resourceIdList.size() == residueNum){
                    removeSensationModelIdList.addAll(resourceIdList);
                    break;
                }
                if (resourceIdList.size() < residueNum){
                    residueNum = residueNum - resourceIdList.size();
                    removeSensationModelIdList.addAll(resourceIdList);
                    if (removeSensationModelIdList.size() == residueNum){
                        removeSensationModelIdList.addAll(resourceIdList);
                        break;
                    }
                }
            }
        }else{
            removeSensationModelIdList = this.sensationService.exportResourceIdList(query,residueNum);
        }
        if(ListUtil.isEmpty(removeSensationModelIdList)){
            return R.success(Constants.ISNULL);
        }
            //删除加入清单接口
            LaundryListRemoveDTO removeDTO = this.sensationConverter.toQuery(query);
            removeDTO.setType(query.getLaundryListType());
            removeDTO.setTabType(1);
            removeDTO.setResourceIdList(removeSensationModelIdList);
            List<Long> deleteLaundryIdList = this.laundryListService.deleteLaundryIdList(removeDTO);
            query.setDeleteLaundryIdList(deleteLaundryIdList);

        reusableAsyncTaskService.addTask(String.format("%s(红人)-下载资源",label), TaskType.Export,query, MediumResourceSensationServiceImpl.class);


        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 获取导入查询模板
     * @param response 请求响应
     */
    @ApiOperation("获取导入查询模板")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:import')" )
    @Log(title = "媒介资源管理-媒介资源红人" , businessType = BusinessType.IMPORT)
    @PostMapping("/importSelectTemplate" )
    public void importSelectTemplate(HttpServletResponse response) {
        try {
            InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("template/import_select.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            response.setContentType("application/binary;charset=ISO8859-1");
            String name = java.net.URLEncoder.encode("选资源(红人)-导入查询下载模板", "UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=" + name + Constants.XLSX);
            ServletOutputStream out = null;
            out = response.getOutputStream();
            workbook.write(out);
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入查询
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @ApiOperation("导入查询")
    @PreAuthorize("@ss.hasPermi('resources:mediumResourceService:import')" )
    @Log(title = "导入查询" , businessType = BusinessType.IMPORT)
    @PostMapping("/importSelectData" )
    public R<ImportSelectResultVO> importSelectData(MultipartFile file, Integer type) throws Exception {
        Assert.notNull(type, "请选择资源类型");
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        ExcelUtil<ImportSelectModel> util = new ExcelUtil<>(ImportSelectModel.class);
        List<ImportSelectModel> modelList = util.importExcel(file.getInputStream());
        if(ListUtil.isEmpty(modelList)){
            throw new ServiceException("导入失败，无法读取数据，请检查");
        }
        ImportSelectQuery query = new ImportSelectQuery();
        query.setSensationType(type);
        return R.success(this.resourceSensationSearchImporter.importSelectData(modelList, query));
    }


    /**
     * 获取资源,红人/服务商的更新记录
     * @param query 查询参数
     * @return 查询结果
     */
    @ApiOperation("获取资源,红人/服务商更新记录")
    @PostMapping("/record")
    public R<PageInfo<PyMediumUpdateRecordVO>> getMediumRecord(@RequestBody PyMediumUpdateRecordQuery query){
        PageInfo<PyMediumUpdateRecordVO>  pyMediumUpdateRcordVOPageInfo = this.pyMediumUpdateRecordService.queryMediumUpdateRecord(query);
        return R.success(pyMediumUpdateRcordVOPageInfo);
    }


    @PostMapping("/handResourceCity")
    public void handResourceCity(){
        this.mediumResourceSensationHandleService.handResourceCity();
    }

    @PostMapping("/syncAddress")
    public R<Void> syncAddress(MultipartFile file)throws Exception{
        //Assert.notNull(projectStatus, "请选择项目状态");
        if (!(Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLSX)
                || Objects.requireNonNull(file.getOriginalFilename()).endsWith(Constants.XLS))) {
            throw new ServiceException("请上传正确的文件格式");
        }

        //校验是否有数据
        ExcelUtil<CityModel> importUtils = new ExcelUtil<>(CityModel.class);
        List<CityModel> importList = importUtils.importExcel(file.getInputStream());
        if (ListUtil.isEmpty(importList)){
            throw new ServiceException("导入失败,无法读取数据,请检查");
        }
        this.mediumResourceSensationHandleService.syncAddress(importList);
        return R.success();
    }
}
