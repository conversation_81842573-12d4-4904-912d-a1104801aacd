package com.py.web.controller.finance.performance;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.project.pysalemanagesourceflow.asynctask.SaleManageInfoServiceImpl;
import com.py.project.pysalemanagesourceflow.asynctask.SaleManageProjectInfoServiceImpl;
import com.py.project.pysalemanagesourceflow.domain.PySaleManageSourceFlow;
import com.py.project.pysalemanagesourceflow.domain.query.PySaleManageCheckBoxQuery;
import com.py.project.pysalemanagesourceflow.domain.query.PySaleManageInfoQuery;
import com.py.project.pysalemanagesourceflow.domain.query.PySaleManageSourceFlowQuery;
import com.py.project.pysalemanagesourceflow.domain.query.SaleManageInfoQuery;
import com.py.project.pysalemanagesourceflow.domain.vo.PySaleManageSourceCheckBoxVO;
import com.py.project.pysalemanagesourceflow.domain.vo.PySaleManageSourceFlowListPageVO;
import com.py.project.pysalemanagesourceflow.domain.vo.SaleManageDetailListVO;
import com.py.project.pysalemanagesourceflow.domain.vo.SaleManageDetailVO;
import com.py.project.pysalemanagesourceflow.service.IPySaleManageSourceFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理与销售人员业绩Controller
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@Api(tags = "管理与销售人员业绩")
@RestController
@RequestMapping("/project/pySaleManageSourceFlow")
public class PySaleManageSourceFlowController extends BaseController {

    /**
     * 管理与销售人员业绩服务
     */
    @Resource
    private IPySaleManageSourceFlowService pySaleManageSourceFlowService;

    /**
     * 可视化异步任务服务
     */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 分页查询管理与销售人员业绩列表
     *
     * @param query 管理与销售人员业绩查询参数
     * @return 管理与销售人员业绩分页
     */
    @ApiOperation("分页查询询管理与销售人员业绩列表")
    @PreAuthorize("@ss.hasPermi('project:pySaleManageSourceFlow:list')")
    @PostMapping("/pagePySaleManageSourceFlow")
    public R<PageInfo<PySaleManageSourceFlowListPageVO>> pagePySaleManageSourceFlow(@RequestBody PySaleManageSourceFlowQuery query) {
        PageInfo<PySaleManageSourceFlowListPageVO> flowListPageList = this.pySaleManageSourceFlowService.pagePySaleManageSourceFlowList(query);
        return R.success(flowListPageList);
    }


    /**
     * 查询管理与销售人员业绩列表,部门名称
     *
     * @param query 部门名称
     * @return 管理与销售人员业绩,部门名称
     */
    @ApiOperation("查询管理与销售人员业绩列表,部门名称")
    @PostMapping("/findPySaleManageSourceFlowDept")
    public R<List<String>> findPySaleManageSourceFlowDept(@RequestBody PySaleManageSourceFlowQuery query) {
        return R.success(this.pySaleManageSourceFlowService.findPySaleManageSourceFlowDept(query));
    }

    /**
     * 查询锅里销售人员详细页面上侧的信息
     * @param saleManageInfoQuery
     * @return
     */
    @ApiOperation("获得明细上侧数据")
    @PostMapping("/getUpperSide")
    public R<SaleManageDetailVO> getInfo(@Validated @RequestBody SaleManageInfoQuery saleManageInfoQuery){
        SaleManageDetailVO saleManageInfoVO = this.pySaleManageSourceFlowService.querySaleManaInfo(saleManageInfoQuery);
        return R.success(saleManageInfoVO);
    }

    /**
     * 分页查询管理与销售人员项目信息
     *
     * @param query 管理与销售人员业绩查询参数
     * @return 管理与销售人员业绩分页
     */
    @ApiOperation("人员业绩详情列表")
    @PreAuthorize("@ss.hasPermi('project:pySaleManageSourceFlow:list')")
    @PostMapping("/pagePySaleManageSourceProjectInfo")
    public R<PageInfo<SaleManageDetailListVO>> pagePySaleManageSourceProjectInfo(@RequestBody PySaleManageInfoQuery query) {
        return R.success(this.pySaleManageSourceFlowService.pagePySaleManageSourceProjectInfo(query));
    }

    /**
     * 导出人员业绩详情列表
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出人员业绩详情列表")
    @PreAuthorize("@ss.hasPermi('project:pySaleManageSourceFlow:export')")
    @Log(title = "导出人员业绩详情列表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportInfo")
    public R<String> exportInfo(@RequestBody PySaleManageInfoQuery query) {
        PySaleManageSourceFlow pySaleManageSourceFlow = pySaleManageSourceFlowService.queryBatchIdInfo(query);
        String fileName = "项目明细-"+pySaleManageSourceFlow.getUserName()+"-"+ DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("管理及销售人员业绩项目信息列表", TaskType.Export, query, SaleManageProjectInfoServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 合计全选
     *
     * @param dto
     * @return 是否成功
     */
    @ApiOperation("合计全选")
    @PreAuthorize("@ss.hasPermi('project:pyExecutiveFlow:edit')")
    @PostMapping("/checkBox")
    public R<PySaleManageSourceCheckBoxVO> checkBox(@RequestBody PySaleManageCheckBoxQuery dto) {
        return R.success(this.pySaleManageSourceFlowService.checkBoxQuery(dto));
    }

    /**
     * 导出管理与销售人员业绩
     *
     * @param query 导出查询参数
     */
    @ApiOperation("导出管理与销售人员业绩")
    @PreAuthorize("@ss.hasPermi('project:pySaleManageSourceFlow:export')")
    @Log(title = "管理与销售人员业绩", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@RequestBody PySaleManageSourceFlowQuery query) {
        String fileName = "管理及销售人员业绩列表数据-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setLoginUser(SecurityUtils.getLoginUser());
        reusableAsyncTaskService.addTask("客户合同信息数据", TaskType.Export, query, SaleManageInfoServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 获取人员下拉列表
     * @return
     */
    @ApiOperation("获取人员下拉列表")
    @GetMapping("/listSelectedUserName")
    public R<List<String>> listSelectedUserName() {
        return R.success(this.pySaleManageSourceFlowService.listSelectedUserName());
    }

    /**
     * 修复空数据-派芽合作主体id
     * @return 是否成功
     */
    @ApiOperation("修复空数据-派芽合作主体id")
    @GetMapping("/repairEmptyData/ManageSource")
    public R<Boolean> repairEmptyDataManageSource() {
        this.pySaleManageSourceFlowService.repairEmptyDataManageSource();
        return R.success();
    }
}
