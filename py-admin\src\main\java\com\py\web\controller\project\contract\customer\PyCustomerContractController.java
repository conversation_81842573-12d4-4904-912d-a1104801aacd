package com.py.web.controller.project.contract.customer;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.file.FileInfoVO;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.project.pycustomercontract.domain.dto.PyContractAnnexDeleteDTO;
import com.py.project.pycustomercontract.domain.dto.PyCustomerContractDTO;
import com.py.project.pycustomercontract.domain.query.PyContractCheckBoxQuery;
import com.py.project.pycustomercontract.domain.query.PyCustomerContractQuery;
import com.py.project.pycustomercontract.domain.vo.*;
import com.py.project.pycustomercontract.service.IPyCustomerContractService;
import com.py.project.pycustomercontract.service.impl.PyCustomerContractServiceImpl;
import com.py.system.mainstayparam.domain.vo.SystemMainstayParamVO;
import com.py.system.mainstayparam.service.ISystemMainstayParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户合同信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@Api(tags = "客户合同信息")
@Validated
@RestController
@RequestMapping("/crm/pyCustomerContract")
public class PyCustomerContractController extends BaseController {

    /** 客户合同信息服务 */
    @Resource
    private IPyCustomerContractService pyCustomerContractService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 系统设置-主体参数设置服务 */
    @Resource
    private ISystemMainstayParamService systemMainstayParamService;

    /**
     * 财务合同的标签页数量
     * @return 客户合同信息视图模型
     */
    @ApiOperation("财务合同的合同标签页数量")
    @PreAuthorize("@ss.hasPermi('crm:pyCustomerContract:query')")
    @PostMapping(value = "/getLabel")
    public R<List<PyContractLabelVO>> getFinanceLabel() {
        return R.success(pyCustomerContractService.getContractLabel());
    }

    /**
     * 获取客户合同信息详细信息
     * @param id 客户合同信息主键
     * @return 客户合同信息视图模型
     */
    @ApiOperation("获取客户合同信息详细信息")
    @PreAuthorize("@ss.hasPermi('crm:pyCustomerContract:query')")
    @GetMapping(value = "getInfo/{id}")
    public R<PyCustomerContractVO> getInfo(@PathVariable("id") Long id) {
        return R.success(pyCustomerContractService.selectPyCustomerContractById(id));
    }

    /**
     * 分页查询客户合同信息列表
     *
     * @param query 客户合同信息查询参数
     * @return 客户合同信息分页
     */
    @ApiOperation("分页查询询客户合同信息列表")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:list')")
    @PostMapping("/page")
    public R<PageInfo<PyCustomerContractListVO>> pagePyCustomerContract(@Validated @RequestBody PyCustomerContractQuery query) {
        PageInfo<PyCustomerContractListVO> voList = this.pyCustomerContractService.pagePyCustomerContractList(query);
        return R.success(voList);
    }

    /**
     * 客户合同和供应商合同标签数量
     * @return PyDailyContractCountVO
     */
    @ApiOperation("客户合同和供应商合同标签数量(项目)")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:count')")
    @GetMapping("/contractCount")
    public R<PyContractCountVO> contractCount() {
        return R.success(this.pyCustomerContractService.contractCount());
    }

    /**
     * 客户合同和供应商合同标签数量(财务)
     * @return PyDailyContractCountVO
     */
    @ApiOperation("客户合同和供应商合同标签数量(财务)")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:count')")
    @GetMapping("/contractCountOnFinance")
    public R<PyContractCountVO> contractCountOnFinance(@RequestParam(value = "pyMainstayId") Long pyMainstayId) {
        PyContractCountVO pyContractCountVO = this.pyCustomerContractService.contractCountOnFinance(pyMainstayId);
        return R.success(pyContractCountVO);
    }

    /**
     * 查看盖章合同
     * @param id 客户合同信息主键
     * @return 客户合同信息视图模型
     */
    @ApiOperation("查看盖章合同(财务)")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:query')")
    @GetMapping(value = "/selectSealedContract/{id}")
    public R<List<FileInfoVO>> selectSealedContract(@PathVariable("id") Long id) {
        return R.success(pyCustomerContractService.selectSealedContractById(id));
    }

    /**
     * 上传盖章合同
     * @param pyCustomerSealedContractVO
     * @return Boolean
     */
    @ApiOperation("上传盖章合同(财务)")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:delete')")
    @PostMapping(value = "/addSealedContract")
    public R<Boolean> addSealedContract(@RequestBody PyCustomerSealedContractVO pyCustomerSealedContractVO) {
        return R.success(pyCustomerContractService.addSealedContractById(pyCustomerSealedContractVO));
    }

    /**
     * 删除盖章合同(财务)
     * @param  pyContractAnnexDeleteDTO
     * @return Boolean
     */
    @ApiOperation("删除盖章合同(财务)")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:delete')")
    @PostMapping(value = "/deleteSealedContract")
    public R<Boolean> deleteSealedContract(@RequestBody PyContractAnnexDeleteDTO pyContractAnnexDeleteDTO) {
        return R.success(pyCustomerContractService.deleteSealedContract(pyContractAnnexDeleteDTO));
    }

    /**
     * 客户合同多选合计
     * @param query
     * @return PyContractMultipleChoiceVO
     */
    @ApiOperation("供应商合同多选框合计")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:query')")
    @PostMapping(value = "/checkBox")
    public R<PyContractMultipleChoiceVO> checkBox(@RequestBody PyContractCheckBoxQuery query) {
        return R.success(pyCustomerContractService.checkBoxAmount(query));
    }

    /**
     * 新增客户合同信息
     *
     * @param dto 客户合同信息修改参数
     * @return 是否成功
     */
    @ApiOperation("新增客户合同信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:add')")
    @Log(title = "客户合同信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<InsertCustomerContractVO> add(@RequestBody @Valid PyCustomerContractDTO dto) {
        return R.success(pyCustomerContractService.insertPyCustomerContract(dto));
    }

    /**
     * 合同再次发起审批
     * @param dto 客户合同信息修改参数
     * @return 是否成功
     */
    @ApiOperation("合同再次发起审批")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:edit')")
    @Log(title = "客户合同信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R<InsertCustomerContractVO> edit(@RequestBody @Valid PyCustomerContractDTO dto) {
        return R.success(pyCustomerContractService.updatePyCustomerContract(dto));
    }

    /**
     * 导出客户合同信息
     * @param
     * @param query 导出查询参数
     */
    @ApiOperation("导出客户合同信息")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:export')")
    @Log(title = "客户合同信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export( @Validated @RequestBody PyCustomerContractQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        if(query.getPyMainstayId() == null){
            String fileName = "客户合同管理列表数据-" +DateUtils.getTimeCn() + ".xlsx";
            query.setFileName(fileName);
            reusableAsyncTaskService.addTask("客户合同信息数据", TaskType.Export,query, PyCustomerContractServiceImpl.class);
        } else {
            SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPyMainstayId());
            String fileName = "客户合同管理列表数据-" + systemMainstayParamVO.getMainstayName() + "-" + DateUtils.getTime() + ".xlsx";
            query.setFileName(fileName);
            reusableAsyncTaskService.addTask("合同管理-客户合同(财务)", TaskType.Export,query, PyCustomerContractServiceImpl.class);
        }
        return R.success("提交成功");
    }

    /**
     * 查询客户合同列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询客户合同列表上的创建部门下拉" )
    @GetMapping("/listCustomerDept" )
    public R<List<String>> listCustomerDept(PyCustomerContractQuery query){
        return R.success(pyCustomerContractService.listCustomerDept(query));
    }

    /**
     * 分页查询客户合同审批查询列表
     *
     * @param query 客户合同信息查询参数
     * @return 客户合同信息分页
     */
    @ApiOperation("分页查询客户合同审批查询列表")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:list')")
    @PostMapping("/listPyCustomerContractAudit")
    public R<PageInfo<CustomerContractAuditVO>> listPyCustomerContractAudit(@Validated @RequestBody PyCustomerContractQuery query) {
        PageInfo<CustomerContractAuditVO> voList = this.pyCustomerContractService.listPyCustomerContractAudit(query);
        return R.success(voList);
    }

    /**
     * 客户合同审批查询列表合计
     * @param query 查询条件
     * @return PyContractMultipleChoiceVO
     */
    @ApiOperation("客户合同审批查询列表合计")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:query')")
    @PostMapping(value = "/checkBoxAudit")
    public R<PyContractMultipleChoiceVO> checkBoxAudit(@RequestBody PyContractCheckBoxQuery query) {
        return R.success(pyCustomerContractService.checkBoxAudit(query));
    }

    /**
     * 审批查询-客户合同和供应商合同标签数量审批查询
     * @param pyMainstayId 合作主体id
     * @return 结果
     */
    @ApiOperation("客户合同和供应商合同标签数量审批查询")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:count')")
    @GetMapping("/contractCountAudit")
    public R<PyContractCountVO> contractCountAudit(@RequestParam(value = "pyMainstayId",required = false) Long pyMainstayId) {
        PyContractCountVO pyContractCountVO = this.pyCustomerContractService.contractCountAudit(pyMainstayId);
        return R.success(pyContractCountVO);
    }

    /**
     * 审批查询-合同审批查询顶部统计
     * @return 结果
     */
    @ApiOperation("合同审批查询顶部统计")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:count')")
    @GetMapping("/contractTopCountAudit")
    public R<List<PyContractLabelVO>> contractTopCountAudit() {
        List<PyContractLabelVO> contractLabelVOList = this.pyCustomerContractService.contractTopCountAudit();
        return R.success(contractLabelVOList);
    }

    /**
     * 导出客户合同信息审批查询"
     * @param
     * @param query 导出查询参数
     */
    @ApiOperation("导出客户合同信息审批查询")
    @PreAuthorize("@ss.hasPermi('com.py.crm:pyCustomerContract:export')")
    @Log(title = "客户合同信息", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAudit")
    public R<String> exportAudit( @Validated @RequestBody PyCustomerContractQuery query) {
        query.setLoginUser(SecurityUtils.getLoginUser());
        SystemMainstayParamVO systemMainstayParamVO = systemMainstayParamService.selectSystemMainstayParamById(query.getPyMainstayId());
        String fileName = "客户合同审批查询列表数据-" + systemMainstayParamVO.getMainstayName() + "-" + DateUtils.format(LocalDateTime.now(), "yyyy-MM-dd-HH：mm：ss") + ".xlsx";
        query.setFileName(fileName);
        query.setIsApproval(true);
        reusableAsyncTaskService.addTask("合同管理-客户合同审批查询", TaskType.Export,query, PyCustomerContractServiceImpl.class);
        return R.success("提交成功");
    }

    /**
     * 查询客户审批列表上的创建部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询客户审批列表上的创建部门下拉" )
    @GetMapping("/listAuditCustomerDept" )
    public R<List<String>> listAuditCustomerDept(PyCustomerContractQuery query){
        return R.success(pyCustomerContractService.listAuditCustomerDept(query));
    }
}
