package com.py.crm.customer.customervisit.domian.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.file.FileAnnex;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 *<AUTHOR>
 *
 */
@Data
@ApiModel("客户管理-访问日志")
public class CustomerVisitDTO {
    private static final long serialVersionUID = 1L;
    /**自增id*/
    @ApiModelProperty("自增id")
    private Long id;

    /**客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /**访问日志id*/
    @ApiModelProperty("访问日志id")
    private Long visitId;

    /**随访人员*/
    @ApiModelProperty("随访人员")
    private List<String> visitPersonnelList;

    /**访问事项*/
    @ApiModelProperty("访问事项")
    private String visitMatters;

    /**事项结果*/
    @ApiModelProperty("事项结果")
    private String visitOutcome;

    /**附件*/
    @ApiModelProperty("附件")
    private List<FileAnnex> annex;

    /**备注*/
    @ApiModelProperty("备注")
    private String note;

    /**访问日期*/
    @ApiModelProperty("访问日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate visitTime;

}
