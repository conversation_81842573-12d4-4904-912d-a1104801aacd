package com.py.web.core.config;

import com.fasterxml.jackson.databind.introspect.AnnotatedField;
import com.fasterxml.jackson.databind.introspect.BeanPropertyDefinition;
import com.py.common.enums.IDict;
import com.py.common.utils.EnumUtils;
import com.py.common.utils.collection.ListUtil;
import org.springframework.stereotype.Component;
import springfox.documentation.service.AllowableListValues;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.schema.ModelPropertyBuilderPlugin;
import springfox.documentation.spi.schema.contexts.ModelPropertyContext;

import java.util.List;

/**
 * Knife4j枚举属性构建插件
 * <AUTHOR>
 */
@SuppressWarnings("all")
@Component
public class Knife4EnumPropertyBuilderPlugin implements ModelPropertyBuilderPlugin {

    @SuppressWarnings("unchecked")
    @Override
    public void apply(ModelPropertyContext context) {
        BeanPropertyDefinition propertyDefinition = context.getBeanPropertyDefinition().orElse(null);
        if(propertyDefinition == null){
            return;
        }
        AnnotatedField field = propertyDefinition.getField();
        if(field == null){
            return;
        }
        Class<?> rawType = field.getRawType();
        if(IDict.class.isAssignableFrom(rawType) == false){
            return;
        }
        List<IDict<?>> enumList = (List<IDict<?>>) EnumUtils.getEnumConstants(rawType);
        List<String> valueList = ListUtil.map(enumList, dict -> dict.getValue().toString());
        AllowableListValues allowableListValues = new AllowableListValues(valueList,"LIST");
        context.getBuilder().allowableValues(allowableListValues).build();
    }

    @Override
    public boolean supports(DocumentationType documentationType) {
        return true;
    }
}
