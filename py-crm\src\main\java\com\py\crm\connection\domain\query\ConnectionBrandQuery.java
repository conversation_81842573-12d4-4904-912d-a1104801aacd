package com.py.crm.connection.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人脉管理表查询对象
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
@ApiModel("人脉管理表查询对象" )
public  class ConnectionBrandQuery {
    private static final long serialVersionUID = 1L;

    /** 企业名称 */
    @ApiModelProperty("企业名称")
    private String name;

    /** 负责品牌/业务线 */
    @ApiModelProperty("负责品牌/业务线")
    private String responsibleBrand;
}
