package com.py.common.constant;

/**
 * <AUTHOR>
 * @Description 审批流常量类
 * @Date 2023/7/14 11:49
 */
public class FlowConstant {

    /**
     * 流程跳过
     */
    public static final String FLOWABLE_SKIP_EXPRESSION_ENABLED = "_FLOWABLE_SKIP_EXPRESSION_ENABLED";

    /**
     * 单个审批人
     */
    public static final String ASSIGNEE = "assignee";

    /**
     * 候选人
     */
    public static final String CANDIDATE_USERS = "candidateUsers";

    /**
     * 审批组
     */
    public static final String CANDIDATE_GROUPS = "candidateGroups";

    /**
     * 会签人员
     */
    public static final String PROCESS_MULTI_INSTANCE_USER = "userList";

    /**
     * 会签节点
     */
    public static final String PROCESS_MULTI_INSTANCE = "multiInstance";

    /**
     * BPMN后缀
     */
    public static final String BPMN_FILE_SUFFIX = ".bpmn";
}
