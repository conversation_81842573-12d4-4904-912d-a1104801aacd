package com.py.crm.customer.customerlaundry.domain.query;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.py.common.core.domain.model.LoginUser;
import com.py.common.core.domain.query.ICreateQuery;
import com.py.common.core.domain.query.IUpdateQuery;
import com.py.common.datascope.IDataScopeArgs;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTaskArgs;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;

/**
 * 客户管理-查看清单查询对象
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@Data
@ApiModel("客户管理-查看清单查询对象" )
public class CustomerLaundryQuery implements IDataScopeArgs, ReusableAsyncTaskArgs {
    private static final long serialVersionUID = 1L;

    /** 自增id*/
    @ApiModelProperty("自增id" )
    private Long id;

    /** 清单id*/
    @ApiModelProperty("清单id" )
    private Long laundryId;

    /** 业务id*/
    @ApiModelProperty("业务id")
    private Long bizId;

    /** 业务类型（0客户，1人脉） */
    @ApiModelProperty("业务类型（0客户，1人脉）")
    private Integer bizType;

    /** 合作状态(0.合作中 1.暂停合作 2.意向合作)*/
    @ApiModelProperty("合作状态(0.合作中 1.暂停合作 2.意向合作)" )
    private Integer cooperationStatus;

    /** 审批状态*/
    @ApiModelProperty("审批状态")
    private Integer auditStatus;

    /** 创建人 */
    @ApiModelProperty("创建人")
    private Long userId;

    /** 数据权限 */
    @ApiModelProperty(hidden = true)
    private String dataScopeSql;

    /** 客户ID */
    @ApiModelProperty("客户ID")
    private List<Long> customerIdList;

    /** 清单ID */
    @ApiModelProperty("清单ID")
    private List<Long> laundryIdList;

    /** 是否全选，true 是 */
    @ApiModelProperty("是否全选，true 是")
    private Boolean isAll = false;

    /** 任务ID*/
    @ApiModelProperty(hidden = true)
    private Long taskId;

    /** 下载文件名 */
    private String fileName;

    /**  */
    private LoginUser loginUser;
}
