package com.py.common.utils.ip;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020/12/10 15:37
 * @description 向前端返回时将整型数字转换为字符串ip
 **/
@Slf4j
public class IpJsonSerializer extends JsonSerializer<Long> {

    @Override
    public void serialize(Long value, JsonGenerator gen,
                          SerializerProvider serializers) throws IOException{
        String text = (value == null ? null : IpUtils.intToIp(value));
        if (text != null) {
            gen.writeString(text);
        }
    }
}
