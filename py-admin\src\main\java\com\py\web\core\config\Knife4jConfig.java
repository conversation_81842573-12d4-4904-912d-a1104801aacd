package com.py.web.core.config;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

/**
 * Knife4j的接口配置
 * 文档地址: http://${url}:{端口}/doc.html
 * <AUTHOR>
 */
@EnableOpenApi
@Import(BeanValidatorPluginsConfiguration.class)
@Configuration
public class Knife4jConfig {

    /** 是否开启swagger */
    @Value("${swagger.enabled}")
    private boolean enabled;

    /**
     * 创建API
     */
    @Bean(value = "System")
    public Docket createSystemApi() {
        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.system");
        return this.createApiDocket(apiSelectors, PathSelectors.any())
                .groupName("系统");
    }

    /**
     * 创建API
     */
    @Bean(value = "Tool")
    public Docket createToolApi() {
        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.tool");
        return this.createApiDocket(apiSelectors, PathSelectors.any())
                .groupName("Tool");
    }

//
//    /**
//     * OA系统
//     */
//    @Bean(value = "oa")
//    public Docket createOAApi() {
//        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.oa");
//        return this.createApiDocket(apiSelectors, PathSelectors.any())
//                .groupName("oa管理");
//    }
//
//    /**
//     * 业绩
//     */
//    @Bean(value = "performance")
//    public Docket createPerformance() {
//        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.finance.performance");
//        return this.createApiDocket(apiSelectors, PathSelectors.any())
//                .groupName("业绩管理");
//    }


//    /**
//     * 收入坏账
//     */
//    @Bean(value = "incomebaddebt")
//    public Docket createIncomebaddebtApi() {
//        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.project.baddebts");
//        return this.createApiDocket(apiSelectors, PathSelectors.any())
//                .groupName("收入坏账");
//    }
//
//    /**
//     * 资源坏账
//     */
//    @Bean(value = "resourcebaddebt")
//    public Docket createResourcebaddebtApi() {
//        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.project.baddebts.resource");
//        return this.createApiDocket(apiSelectors, PathSelectors.any())
//                .groupName("资源坏账");
//    }


//    /**
//     * 合同
//     */
//    @Bean(value = "contract")
//    public Docket createContractApi() {
//        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.project.contract.customer");
//        return this.createApiDocket(apiSelectors, PathSelectors.any())
//                .groupName("客户合同");
//    }
//
//    /**
//     * ssswww
//     */
//    @Bean(value = "supplierContract")
//    public Docket createSupplierContractApi() {
//        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.project.contract.supplier");
//        return this.createApiDocket(apiSelectors, PathSelectors.any())
//                .groupName("供应商合同");
//    }

    /**
     * 项目管理
     */
    @Bean(value = "project")
    public Docket createProjectApi() {
        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.project");
        return this.createApiDocket(apiSelectors, PathSelectors.any())
                .groupName("项目管理");
    }
    /**
     * 媒介资源API
     */
    @Bean(value = "Resources")
    public Docket createResourcesApi() {
        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.resources");
        return this.createApiDocket(apiSelectors, PathSelectors.any())
                .groupName("媒介资源");
    }

    /**
     * 财务管理
     */
    @Bean(value = "finance")
    public Docket createFinanceApi() {
        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.finance");
        return this.createApiDocket(apiSelectors, PathSelectors.any())
                .groupName("财务管理");
    }


    /**
     * 流程管理API
     */
    @Bean(value = "flow")
    public Docket createFlowApi() {
        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.flow");
        return this.createApiDocket(apiSelectors, PathSelectors.any())
                .groupName("流程管理");
    }

    /**
     * 通用Api
     */
    @Bean(value = "Common")
    public Docket createCommonApi() {
        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.common");
        return this.createApiDocket(apiSelectors, PathSelectors.any())
                .groupName("通用");
    }

    /**
     * 系统监控APi
     */
    @Bean(value = "monitor")
    public Docket createMonitorApi() {
        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.monitor");
        return this.createApiDocket(apiSelectors, PathSelectors.any())
                .groupName("监控");
    }

    /**
     * 客户APi
     */
    @Bean(value = "crm")
    public Docket createCustomerApi() {
        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.crm");
        return this.createApiDocket(apiSelectors, PathSelectors.any())
                .groupName("客户");
    }

    /**
     * 员工交接
     */
    @Bean(value = "workHandover")
    public Docket createWorkHandover() {
        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.crm.workhandover");
        return this.createApiDocket(apiSelectors, PathSelectors.any())
                .groupName("员工交接");
    }




    /**
     * 资源Api
     */
    @Bean(value = "resource")
    public Docket createResourceApi() {
        Predicate<RequestHandler> apiSelectors = RequestHandlerSelectors.basePackage("com.py.web.controller.resources");
        return this.createApiDocket(apiSelectors, PathSelectors.any())
                .groupName("资源");
    }
    /**
     * 通用Api选择器
     * @return 通用Api选择器
     */
    private Predicate<RequestHandler> commonApiSelectors() {
        //noinspection deprecation
        return RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class)
                .and(RequestHandlerSelectors.withClassAnnotation(Api.class)
                        .and(requestHandler ->
                                requestHandler.declaringClass()
                                        .getAnnotation(Api.class).hidden() == false));
    }

    /**
     * 创建文档组
     * @param apiSelectors Api过滤器
     * @param pathSelectors url过滤器
     * @return 文档组
     */
    private Docket createApiDocket(Predicate<RequestHandler> apiSelectors, Predicate<String> pathSelectors) {
        return new Docket(DocumentationType.OAS_30)
                // 是否启用Swagger
                .enable(enabled)
                // 用来创建该API的基本信息，展示在文档的页面中（自定义展示的信息）
                .apiInfo(apiInfo())
                // 设置哪些接口暴露给Swagger展示
                .select()
//                // 扫描所有有注解的api，用这种方式更灵活
//                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                // 扫描指定包中的swagger注解
                .apis(apiSelectors)
                // 扫描所有 .apis(RequestHandlerSelectors.any())
                .paths(pathSelectors)
                .build()
                /* 设置安全模式，swagger可以设置访问token */
                .securitySchemes(securitySchemes())
                .securityContexts(securityContexts());
    }

    /**
     * 按照包地址创建文档组
     * @param packagePath 包地址
     * @return 文档组实例
     */
    private Docket createApiDocketByPackagePath(String packagePath) {
        Predicate<RequestHandler> apiSelectors = this.commonApiSelectors()
                .and(RequestHandlerSelectors.basePackage(packagePath));
        return createApiDocket(apiSelectors, PathSelectors.any());
    }

    /**
     * 安全模式，这里指定token通过Authorization头请求头传递
     */
    private List<SecurityScheme> securitySchemes() {
        List<SecurityScheme> apiKeyList = new ArrayList<>();
        apiKeyList.add(new ApiKey("Authorization", "Authorization", In.HEADER.toValue()));
        return apiKeyList;
    }

    /**
     * 安全上下文
     */
    private List<SecurityContext> securityContexts()
    {
        List<SecurityContext> securityContexts = new ArrayList<>();
        securityContexts.add(
                SecurityContext.builder()
                        .securityReferences(defaultAuth())
                        .operationSelector(o -> o.requestMappingPattern().matches("/.*"))
                        .build());
        return securityContexts;
    }

    /**
     * 默认的安全上引用
     */
    private List<SecurityReference> defaultAuth()
    {
        AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverything");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
        authorizationScopes[0] = authorizationScope;
        List<SecurityReference> securityReferences = new ArrayList<>();
        securityReferences.add(new SecurityReference("Authorization", authorizationScopes));
        return securityReferences;
    }

    /**
     * 添加摘要信息
     */
    private ApiInfo apiInfo()
    {
        // 用ApiInfoBuilder进行定制
        return new ApiInfoBuilder()
                // 设置标题
                .title("标题：派芽管理系统_接口文档")
                // 描述
//                .description("描述：用于管理集团旗下公司的人员信息,具体包括XXX,XXX模块...")
                // 作者信息
//                .contact(new SupContact(ruoyiConfig.getName(), null, null))
                // 版本
//                .version("版本号:" + ruoyiConfig.getVersion())
                .build();
    }
}
