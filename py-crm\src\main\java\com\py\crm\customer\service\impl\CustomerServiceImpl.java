package com.py.crm.customer.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageInfo;
import com.py.common.constant.DictConstant;
import com.py.common.core.domain.entity.SysDictData;
import com.py.common.core.domain.entity.SysUser;
import com.py.common.core.domain.vo.user.AuthUser;
import com.py.common.datascope.DataScopePageType;
import com.py.common.datascope.DataScopeUtils;
import com.py.common.enums.DelFlag;
import com.py.common.enums.SnapshotBizType;
import com.py.common.enums.flow.FlowApprovalStatus;
import com.py.common.exception.ServiceException;
import com.py.common.file.FileInfoVO;
import com.py.common.mybatisplus.ShowTableNameLambdaQueryWrapper;
import com.py.common.mybatisplus.SuperServiceImpl;
import com.py.common.oss.IOssService;
import com.py.common.tools.modifycomparator.IObjectComparator;
import com.py.common.tools.modifycomparator.model.BaseModifyDiff;
import com.py.common.tools.multisheetexcelexporter.MultiSheetExcelExporter;
import com.py.common.tools.multisheetexcelexporter.config.ExcelSheetConfig;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.model.ReusableAsyncTask;
import com.py.common.tools.verify.ObjectValidator;
import com.py.common.tools.verify.domain.RowVerifyError;
import com.py.common.utils.*;
import com.py.common.utils.collection.JoinUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.crm.compareddraft.domain.ComparedDraft;
import com.py.crm.compareddraft.service.IComparedDraftService;
import com.py.crm.connection.service.IConnectionService;
import com.py.crm.connectionemployment.service.IConnectionEmploymentService;
import com.py.crm.crmupdaterecord.domain.dto.CrmUpdateRecordDTO;
import com.py.crm.crmupdaterecord.domain.enums.RecordBizType;
import com.py.crm.crmupdaterecord.domain.enums.RecordMenuType;
import com.py.crm.crmupdaterecord.service.ICrmUpdateRecordService;
import com.py.crm.customer.contact.domain.SupContact;
import com.py.crm.customer.contact.domain.vo.ContactVO;
import com.py.crm.customer.contact.service.IContactService;
import com.py.crm.customer.converter.CustomerConverter;
import com.py.crm.customer.cooperatemainstay.domain.dto.CooperateMainstayDTO;
import com.py.crm.customer.cooperatemainstay.domain.vo.CooperateMainstayListVO;
import com.py.crm.customer.cooperatemainstay.domain.vo.CooperateMainstayVO;
import com.py.crm.customer.cooperatemainstay.service.ICooperateMainstayService;
import com.py.crm.customer.customeraccount.domain.vo.CustomerAccountVO;
import com.py.crm.customer.customeraccount.service.ICustomerAccountService;
import com.py.crm.customer.customeraddress.domain.vo.CustomerAddressVO;
import com.py.crm.customer.customeraddress.service.ICustomerAddressService;
import com.py.crm.customer.customercategory.domain.CustomerCategory;
import com.py.crm.customer.customercategory.service.ICustomerCategoryService;
import com.py.crm.customer.customerlaundry.domain.dto.CustomerLaundryBaseInfo;
import com.py.crm.customer.customervisit.domian.vo.CustomerVisitVO;
import com.py.crm.customer.customervisit.service.ICustomerVisitService;
import com.py.crm.customer.domain.SupCustomer;
import com.py.crm.customer.domain.dto.CustomerDTO;
import com.py.crm.customer.domain.dto.CustomerExportModel;
import com.py.crm.customer.domain.query.CustomerLineBusinessQuery;
import com.py.crm.customer.domain.query.CustomerNameDownQuery;
import com.py.crm.customer.domain.query.CustomerQuery;
import com.py.crm.customer.domain.vo.*;
import com.py.crm.customer.invoicing.domain.vo.InvoicingVO;
import com.py.crm.customer.invoicing.service.IInvoicingService;
import com.py.crm.customer.mapper.CustomerMapper;
import com.py.crm.customer.service.ICustomerService;
import com.py.crm.customer.wanderabout.domain.WanderAbout;
import com.py.crm.customer.wanderabout.domain.dto.WanderAboutDTO;
import com.py.crm.customer.wanderabout.service.IWanderAboutService;
import com.py.crm.customerdevote.domain.query.CustomerDevoteQuery;
import com.py.crm.customerdevote.domain.vo.CloseCaseMoneyListVO;
import com.py.crm.customerdevote.domain.vo.CustomerDevoteListVO;
import com.py.flow.api.IApprovalService;
import com.py.flow.domain.dto.flow.ApprovalSubmitDTO;
import com.py.flow.domain.dto.flow.ReApprovalSubmitDTO;
import com.py.flow.domain.enums.ApprovalBizType;
import com.py.system.dict.service.ISysDictDataService;
import com.py.system.recyclebin.domain.dto.DeleteAddDTO;
import com.py.system.recyclebin.domain.enums.RecycleBizType;
import com.py.system.recyclebin.service.IRecycleBinService;
import com.py.system.snapshot.domain.Snapshot;
import com.py.system.snapshot.service.ISnapshotService;
import com.py.system.tools.reusableasynctask.domain.ImportTaskResult;
import com.py.system.tools.reusableasynctask.service.ITaskRecodeService;
import com.py.system.user.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户管理-客户Service业务层处理
 * <AUTHOR>
 */
@Slf4j
@Service
public class CustomerServiceImpl
        extends SuperServiceImpl<CustomerMapper, SupCustomer>
        implements ICustomerService, ReusableAsyncTask<CustomerQuery> {

    /** 客户管理-客户模型转换器 */
    @Resource
    private CustomerConverter customerConverter;

    /** 客户管理-客户Mapper接口 */
    @Resource
    private CustomerMapper customerMapper;

    /** 客户管理-客户-企业联系人服务 */
    @Resource
    private IContactService contactService;

    /** 客户管理-客户-合作主体服务 */
    @Resource
    private ICooperateMainstayService cooperateMainstayService;

    /** 客户/人脉(状态)更新记录表服务 */
    @Resource
    private ICrmUpdateRecordService crmUpdateRecordService;

    /** 客户管理-客户-账户信息服务 */
    @Resource
    private ICustomerAccountService customerAccountService;

    /** 客户管理-客户-客户地址服务 */
    @Resource
    private ICustomerAddressService customerAddressService;

    /** 客户管理-客户-开票信息服务 */
    @Resource
    private IInvoicingService invoicingService;

    /** 对象比较器 */
    @Resource
    private IObjectComparator objectComparator;

    /** 用户 业务层 */
    @Resource
    private ISysUserService userService;

    /** 字典服务 */
    @Resource
    private ISysDictDataService sysDictDataService;

    /** 快照服务 */
    @Resource
    private ISnapshotService snapshotService;

    /** 客户管理-客户与行业类目关联Service接口 */
    @Resource
    private ICustomerCategoryService customerCategoryService;

    /** 客户管理-客户流转Service接口 */
    @Resource
    private IWanderAboutService wanderAboutService;

    /** 回收站服务 */
    @Resource
    private final IRecycleBinService recycleBinService;

    /** 对象存储服务 */
    @Resource
    private IOssService ossService;

    /** 审批服务 */
    @Resource
    private IApprovalService approvalService;

    /** 异步任务执行记录服务 */
    @Resource
    private ITaskRecodeService taskRecodeService;

    /**对象验证器*/
    @Resource
    private ObjectValidator objectValidator;

    /** 人脉管理表Service接口 */
    @Resource
    private IConnectionService connectionService;

    /** 人脉从业经历表Service接口 */
    @Resource
    private IConnectionEmploymentService connectionEmploymentService;

    @Resource
    private ICustomerVisitService customerVisitService;

    @Resource
    private IComparedDraftService comparedDraftService;

    public CustomerServiceImpl(
            IRecycleBinService recycleBinService) {
        recycleBinService.registeredRestoreCallback(RecycleBizType.CUSTOMER, this::restoreCustomer);
        this.recycleBinService = recycleBinService;
    }

    /**
     * 恢复客户指定主键逻辑删除的记录
     * @param customerId 主键ID
     * @return 是否成功
     */
    private boolean restoreCustomer(Long customerId) {
        SupCustomer customer = customerMapper.selectByCustomerId(customerId);
        if(Objects.isNull(customer)) {
            throw new ServiceException("恢复失败");
        }
        this.unique(customer.getCustomerId(), customer.getLineBusiness());

        boolean restore = restore(customerId);
        if(!restore){
            throw new ServiceException("恢复失败");
        }
        return true;
    }

    /***
     * 删除审批作废的客户
     * @param bizIds 删除数据
     * @return 是否删除
     */
    @Override
    public boolean deleteCustomerByBizIds(List<Long> bizIds) {
        if(ListUtil.isEmpty(bizIds)){
            return true;
        }
        // 客户与行业类目关联
        customerCategoryService.deleteByCustomerIds(bizIds);
        // 企业联系人
        contactService.deleteByCustomerIds(bizIds);
        // 客户管理-客户-合作主体
        cooperateMainstayService.deleteByCustomerIds(bizIds);
        // 客户管理-客户-客户地址
        customerAddressService.deleteByCustomerIds(bizIds);
        // 账户信息数据
        customerAccountService.deleteByCustomerIds(bizIds);
        // 开票信息数据
        invoicingService.deleteByCustomerIds(bizIds);

        return this.removeByIds(bizIds);
    }

    /**
     * 分页客户管理-客户列表
     * @param query 客户管理-客户
     * @return 客户管理-客户分页
     */
    @Override
    public PageInfo<CustomerListVO> pageCustomerList(CustomerQuery query) {
        if(query == null){
            query = new CustomerQuery();
        }
        ShowTableNameLambdaQueryWrapper<SupCustomer> queryWrapper = new ShowTableNameLambdaQueryWrapper<>(SupCustomer.class);
        queryWrapper.eq(query.getCooperationStatus() != null,SupCustomer::getCooperationStatus,query.getCooperationStatus());
        queryWrapper.like(StringUtils.isNotBlank(query.getName()),SupCustomer::getName,query.getName());
        queryWrapper.like(StringUtils.isNotBlank(query.getLineBusiness()),SupCustomer::getLineBusiness,query.getLineBusiness());
        queryWrapper.eq(SupCustomer::getAuditStatus,FlowApprovalStatus.APPROVE.getValue());
        queryWrapper.eq(SupCustomer::getDelFlag, DelFlag.NOT_DELETED.getValue());
        SqlHelper.filterUpdateInfoTableAlias(queryWrapper, query,"customer");
        SqlHelper.filterCreateListInfo(queryWrapper,query,"customer");
        SqlHelper.filterCreateDeptListInfo(queryWrapper,query,"customer");
        queryWrapper.in(ListUtil.isNotEmpty(query.getCreateUserList()),SupCustomer::getCreateBy,query.getCreateUserList());
        queryWrapper.setTableAlias("customer");

        // 客户管理列表查询,是否任意一项不为空
        if (ListUtil.isNotEmpty(query.getDownCustomerIdList()) ||
            ListUtil.isNotEmpty(query.getDownCustomerNameList())||
            ListUtil.isNotEmpty(query.getDownBusinessLineNameList())){
            query.setDownStatus(true);
        }

        PageUtils.startPage();
        List<SupCustomer> supCustomerList = customerMapper.pageCustomerList(query, queryWrapper);
        if(ListUtil.isEmpty(supCustomerList)){
            return ListUtil.emptyPage();
        }
        List<CustomerListVO> customerVoList = this.customerConverter.toListVoByEntity(supCustomerList);
        this.setServiceUserCategory(customerVoList);
        this.userService.relatedUpdateInfo(customerVoList);
        return ListUtil.pageConvert(supCustomerList, customerVoList);
    }

    /**
     * 判断权限
     * @param customerVoList 返回参数
     */
    private void judgmentPerm(List<CustomerListVO> customerVoList) {
        if(SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            customerVoList.forEach(customerListVO -> customerListVO.setIsQuery(true));
            return;
        }
        List<Long> queryCustomerIdList = ListUtil.distinctMap(customerVoList, CustomerListVO::getCustomerId);
        List<Long> customerIdList = getJudgmentCustomerId(queryCustomerIdList);
        customerVoList.forEach(customerListVO -> customerListVO.setIsQuery(customerIdList.contains(customerListVO.getCustomerId())));
    }

    /***
     * 获取有操作权限的客户id
     * @return 客户id
     */
    private List<Long> getJudgmentCustomerId(List<Long> customerIdList) {
        CustomerQuery query = new CustomerQuery();
        query.setCustomerIdList(customerIdList);
        return customerMapper.getJudgmentCustomerId(query);
    }

    /**
     * 设置目标服务人员的名和部门
     * @param customerVoList 客户列表
     */
    @Override
    public void setServiceUserCategory(List<? extends CustomerLaundryBaseInfo> customerVoList) {
        List<Long> customerIdList = ListUtil.distinctMap(customerVoList, CustomerLaundryBaseInfo::getCustomerId);
        if(ListUtil.isEmpty(customerIdList)){
            return;
        }
        // 行业类目
        List<CustomerCategory> customerCategoryList = customerCategoryService.selectCustomerCategory(customerIdList);
        if(ListUtil.isNotEmpty(customerCategoryList)){
            JoinUtils.oneByMany(customerVoList,customerCategoryList,
                    CustomerLaundryBaseInfo::getCustomerId,CustomerCategory::getCustomerId,
                    (customer,categoryList) -> {
                        List<Long> list = ListUtil.distinctMap(categoryList, CustomerCategory::getIndustryCategoryId);
                        customer.setIndustryCategoryIdList(list);
            });
        }
        // 分配人员的id和名和部门
        List<WanderAbout> wanderAboutList = wanderAboutService.listWanderAboutByCustomerId(customerIdList);
        if(ListUtil.isEmpty(wanderAboutList)){
            return;
        }
        JoinUtils.oneByMany(customerVoList,wanderAboutList
                ,CustomerLaundryBaseInfo::getCustomerId,WanderAbout::getCustomerId
                , (vo , wanderAbout) -> {
            List<Long> serviceUserIdList = new ArrayList<>(wanderAbout.size());
            if(!ListUtil.isSingleton(wanderAbout)){
                List<WanderAbout> wanderAbouts = wanderAbout.stream()
                        .filter(about -> about.getIsDistribution() != null && about.getIsDistribution())
                        .collect(Collectors.toList());
                serviceUserIdList = ListUtil.distinctMap(wanderAbouts, WanderAbout::getUserId);
            } else {
                WanderAbout about = ListUtil.singleOrThrow(wanderAbout);
                if(!about.getUserId().equals(vo.getCreateId())){
                    serviceUserIdList.add(about.getUserId());
                }
            }
            vo.setServiceUserIdList(serviceUserIdList);
        });
        List<Long> userIdList = ListUtil.distinctMap(wanderAboutList, WanderAbout::getUserId);
        List<AuthUser> authUsers = userService.selectUserById(userIdList);
        JoinUtils.manyByMany(customerVoList,authUsers
                ,CustomerLaundryBaseInfo::getServiceUserIdList,AuthUser::getUserId
                , (vo , userList) -> {
                    String content = getServiceUserName(userList,"、");
                    vo.setServiceUserName(content);
                    vo.setServiceUserNameList(ListUtil.distinctMap(userList,AuthUser::getUserName));
        });
    }

    /**
     * 查询客户管理-客户
     * @param id 客户管理-客户主键
     * @return 客户管理-客户视图模型
     */
    @Override
    public CustomerVO selectCustomerById(Long id) {
        SupCustomer supCustomer = this.getById(id);
        CustomerVO customerVO = this.customerConverter.toVoByEntity(supCustomer);
        if(Objects.isNull(customerVO)) {
            return new CustomerVO();
        }

        long snapshotInfo = snapshotService.querySnapshotInfo(supCustomer.getCustomerId(), supCustomer.getVersion(), SnapshotBizType.CUSTOMER.getValue());
        // 添加快照
        if(snapshotInfo == 0){
            this.addSnapshot(customerVO);
        }
        customerVO.setCreateTime(supCustomer.getInitiationTime());
        List<Long> list = customerCategoryService.selectCustomerCategoryById(id);
        customerVO.setIndustryCategoryIdList(list);
        customerVO.setBizType(ApprovalBizType.InsertCustomer);
        List<FileInfoVO> fileInfoByKey = ossService.getFileInfoByKey(customerVO.getAnnex());
        customerVO.setAnnexList(fileInfoByKey);
        return customerVO;
    }

    /**
     * 新增快照
     * @param customerVO 客户信息
     */
    private void addSnapshot(CustomerVO customerVO) {
        CustomerSnapshotVO customerSnapshotVO = this.getCustomerSnapshot(customerVO);
        Snapshot snapshot = new Snapshot();
        snapshot.setBizId(customerVO.getCustomerId());
        snapshot.setVersion(customerVO.getVersion());
        snapshot.setBizType(SnapshotBizType.CUSTOMER.getValue());
        snapshot.setSnapshotInfo(JsonUtil.obj2String(customerSnapshotVO));
        snapshotService.insert(snapshot);
    }

    /**
     * 获取客户快照信息
     * @param customerVO 客户基本信息
     * @return 客户快照信息
     */
    @Override
    public CustomerSnapshotVO getCustomerSnapshot(CustomerVO customerVO) {
        CustomerSnapshotVO customerSnapshotVO = customerConverter.toSnapshotByVO(customerVO);
        // 行业类目
        List<Long> list = customerCategoryService.selectCustomerCategoryById(customerVO.getCustomerId());
        customerSnapshotVO.setIndustryCategoryIdList(list);
        // 企业联系人
        List<ContactVO> contactVOList = contactService.selectContactDetailById(customerVO.getCustomerId());
        customerSnapshotVO.setContactVOList(contactVOList);
        // 客户管理-客户-合作主体
        List<CooperateMainstayVO> cooperateMainstayVOList = cooperateMainstayService.selectCooperateMainstayDetailById(customerVO.getCustomerId());
        customerSnapshotVO.setCooperateMainstayVOList(cooperateMainstayVOList);
        // 客户管理-客户-客户地址
        List<CustomerAddressVO> customerAddressVOList = customerAddressService.selectCustomerAddressDetailById(customerVO.getCustomerId());
        customerSnapshotVO.setCustomerAddressVOList(customerAddressVOList);
        // 账户信息数据
        List<CustomerAccountVO> customerAccountVOList = customerAccountService.selectCustomerAccountDetailById(customerVO.getCustomerId());
        customerSnapshotVO.setCustomerAccountVOList(customerAccountVOList);
        // 开票信息数据
        List<InvoicingVO> invoicingVOList = invoicingService.selectinvoicingDetailById(customerVO.getCustomerId());
        customerSnapshotVO.setInvoicingVOList(invoicingVOList);
        //客户拜访日志
        List<CustomerVisitVO> customerVisitVOList = customerVisitService.selectCustomerVisitByCustomerId(customerVO.getCustomerId());
        customerSnapshotVO.setCustomerVisitVOList(customerVisitVOList);
        return customerSnapshotVO;
    }

    /**
     * 新增客户管理-客户
     * @param dto 客户管理-客户修改参数
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertCustomer(CustomerDTO dto) {

        // 品牌/业务线唯一校验
        this.unique(dto.getCustomerId(), dto.getLineBusiness());
        SupCustomer supCustomer = this.customerConverter.toEntityByDto(dto);
        supCustomer.setAuditStatus(FlowApprovalStatus.IN_EXAMINATION_AND_APPROVAL.getValue());
        SqlHelper.appendAddUpdateInfo(supCustomer);
        //发起时间赋值
        supCustomer.setInitiationTime(LocalDateTime.now());
        if(dto.getCustomerId() == null){
            boolean save = this.save(supCustomer);
            if(!save) {
                throw new ServiceException("客户新增失败");
            }
            supCustomer.setIndustryCategoryIdList(dto.getIndustryCategoryIdList());
        } else {
            // 审批驳回后的修改
            supCustomer.setCreateTime(LocalDateTime.now());
            boolean update = this.updateById(supCustomer);
            if(!update) {
                throw new ServiceException("客户新增失败");
            }
        }
        this.addUpdateRecording(supCustomer, dto.getIsApprovalEdit());
        // 新增客户管理-客户与行业类目关联
        customerCategoryService.insertBatchCustomerCategory(dto.getIndustryCategoryIdList(), supCustomer.getCustomerId());

        // 批量新增客户管理-客户-企业联系人
        contactService.insertBatchContact(dto.getContactDTOList(), supCustomer.getCustomerId(), dto.getIsApprovalEdit());

        // 批量新增客户管理-客户-合作主体
        cooperateMainstayService.insertBatchCooperateMainstay(dto.getCooperateMainstayDTOList(), supCustomer.getCustomerId(), dto.getIsApprovalEdit());

        // 批量新增客户管理-客户-客户地址
        customerAddressService.insertBatchCustomerAddress(dto.getCustomerAddressDTOList(), supCustomer.getCustomerId(), dto.getIsApprovalEdit());

        // 批量新增账户信息数据
        customerAccountService.insertBatchCustomerAccount(dto.getCustomerAccountDTOList(), supCustomer.getCustomerId(), dto.getIsApprovalEdit());

        // 批量新增开票信息数据
        invoicingService.insertBatchInvoicing(dto.getInvoicingDTOList(), supCustomer.getCustomerId(), dto.getIsApprovalEdit());

        // 批量新增客户拜访日志数据
        customerVisitService.insertBatchCustomerVisit(dto.getCustomerVisitDTOList(),supCustomer.getCustomerId(),dto.getIsApprovalEdit());

        // 审核接口
        if(dto.getLastFlowInstanceId() == null){
            ApprovalSubmitDTO approvalSubmitDTO = new ApprovalSubmitDTO();
            approvalSubmitDTO.setBizId(supCustomer.getCustomerId());
            approvalSubmitDTO.setBizType(ApprovalBizType.InsertCustomer);
            approvalService.submitFlow(approvalSubmitDTO);
        } else {
            ReApprovalSubmitDTO reApprovalSubmitDTO = new ReApprovalSubmitDTO();
            reApprovalSubmitDTO.setBizId(supCustomer.getCustomerId());
            reApprovalSubmitDTO.setBizType(ApprovalBizType.InsertCustomer);
            reApprovalSubmitDTO.setLastFlowInstanceId(dto.getLastFlowInstanceId());
            approvalService.reSubmitFlow(reApprovalSubmitDTO);
        }
        return supCustomer.getCustomerId();
    }

    /** 新增更新记录 */
    private void addUpdateRecording(SupCustomer supCustomer,Boolean isApprovalEdit) {
        if(isApprovalEdit){
            crmUpdateRecordService.removeUpdateRecordByBizId(supCustomer.getCustomerId(), RecordBizType.CUSTOMER.getValue());
        }
        List<BaseModifyDiff<?>> baseModifyDiffs = objectComparator.compareObject(new SupCustomer(), supCustomer, SupCustomer.class);
        if(ListUtil.isNotEmpty(baseModifyDiffs)){
            List<CrmUpdateRecordDTO> crmUpdateRecordDTOList = new ArrayList<>();
            baseModifyDiffs.forEach(baseModifyDiff -> {
                // 新增更新记录
                CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                String content = "基本信息" + "-【" + ListUtil.firstOrThrow(baseModifyDiff.getFieldPath()) +"】新增" +
                        "“" + baseModifyDiff.getAfter() + "”";
                crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(), supCustomer.getCustomerId()
                        , content, RecordMenuType.UPDATE_RECORD.getValue(), null, null);
                crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
            });
            if(ListUtil.isNotEmpty(crmUpdateRecordDTOList)){
                crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordDTOList);
            }
        }
    }

    /**
     * 判断当前操作是否有权限
     * @param customerId 客户id
     */
    private void operationPerm(Long customerId) {
        if(SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            return;
        }
        List<Long> customerIdList = getJudgmentCustomerId(Collections.singletonList(customerId));
        if(!customerIdList.contains(customerId)){
            throw new ServiceException("当前操作无权限");
        }
    }

    /**
     * 修改客户管理-客户
     * @param dto 客户管理-客户修改参数
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateCustomer(CustomerDTO dto) {
        // 判断当前操作是否有权限
        this.operationPerm(dto.getCustomerId());

        // 品牌/业务线唯一校验
        this.unique(dto.getCustomerId(), dto.getLineBusiness());
        SupCustomer editSupCustomer = this.customerConverter.toEntityByDto(dto);
        SupCustomer sourceSupCustomer = this.getInfo(editSupCustomer.getCustomerId());
        this.fieldComparison(dto, sourceSupCustomer);
        if(ListUtil.isNotEmpty(dto.getIndustryCategoryIdList())){
            customerCategoryService.insertBatchCustomerCategory(dto.getIndustryCategoryIdList(), dto.getCustomerId());
        }

        //校验客户名称和品牌业务线是否发生了改变
        Boolean isCheckCustomer = !StringUtils.equals(editSupCustomer.getName(), sourceSupCustomer.getName()) ||
                !StringUtils.equals(editSupCustomer.getLineBusiness(), sourceSupCustomer.getLineBusiness());

        this.updateById(editSupCustomer);

        //根据客户查询比搞id
        List<ComparedDraft> comparedDraftList = comparedDraftService.queryComparedDraftByCustomerId(dto.getCustomerId());

        //如果客户的比稿记录不为空
        if (CollectionUtils.isNotEmpty(comparedDraftList)){

            List<Long> cooperateMainstayIdList = dto.getCooperateMainstayDTOList()
                    .stream()
                    .map(CooperateMainstayDTO::getCooperateMainstayId)
                    .collect(Collectors.toList());

            comparedDraftList.stream().peek(comparedDraft -> {
                //更新客户名称
                comparedDraft.setCustomerName(editSupCustomer.getName());
                //如果比稿的合作主体id不为空
                if (comparedDraft.getCustomerCooperateId() != null ){
                    //如果这条比稿的合作主体id没有被删除
                    if (cooperateMainstayIdList.contains(comparedDraft.getCustomerCooperateId())){
                        for (CooperateMainstayDTO cooperateMainstayDTO : dto.getCooperateMainstayDTOList()) {
                            if (cooperateMainstayDTO.getCooperateMainstayId().equals(comparedDraft.getCustomerCooperateId())) {
                                comparedDraft.setCustomerCooperateName(cooperateMainstayDTO.getMainstayName());
                                break;
                            }
                        }
                    }else {
                        //如果这条比稿的合作主体id被删除了，将比稿的合作主体id置为null
                        comparedDraft.setCustomerCooperateName(null);
                        comparedDraft.setCustomerCooperateId(null);
                    }
                }
                comparedDraft.setBrandName(editSupCustomer.getLineBusiness());
            }).collect(Collectors.toList());

            comparedDraftService.updateComparedDraftByCustomerId(comparedDraftList);
        }

        // 企业联系人数据传输模型
        if(ListUtil.isNotEmpty(dto.getContactDTOList())) {
            contactService.updateBatchContact(dto.getContactDTOList(), dto.getCustomerId(), editSupCustomer.getName(), editSupCustomer.getLineBusiness(), isCheckCustomer);
        } else {
            //编辑客户时，如果未传企业联系人数据，则需删除旧的企业联系人数据
            contactService.deleteBatchContact(dto.getCustomerId(), editSupCustomer.getName());
            //如果客户名称和品牌业务线发生了改变
            if(isCheckCustomer) {
                //如果从客户管理里更新了客户名称和品牌业务线，那么人脉管理-基本信息中的任职企业名称和品牌业务线同步更新
                connectionService.updateBatchCustomerInfo(editSupCustomer.getCustomerId(), editSupCustomer.getName(), editSupCustomer.getLineBusiness(), null);
            }
        }

        // 批量新增客户管理-客户-合作主体
        if(ListUtil.isNotEmpty(dto.getCooperateMainstayDTOList())) {
            cooperateMainstayService.updateBatchCooperateMainstay(dto.getCooperateMainstayDTOList(), dto.getCustomerId(), editSupCustomer.getName());
        } else {
            //编辑客户时，如果未传合作主体数据，则需删除旧的合作主体数据
            cooperateMainstayService.deleteBatchCooperateMainstay(dto.getCustomerId(), editSupCustomer.getName());
        }

        // 批量新增客户管理-客户-客户地址
        if(ListUtil.isNotEmpty(dto.getCustomerAddressDTOList())) {
            customerAddressService.updateBatchCustomerAddress(dto.getCustomerAddressDTOList(), dto.getCustomerId(), editSupCustomer.getName());
        } else {
            //编辑客户时，如果未传客户地址数据，则需删除旧的客户地址数据
            customerAddressService.deleteBatchCustomerAddress(dto.getCustomerId(), editSupCustomer.getName());
        }

        // 批量新增账户信息数据
        if(ListUtil.isNotEmpty(dto.getCustomerAccountDTOList())) {
            customerAccountService.updateBatchCustomerAccount(dto.getCustomerAccountDTOList(), dto.getCustomerId(), editSupCustomer.getName());
        } else {
            //编辑客户时，如果未传账户信息数据，则需删除旧的账户信息数据
            customerAccountService.deleteBatchCustomerAccount(dto.getCustomerId(), editSupCustomer.getName());
        }

        // 批量新增开票信息数据
        if(ListUtil.isNotEmpty(dto.getInvoicingDTOList())) {
            invoicingService.updateBatchInvoicing(dto.getInvoicingDTOList(), dto.getCustomerId(), editSupCustomer.getName());
        } else {
            //编辑客户时，如果未传开票信息数据，则需删除旧的开票信息数据
            invoicingService.deleteBatchInvoicing(dto.getCustomerId(), editSupCustomer.getName());
        }
        if(ListUtil.isNotEmpty(dto.getCustomerVisitDTOList())){
            customerVisitService.updateBatchCustomerVisit(dto.getCustomerVisitDTOList(),dto.getCustomerId(), editSupCustomer.getName());
        }else {
            //编辑客户时，如果未传客户拜访日志数据，则删除旧的客户拜访日志数据
            customerVisitService.deleteBatchCustomerVisit(dto.getCustomerId(),editSupCustomer.getName());
        }

        return editSupCustomer.getCustomerId();
    }

    /**
     * 字段校验是否修改
     * @param dto 客户修改参数
     * @param sourceSupCustomer 源数据
     */
    private void fieldComparison(CustomerDTO dto, SupCustomer sourceSupCustomer) {
        SupCustomer editSupCustomer = this.customerConverter.toEntityByDto(dto);
        List<Long> categoryIdList = customerCategoryService.selectCustomerCategoryById(dto.getCustomerId());
        sourceSupCustomer.setIndustryCategoryIdList(categoryIdList);
        List<BaseModifyDiff<?>> baseModifyDiffs = objectComparator.compareObject(sourceSupCustomer, editSupCustomer, SupCustomer.class);
        List<CrmUpdateRecordDTO> crmUpdateRecordDTOList = new ArrayList<>();
        if(ListUtil.isNotEmpty(baseModifyDiffs)) {
            baseModifyDiffs.forEach(baseModifyDiff -> {
                CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                Object source = baseModifyDiff.getBefore() != null ? baseModifyDiff.getBefore() : "--";
                Object after = baseModifyDiff.getAfter() != null ? baseModifyDiff.getAfter() : "--";
                String content = "基本信息-【" + ListUtil.last(baseModifyDiff.getFieldPath()) + "】从" + "“" + source + "”更新为“" + after + "”";
                crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(),editSupCustomer.getCustomerId()
                        ,content,RecordMenuType.UPDATE_RECORD.getValue(),null,null);
                crmUpdateRecordDTOList.add(crmUpdateRecordDTO);
            });
        }
        crmUpdateRecordService.insertBatchCrmUpdateRecord(crmUpdateRecordDTOList);
        if(!editSupCustomer.getCooperationStatus().equals(sourceSupCustomer.getCooperationStatus())){
            List<SysDictData> sysDictData = sysDictDataService.selectAllByType(DictConstant.COOPERATION_STATUS);
            Map<String, String> dictValueLabelMap = ListUtil.toMap(sysDictData, SysDictData::getDictValue, SysDictData::getDictLabel);
            CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
            String deptName = StringUtils.isNotBlank(SecurityUtils.getLoginUser().getDeptName()) ? SecurityUtils.getLoginUser().getDeptName() : "无";
            String content = deptName + "-" + SecurityUtils.getUsername() + "把【客户状态】从" +
                    "“" + dictValueLabelMap.get(sourceSupCustomer.getCooperationStatus().toString()) +
                    "”更新为“" + dictValueLabelMap.get(editSupCustomer.getCooperationStatus().toString()) + "”";
            crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(), editSupCustomer.getCustomerId()
                    , content, RecordMenuType.STATUS_UPDATE.getValue(), editSupCustomer.getChangeStatusReason(), null);
            crmUpdateRecordService.insertCrmUpdateRecord(crmUpdateRecordDTO);
        }
    }

    /**
     * 品牌/业务线唯一校验
     * @param customerId 客户id
     * @param lineBusiness 品牌/业务线
     */
    private void unique(Long customerId, String lineBusiness) {
        SupCustomer customer = this.customerMapper.unique(lineBusiness);
        if(Objects.isNull(customer)) {
            return;
        }
        if (Objects.isNull(customerId) || !customer.getCustomerId().equals(customerId)) {
            throw new ServiceException("该品牌/业务线已添加");
        }
    }

    /**
     * 批量删除客户管理-客户
     * @param id 需要删除的客户管理-客户主键
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCustomerById(Long id) {
        // 判断当前操作是否有权限
        this.operationPerm(id);

        SupCustomer supCustomer = this.getInfo(id);
        DeleteAddDTO deleteAddDTO = new DeleteAddDTO();
        deleteAddDTO.setContent("客户名称:" + supCustomer.getName());
        deleteAddDTO.setBizId(id);
        boolean record = recycleBinService.addDeleteRecord(RecycleBizType.CUSTOMER, deleteAddDTO);
        if(!record) {
            throw new ServiceException("客户删除失败");
        }
        return this.removeById(id);
    }

    /**
     * 客户状态变更
     * @param dto 参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean stateUpdate(CustomerDTO dto) {
        if(dto.getCooperationStatus() == null || dto.getCustomerId() == null) {
            throw new ServiceException("必填为空");
        }
        // 判断当前操作是否有权限
        this.operationPerm(dto.getCustomerId());

        SupCustomer supCustomer = this.getInfo(dto.getCustomerId());
        supCustomer.setServiceUserId(dto.getServiceUserId());
        //更新前的合同状态
        Integer cooperationStatusOld = supCustomer.getCooperationStatus();
        //合同状态
        supCustomer.setCooperationStatus(dto.getCooperationStatus());
        //变更原因
        supCustomer.setChangeStatusReason(dto.getChangeStatusReason());
        SqlHelper.appendAddUpdateInfo(supCustomer);
        boolean update = this.updateById(supCustomer);
        if(!update) {
            throw new ServiceException("状态变更失败");
        }
        CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
        String cooperationStatusOldLabel = sysDictDataService.selectDictLabel(DictConstant.COOPERATION_STATUS, String.valueOf(cooperationStatusOld));
        String cooperationStatusNewLabel = sysDictDataService.selectDictLabel(DictConstant.COOPERATION_STATUS, String.valueOf(supCustomer.getCooperationStatus()));
        String content = supCustomer.getUpdateDept() + "-" + supCustomer.getUpdateBy() + "把【客户状态】从“"
                + cooperationStatusOldLabel + "“更新为”" + cooperationStatusNewLabel + "”";
        crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(), supCustomer.getCustomerId(),
                content, RecordMenuType.STATUS_UPDATE.getValue(), dto.getChangeStatusReason(), null);

        return crmUpdateRecordService.insertCrmUpdateRecord(crmUpdateRecordDTO);
    }

    /**
     * 客户流转
     * @param dto 参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean customerCirculation(CustomerDTO dto) {
        if(ListUtil.isEmpty(dto.getServiceUserIdList()) ) {
            throw new ServiceException("目标服务人员不能为空");
        }
        if( dto.getCustomerId() == null) {
            throw new ServiceException("必填为空");
        }
        // 添加分配
        List<WanderAboutDTO> wanderAboutDTOList = new ArrayList<>();
        dto.getServiceUserIdList().forEach(userId -> {
            WanderAboutDTO wanderAboutDTO = new WanderAboutDTO();
            wanderAboutDTO.setCustomerId(dto.getCustomerId());
            wanderAboutDTO.setIsDistribution(true);
            wanderAboutDTO.setUserId(userId);
            wanderAboutDTOList.add(wanderAboutDTO);
        });
        wanderAboutService.insertBatchWanderAbout(wanderAboutDTOList);
        // 分配记录
        List<AuthUser> authUsers = userService.selectUserById(dto.getServiceUserIdList());
        // 根据用户信息组装服务人员
        String content = getServiceUserName(authUsers,"，");

        CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
        crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(), dto.getCustomerId()
                , content, RecordMenuType.CIRCULATION.getValue(), dto.getCirculationReason(), null);
        return crmUpdateRecordService.insertCrmUpdateRecord(crmUpdateRecordDTO);
    }

    /**
     * 根据用户信息组装服务人员
     * @param authUsers 用户信息
     * @param delimiter 分隔符
     * @return 服务人员
     */
    @Override
    public String getServiceUserName(List<AuthUser> authUsers, String delimiter) {
        List<String> contentList = new ArrayList<>();
        authUsers.forEach(user -> {
            String userName = user.getUserName();
            if(StringUtils.isNotBlank(user.getDeptName())){
                userName = userName + "(" + user.getDeptName() + ")";
            }
            contentList.add(userName);
        });
        return ListUtil.joining(contentList, delimiter, user -> user);
    }

    /**
     * 客户下拉
     * @param query 参数
     * @return 下拉
     */
    @Override
    public List<CustomerListVO> pollDown(CustomerQuery query) {
        LambdaQueryWrapper<SupCustomer> queryWrapper = getPollQuery(query);
        List<SupCustomer> supCustomerList = this.list(queryWrapper);
        List<CustomerListVO> customerListVoS = customerConverter.toListVoByEntity(supCustomerList);
        List<Long> customerIdList = ListUtil.distinctMap(customerListVoS, CustomerListVO::getCustomerId);
        if(ListUtil.isEmpty(customerIdList)){
            return ListUtil.emptyList();
        }
        List<CustomerCategory> customerCategoryList = customerCategoryService.selectCustomerCategory(customerIdList);
        if(ListUtil.isNotEmpty(customerCategoryList)){
            JoinUtils.oneByMany(customerListVoS,customerCategoryList,
                    CustomerListVO::getCustomerId,CustomerCategory::getCustomerId,
                    (customer,categoryList) -> {
                        List<Long> list = ListUtil.distinctMap(categoryList, CustomerCategory::getIndustryCategoryId);
                        customer.setIndustryCategoryIdList(list);
                    });
        }
        List<CooperateMainstayListVO> mainstayListVoS = cooperateMainstayService.listCooperateMainByCustomerIds(customerIdList);
        Map<Long, List<CooperateMainstayListVO>> customerIdMainstayMap = ListUtil.toGroup(mainstayListVoS, CooperateMainstayListVO::getCustomerId);
        if(ListUtil.isNotEmpty(customerListVoS)){
            customerListVoS.forEach(customerListVO -> {
                List<CooperateMainstayListVO> cooperateMainstayListVoS = customerIdMainstayMap.get(customerListVO.getCustomerId());
                if(ListUtil.isNotEmpty(cooperateMainstayListVoS)){
                    CooperateMainstayListVO mainstayListVO = ListUtil.first(cooperateMainstayListVoS);
                    customerListVO.setCooperateMainstayId(mainstayListVO.getCooperateMainstayId());
                    customerListVO.setMainstayName(mainstayListVO.getMainstayName());
                }
            });
        }
        return customerListVoS;
    }


    /**
     * 客户下拉模糊搜索
     * @param query 客户名称
     * @return 下拉
     */
    public List<CustomerListVO> pollDownSelect(CustomerNameDownQuery query) {
        if (StringUtils.isEmpty(query.getCusterName())){
            return ListUtil.emptyList();
        }
        LambdaQueryWrapper<SupCustomer> wrapper = Wrappers.<SupCustomer>lambdaQuery()
                .like(StringUtils.isNotBlank(query.getCusterName()), SupCustomer::getName, query.getCusterName())
                .eq(SupCustomer::getAuditStatus, FlowApprovalStatus.APPROVE.getValue())
                .eq(SupCustomer::getDelFlag, DelFlag.NOT_DELETED);
        List<SupCustomer> supCustomerList = this.list(wrapper);
        if (ListUtil.isEmpty(supCustomerList)) {
            return ListUtil.emptyList();
        }
        return customerConverter.toListVoByEntity(supCustomerList);
    }

    /**
     * 根据用户对应的云客户数据权限获取客户下拉
     * @param query 参数
     * @return 下拉数据
     */
    @Override
    public List<CustomerListVO> pollDownUserDataScope(CustomerQuery query) {

        ShowTableNameLambdaQueryWrapper<SupCustomer> queryWrapper = getPollQueryUserDateScope(query);
        queryWrapper.setTableAlias("customer");

        List<SupCustomer> supCustomerList = customerMapper.userCustomerList(query,queryWrapper);
        List<CustomerListVO> customerListVoS = customerConverter.toListVoByEntity(supCustomerList);
        List<Long> customerIdList = ListUtil.distinctMap(customerListVoS, CustomerListVO::getCustomerId);
        if(ListUtil.isEmpty(customerIdList)){
            return ListUtil.emptyList();
        }
        List<CustomerCategory> customerCategoryList = customerCategoryService.selectCustomerCategory(customerIdList);
        if(ListUtil.isNotEmpty(customerCategoryList)){
            JoinUtils.oneByMany(customerListVoS,customerCategoryList,
                    CustomerListVO::getCustomerId,CustomerCategory::getCustomerId,
                    (customer,categoryList) -> {
                        List<Long> list = ListUtil.distinctMap(categoryList, CustomerCategory::getIndustryCategoryId);
                        customer.setIndustryCategoryIdList(list);
                    });
        }
        List<CooperateMainstayListVO> mainstayListVoS = cooperateMainstayService.listCooperateMainByCustomerIds(customerIdList);
        Map<Long, List<CooperateMainstayListVO>> customerIdMainstayMap = ListUtil.toGroup(mainstayListVoS, CooperateMainstayListVO::getCustomerId);
        if(ListUtil.isNotEmpty(customerListVoS)){
            customerListVoS.forEach(customerListVO -> {
                List<CooperateMainstayListVO> cooperateMainstayListVoS = customerIdMainstayMap.get(customerListVO.getCustomerId());
                if(ListUtil.isNotEmpty(cooperateMainstayListVoS)){
                    CooperateMainstayListVO mainstayListVO = ListUtil.first(cooperateMainstayListVoS);
                    customerListVO.setCooperateMainstayId(mainstayListVO.getCooperateMainstayId());
                    customerListVO.setMainstayName(mainstayListVO.getMainstayName());
                }
            });
        }
        return customerListVoS;
    }

    /**
     * 获取详情
     * @param id 客户id
     * @return 客户
     */
    @Override
    public SupCustomer getInfo(Long id) {
        return this.getById(id);
    }

    /**
     * 客户审核修改状态
     * @param dto 参数
     * @return 结果
     */
    @Override
    public Boolean auditAfter(CustomerDTO dto) {
        if(dto.getCustomerId() == null || dto.getAuditStatus() == null) {
            throw new ServiceException("必填为空");
        }
        LambdaUpdateWrapper<SupCustomer> updateWrapper = Wrappers.<SupCustomer>lambdaUpdate()
                .set(SupCustomer::getAuditStatus, dto.getAuditStatus())
                .eq(SupCustomer::getCustomerId, dto.getCustomerId());
        if(dto.getAuditStatus().equals(FlowApprovalStatus.APPROVE.getValue())){
            // 新增客户管理-客户流转记录
            SupCustomer info = this.getInfo(dto.getCustomerId());
            WanderAboutDTO wanderAboutDTO = new WanderAboutDTO();
            wanderAboutDTO.setCustomerId(dto.getCustomerId());
            wanderAboutDTO.setUserId(info.getCreateId());
            wanderAboutDTO.setIsDistribution(true);
            wanderAboutDTO.setIsCreate(true);
            wanderAboutService.insertWanderAbout(wanderAboutDTO);

            // 审核通过修改企业联系人
            contactService.auditAfter(dto.getCustomerId());
            updateWrapper.set(SupCustomer::getAuditTime, LocalDateTime.now());
            updateWrapper.set(SupCustomer::getCreateTime, LocalDateTime.now());
        }
        return this.update(updateWrapper);
    }

    /**
     * 客户统计
     * @return 客户统计
     */
    @Override
    public CustomerCountVO customerCount(CustomerQuery query) {
        return customerMapper.customerCount(query);
    }

    /**
     * 客户贡献列表按成交金额倒序
     * @param query 查询条件
     * @return
     */
    @Override
    public List<SupCustomer> getCustomerDevoteList(CustomerDevoteQuery query) {
        LambdaQueryWrapper<SupCustomer> customerDataScopeWrapper = Wrappers.lambdaQuery(SupCustomer.class);
        DataScopeUtils.setDateScope(customerDataScopeWrapper, DataScopePageType.CRM_CustomerManage, context -> {
            context.setUserTableAlias("py_crm_wander_about");
            context.setUserIdAlias("user_id");
            context.setDateTableAlias("customer");
            context.setDateAlias("audit_time");
        });
        customerDataScopeWrapper.setParamAlias("customerDataScopeWrapper");
        return customerMapper.getCustomerDevoteList(query, customerDataScopeWrapper);
    }

    /**
     * 比稿金额
     * @param query  客户id列表-结案时间-合作时间
     * @return
     */
    @Override
    public List<ComparedDraftMoneyVO> getCustomerDevoteProjectList(CustomerDevoteQuery query) {
        //没有合作时间,结案时间就查客户的比稿金额,否则就关联项目
        if (StringUtils.isBlank(query.getCooperateBeginTime()) && StringUtils.isBlank(query.getCooperateEndTime())
        && StringUtils.isBlank(query.getCloseCaseBeginTime()) && StringUtils.isBlank(query.getCloseCaseEndTime())){
            return customerMapper.getCustomerComparedList(query);
        }else {
            return customerMapper.getCustomerDevoteProjectList(query);
        }
    }
    /**
     * 查询项目id
     * @param query
     * @return
     */
    @Override
    public List<Long> getCustomerDevoteProjectIdList(CustomerDevoteQuery query) {
        return customerMapper.getCustomerDevoteProjectIdList(query);
    }
    /**
     * 结案金额
     * @param projectIds
     * @return
     */
    @Override
    public List<CloseCaseMoneyListVO>  getCloseCaseMoneyMap(List<Long> projectIds) {
        List<CloseCaseMoneyListVO> closeProfitMarginList = customerMapper.getCloseProfitMarginList(projectIds);
        List<CloseCaseMoneyListVO> closeCaseMoneyList = customerMapper.getCloseCaseMoneyList(projectIds);
        //把 结案金额的数据集合赋值到毛利率集合
        JoinUtils.oneByOne(closeProfitMarginList,closeCaseMoneyList,
                CloseCaseMoneyListVO::getCustomerId,CloseCaseMoneyListVO::getCustomerId,
                (closeProfitMargin, closeCaseMoney) -> closeProfitMargin.setCloseCaseMoney(closeCaseMoney.getCloseCaseMoney()));
        return closeProfitMarginList;
    }

    /**
     * 坏账金额
     * @param query
     * @return
     */
    @Override
    public  List<CustomerDevoteListVO> getBadeMoneyMap(CustomerDevoteQuery query) {
        return customerMapper.getBadeMoneyMap(query);
    }

    /**
     * 人脉客户下拉(包括从人脉中获取的客户数据)
     * @param query
     * @return
     */
    @Override
    public List<String> getPollConnectionDown(CustomerQuery query) {
        //获取客户中的所有的客户名称
        List<SupCustomer> supCustomerList = this.list(Wrappers.<SupCustomer>lambdaQuery()
                .select(SupCustomer::getName, SupCustomer::getCustomerId)
                .like(StringUtils.isNotBlank(query.getName()), SupCustomer::getName, query.getName())
                .eq(SupCustomer::getAuditStatus,FlowApprovalStatus.APPROVE.getValue())
                .eq(SupCustomer::getDelFlag, DelFlag.NOT_DELETED));

        List<String> nameList = new ArrayList<>();
        if(ListUtil.isNotEmpty(supCustomerList)) {
            nameList = ListUtil.distinctMap(supCustomerList, SupCustomer::getName);
        }

        //获取所有人脉基本信息中的的任职企业名称
        List<String> currentEmployerList = connectionService.listCurrentEmployerList(query.getName());
        nameList.addAll(currentEmployerList);

        //获取所有人脉从业经历中的的任职企业名称
        List<String> employerNameList = connectionEmploymentService.listCurrentEmployerList(query.getName());
        nameList.addAll(employerNameList);

        //去重返回
        return nameList.stream()
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取客户管理中该客户下的品牌/业务线列表
     * @param name
     * @param responsibleBrand
     * @return
     */
    @Override
    public List<String> listResponsibleBrandByName(String name, String responsibleBrand) {
        //获取客户中的所有的客户名称
        List<SupCustomer> supCustomerList = this.list(Wrappers.<SupCustomer>lambdaQuery()
                .select(SupCustomer::getName, SupCustomer::getLineBusiness)
                .like(StringUtils.isNotBlank(responsibleBrand), SupCustomer::getLineBusiness, responsibleBrand)
                .eq(SupCustomer::getName, name)
                .eq(SupCustomer::getAuditStatus, FlowApprovalStatus.APPROVE.getValue())
                .eq(SupCustomer::getDelFlag, DelFlag.NOT_DELETED));

        List<String> nameList = new ArrayList<>();
        if(ListUtil.isNotEmpty(supCustomerList)) {
            nameList = ListUtil.distinctMap(supCustomerList, SupCustomer::getLineBusiness);
        }
        return nameList;
    }

    /**
     * 根据企业名称和负责品牌业务线获取企业id
     * @param name
     * @param lineBusinessList
     * @return
     */
    @Override
    public Long getCustomerId(String name, List<String> lineBusinessList) {
        if(StringUtils.isBlank(name) || ListUtil.isEmpty(lineBusinessList) || lineBusinessList.size() > 1) {
            return null;
        }

        //根据企业名称和负责品牌业务线获取企业信息
        SupCustomer customer = this.getOne(Wrappers.lambdaQuery(SupCustomer.class)
                .eq(SupCustomer::getName, name)
                .eq(SupCustomer::getLineBusiness, lineBusinessList.get(0)));
        if(Objects.isNull(customer)) {
            return null;
        }
        return customer.getCustomerId();
    }

    /**
     * 根据客户ID和在职手机号查询其他关联的在职的客户并将在职改为离职
     * @param customerId 客户ID
     * @param phoneList 手机号
     * @return 客户信息
     */
    @Override
    public Map<String,SupCustomer> listCustomerByPhoneId(Long customerId, List<String> phoneList) {
        List<SupContact> supContactList = contactService.listContactByByPhoneId(customerId,phoneList);
        contactService.updateContactLeaveOffice(supContactList);
        if(ListUtil.isEmpty(supContactList)){
            return ListUtil.emptyMap();
        }
        List<Long> customerIdList = ListUtil.distinctMap(supContactList, SupContact::getCustomerId);
        List<SupCustomer> supCustomerList = this.listByIds(customerIdList);
        List<CustomerCategory> customerCategoryList = customerCategoryService.selectCustomerCategory(customerIdList);
        Map<Long, List<Long>> customerCategoryMap = ListUtil.toGroup(customerCategoryList, CustomerCategory::getCustomerId,CustomerCategory::getIndustryCategoryId);
        Map<Long, SupCustomer> supCustomerMap = ListUtil.toMap(supCustomerList, SupCustomer::getCustomerId);
        Map<String,SupCustomer> phoneMap = new HashMap<>(supContactList.size());
        supContactList.forEach(supContact -> {
            SupCustomer supCustomer = supCustomerMap.get(supContact.getCustomerId());
            if(supCustomer != null){
                List<Long> list = customerCategoryMap.get(supCustomer.getCustomerId());
                supCustomer.setIndustryCategoryIdList(list);
                phoneMap.put(supContact.getPhone(),supCustomer);
            }
        });
        return phoneMap;
    }

    /**
     * 客户名称模糊检索
     * 业务线模糊检索
     * @param query 客户名称,业务线名称
     * @return 客户id,客户名称,业务线
     */
    @Override
    public List<CustomerLineBusinessVO> lineBusinessByCustomer(CustomerLineBusinessQuery query) {
        LambdaQueryWrapper<SupCustomer> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtils.isNotEmpty(query.getName()),SupCustomer::getName,query.getName())
               .like(StringUtils.isNotEmpty(query.getLineBusiness()),SupCustomer::getLineBusiness,query.getLineBusiness())
                .eq(StringUtils.isNotNull(query.getCooperationStatus()),SupCustomer::getCooperationStatus,query.getCooperationStatus())
                .eq(SupCustomer::getDelFlag,DelFlag.NOT_DELETED.getValue())
                .eq(SupCustomer::getAuditStatus,FlowApprovalStatus.APPROVE.getValue())
                .select(SupCustomer::getCustomerId,SupCustomer::getName,SupCustomer::getLineBusiness,SupCustomer::getCooperationStatus);
        List<SupCustomer> supCustomerList = this.list(wrapper);
        if (ListUtil.isEmpty(supCustomerList)){
            return ListUtil.emptyList();
        }
        return this.customerConverter.toLineBusinessListVoByEntityList(supCustomerList);
    }


    /**
     * 客户管理-客户列表,模糊搜索列表,服务人部门集合
     *
     * @param query 客户管理-客户查询参数(服务人部门名称)
     * @return 客户管理-客户列表,部门集合
     */
    @Override
    public List<String> findCustomerServiceDeptList(CustomerQuery query) {
        ShowTableNameLambdaQueryWrapper<SupCustomer> queryWrapper = new ShowTableNameLambdaQueryWrapper<>(SupCustomer.class);
        queryWrapper.eq(query.getCooperationStatus() != null, SupCustomer::getCooperationStatus, query.getCooperationStatus())
                .eq(SupCustomer::getAuditStatus, FlowApprovalStatus.APPROVE.getValue())
                .eq(SupCustomer::getDelFlag, DelFlag.NOT_DELETED.getValue());
        queryWrapper.setTableAlias("customer");


        List<SupCustomer> supCustomerList = customerMapper.findCustomerServiceDeptList(query, queryWrapper);
        if (ListUtil.isEmpty(supCustomerList)) {
            return new ArrayList<>();
        }
        return supCustomerList.stream()
                .map(supCustomer -> Arrays.asList(supCustomer.getCreateDept().split(",")))
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 客户管理-客户列表,模糊搜索列表,录入人部门集合
     *
     * @param query 客户管理-客户查询参数(录入人部门名称)
     * @return 客户管理-客户列表,部门集合
     */
    @Override
    public List<String> findCustomerCreateDeptList(CustomerQuery query) {
        ShowTableNameLambdaQueryWrapper<SupCustomer> queryWrapper = new ShowTableNameLambdaQueryWrapper<>(SupCustomer.class);
        queryWrapper.eq(query.getCooperationStatus() != null, SupCustomer::getCooperationStatus, query.getCooperationStatus())
                .eq(SupCustomer::getAuditStatus, FlowApprovalStatus.APPROVE.getValue())
                .eq(SupCustomer::getDelFlag, DelFlag.NOT_DELETED.getValue())
                .isNotNull(SupCustomer::getCreateDept);
        queryWrapper.setTableAlias("customer");


        List<SupCustomer> supCustomerList = customerMapper.findCustomerCreateDeptList(query, queryWrapper);
        if(ListUtil.isEmpty(supCustomerList)){
            return new ArrayList<>();
        }
        List<CustomerListVO> customerVoList = this.customerConverter.toListVoByEntity(supCustomerList);
        this.setServiceUserCategory(customerVoList);
        return supCustomerList.stream()
                .map(supCustomer -> Arrays.asList(supCustomer.getCreateDept().split(",")))
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 人脉客户下拉
     * @param query 参数
     * @return 下拉
     */
    @Override
    public List<CustomerConnectionVO> pollConnectionDown(CustomerQuery query) {
        LambdaQueryWrapper<SupCustomer> queryWrapper = getPollQuery(query);
        List<SupCustomer> supCustomerList = this.list(queryWrapper);
        if(ListUtil.isEmpty(supCustomerList)){
            return ListUtil.emptyList();
        }
        List<CustomerConnectionVO> customerConnectionVoS = customerConverter.toCustomerConnectionVOByEntity(supCustomerList);
        // 行业类目
        List<Long> customerIdList = ListUtil.distinctMap(customerConnectionVoS, CustomerConnectionVO::getCustomerId);
        List<SysDictData> sysDictData = sysDictDataService.selectAllByType(DictConstant.INDUSTRY_CATEGORY);
        Map<String, String> valueLabelMap = ListUtil.toMap(sysDictData, SysDictData::getDictValue,SysDictData::getDictLabel);
        List<CustomerCategory> customerCategoryList = customerCategoryService.selectCustomerCategory(customerIdList);
        if(ListUtil.isNotEmpty(customerCategoryList)){
            JoinUtils.oneByMany(customerConnectionVoS,customerCategoryList,
                    CustomerConnectionVO::getCustomerId,CustomerCategory::getCustomerId,
                    (customer,categoryList) -> {
                        List<CategoryCustomer> categoryCustomerList = new ArrayList<>();
                        categoryList.forEach(customerCategory -> {
                            CategoryCustomer categoryCustomer = new CategoryCustomer();
                            categoryCustomer.setIndustryCategoryId(customerCategory.getIndustryCategoryId());
                            String dictLabel = valueLabelMap.get(String.valueOf(customerCategory.getIndustryCategoryId()));
                            categoryCustomer.setIndustryCategoryName(dictLabel);
                            categoryCustomerList.add(categoryCustomer);
                        });
                        customer.setIndustryCategoryList(categoryCustomerList);
                    });
        }

        Map<String, List<CustomerConnectionVO>> nameListMap = ListUtil.toGroup(customerConnectionVoS, CustomerConnectionVO::getName);
        // 下拉品牌/业务线下拉选择
        List<CustomerConnectionVO> customerConnectionVOList = new ArrayList<>();
        nameListMap.keySet().forEach(name -> {
            List<CustomerConnectionVO> list = nameListMap.get(name);
            CustomerConnectionVO customerConnectionVO = new CustomerConnectionVO();
            customerConnectionVO.setCustomerId(ListUtil.firstOrThrow(list).getCustomerId());
            customerConnectionVO.setName(name);
            List<String> lineBusinessList = ListUtil.distinctMap(list, CustomerConnectionVO::getLineBusiness);
            customerConnectionVO.setLineBusinessList(lineBusinessList);
            List<List<CategoryCustomer>> industryCategoryList = ListUtil.distinctMap(list, CustomerConnectionVO::getIndustryCategoryList);
            List<CategoryCustomer> categoryCustomers = ListUtil.flatDistinct(industryCategoryList);
            customerConnectionVO.setIndustryCategoryList(categoryCustomers);
            customerConnectionVOList.add(customerConnectionVO);
        });

        return customerConnectionVOList;
    }

    /**
     * 全选时根据状态和客户id查询客户id
     * @param customerIdList 客户id
     * @param query 查询条件
     * @return customerId
     */
    @Override
    public List<Long> listAllCustomerIdByStatus(List<Long> customerIdList, CustomerQuery query) {
        PageInfo<CustomerListVO> customerListVoPageInfo = this.pageCustomerList(query);
        return ListUtil.distinctMap(customerListVoPageInfo.getList(),CustomerListVO::getCustomerId);
    }

    /**
     * 根据客户ID查询客户信息
     * @param customerIdList 客户ID
     * @return 客户信息
     */
    @Override
    public List<SupCustomer> listCustomerIdByCustomerIds(List<Long> customerIdList) {
        return this.customerMapper.listCustomerIdByCustomerIds(customerIdList);
    }

    /**
     * 获取下拉选择的 wrapper
     * @param query 参数
     * @return wrapper
     */
    private LambdaQueryWrapper<SupCustomer> getPollQuery(CustomerQuery query) {
        return Wrappers.<SupCustomer>lambdaQuery()
                .like(StringUtils.isNotBlank(query.getName()), SupCustomer::getName, query.getName())
                .like(StringUtils.isNotBlank(query.getLineBusiness()), SupCustomer::getLineBusiness, query.getLineBusiness())
                .eq(query.getCooperationStatus() != null ,SupCustomer::getCooperationStatus,query.getCooperationStatus())
                .eq(SupCustomer::getAuditStatus,FlowApprovalStatus.APPROVE.getValue())
                .eq(SupCustomer::getDelFlag, DelFlag.NOT_DELETED);
    }

    /**
     * 获取下拉选择的 wrapper
     * @param query 参数
     * @return wrapper
     */
    private ShowTableNameLambdaQueryWrapper<SupCustomer> getPollQueryUserDateScope(CustomerQuery query){
        ShowTableNameLambdaQueryWrapper<SupCustomer> queryWrapper = new ShowTableNameLambdaQueryWrapper<>(SupCustomer.class);
        queryWrapper.like(StringUtils.isNotBlank(query.getName()), SupCustomer::getName, query.getName())
                .like(StringUtils.isNotBlank(query.getLineBusiness()), SupCustomer::getLineBusiness, query.getLineBusiness())
                .eq(query.getCooperationStatus() != null ,SupCustomer::getCooperationStatus,query.getCooperationStatus())
                .eq(SupCustomer::getAuditStatus,FlowApprovalStatus.APPROVE.getValue())
                .eq(SupCustomer::getDelFlag, DelFlag.NOT_DELETED);
        return queryWrapper;
    }

    /**
     * 导入
     * @param query 查询条件
     */
    private void importAssignment(CustomerQuery query) {
        taskRecodeService.editTaskBegin(query.getTaskId());
        ImportTaskResult importTaskResult = new ImportTaskResult();
        importTaskResult.setSucceedFileName(query.getFileName());

        query.setUserName(SecurityUtils.getUsername());
        try {
            ExcelUtil<CustomerExportModel> util = new ExcelUtil<>(CustomerExportModel.class);
            byte[] download = ossService.download(query.getFileKey());
            InputStream inputStream = new ByteArrayInputStream(download);
            List<CustomerExportModel> customerExportModelList = util.importExcel(inputStream,0);
            // 校验是否为空
            List<RowVerifyError<CustomerExportModel>> rowVerifyErrors = objectValidator.verifyImportList(customerExportModelList, CustomerExportModel.getVerifyConfig());

            // 判断重复
            this.checkDuplicates(customerExportModelList, rowVerifyErrors);

            // 获取客户名和品牌/业务线
            List<String> nameList = ListUtil.distinctMap(customerExportModelList, CustomerExportModel::getName);
            LambdaQueryWrapper<SupCustomer> queryWrapper = Wrappers.<SupCustomer>lambdaQuery().in(SupCustomer::getName, nameList);
            List<SupCustomer> supCustomerList = customerMapper.pageCustomerList(query, queryWrapper);

            Map<String, List<SupCustomer>> nameCustomerMap = ListUtil.toGroup(supCustomerList, SupCustomer::getName);

            // 错误的序号
            Set<Integer> rowNoSet = ListUtil.toSet(rowVerifyErrors, RowVerifyError::getRowNo);
            // 正确的数据
            List<CustomerExportModel> customerSuccessExportModelList = new ArrayList<>();
            for(CustomerExportModel customerExportModel : customerExportModelList) {
                List<String> errorList = new ArrayList<>();
                if(StringUtils.isNotBlank(customerExportModel.getName())) {
                    // 获取客户id
                    this.getCustomerId(nameCustomerMap, customerExportModel, errorList);
                }
                if(StringUtils.isNotBlank(customerExportModel.getServiceUser())) {
                    // 获取服务人员id
                    this.getServiceUser(customerExportModel, errorList);
                }
                if(ListUtil.isNotEmpty(errorList)) {
                    rowVerifyErrors.add(new RowVerifyError<>(customerExportModel.getSerialNumber(), customerExportModel, errorList));
                    continue;
                } else {
                    if(!rowNoSet.contains(customerExportModel.getSerialNumber())){
                        customerSuccessExportModelList.add(customerExportModel);
                    }
                }
            }
            // 新增分配记录
            List<CrmUpdateRecordDTO> updateRecordList = new ArrayList<>();
            if(ListUtil.isNotEmpty(customerSuccessExportModelList)){
                List<WanderAboutDTO> wanderAboutDTOList = new ArrayList<>();
                customerSuccessExportModelList.forEach(customerExportModel -> {
                    customerExportModel.getServiceUserIdList().forEach(userId -> {
                        WanderAboutDTO wanderAboutDTO = new WanderAboutDTO();
                        wanderAboutDTO.setCustomerId(customerExportModel.getCustomerId());
                        wanderAboutDTO.setUserId(userId);
                        wanderAboutDTO.setIsDistribution(true);
                        wanderAboutDTOList.add(wanderAboutDTO);
                    });
                    // 分配记录
                    List<AuthUser> authUsers = userService.selectUserById(customerExportModel.getServiceUserIdList());
                    // 根据用户信息组装服务人员
                    String content = getServiceUserName(authUsers,"，");

                    CrmUpdateRecordDTO crmUpdateRecordDTO = new CrmUpdateRecordDTO();
                    crmUpdateRecordDTO.addEvent(RecordBizType.CUSTOMER.getValue(), customerExportModel.getCustomerId()
                            , content, RecordMenuType.CIRCULATION.getValue(), null, null);
                    updateRecordList.add(crmUpdateRecordDTO);
                });
                //批量新增客户管理-客户流转
                wanderAboutService.insertBatchWanderAbout(wanderAboutDTOList);
                //批量新增客户/人脉(状态)更新记录表
                crmUpdateRecordService.insertBatchCrmUpdateRecord(updateRecordList);
            }
            importTaskResult.setSucceed(customerExportModelList.size());
            importTaskResult.setIsSuccess(true);
            if(ListUtil.isNotEmpty(rowVerifyErrors)){
                rowVerifyErrors = RowVerifyError.mergeSameRowNoError(rowVerifyErrors);
                // 排序
                List<RowVerifyError<CustomerExportModel>> verifyErrors = rowVerifyErrors.stream().sorted(Comparator.comparing(RowVerifyError::getRowNo))
                        .collect(Collectors.toList());
                String fileName = "客户管理批量分配错误信息.xlsx";
                ExcelSheetConfig<RowVerifyError<CustomerExportModel>> excelConfig = this.customerErrorExcelErrorConfig();
                //导出错误
                MultiSheetExcelExporter excelExporter = new MultiSheetExcelExporter();
                excelExporter.addSheetConfig(verifyErrors, excelConfig, "客户管理");
                byte[] bytes = excelExporter.exportExcelToByte("客户管理批量分配错误信息.xlsx");
                String file = this.ossService.uploadFileToOss(bytes, fileName);
                importTaskResult.setIsSuccess(false);
                importTaskResult.setSucceed(importTaskResult.getSucceed() - verifyErrors.size());
                importTaskResult.setError(verifyErrors.size());
                importTaskResult.setKey(file);
                importTaskResult.setErrorFileName(fileName);
                taskRecodeService.taskImportFailed(query.getTaskId(),importTaskResult);
                return;
            }
            taskRecodeService.taskImportCompleted(query.getTaskId(),importTaskResult);
        } catch(Exception e){
            log.error("客户批量分配错误：" + e);
            importTaskResult.setIsSuccess(false);
            taskRecodeService.taskImportFailed(query.getTaskId(),importTaskResult);
        }

    }

    /**
     * 批量分配错误信息模版
     * @return 模版
     */
    private ExcelSheetConfig<RowVerifyError<CustomerExportModel>> customerErrorExcelErrorConfig() {
        ExcelSheetConfig<RowVerifyError<CustomerExportModel>> config = new ExcelSheetConfig<>();
        config.setNeedSerialNumber(false)
                .addConfig(RowVerifyError::getErrorInfo, "错误信息提示")
                //设置颜色
                .appendStyleModifier(style -> style.setFillForegroundColor(IndexedColors.RED.index))
                // 设置资源信息导出信息
                .includeSubConfig(RowVerifyError::getErrorObject,this.getCustomerTemplateConfig());
        return config;
    }

    /**
     * 批量分配模版
     * @return 模版
     */
    @Override
    public ExcelSheetConfig<CustomerExportModel> getCustomerTemplateConfig() {
        ExcelSheetConfig<CustomerExportModel> config = new ExcelSheetConfig<>();
        config.setNeedSerialNumber(false)
                .addConfig(CustomerExportModel::getName,"*客户名称")
                .addConfig(CustomerExportModel::getLineBusiness,"品牌/业务线")
                .addConfig(CustomerExportModel::getServiceUser,"*服务人员");
        return config;
    }

    /**
     * 校验重复
     * @param customerExportModelList 导入数据
     * @param rowVerifyErrors 错误信息
     */
    private void checkDuplicates(List<CustomerExportModel> customerExportModelList, List<RowVerifyError<CustomerExportModel>> rowVerifyErrors) {
        Set<String> nameSet = new HashSet<>();
        Set<String> lineBusinessSet = new HashSet<>();
        customerExportModelList.forEach(customerExportModel -> {
            if(StringUtils.isNotBlank(customerExportModel.getName())
                    && nameSet.contains(customerExportModel.getName())){
                if(StringUtils.isNotBlank(customerExportModel.getLineBusiness())
                        && lineBusinessSet.contains(customerExportModel.getLineBusiness())){
                    rowVerifyErrors.add(new RowVerifyError<>(customerExportModel.getSerialNumber(),
                            customerExportModel,Collections.singletonList("客户名称和品牌/业务线重复")));
                }
            }
            nameSet.add(customerExportModel.getName());
            lineBusinessSet.add(customerExportModel.getLineBusiness());
        });
    }

    /**
     * 获取客户ID
     * @param nameCustomerMap <客户名称,客户信息>
     * @param customerExportModel 客户分配导入数据
     * @param errorList 错误信息
     */
    private void getCustomerId(Map<String, List<SupCustomer>> nameCustomerMap, CustomerExportModel customerExportModel, List<String> errorList) {
        List<SupCustomer> supCustomers = nameCustomerMap.get(customerExportModel.getName());
        if(ListUtil.isEmpty(supCustomers)) {
            errorList.add("客户名称不存在");
        } else {
            if(!ListUtil.isSingleton(supCustomers)){
                if(StringUtils.isBlank(customerExportModel.getLineBusiness())){
                    errorList.add("客户名称重复请填写品牌/业务线");
                } else {
                    Map<String, SupCustomer> lineBusinessMap = ListUtil.toMap(supCustomers, SupCustomer::getLineBusiness);
                    SupCustomer supCustomer = lineBusinessMap.get(customerExportModel.getLineBusiness());
                    if(supCustomer == null){
                        errorList.add("品牌/业务线不存在");
                    } else {
                        customerExportModel.setCustomerId(supCustomer.getCustomerId());
                    }
                }
            } else {
                SupCustomer supCustomer = ListUtil.firstOrThrow(supCustomers);
                customerExportModel.setCustomerId(supCustomer.getCustomerId());
            }
        }
    }

    /**
     * 获取服务人员id
     * @param customerExportModel 客户分配导入数据
     * @param errorList 错误信息
     */
    private void getServiceUser(CustomerExportModel customerExportModel, List<String> errorList) {
        String serviceUser = customerExportModel.getServiceUser();
        List<String> userNameList = new ArrayList<>();
        String[] userName = serviceUser.split("、");
        // 获取用户名
        for(String user : userName) {
            String[] userPhone  = user.split(",");
            userNameList.add(userPhone[0]);
        }
        List<SysUser> sysUserList = userService.listUserByName(userNameList);
        Map<String, List<SysUser>> userNameMap = ListUtil.toGroup(sysUserList, SysUser::getUserName);
        // 获取服务人员id
        List<Long> serviceUserIdList = new ArrayList<>();
        for(String user : userName) {
            String[] userPhone  = user.split(",");
            List<SysUser> sysUsers = userNameMap.get(userPhone[0]);
            if(ListUtil.isEmpty(sysUsers)){
                errorList.add("服务人员不存在");
            } else {
                if(!ListUtil.isSingleton(sysUsers)){
                    if(userPhone.length != 2){
                        errorList.add("服务人员重复请填写手机号");
                    } else {
                        // 判断手机号是否存在
                        String phone = userPhone[1];
                        Map<String, SysUser> userPhoneMap = ListUtil.toMap(sysUsers, SysUser::getPhoneNumber);
                        SysUser sysUser = userPhoneMap.get(phone);
                        if(sysUser == null){
                            errorList.add("手机号不存在");
                        } else {
                            serviceUserIdList.add(sysUser.getUserId());
                        }
                    }
                } else {
                    SysUser sysUser = ListUtil.firstOrThrow(sysUsers);
                    serviceUserIdList.add(sysUser.getUserId());
                }
            }
        }
        customerExportModel.setServiceUserIdList(serviceUserIdList);
    }


    /**
     * 执行任务
     * @param args 任务参数
     */
    @Override
    public void execute(CustomerQuery args) {
        this.importAssignment(args);
    }

    /**
     * 获取任务参数类型
     * @return 任务参数类型
     */
    @Override
    public Class<CustomerQuery> argsClass() {
        return CustomerQuery.class;
    }
}
