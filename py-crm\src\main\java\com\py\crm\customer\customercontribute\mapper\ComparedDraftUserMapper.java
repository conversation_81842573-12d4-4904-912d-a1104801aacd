package com.py.crm.customer.customercontribute.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.py.crm.customer.customercontribute.domain.ComparedDraftUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户管理-比稿策划人关联Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
public interface ComparedDraftUserMapper extends BaseMapper<ComparedDraftUser> {

    /**
     * 根据策划人id查询比稿项目id
     * @param userIdList 策划人id
     * @param pageNum 当前记录起始索引
     * @param pageSize 每页显示记录数
     * @return 比稿项目id
     */
    List<Long> listComparedDraftIdByUserIds(@Param("userIdList") List<Long> userIdList,@Param("pageNum") Integer pageNum,@Param("pageSize") Integer pageSize);
}
