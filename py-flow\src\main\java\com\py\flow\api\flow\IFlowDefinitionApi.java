package com.py.flow.api.flow;

import com.py.flow.domain.enums.ApprovalBizType;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.repository.ProcessDefinition;

import java.io.InputStream;

/**
 * 流程定义API
 * <AUTHOR>
 * @date 2023/7/14 10:57
 */
public interface IFlowDefinitionApi {

    /**
     * 新增流程定义
     * @param deploymentName 部署的流程名称
     * @param approvalBizType 流程关联的审批业务
     * @param flowVersionId 关联的流程版本ID
     * @param bpmnXml 流程定义的xml
     * @return 流程定义
     */
    ProcessDefinition addProcessDefinition(String deploymentName, ApprovalBizType approvalBizType, Long flowVersionId, String bpmnXml);

    /**
     * 获取流程版本流程设计信息
     *
     * @param flowManageVersionId
     */
    InputStream getFlowVersionFlowImage(Long flowManageVersionId);

    /**
     * 获取流程设计xml文件
     * @param processDeploymentId 流程部署ID
     * @return 流程Xml
     */
    String getFlowBpmnXml(String processDeploymentId);


    /**
     * 获取流程Bpmn模型
     * @param processDefinitionId 流程定义ID
     * @return 流程Bpmn模型
     */
    BpmnModel getProcessBpmnModel(String processDefinitionId);
}
