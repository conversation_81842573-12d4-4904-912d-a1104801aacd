package com.py.flow.flowinstance.approvalsnapshot.service.mock;

import com.py.flow.domain.enums.ApprovalBizType;
import com.py.flow.flowinstance.approvalsnapshot.IApprovalBizInfoHandler;
import org.springframework.stereotype.Component;

/**
 * 测试审批业务详情服务
 * <AUTHOR>
 */
@Component
public class TestApprovalBizInfoHandler implements IApprovalBizInfoHandler {
    /**
     * 获取支持的审批业务类型
     * @return 审批业务类型
     */
    @Override
    public ApprovalBizType getSupport() {
        return ApprovalBizType.Test;
    }

    /**
     * 获取审批业务详情
     * @param bizId 审批业务ID
     * @return 审批业务详情
     */
    @Override
    public Object getApprovalBizInfo(Long bizId) {
        return bizId;
    }
}
