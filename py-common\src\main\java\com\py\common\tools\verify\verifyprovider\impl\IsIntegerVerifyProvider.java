package com.py.common.tools.verify.verifyprovider.impl;

import com.py.common.tools.verify.enums.VerifyType;
import com.py.common.tools.verify.verifyprovider.VerifyProvider;
import com.py.common.utils.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 输入为正整数验证提供者
 * <AUTHOR>
 */
@Component
public class IsIntegerVerifyProvider implements VerifyProvider {

    /**
     * 支持的验证类型
     * @return 支持的验证类型
     */
    @Override
    public VerifyType supportedVerifyType() {
        return VerifyType.IsInteger;
    }

    /**
     * 验证
     * @param target 验证目标
     * @return 验证结果 true:验证通过  false:验证失败
     * @throws Exception 验证异常
     */
    @Override
    public boolean verify(Object target) throws Exception {
        if(target == null) {
            return true;
        }

        if(target instanceof String == false) {
            throw new IllegalArgumentException("只有字符串才能使用 VerifyType.IsInteger 验证");
        }
        String value = (String) target;
        if(StringUtils.isBlank(value)) {
            return true;
        }

        return StringUtils.isNumeric(value);
    }
}
