package com.py.common.config;

import com.baomidou.mybatisplus.core.toolkit.Sequence;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 流程id生成处理
 *
 * <AUTHOR>
 * @date 2023/7/14 10:56
 */
@Configuration
public class FlowableConfig implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {

    /** 主键生成器 */
    @Resource
    private Sequence sequence;

    @Override
    public void configure(SpringProcessEngineConfiguration engineConfiguration) {
        engineConfiguration.setActivityFontName("宋体");
        engineConfiguration.setLabelFontName("宋体");
        engineConfiguration.setAnnotationFontName("宋体");
        engineConfiguration.setIdGenerator((() -> Long.toString(this.sequence.nextId())));
    }

}
