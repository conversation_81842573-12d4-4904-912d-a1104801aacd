package com.py.common.typehandler;

import com.py.common.utils.JsonUtil;
import com.py.common.utils.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * 基础列表类型处理
 * <AUTHOR>
 */
public abstract class BaseListTypeHandler<T> extends BaseTypeHandler<List<T>> {

    @Override
    public void setNonNullParameter(
            PreparedStatement preparedStatement,
            int i,
            List<T> list,
            JdbcType jdbcType) throws SQLException {
        preparedStatement.setString(i, JsonUtil.obj2String(list));
    }

    @Override
    public List<T> getNullableResult(ResultSet resultSet, String columnName) throws SQLException {
        return this.deserializeFieldList(resultSet.getString(columnName));
    }

    @Override
    public List<T> getNullableResult(ResultSet resultSet, int columnIndex) throws SQLException {
        return this.deserializeFieldList(resultSet.getString(columnIndex));
    }

    @Override
    public List<T> getNullableResult(CallableStatement callableStatement, int columnIndex) throws SQLException {
        return this.deserializeFieldList(callableStatement.getString(columnIndex));
    }

    /**
     * 元素类型
     * @return 元素类型
     */
    protected abstract Class<T> getItemClass();

    /**
     * 反序列化接口字段列表
     * @param fieldListJson 接口字段列表JSON
     * @return 接口字段列表对象
     */
    private List<T> deserializeFieldList(String fieldListJson){
        if(StringUtils.isBlank(fieldListJson)){
            return null;
        }
        return JsonUtil.string2Obj(fieldListJson, ArrayList.class, this.getItemClass());
    }
}
