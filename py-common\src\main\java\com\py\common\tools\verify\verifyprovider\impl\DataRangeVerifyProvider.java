package com.py.common.tools.verify.verifyprovider.impl;

import com.py.common.tools.verify.enums.VerifyType;
import com.py.common.tools.verify.verifypackage.DataRangeVerifyPackage;
import com.py.common.tools.verify.verifyprovider.VerifyProvider;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;

/**
 * 数据范围验证提供者
 * <AUTHOR>
 */
@Configuration
public class DataRangeVerifyProvider {

    /** 提供在数据数据范围内验证提供者的Bean */
    @Bean
    public InDataRangeVerifyProvider inDataRangeVerifyProvider() {
        return new InDataRangeVerifyProvider();
    }

    /** 提供不在数据数据范围内验证提供者的Bean */
    @Bean
    public NotInDataRangeVerifyProvider notInDataRangeVerifyProvider() {
        return new NotInDataRangeVerifyProvider();
    }

    /** 在数据数据范围内验证提供者 */
    public static class InDataRangeVerifyProvider implements VerifyProvider {

        /**
         * 支持的验证类型
         * @return 支持的验证类型
         */
        @Override
        public VerifyType supportedVerifyType() {
            return VerifyType.InDataRange;
        }

        /**
         * 验证
         * @param target 验证目标
         * @return 验证结果 true:验证通过  false:验证失败
         * @throws Exception 验证异常
         */
        @Override
        public boolean verify(Object target) throws Exception {
            DataRangeVerifyPackage<?> verifyPackage = getDataRangeVerifyPackage(target);
            if(verifyPackage == null) {
                return true;
            }

            if(ListUtil.isEmpty(verifyPackage.getRangeSet())) {
                return false;
            }

            return verifyPackage.getRangeSet().contains(verifyPackage.getValue());
        }
    }

    /** 不在数据数据范围内验证提供者 */
    public static class NotInDataRangeVerifyProvider implements VerifyProvider {

        /**
         * 支持的验证类型
         * @return 支持的验证类型
         */
        @Override
        public VerifyType supportedVerifyType() {
            return VerifyType.NotInDataRange;
        }

        /**
         * 验证
         * @param target 验证目标
         * @return 验证结果 true:验证通过  false:验证失败
         * @throws Exception 验证异常
         */
        @Override
        public boolean verify(Object target) throws Exception {
            DataRangeVerifyPackage<?> verifyPackage = getDataRangeVerifyPackage(target);
            if(verifyPackage == null) {
                return true;
            }

            if(ListUtil.isEmpty(verifyPackage.getRangeSet())) {
                return true;
            }

            return verifyPackage.getRangeSet().contains(verifyPackage.getValue()) == false;
        }
    }

    /**
     * 获取数据范围验证参数包, 无法获取时抛异常, 无需验证时返回null
     * @param target 验证目标
     * @return 数据范围验证参数包
     */
    private static DataRangeVerifyPackage<?> getDataRangeVerifyPackage(Object target) {
        Assert.notNull(target, "数据范围验证参数包不能为空");
        if(target instanceof DataRangeVerifyPackage == false) {
            throw new IllegalArgumentException("数据范围验证应使用 DataRangeVerifyPackage.valueOf 作为入参");
        }

        DataRangeVerifyPackage<?> verifyPackage = (DataRangeVerifyPackage<?>) target;
        if(verifyPackage.getValue() == null) {
            return null;
        }
        if(verifyPackage.getValue() instanceof String && StringUtils.isBlank((String) verifyPackage.getValue())) {
            return null;
        }
        return verifyPackage;
    }
}
