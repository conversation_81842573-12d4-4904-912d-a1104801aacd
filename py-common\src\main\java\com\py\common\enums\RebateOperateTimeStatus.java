package com.py.common.enums;

import lombok.AllArgsConstructor;

/**
 * 返点-操作时间类型：1-确认返点通过时间；2-返点登记时间
 * <AUTHOR>
 */
@AllArgsConstructor
public enum RebateOperateTimeStatus implements IDict<Integer> {
    /**
     * 确认返点通过时间
     */
    PASS_TIME(1,"确认返点通过时间"),

    /**
     * 返点登记时间
     */
    REGISTER_TIME(2,"返点登记时间")
    ;

    private final Integer value;
    private final String label;

    @Override
    public String getLabel() {
        return this.label;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
