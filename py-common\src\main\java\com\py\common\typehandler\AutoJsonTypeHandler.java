package com.py.common.typehandler;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * 自动Json类型处理器
 * <AUTHOR>
 */
@Log4j2
@SuppressWarnings("rawtypes")
@MappedTypes({Object.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class AutoJsonTypeHandler extends BaseJsonTypeHandler{

    private final Class<?> type;

    public AutoJsonTypeHandler(Class<?> type) {
        if (log.isTraceEnabled()) {
            log.trace("AutoJsonTypeHandler(" + type + ")");
        }
        Assert.notNull(type, "Type argument cannot be null");
        this.type = type;
    }

    /**
     * 元素类型
     * @return 元素类型
     */
    @Override
    protected Class getItemClass() {
        return this.type;
    }
}
