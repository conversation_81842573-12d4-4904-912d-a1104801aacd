package com.py.web.controller.system;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.utils.SecurityUtils;
import com.py.common.utils.tree.TreeSelect;
import com.py.common.utils.tree.TreeUtil;
import com.py.system.dept.domain.vo.DeptVO;
import com.py.system.menu.domain.SysMenu;
import com.py.system.menu.domain.dto.MenuMoveDTO;
import com.py.system.menu.domain.dto.SysMenuDTO;
import com.py.system.menu.domain.query.MenuListQuery;
import com.py.system.menu.service.ISysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 菜单信息
 * <AUTHOR>
 */
@Api(tags = "系统设置 - 菜单管理")
@RestController
@RequestMapping("/system/menu")
public class SysMenuController extends BaseController {

    /** 菜单服务 */
    @Resource
    private ISysMenuService menuService;

    /**
     * 获取菜单列表
     * @param query 菜单查询条件
     * @return 菜单列表
     */
    @ApiOperation(value = "获取菜单列表")
    @GetMapping("/list")
    public R<List<SysMenu>> list(MenuListQuery query) {
        List<SysMenu> menuList = menuService.selectMenuList(query, SecurityUtils.getUserId());
        List<SysMenu> menuTree = TreeUtil.buildTree(menuList);
        return R.success(menuTree);
    }

    /**
     * 菜单详情
     * @param menuId 菜单编号
     */
    @ApiOperation("菜单详情")
    @GetMapping(value = "/{menuId}")
    public R<SysMenu> getInfo(@PathVariable Long menuId) {
        return R.success(menuService.selectMenuById(menuId));
    }

    /**
     * 获取菜单下拉树列表
     * @param query 菜单查询条件
     * @return 菜单下拉树列表
     */
    @ApiOperation("获取菜单下拉树列表")
    @GetMapping("/treeselect")
    public R<List<TreeSelect<SysMenu>>> treeSelect(MenuListQuery query) {
        List<SysMenu> menus = menuService.selectMenuList(query, SecurityUtils.getUserId());
        List<TreeSelect<SysMenu>> menuTree = menuService.buildMenuTreeSelect(menus);
        return R.success(menuTree);
    }

    /**
     * 新增菜单
     */
    @ApiOperation("新增菜单")
    @Log(title = "菜单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@Validated @RequestBody SysMenuDTO menuDto) {
        this.menuService.insertMenu(menuDto);
        return R.success();
    }

    /**
     * 修改菜单
     */
    @ApiOperation("修改菜单")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@Validated @RequestBody SysMenuDTO menuDto) {
        this.menuService.updateMenu(menuDto);
        return R.success();
    }

    /**
     * 删除菜单
     * @param menuId 菜单ID
     * @return 是否成功
     */
    @ApiOperation("删除菜单")
    @Log(title = "菜单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{menuId}")
    public R<Boolean> remove(@PathVariable("menuId") Long menuId) {
        this.menuService.deleteMenuById(menuId);
        return R.success();
    }

    /**
     * 移动菜单
     * @param moveDto 菜单移动DTO
     * @return 是否成功
     */
    @ApiOperation("移动菜单")
    @Log(title = "移动菜单", businessType = BusinessType.UPDATE)
    @PutMapping("/move")
    public R<Boolean> move(@Validated @RequestBody MenuMoveDTO moveDto){
        this.menuService.move(moveDto);
        return R.success();
    }

    /**
     * 刷新菜单祖级列表
     * @return 刷新结果
     */
    @ApiOperation("刷新菜单祖级列表")
    @PutMapping("/refreshMenuAncestors")
    public R<Boolean> refreshMenuAncestors(){
        this.menuService.refreshMenuAncestors();
        return R.success();
    }

    /**
     * 查询列表上的更新部门下拉
     * @param query 查询条件
     * @return 是否成功
     */
    @ApiOperation("查询列表上的更新部门下拉" )
    @GetMapping("/listMenuDept" )
    public R<PageInfo<DeptVO>> listMenuDept(MenuListQuery query){
        return R.success(menuService.listMenuDept(query));
    }
}
