package com.py.crm.customer.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.py.common.constant.DictConstant;
import com.py.common.core.domain.BaseEntity;
import com.py.common.file.FileAnnex;
import com.py.common.tools.modifycomparator.annotation.CompareField;
import com.py.common.typehandler.impl.FileAnnexTypeHandler;
import com.py.common.typehandler.impl.StringSetTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户管理-客户对象
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@TableName(value = "py_crm_customer" ,autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("客户管理-客户" )
public class SupCustomer extends BaseEntity {
private static final long serialVersionUID=1L;

    /** 审批状态*/
    @ApiModelProperty("审批状态")
    private Integer auditStatus;

    /** 审批通过时间*/
    @ApiModelProperty("审批通过时间")
    private LocalDateTime auditTime;

    /** 发起时间*/
    @ApiModelProperty("发起时间")
    private LocalDateTime initiationTime;

    /** 报价策略(0.资源差价模式 1.拆分模式（策略费+服务费+资源返点）2.资源差价+拆分模式)*/
    @ApiModelProperty("报价策略(0.资源差价模式 1.拆分模式（策略费+服务费+资源返点）2.资源差价+拆分模式)")
    @CompareField(value = "报价策略",dictName = DictConstant.BIDDING_STRATEGY)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer biddingStrategy;

    /** 品牌阶段(0.初创品牌 1.成长期品牌 2.成熟品牌)*/
    @ApiModelProperty("品牌阶段(0.初创品牌 1.成长期品牌 2.成熟品牌)")
    @CompareField(value = "品牌阶段",dictName = DictConstant.BRAND_STAGE)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer brandStage;

    /** 品牌核心生意来源(0.全渠道销售（线下强，线上辅助）1.全渠道销售（线上强，线下辅助）2. 纯线上渠道)*/
    @ApiModelProperty("品牌核心生意来源(0.全渠道销售（线下强，线上辅助）1.全渠道销售（线上强，线下辅助）2. 纯线上渠道)")
    @CompareField(value = "品牌核心生意来源",dictName = DictConstant.BUSINESS_SOURCE)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer businessSource;

    /** 状态变更原因*/
    @ApiModelProperty("状态变更原因")
    private String changeStatusReason;

    /** 合作状态(0.合作中 1.暂停合作 2.意向合作)*/
    @ApiModelProperty("合作状态(0.合作中 1.暂停合作 2.意向合作)")
    @CompareField(value = "合作状态",dictName = DictConstant.COOPERATION_STATUS)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer cooperationStatus;

    /** 合作部门*/
    @ApiModelProperty("合作部门")
    @CompareField(value = "合作部门")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String cooperativeSector;

    /** 客户id*/
    @ApiModelProperty("客户id")
    @TableId
    private Long customerId;

    /** 客户来源(0.老客推荐 1.熟人介绍 2.主动BD 3.客户直联)*/
    @ApiModelProperty("客户来源(0.老客推荐 1.熟人介绍 2.主动BD 3.客户直联)")
    @CompareField(value = "客户来源",dictName = DictConstant.CUSTOMER_SOURCE)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer customerSource;

    /** 客户类型(0.品牌客户 1.平台客户 2.集团客户 3.渠道客户)*/
    @ApiModelProperty("客户类型(0.品牌客户 1.平台客户 2.集团客户 3.渠道客户)")
    @CompareField(value = "客户类型",dictName = DictConstant.CUSTOMER_TYPE)
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer customerType;

    /** 决策链路*/
    @ApiModelProperty("决策链路")
    @CompareField(value = "决策链路")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String decisionLink;

    /** 自增id*/
    @ApiModelProperty("自增id")
    private Long id;

    /** 品牌/业务线*/
    @ApiModelProperty("品牌/业务线")
    @CompareField(value = "品牌/业务线")
    private String lineBusiness;

    /** 客户名称*/
    @ApiModelProperty("客户名称")
    @CompareField(value = "客户名称")
    private String name;

    /** 备注*/
    @ApiModelProperty("备注")
    @CompareField(value = "备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String remark;

    /** 派芽服务产品(0.内容营销全链路服务 1.整合营销服务 2.内容营销策略及执行服务 3.媒介投放服务 4.MCN服务)*/
    @ApiModelProperty("派芽服务产品(0.内容营销全链路服务 1.整合营销服务 2.内容营销策略及执行服务 3.媒介投放服务 4.MCN服务)")
    @CompareField(value = "派芽服务产品",dictName = DictConstant.SERVICE_PRODUCT,isListDict = true)
    @TableField(updateStrategy = FieldStrategy.IGNORED,typeHandler = StringSetTypeHandler.class)
    private List<String> serviceProduct;

    /** 目标服务人员id*/
    @ApiModelProperty("目标服务人员id")
    private Long serviceUserId;

    /** 版本*/
    @ApiModelProperty("版本")
    @Version
    private Integer version;

    /** 成交金额 */
    @ApiModelProperty("成交金额")
    @TableField(exist = false)
    private BigDecimal transactionMoney;

    /** 行业类目id*/
    @ApiModelProperty("行业类目id" )
    @CompareField(value = "行业类目",dictName = DictConstant.INDUSTRY_CATEGORY,isListDict = true)
    @TableField(exist = false)
    private List<Long> industryCategoryIdList;


    /** 附件地址 {filename  ossKy}*/
    @TableField(typeHandler = FileAnnexTypeHandler.class,updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("附件地址 {filename  ossKy}")
    private List<FileAnnex> annex;

}
