package com.py.common.oss.model;

import com.py.common.tools.multisheetexcelexporter.domain.HyperlinkPackage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Oss上传结果
 * <AUTHOR>
 */
@Data
@ApiModel("Oss上传结果")
public class OssUploadResult {

    /** 上传文件名 */
    @ApiModelProperty("上传文件名")
    private String uploadFileName;

    /** 对象存储地址 */
    @ApiModelProperty("对象存储地址")
    private String ossKey;

    /** 对象临时访问Url */
    @ApiModelProperty("对象临时访问Url")
    private String ossUrl;

    public static HyperlinkPackage toHyperlinkPackage(OssUploadResult ossUploadResult) {
        if (ossUploadResult == null) {
            return new HyperlinkPackage();
        }
        return new HyperlinkPackage(ossUploadResult.getUploadFileName(), ossUploadResult.getOssUrl());
    }
}
