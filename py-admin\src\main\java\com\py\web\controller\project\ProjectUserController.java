package com.py.web.controller.project;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.poiexcel.ExcelUtil;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.project.projectuser.domain.dto.ProjectUserDTO;
import com.py.project.projectuser.domain.dto.ProjectUserExportModel;
import com.py.project.projectuser.domain.query.ProjectUserQuery;
import com.py.project.projectuser.domain.vo.ProjectUserListVO;
import com.py.project.projectuser.domain.vo.ProjectUserVO;
import com.py.project.projectuser.service.IProjectUserService;
import com.py.project.projectuser.service.impl.ProjectUserServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 项目管理-项目成员Controller
 *
 * <AUTHOR>
 * @date 2023-08-01
 */
@Api(tags = "项目管理-项目成员")
@RestController
@RequestMapping("/project/projectUser")
public class ProjectUserController extends BaseController {

    /** 项目管理-项目成员服务 */
    @Resource
    private IProjectUserService projectUserService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /**
     * 查询项目管理-项目成员列表
     *
     * @param query 项目管理-项目成员查询参数
     * @return 项目管理-项目成员列表
     */
    @ApiOperation("查询项目管理-项目成员列表")
    @PreAuthorize("@ss.hasPermi('project:projectUser:list')")
    @GetMapping("/listProjectUser")
    public R<List<ProjectUserListVO>> listProjectUser(ProjectUserQuery query) {
        List<ProjectUserListVO> voList = this.projectUserService.listProjectUser(query);
        return R.success(voList);
    }

    /**
     * 分页查询项目管理-项目成员列表
     *
     * @param query 项目管理-项目成员查询参数
     * @return 项目管理-项目成员分页
     */
    @ApiOperation("分页查询询项目管理-项目成员列表")
    @PreAuthorize("@ss.hasPermi('project:projectUser:list')")
    @GetMapping("/pageProjectUser")
    public R<PageInfo<ProjectUserListVO>> pageProjectUser(ProjectUserQuery query) {
        PageInfo<ProjectUserListVO> voList = this.projectUserService.pageProjectUserList(query);
        return R.success(voList);
    }

    /**
     * 获取项目管理-项目成员详细信息
     * @param id 项目管理-项目成员主键
     * @return 项目管理-项目成员视图模型
     */
    @ApiOperation("获取项目管理-项目成员详细信息")
    @PreAuthorize("@ss.hasPermi('project:projectUser:query')")
    @GetMapping(value = "/{id}")
    public R<ProjectUserVO> getInfo(@PathVariable("id") Long id) {
        return R.success(projectUserService.selectProjectUserById(id));
    }

    /**
     * 新增项目管理-项目成员
     *
     * @param dto 项目管理-项目成员修改参数
     * @return 是否成功
     */
    @ApiOperation("新增项目管理-项目成员")
    @PreAuthorize("@ss.hasPermi('project:projectUser:add')")
    @Log(title = "项目管理-项目成员", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody List<ProjectUserDTO> dto) {
        return R.success(projectUserService.insertProjectUser(dto));
    }

    /**
     * 修改项目管理-项目成员
     *
     * @param dto 项目管理-项目成员修改参数
     * @return 是否成功
     */
    @ApiOperation("修改项目管理-项目成员")
    @PreAuthorize("@ss.hasPermi('project:projectUser:edit')")
    @Log(title = "项目管理-项目成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody ProjectUserDTO dto) {
        return R.success(projectUserService.updateProjectUser(dto));
    }

    /**
     * 删除项目管理-项目成员
     * @param ids 需要删除的项目管理-项目成员主键集合
     * @return 是否成功
     */
    @ApiOperation("删除项目管理-项目成员" )
    @PreAuthorize("@ss.hasPermi('project:projectUser:remove')")
    @Log(title = "项目管理-项目成员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}" )
    public R<Boolean> remove(@PathVariable List<Long> ids) {
        return R.success(projectUserService.deleteProjectUserByIds(ids));
    }

    /**
     * 导出项目管理-项目成员
     * @param query 导出查询参数
     */
    @ApiOperation("导出项目管理-项目成员")
    @PreAuthorize("@ss.hasPermi('project:projectUser:export')")
    @Log(title = "项目管理-项目成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<String> export(@RequestBody ProjectUserQuery query) throws Exception {
        reusableAsyncTaskService.addTask("项目详情人员下载", TaskType.Export,query, ProjectUserServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 导入
     * @param file 导入文件
     * @return 导入结果
     * @throws Exception 导入异常
     */
    @PreAuthorize("@ss.hasPermi('project:projectUser:import')" )
    @Log(title = "项目管理-项目成员" , businessType = BusinessType.IMPORT)
    @PostMapping("/importData" )
    public R<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<ProjectUserExportModel> util = new ExcelUtil<>(ProjectUserExportModel.class);
        List<ProjectUserExportModel> projectUserList = util.importExcel(file.getInputStream());
        String message = this.projectUserService.importProjectUser(projectUserList);
        return R.success(message);
    }

    /**
     * 获取导入模板
     * @param response 请求响应
     */
    @PreAuthorize("@ss.hasPermi('project:projectUser:import')" )
    @Log(title = "项目管理-项目成员" , businessType = BusinessType.IMPORT)
    @PostMapping("/importTemplate" )
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ProjectUserExportModel> util = new ExcelUtil<>(ProjectUserExportModel.class);
        util.importTemplateExcel(response, "项目管理-项目成员数据" );
    }

}
