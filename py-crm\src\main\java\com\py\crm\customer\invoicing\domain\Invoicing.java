package com.py.crm.customer.invoicing.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.py.common.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.py.common.tools.modifycomparator.annotation.CompareField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户管理-客户-开票信息对象
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@TableName("py_crm_invoicing" )
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("客户管理-客户-开票信息" )
public class Invoicing extends BaseEntity {
private static final long serialVersionUID=1L;

    /** 银行账号*/
    @ApiModelProperty("银行账号")
    @CompareField(value = "银行账号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String bankAccount;

    /** 开户银行*/
    @ApiModelProperty("开户银行")
    @CompareField(value = "开户银行")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String bankDeposit;

    /** 公司地址*/
    @ApiModelProperty("公司地址")
    @CompareField(value = "公司地址")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String companyAddress;

    /** 客户id*/
    @ApiModelProperty("客户id")
    private Long customerId;

    /** 税号*/
    @ApiModelProperty("税号")
    @CompareField(value = "税号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String dutyParagraph;

    /** 自增id*/
    @ApiModelProperty("自增id")
    private Long id;

    /** 开票id*/
    @ApiModelProperty("开票id")
    @TableId
    private Long invoicingId;

    /** 开票名称*/
    @ApiModelProperty("开票名称")
    @CompareField(value = "开票名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String invoicingName;

    /** 电话*/
    @ApiModelProperty("电话")
    @CompareField(value = "电话")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String phone;
    /** 展示id */
    @ApiModelProperty("展示id")
    private Integer showId;
}
