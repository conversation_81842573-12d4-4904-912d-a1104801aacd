package com.py.web.controller.resources;

import com.github.pagehelper.PageInfo;
import com.py.common.annotation.Log;
import com.py.common.constant.Constants;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.common.core.domain.model.LoginUser;
import com.py.common.enums.BusinessType;
import com.py.common.enums.TaskType;
import com.py.common.tools.codegenerator.IBusinessSerialNumberGenerator;
import com.py.common.tools.codegenerator.enums.SerialNumberBizType;
import com.py.common.tools.reusableasynctask.IReusableAsyncTaskService;
import com.py.common.utils.DateUtils;
import com.py.common.utils.SecurityUtils;
import com.py.resources.download.domain.dto.DownloadDTO;
import com.py.resources.download.domain.query.DownloadQuery;
import com.py.resources.download.domain.vo.DownloadListVO;
import com.py.resources.download.service.IDownloadService;
import com.py.resources.download.service.impl.DownloadServiceImpl;
import com.py.resources.downloadcontrol.domain.dto.DownloadControlDTO;
import com.py.resources.downloadcontrol.domain.vo.DownloadControlVO;
import com.py.resources.downloadcontrol.service.IDownloadControlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 媒介资源管理-资源下载记录Controller
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Api(tags = "媒介资源管理-资源下载记录")
@RestController
@RequestMapping("/download")
public class DownloadController extends BaseController {

    /** 媒介资源管理-资源下载记录服务 */
    @Resource
    private IDownloadService downloadService;

    /** 媒介资源管理-下载控制Service接口 */
    @Resource
    private IDownloadControlService downloadControlService;

    /** 可视化异步任务服务 */
    @Resource
    private IReusableAsyncTaskService reusableAsyncTaskService;

    /** 业务序列号生成器 */
    @Resource
    private IBusinessSerialNumberGenerator businessSerialNumberGenerator;

    /**
     * 分页查询媒介资源管理-资源下载记录列表
     *
     * @param query 媒介资源管理-资源下载记录查询参数
     * @return 媒介资源管理-资源下载记录分页
     */
    @ApiOperation("分页查询询媒介资源管理-资源下载记录列表")
    @PreAuthorize("@ss.hasPermi('com.py.resources:download:page')")
    @GetMapping("/page")
    public R<PageInfo<DownloadListVO>> pageDownload(DownloadQuery query) {
        PageInfo<DownloadListVO> voList = this.downloadService.pageDownloadList(query);
        return R.success(voList);
    }

    /**
     * 查询媒介资源管理-资源下载记录列表,下载人部门
     *
     * @param query 媒介资源管理-资源下载记录查询参数
     * @return 媒介资源管理-资源下载记录分页
     */
    @ApiOperation("分页查询询媒介资源管理-资源下载记录列表")
    @PostMapping("/findCreateDept")
    public R<List<String>> findCreateDept(@RequestBody DownloadQuery query) {
        return R.success(this.downloadService.findCreateDept(query));
    }

    /**
     * 新增媒介资源管理-资源下载记录
     *
     * @param dto 媒介资源管理-资源下载记录修改参数
     * @return 是否成功
     */
    @ApiOperation("新增媒介资源管理-资源下载记录")
    @PreAuthorize("@ss.hasPermi('com.py.resources:download:add')")
    @Log(title = "媒介资源管理-资源下载记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R<Boolean> add(@Validated @RequestBody DownloadDTO dto) {
        return R.success(downloadService.insertDownload(dto));
    }

    /**
     * 下载媒介资源管理-资源下载记录
     * @param query 导出查询参数
     */
    @ApiOperation("下载媒介资源管理-资源下载记录")
    @PreAuthorize("@ss.hasPermi('com.py.resources:download:export')")
    @Log(title = "媒介资源管理-资源下载记录", businessType = BusinessType.EXPORT)
    @PostMapping("/batchDetail")
    public R<String> batchDetail(@RequestBody DownloadQuery query){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        query.setLoginUser(loginUser);
        String time = DateUtils.dateTime();
        long serialNo = businessSerialNumberGenerator.generateSerialNumber(SerialNumberBizType.DOWNLOAD
                , Constants.DOWNLOAD_NAME_NUMBER + time, () -> 1L
                , DateUtils.getTodayToTomorrowTime(), TimeUnit.MINUTES);
        String filename = time + "-" + serialNo + ".zip";
        query.setFileName(filename);
        reusableAsyncTaskService.addTask("资源下载管理", TaskType.Export, query, DownloadServiceImpl.class);
        return R.success(Constants.SUBMIT_SUCCESS);
    }

    /**
     * 新增管理员媒介资源管理-资源下载控制
     *
     * @param dto 媒介资源管理-资源下载控制修改参数
     * @return 是否成功
     */
    @ApiOperation("新增管理员媒介资源管理-资源下载控制")
    @PreAuthorize("@ss.hasPermi('com.py.resources:download:saveAdminControl')")
    @Log(title = "媒介资源管理-资源下载控制", businessType = BusinessType.INSERT)
    @PostMapping("/saveAdminControl")
    public R<Boolean> saveAdminControl(@Validated @RequestBody DownloadControlDTO dto) {
        return R.success(downloadControlService.saveAdminDownloadControl(dto));
    }

    /**
     * 新增媒介资源管理-资源下载控制
     *
     * @param dtoList 媒介资源管理-资源下载控制修改参数
     * @return 是否成功
     */
    @ApiOperation("新增媒介资源管理-资源下载控制")
    @PreAuthorize("@ss.hasPermi('com.py.resources:download:saveControl')")
    @Log(title = "媒介资源管理-资源下载控制", businessType = BusinessType.INSERT)
    @PostMapping("/saveControl")
    public R<Boolean> saveControl(@RequestBody List<DownloadControlDTO> dtoList) {
        return R.success(downloadControlService.saveDownloadControl(dtoList));
    }

    /**
     * 查看媒介资源管理-资源下载控制
     *
     * @return 是否成功
     */
    @ApiOperation("查看媒介资源管理-资源下载控制")
    @PreAuthorize("@ss.hasPermi('com.py.resources:download:queryControl')")
    @GetMapping("/queryControl")
    public R<List<DownloadControlVO>> queryControl() {
        return R.success(downloadControlService.queryControl());
    }
}
