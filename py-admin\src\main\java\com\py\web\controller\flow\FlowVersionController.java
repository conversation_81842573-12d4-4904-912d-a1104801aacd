package com.py.web.controller.flow;

import com.github.pagehelper.PageInfo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.py.common.core.controller.BaseController;
import com.py.common.core.domain.R;
import com.py.flow.flowversion.domain.dto.ChangeFlowVersionStatusDTO;
import com.py.flow.flowversion.domain.dto.FlowVersionCopyDTO;
import com.py.flow.flowversion.domain.dto.FlowVersionDTO;
import com.py.flow.flowversion.domain.query.FlowPropertyQuery;
import com.py.flow.flowversion.domain.query.FlowVersionQuery;
import com.py.flow.flowversion.domain.vo.FlowPropertyVO;
import com.py.flow.flowversion.domain.vo.FlowVersionListVO;
import com.py.flow.flowversion.domain.vo.FlowVersionVO;
import com.py.flow.flowversion.service.IFlowVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 流程版本
 * <AUTHOR>
 * @date 2023/7/20 11:27
 */
@Api(tags = "流程管理 - 流程版本")
@RestController
@RequestMapping("/flow-version")
public class FlowVersionController extends BaseController {

    /** 流程版本服务 */
    @Resource
    private IFlowVersionService flowVersionService;

    /**
     * 列表版本列表
     * @param flowVersionQuery 流程版本查询参数
     * @return 分页列表
     */
    @ApiOperation("列表版本列表")
    @GetMapping("/pageFlowVersion")
    public R<PageInfo<FlowVersionListVO>> pageFlowVersion(@Validated FlowVersionQuery flowVersionQuery) {
        return R.success(this.flowVersionService.pageFlowVersion(flowVersionQuery));
    }

    /**
     * 获取流程版本详情
     * @param flowVersionId 流程ID
     * @return 流程版本详情
     */
    @ApiOperation("流程版本详情")
    @GetMapping("/getInfo")
    public R<FlowVersionVO> getInfo(@RequestParam("flowVersionId") Long flowVersionId) {
        return R.success(flowVersionService.getFlowVersionInfo(flowVersionId));
    }

    /**
     * 获取审批页面可用的流程属性
     * @param flowPropertyQuery 流程属性查询参数
     * @return 流程属性列表
     */
    @ApiOperation("获取审批页面可用的流程属性")
    @GetMapping("/listApprovalPageUsableFlowProperty")
    public R<List<FlowPropertyVO>> listApprovalPageUsableFlowProperty(@Validated FlowPropertyQuery flowPropertyQuery) {
        return R.success(this.flowVersionService.listApprovalPageUsableFlowProperty(flowPropertyQuery));
    }

    /**
     * 新增流程版本配置
     * @param flowVersionDto 流程版本配置
     * @return 是否成功
     */
    @ApiOperation("新增流程版本配置")
    @PostMapping("/addFlowVersion")
    public R<Boolean> addFlowVersion(@Validated @RequestBody FlowVersionDTO flowVersionDto) {
        this.flowVersionService.addFlowVersion(flowVersionDto);
        return R.success();
    }

    /**
     * 更新流程版本配置
     * @param flowVersionDto 流程版本配置
     * @return 是否成功
     */
    @ApiOperation("更新流程版本配置")
    @PostMapping("/updateFlowVersion")
    public R<Boolean> updateFlowVersion(@Validated @RequestBody FlowVersionDTO flowVersionDto) {
        this.flowVersionService.updateFlowVersion(flowVersionDto);
        return R.success();
    }

    /**
     * 上下线流程版本
     * @param changeDto 流程版本状态修改DTO
     * @return 是否成功
     */
    @ApiOperation("上下线流程版本")
    @PostMapping("/onOffFlowVersion")
    public R<Boolean> onOffFlowVersion(@Validated @RequestBody ChangeFlowVersionStatusDTO changeDto) {
        this.flowVersionService.onOffFlowVersion(changeDto);
        return R.success();
    }

    /**
     * 复制流程版本
     * @param copyDto 流程复制DTO
     * @return 是否成功
     */
    @ApiOperation("复制流程版本")
    @PostMapping("/copyFlowVersion")
    public R<Boolean> copyFlowVersion(@Validated @RequestBody FlowVersionCopyDTO copyDto) {
        this.flowVersionService.copyFlowVersion(copyDto);
        return R.success();
    }

    /**
     * 删除流程版本
     * @param flowVersionId 流程版本id
     * @return 是否成功
     */
    @ApiOperation("删除流程版本")
    @DeleteMapping("/deleteFlowVersion/{flowVersionId}")
    public R<Boolean> deleteFlow(@PathVariable Long flowVersionId) {
        this.flowVersionService.deleteFlowVersion(flowVersionId);
        return R.success();
    }

    /**
     * 刷新流程版本配置
     * @return 是否成功
     */
    @ApiOperationSupport(order = 9999)
    @ApiOperation("刷新流程版本配置")
    @PostMapping("/refreshFlowVersionConfig")
    public R<Boolean> refreshFlowVersionConfig() {
        this.flowVersionService.refreshFlowVersionConfig();
        return R.success();
    }
}
