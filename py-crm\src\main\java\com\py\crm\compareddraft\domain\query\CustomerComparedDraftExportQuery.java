package com.py.crm.compareddraft.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 客户管理-比稿管理导出
 * <AUTHOR>
 */
@Data
@ApiModel("客户管理-比稿管理导出")
public class CustomerComparedDraftExportQuery {

    /** 客户ID */
    @ApiModelProperty("客户ID")
    @NotNull(message = "客户ID不能为空")
    private Long customerId;

    /** 比稿ID列表 */
    @NotEmpty(message = "比稿ID列表不能为空")
    @ApiModelProperty("比稿ID列表")
    private List<Long> comparedDraftIdList;
}
