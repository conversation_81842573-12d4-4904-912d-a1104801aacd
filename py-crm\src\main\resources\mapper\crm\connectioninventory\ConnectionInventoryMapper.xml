<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.py.crm.connectioninventory.mapper.ConnectionInventoryMapper">

    <resultMap type="com.py.crm.connectioninventory.domain.ConnectionInventory" id="ConnectionInventoryResult">
        <result property="id" column="id" />
        <result property="inventoryId" column="inventory_id" />
        <result property="connectionId" column="connection_id" />
        <result property="createId" column="create_id" />
        <result property="createBy" column="create_by" />
        <result property="createDept" column="create_dept" />
        <result property="createTime" column="create_time" />
        <result property="updateId" column="update_id" />
        <result property="updateBy" column="update_by" />
        <result property="updateDept" column="update_dept" />
        <result property="updateTime" column="update_time" />
        <result property="delFlag" column="del_flag" />
    </resultMap>

    <resultMap type="com.py.crm.connectioninventory.domain.vo.ConnectionInventoryListVO" id="ConnectionResult">
        <result property="inventoryId" column="inventory_id" />
        <result property="connectionId" column="connection_id" />
        <result property="connectionName" column="connection_name" />
        <result property="phone" column="phone" />
        <result property="status" column="status" />
        <result property="connectionAddress" column="connection_address" />
        <result property="wechatNumber" column="wechat_number" />
        <result property="otherNumber" column="other_number" />
        <result property="currentEmployer" column="current_employer" />
        <result property="responsibleBrand" column="responsible_brand"
                typeHandler="com.py.common.typehandler.impl.StringSetTypeHandler"/>
        <result property="industryCategory" column="industry_category"
                typeHandler="com.py.common.typehandler.impl.LongSetTypeHandler"/>
        <result property="departmentName" column="department_name" />
        <result property="postName" column="post_name" />
        <result property="pyTrustLevel" column="py_trust_level" />
        <result property="serviceUserId" column="service_user_id" />
        <result property="createBy" column="create_by" />
        <result property="createDept" column="create_dept" />
        <result property="createTime" column="create_time" />
    </resultMap>

    <!---分页查询人脉清单列表数据-->
    <select id="listConnectionInventory" resultMap="ConnectionResult">
        select distinct crmconnection.connection_id, crmconnection.connection_name, crmconnection.phone, crmconnection.wechat_number, crmconnection.other_number,
            crmconnection.current_employer, crmconnection.responsible_brand, crmconnection.department_name, crmconnection.post_name,
            crmconnection.py_trust_level, crmconnection.service_user_id, crmconnection.create_time, crmconnection.create_by, crmconnection.create_dept,
            inventory.inventory_id, crmconnection.industry_category, crmconnection.status, crmconnection.connection_address, crmconnection.remark,inventory.id
        from py_crm_connection crmconnection
        join py_crm_connection_inventory inventory on crmconnection.connection_id = inventory.connection_id
        join py_crm_connection_wander wander on crmconnection.connection_id = wander.connection_id
        <where>
            <trim prefixOverrides="WHERE">
                ${connectionWrapper.customSqlSegment}
            </trim>
            AND crmconnection.del_flag = 0
            AND inventory.del_flag = 0
            AND wander.del_flag = 0
            AND inventory.create_id = #{createId}
        </where>
        order by crmconnection.status desc, crmconnection.create_time desc
    </select>

    <!--查询人脉清单数量-->
    <select id="countConnectionInventory"
            resultType="com.py.crm.connectioninventory.domain.vo.ConnectionInventoryCountVO">
        select count(distinct crmconnection.id) count,
                crmconnection.STATUS status
        from py_crm_connection crmconnection
        join py_crm_connection_inventory inventory on crmconnection.connection_id = inventory.connection_id
        join py_crm_connection_wander wander on crmconnection.connection_id = wander.connection_id
        <where>
            <trim prefixOverrides="WHERE">
                ${connectionWrapper.customSqlSegment}
            </trim>
            AND wander.del_flag = 0
            AND crmconnection.del_flag = 0
            AND inventory.del_flag = 0
            AND inventory.create_id = #{createId}
        </where>
        GROUP BY crmconnection.STATUS
    </select>

</mapper>
