package com.py.common.tools.multisheetexcelexporter.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 附件相对实体
 * <AUTHOR>
 * @version HyperlinkPathPackage 2023/8/17 11:49
 */
@Data
public class HyperlinkPathPackage {
    /** 文件名 */
    @ApiModelProperty("文件名")
    private String fileName;

    /** 相对地址 */
    @ApiModelProperty("相对地址")
    private String pathUrl;


    public HyperlinkPathPackage(HyperlinkPathPackage apply) {
        this.fileName = apply.fileName;
        this.pathUrl = apply.pathUrl;
    }

    public HyperlinkPathPackage() {
    }

    public HyperlinkPathPackage(String fileName, String pathUrl) {
        this.fileName = fileName;
        this.pathUrl = pathUrl;
    }
}
