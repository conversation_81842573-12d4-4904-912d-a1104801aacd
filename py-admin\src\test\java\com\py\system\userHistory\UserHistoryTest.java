package com.py.system.userHistory;

import com.py.common.core.domain.entity.SysUser;
import com.py.common.utils.collection.ListUtil;
import com.py.system.user.domain.query.UserOptionQuery;
import com.py.system.user.service.ISysUserService;
import com.py.system.user.userhistory.domain.dto.UserHistoryDTO;
import com.py.system.user.userhistory.domain.vo.UserHistoryListVO;
import com.py.system.user.userhistory.service.IUserHistoryService;
import lombok.extern.log4j.Log4j2;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version UserHistoryTest 2024/1/24 17:52
 */
@Log4j2
@SpringBootTest
@RunWith(SpringRunner.class)
public class UserHistoryTest {

    /**
     * 用户 业务层
     */
    @Resource
    private ISysUserService sysUserService;

    /** 用户名称历史Service接口 */
    @Resource
    private IUserHistoryService userHistoryService;
    /** 同步老数据 */
    @Test
    @Ignore
    public void addUserHistory() {
        List<UserHistoryListVO> userHistoryListVOS = userHistoryService.listUserHistory(new UserOptionQuery());
        List<Long> userIdList = ListUtil.distinctMap(userHistoryListVOS, UserHistoryListVO::getUserId);
        List<SysUser> sysUserList = sysUserService.selectUserByUserId(userIdList);
        // 新增历史用户名
        if(ListUtil.isNotEmpty(sysUserList)){
            List<UserHistoryDTO> userHistoryDTOList = new ArrayList<>(sysUserList.size());
            sysUserList.forEach(sysUser -> {
                UserHistoryDTO userHistoryDTO = new UserHistoryDTO();
                userHistoryDTO.setUserId(sysUser.getUserId());
                userHistoryDTO.setUserName(sysUser.getUserName());
                userHistoryDTO.setPhoneNumber(sysUser.getPhoneNumber());
                userHistoryDTO.setCreateTime(sysUser.getUpdateTime());
                userHistoryDTOList.add(userHistoryDTO);
            });
            userHistoryService.batchUserHistory(userHistoryDTOList);
        }
    }
}
