package com.py.common.core.domain;

import com.py.common.enums.DelFlag;

import java.time.LocalDateTime;

/**
 * 基础新增信息接口
 * <AUTHOR>
 */
public interface IBaseCreateInfo {

    /**
     * 获取创建者Id
     * @return 创建者Id
     */
    Long getCreateId();

    /**
     * 设置创建者Id
     * @param createId 创建者Id
     */
    void setCreateId(Long createId);

    /**
     * 获取创建者
     * @return 创建者
     */
    String getCreateBy();

    /**
     * 设置创建者
     * @param createBy 创建者
     */
    void setCreateBy(String createBy);

    /**
     * 获取创建时间
     * @return 创建时间
     */
    LocalDateTime getCreateTime();

    /**
     * 设置创建时间
     * @param createTime 创建时间
     */
    void setCreateTime(LocalDateTime createTime);

    /**
     * 获取删除标志
     * @return 删除标志
     */
    DelFlag getDelFlag();

    /**
     * 设置删除标志
     * @param delFlag 删除标志
     */
    void setDelFlag(DelFlag delFlag);

    /**
     * 获取创建部门
     * @return 创建部门
     */
    String getCreateDept();

    /**
     * 设置创建部门
     * @param createDept 创建部门
     */
    void setCreateDept(String createDept);

}
