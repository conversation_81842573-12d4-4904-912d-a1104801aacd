package com.py.crm.customer.invoicing.domain.dto;

import com.py.common.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * 客户管理-客户-开票信息数据传输模型
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Data
@ApiModel("客户管理-客户-开票信息数据传输模型" )
public class InvoicingDTO {
    private static final long serialVersionUID = 1L;

    /** 银行账号*/
    @ApiModelProperty("银行账号" )
    @Size(max = 100,message = "银行账号最大100字")
    private String bankAccount;

    /** 开户银行*/
    @ApiModelProperty("开户银行" )
    @Size(max = 100,message = "开户银行最大100字")
    private String bankDeposit;

    /** 公司地址*/
    @ApiModelProperty("公司地址" )
    @Size(max = 100,message = "公司地址最大100字")
    private String companyAddress;

    /** 客户id*/
    @ApiModelProperty("客户id" )
    private Long customerId;

    /** 税号*/
    @ApiModelProperty("税号" )
    @Size(max = 100,message = "税号最大100字")
    private String dutyParagraph;

    /** 自增id*/
    @ApiModelProperty("自增id" )
    private Long id;

    /** 开票id*/
    @ApiModelProperty("开票id" )
    private Long invoicingId;

    /** 开票名称*/
    @ApiModelProperty("开票名称" )
    @Size(max = 100,message = "开票名称最大100字")
    private String invoicingName;

    /** 电话*/
    @ApiModelProperty("电话" )
    @Size(max = 100,message = "电话最大100字")
    private String phone;

    /** 展示id */
    @ApiModelProperty("展示id")
    private Integer showId;

    /** 空 false */
    public Boolean isEmpty(){
        return StringUtils.isBlank(invoicingName) &&
                StringUtils.isBlank(phone) &&
                StringUtils.isBlank(dutyParagraph) &&
                StringUtils.isBlank(companyAddress) &&
                StringUtils.isBlank(bankDeposit) &&
                StringUtils.isBlank(bankAccount);
    }

}
