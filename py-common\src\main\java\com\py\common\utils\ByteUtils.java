package com.py.common.utils;

/**
 * 字节工具类
 * <AUTHOR>
 */
public final class ByteUtils {

    /**
     * 将字节数组按索引倒序转换为二进制字符串
     * @param bytes 需要转换的字节数组
     * @return 二进制字符串
     */
    public static String toBinString(byte[] bytes) {
        if(bytes == null || bytes.length == 0) {
            return StringUtils.EMPTY;
        }

        StringBuilder stringBuilder = new StringBuilder(bytes.length * Byte.SIZE);
        for(byte value : bytes) {
            for(int index = Byte.SIZE - 1; index >= 0; index--) {
                int result = value & (1 << index);
                stringBuilder.append(result != 0 ? "1" : "0");
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 对字节数组所有值取反码
     * @param bytes 需求设置的字节数组
     */
    public static void setInverseCode(byte[] bytes) {
        for(int i = 0, bytesLength = bytes.length; i < bytesLength; i++) {
            bytes[i] = (byte) ~bytes[i];
        }
    }
}
