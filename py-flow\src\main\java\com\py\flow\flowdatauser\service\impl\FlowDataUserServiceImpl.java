package com.py.flow.flowdatauser.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.py.common.mybatisplus.SuperServiceImpl;
import com.py.common.utils.StringUtils;
import com.py.common.utils.collection.ListUtil;
import com.py.flow.domain.enums.FlowReApprovalStrategy;
import com.py.flow.domain.enums.FlowUserEventType;
import com.py.flow.flowdatauser.converter.FlowDataUserConverter;
import com.py.flow.flowdatauser.domain.FlowDataUser;
import com.py.flow.flowdatauser.domain.enums.FlowDataUserAuditStatus;
import com.py.flow.flowdatauser.domain.vo.ApproveUserVO;
import com.py.flow.flowdatauser.domain.vo.FlowDataUserVO;
import com.py.flow.flowdatauser.mapper.FlowDataUserMapper;
import com.py.flow.flowdatauser.service.IFlowDataUserService;
import com.py.flow.flowinstance.domain.FlowInstance;
import com.py.flow.tools.flownotifier.model.FlowUserEvent;
import com.py.flow.tools.flownotifier.model.extend.ApprovalForwardEvent;
import com.py.flow.tools.flownotifier.model.extend.FlowUserApplyEvent;
import com.py.flow.tools.flownotifier.model.extend.FlowUserReturnEvent;
import com.py.system.user.service.ISysUserService;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审批数据与人员关联Service业务层处理
 * <AUTHOR>
 */
@Service
public class FlowDataUserServiceImpl
        extends SuperServiceImpl<FlowDataUserMapper, FlowDataUser>
        implements IFlowDataUserService {

    /** 审批数据与人员关联模型转换器 */
    @Resource
    private FlowDataUserConverter flowDataUserConverter;

    /** 用户 业务层 */
    @Resource
    private ISysUserService sysUserService;

    /**
     * 处理用户审批事件
     * @param event 用户审批事件
     * @return 是否成功
     */
    @Override
    public boolean handleFlowUserEvent(FlowUserEvent event) {
        Assert.notEmpty(event.getSeedUserIdList(), "审批人不可为空");

        switch(event.getEventType()) {
            // 审批申请
            case ApprovalApply:
                Assert.isInstanceOf(FlowUserApplyEvent.class, event, "审批申请事件类型错误");
                return this.handleFlowUserEventOnApprovalApply((FlowUserApplyEvent) event);
            // 审批转交
            case ApprovalForward:
                Assert.isInstanceOf(ApprovalForwardEvent.class, event, "审批申请事件类型错误");
                return this.handleFlowUserEventOnApprovalForward((ApprovalForwardEvent) event);
            // 审批同意 | 审批驳回
            case ApprovalAgree:
            case ApprovalReject:
                return this.handleFlowUserEventOnApproverOperation(event);
            // 审批退回
            case ApprovalReturn:
                Assert.isInstanceOf(FlowUserReturnEvent.class, event, "审批退回事件类型错误");
                return this.handleFlowUserEventOnApprovalReturn((FlowUserReturnEvent) event);
            // 审批通过 | 审批作废 | 审批取消
            case ApprovalPass:
            case ApprovalInvalidated:
            case ApprovalCancel:
                return this.handleFlowUserEventOnApprovalEnd(event);
            // 忽略其他事件
            default:
                return true;
        }
    }


    /**
     * 根据业务id查询审批人
     * @param bizIdList 业务id
     * @return 业务审批人Map (业务Id, 审批人)
     */
    @Override
    public Map<Long, String> listFlowDataUserByBizId(List<Long> bizIdList) {
        if(ListUtil.isEmpty(bizIdList)) {
            return ListUtil.emptyMap();
        }
        List<FlowDataUser> flowDataUserList = this.list(Wrappers.<FlowDataUser>lambdaQuery()
                .eq(FlowDataUser::getAuditStatus, FlowDataUserAuditStatus.Approval)
                .eq(FlowDataUser::getCurrentNode, true)
                .in(FlowDataUser::getBizId, bizIdList));
        List<FlowDataUserVO> flowDataUserVOList = flowDataUserConverter.toVoByEntity(flowDataUserList);
        sysUserService.relatedUpdateInfo(flowDataUserVOList);
        Map<Long, List<FlowDataUserVO>> bizIdListMap = ListUtil.toGroup(flowDataUserVOList, FlowDataUserVO::getBizId);
        Map<Long, String> bizIdUserNameMap = new HashMap<>(bizIdList.size());
        bizIdList.forEach(id -> {
            List<FlowDataUserVO> flowDataUserVoS = bizIdListMap.get(id);
            String userName = ListUtil.joining(flowDataUserVoS, ",", FlowDataUserVO::getUserName);
            bizIdUserNameMap.put(id, userName);
        });
        return bizIdUserNameMap;
    }

    /**
     * 赋值审批人
     * @param approveUserVOList 所要赋值的数据
     */
    @Override
    public void setApprovalUser(List<? extends ApproveUserVO> approveUserVOList) {
        if(ListUtil.isEmpty(approveUserVOList)) {
            return;
        }
        List<Long> bizIdList = ListUtil.distinctMap(approveUserVOList, ApproveUserVO::getBizId);
        Map<Long, String> bizIdUserNameMap = this.listFlowDataUserByBizId(bizIdList);
        if(ListUtil.isNotEmpty(approveUserVOList)) {
            approveUserVOList.forEach(approveUserVO -> {
                String userName = bizIdUserNameMap.get(approveUserVO.getBizId());
                if(StringUtils.isNotBlank(userName)) {
                    approveUserVO.setApproveUserName(userName);
                }
            });
        }
    }

    /**
     * 添加上一流程审批人到当前流程
     * @param lastFlowInstance 上一流程实例
     * @param newFlowInstance 当前流程实例
     */
    @Override
    public void addLastFlowHistoricalApproverToCurrentFlow(FlowInstance lastFlowInstance, FlowInstance newFlowInstance) {
        LambdaQueryWrapper<FlowDataUser> lastFlowHistoricalApproverQueryWrapper = Wrappers.lambdaQuery(FlowDataUser.class)
                .eq(FlowDataUser::getProcessInstanceId, lastFlowInstance.getProcessInstanceId())
                .eq(FlowDataUser::getHistoryApprover, true);
        List<FlowDataUser> lastFlowHistoricalApproverList = this.list(lastFlowHistoricalApproverQueryWrapper);
        if(ListUtil.isEmpty(lastFlowHistoricalApproverList)){
            return;
        }

        List<FlowDataUser> newFlowDataUserList = ListUtil.map(lastFlowHistoricalApproverList, lastHistoricalApprover -> {
            FlowDataUser newFlowDataUser = this.flowDataUserConverter.deepCopyIgBaseData(lastHistoricalApprover);
            newFlowDataUser.setBizId(newFlowInstance.getBizId());
            newFlowDataUser.setProcessInstanceId(newFlowInstance.getProcessInstanceId());
            newFlowDataUser.setCurrentNode(false);
            return newFlowDataUser;
        });
        this.saveBatch(newFlowDataUserList);
    }

    /**
     * 处理用户审批事件 - 审批结束
     * <p>审批进入终止状态时, 将当前审批的所有节点都设置为非当前节点</p>
     * @param event 用户审批事件
     * @return 是否成功
     */
    private boolean handleFlowUserEventOnApprovalEnd(FlowUserEvent event) {
        LambdaUpdateWrapper<FlowDataUser> updateWrapper = Wrappers.lambdaUpdate(FlowDataUser.class)
                .set(FlowDataUser::getCurrentNode, false)
                .set(FlowDataUser::getAuditStatus, FlowDataUserAuditStatus.ApprovedPass)
                .eq(FlowDataUser::getProcessInstanceId, event.getProcessInstanceId());
        return this.update(updateWrapper);
    }

    /**
     * 处理用户审批事件 - 新增审批申请时
     * <p>新增当前审批人, 并将其他审批节点设置为非当前审批人</p>
     * @param event 用户审批申请事件
     * @return 是否成功
     */
    private boolean handleFlowUserEventOnApprovalApply(FlowUserApplyEvent event) {
        List<FlowDataUser> flowDataUserList = ListUtil.map(event.getSeedUserIdList(), userId -> {
            FlowDataUser flowDataUser = new FlowDataUser();
            flowDataUser.setUserId(userId);
            flowDataUser.setBizType(event.getBizType());
            flowDataUser.setBizId(event.getBizId());
            flowDataUser.setActiveId(event.getLogicActiveId());
            flowDataUser.setCurrentNode(true);
            flowDataUser.setHistoryApprover(false);
            flowDataUser.setTaskId(event.getTaskId());
            flowDataUser.setProcessInstanceId(event.getProcessInstanceId());
            flowDataUser.setAuditStatus(FlowDataUserAuditStatus.Approval);
            return flowDataUser;
        });
        // 新增流程审批人
        boolean isSucceeded = this.saveBatch(flowDataUserList);

        // 更新其他流程节点的审批人记录为非当前节点
        LambdaUpdateWrapper<FlowDataUser> wrapper = Wrappers.lambdaUpdate(FlowDataUser.class)
                .set(FlowDataUser::getCurrentNode, false)
                .eq(FlowDataUser::getCurrentNode, true)
                .eq(FlowDataUser::getProcessInstanceId, event.getProcessInstanceId())
                .ne(FlowDataUser::getActiveId, event.getLogicActiveId());
        this.update(wrapper);

        return isSucceeded;
    }

    /**
     * 处理用户审批事件 - 审批转交
     * <p>转交时将被转交人设置为当前审批人, 将转交人设置为非当前审批人</p>
     * @param event 用户审批转交事件
     * @return 是否成功
     */
    private boolean handleFlowUserEventOnApprovalForward(ApprovalForwardEvent event) {
        // 新增转交人的审批节点
        boolean isSucceeded = this.handleFlowUserEventOnApprovalApply(event);
        // 将转交人的审批节点设置为非当前节点
        LambdaUpdateWrapper<FlowDataUser> wrapper = Wrappers.lambdaUpdate(FlowDataUser.class)
                .set(FlowDataUser::getCurrentNode, false)
                .eq(FlowDataUser::getCurrentNode, true)
                .eq(FlowDataUser::getUserId, event.getOldApproverId())
                .eq(FlowDataUser::getProcessInstanceId, event.getProcessInstanceId())
                .eq(FlowDataUser::getActiveId, event.getLogicActiveId());
        this.update(wrapper);
        return isSucceeded;
    }

    /**
     * 处理用户审批事件 - 审批同意 | 审批驳回
     * <p>设置当前审批节点通过, 驳回时调用审批结束处理</p>
     * @param event 用户审批事件
     * @return 是否成功
     */
    private boolean handleFlowUserEventOnApproverOperation(FlowUserEvent event) {
        if(event.getEventType() == FlowUserEventType.ApprovalReject) {
            this.handleFlowUserEventOnApprovalEnd(event);
        }
        LambdaUpdateWrapper<FlowDataUser> updateWrapper = Wrappers.<FlowDataUser>lambdaUpdate()
                .set(FlowDataUser::getAuditStatus, FlowDataUserAuditStatus.ApprovedPass)
                .eq(FlowDataUser::getProcessInstanceId, event.getProcessInstanceId())
                .eq(FlowDataUser::getBizId, event.getBizId())
                .eq(FlowDataUser::getBizType, event.getBizType())
                .eq(FlowDataUser::getUserId, event.getOperatorId());

        if(event.getEventType() == FlowUserEventType.ApprovalAgree){
            // 用户审批同意时, 将其标志为历史审批人
            updateWrapper.set(FlowDataUser::getHistoryApprover, true);
        }

        return this.update(updateWrapper);
    }

    /**
     * 处理用户审批事件 - 审批退回
     * <p>当审批退回时, 根据审批退回策略:</p>
     * <p>  ○ 重新逐级审批时, 被退回的中间节点不算历史审批人</p>
     * <p>  ○ 从上一失败节点审批时, 被退回的中间节点算历史审批人, 退回到的两个节点不算历史审批人</p>
     * @param event 用户审批退回事件
     * @return 是否成功
     */
    private boolean handleFlowUserEventOnApprovalReturn(FlowUserReturnEvent event) {
        FlowReApprovalStrategy reApprovalStrategy = event.getFlowInstance().getReApprovalStrategy();
        if(reApprovalStrategy == FlowReApprovalStrategy.Restart){
            // 重新逐级审批时
            LambdaUpdateWrapper<FlowDataUser> wrapper = Wrappers.lambdaUpdate(FlowDataUser.class)
                    .set(FlowDataUser::getHistoryApprover, false)
                    .eq(FlowDataUser::getProcessInstanceId, event.getProcessInstanceId())
                    .in(FlowDataUser::getActiveId, event.getReturnActiveIdSet());
            this.update(wrapper);
        }else if(reApprovalStrategy == FlowReApprovalStrategy.JumpToFailNode){
            // 从上一失败节点审批时
            LambdaUpdateWrapper<FlowDataUser> wrapper = Wrappers.lambdaUpdate(FlowDataUser.class)
                    .set(FlowDataUser::getHistoryApprover, false)
                    .eq(FlowDataUser::getProcessInstanceId, event.getProcessInstanceId())
                    .eq(FlowDataUser::getActiveId, event.getReturnTargetActiveId());
            this.update(wrapper);
        }else {
            throw new UnsupportedOperationException(String.format("枚举: %s未对应操作未实现", reApprovalStrategy));
        }

        return this.handleFlowUserEventOnApprovalEnd(event);
    }

}
