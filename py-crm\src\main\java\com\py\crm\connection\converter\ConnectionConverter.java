package com.py.crm.connection.converter;

import com.py.common.core.converter.BaseDomainModelConverter;
import com.py.common.tools.verify.domain.RowVerifyError;
import com.py.crm.connection.domain.Connection;
import com.py.crm.connection.domain.dto.*;
import com.py.crm.connection.domain.vo.ConnectionListVO;
import com.py.crm.connection.domain.vo.ConnectionVO;
import com.py.crm.connectionemployment.domain.dto.ConnectionEmploymentDTO;
import com.py.crm.connectioninventory.domain.vo.ConnectionInventoryListVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * 人脉管理表模型转换器
 *
 * <AUTHOR>
 * @date 2023-07-19
 */
@Mapper(componentModel = "spring")
public interface ConnectionConverter extends BaseDomainModelConverter<Connection, ConnectionVO, ConnectionDTO> {

    /**
     * 将Entity转换为ListVo
     * @param entity entity
     * @return listVo
     */
    ConnectionListVO toListVoByEntity(Connection entity);

    /**
     * 将Entity转换为ListVo
     * @param entity entity
     * @return listVo
     */
    List<ConnectionListVO> toListVoByEntity(List<Connection> entity);

    /**
     * 将Entity转换为导出模型
     * @param entityList entity列表
     * @return 导出模型列表
     */
    List<ConnectionExportModel> toExportModel(List<Connection> entityList);

    /**
     * 将导出模型转换为Entity
     * @param exportList 导出数据列表
     * @return entity列表
     */
    List<Connection> toEntityByExportModel(List<ConnectionExportModel> exportList);

    /**
     * 转换为从业经历
     * @param oldConnection Entity
     * @return DTO
     */
    ConnectionEmploymentDTO toEmploymentDtoByEntity(Connection oldConnection);

    /**
     * 转换为从业经历
     * @param entityList entity
     * @return DTO
     */
    List<ConnectionUpdateDTO> toUpdateDtoByEntity(List<Connection> entityList);

    /**
     * 转换为从业经历
     * @param updateDTOList DTOList
     * @return entity
     */
    List<Connection> toEntityByUpdateDto(List<ConnectionUpdateDTO> updateDTOList);

    /**
     * 禁用启用转换成entity
     * @param dto  dto
     * @return entity
     */
    Connection toEntityByEnableDto(ConnectionEnableDTO dto);

    /**
     * 流转信息转换
     * @param dto dto
     * @return entity
     */
    Connection toEntityByCirculationDto(ConnectionCirculationDTO dto);

    /**
     * update信息转换
     * @param connectionUpdate DTO
     * @return vo
     */
    ConnectionVO toVoByUpdateDto(ConnectionUpdateDTO connectionUpdate);

    /**
     * 错误信息转换
     * @param errorRowVerifyList
     * @return
     */
    List<RowVerifyError<ConnectionExportErrorModel>> toExportByConnectionImport(List<RowVerifyError<ConnectionImportModel>> errorRowVerifyList);

    /**
     * 人脉信息转换
     * @param connectionList
     * @return
     */
    List<ConnectionInventoryListVO> toListVoByEntityList(List<Connection> connectionList);
}
